<!DOCTYPE html>
<html>
<head>
  #parse("/templates/consumer/tempsolver/header_custom.vm")
  #if($isSHowMeat)
  <meta id="duiba-share-url" name="duiba-share-url" content="${hdShareLink}" >
  #end
  <title>${title}</title>
  #parse("/templates/consumer/tempsolver/common_custom.vm")
  <link rel="stylesheet" type="text/css" href="${domainConfig.cdnDomain}/h5/activity/turnCard/3.0.0/index_201712211533.css" />
  #if($showCredits)
  <style>
    .myCredits{display: none;}
    .needCredits{display: none;}
  </style>
  #end
  #if($showRecord)
  <style>
    .record {display: none!important;}
  </style>
  #end
</head>
<body duiba-page-id="4">
  #parse("/templates/consumer/tempsolver/new_nav_header.vm")
  <div id="db-content">
    <img class="banner" src="">
    <a class="record" href="${crecord}"></a>
    <div class="status">
      <div class="myCredits"></div>
      <div class="needCredits"><p></p></div>
    </div>
    <div class="main" id="main">
      <div class="start" id="start"></div>
      <p class="start-tip">请任意选择一张</p>
      <div class="mask">洗牌中···</div>
    </div>
    <div class="rule">
      <div class="rule-title"></div>
      <section></section>
    </div>
    <div class="prizes" >
      <div class="prizes-title"></div>
      <section id="prizes-list"></section>
    </div>
    <div class="probability-rules">
      <header>
        <div class="line left"></div>
        <span>中奖概率说明</span>
        <i></i>
        <div class="line right"></div>
      </header>
      <section></section>
    </div>
  </div>
  
  #parse("consumer/tempsolver/token_custom.vm")
  #parse("consumer/tempsolver/base_custom.vm") 
  <script type="text/javascript">
    CFG.appName = 'turnCard';
    CFG.asyncFiles = [
      '${domainConfig.cdnDomain}/h5/activity/turnCard/3.0.0/components_201712211533.js'
    ];
  </script>
  <script type="text/javascript" src="${domainConfig.cdnDomain}/h5/activity/turnCard/3.0.0/index_201712211533.js"></script>
  #if($isHdShareOpen)
    #evaluate($hdShareProgram)
  #end
  #parse("consumer/tempsolver/editor.vm")
  #if($isDeveloperHdTool)
    #parse("consumer/tempsolver/_main_meet_aerosol.vm")
  #end
  #parse("consumer/tempsolver/_requirelogin.vm")
  #parse("consumer/tempsolver/customer_service.vm")
</body>
</html>