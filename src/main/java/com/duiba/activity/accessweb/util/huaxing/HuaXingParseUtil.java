package com.duiba.activity.accessweb.util.huaxing;

import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.vo.huaxing.GdhxChargeRequest;
import com.duiba.activity.accessweb.vo.huaxing.GdhxGoodsInfo;
import com.duiba.activity.accessweb.vo.huaxing.GdhxPriceInfo;
import com.duiba.activity.accessweb.vo.huaxing.GdhxRefundStatInfo;
import com.duiba.activity.accessweb.vo.huaxing.GdhxStatiInfo;
import com.duiba.activity.accessweb.vo.huaxing.GdhxWapRefundChargeRequest;
import com.google.common.collect.Lists;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.Security;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.duiba.activity.accessweb.util.huaxing.FileSendAndReciveDemo.secretKey;

/**
 * 声明：以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己需要，按照技术文档编写。该代码仅供参考，不提供编码，性能，规范性等方面的保障<br>
 */
public class HuaXingParseUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HuaXingParseUtil.class);

    public static final String PROJECT_NAME = "华兴银行 ";
    public static final String ENCODE_UTF_8 = "UTF-8";
    private static final String RES_TIME_PATTERN = "yyyyMMddHHmmssSSS";
    private static final String RES_ID_PREFIX = "DUI";
    private static volatile String SECRET_KEY_OLD = null;

    static {
        if (Security.getProvider("BC") != null) {
            Security.removeProvider("BC");
        }
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    /**
     * 组装请求报文
     *
     * @param privateString
     * @param secretKey
     * @param secretKey
     * @return
     */
    public static JSONObject getGHBRequestMsg(String privateString, String userId, String secretKey, JSONObject res) {
        // 报文头Json对象
        JSONObject headerJsonObject = res.getJSONObject("header");
        try {
            String responseTime = getResponseTime();
            headerJsonObject.put("charset", ENCODE_UTF_8);
            headerJsonObject.put("responseId", getResponseId(responseTime));
            headerJsonObject.put("responseTime", responseTime);
            headerJsonObject.put("subCode", StringUtils.EMPTY);
            headerJsonObject.put("subMsg", StringUtils.EMPTY);
            // 报文体字符串
            String bodyStr = StringUtils.defaultIfBlank(res.getString("body"), StringUtils.EMPTY);
            String encryptData = HuaXingSM4Utils.encryptData_ECB(bodyStr.trim(), secretKey, true);
            encryptData = Base64.encodeBase64String(encryptData.getBytes(ENCODE_UTF_8));
            res.put("body", encryptData);
            //签名原文
            String srcData = encryptData + headerJsonObject.getString("requestId") + headerJsonObject.getString("responseId");
            String signData = sign(userId, srcData, getPrivateKey(privateString));
            signData = Base64.encodeBase64String(signData.getBytes(ENCODE_UTF_8));
            headerJsonObject.put("signData", signData);
            byte[] md = new byte[32];
            HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
            sm3.update(bodyStr.getBytes(), 0, bodyStr.getBytes().length);
            sm3.doFinal(md, 0);
            String bodySm3Value = new String(Hex.encode(md)).toUpperCase();
            headerJsonObject.put("reserve", bodySm3Value);
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("{} 响应数据 验签错误 {}", PROJECT_NAME, res.toJSONString(), e);
            headerJsonObject.put("errorCode", HuaXingCodeEnum.PARAM_SIGN_ERROR.getCode());
            headerJsonObject.put("errorMsg", HuaXingCodeEnum.PARAM_SIGN_ERROR.getMsg());
        }
        return res;
    }

    /**
     * 组装请求报文
     *
     * @param privateString
     * @param secretKey
     * @param secretKey
     * @return
     */
    public static JSONObject getGHBRequestMsgNew(String privateString, String userId, String secretKey, JSONObject res) {
        // 报文头Json对象
        JSONObject headerJsonObject = res.getJSONObject("header");
        try {
            String responseTime = getResponseTime();
            headerJsonObject.put("charset", ENCODE_UTF_8);
            headerJsonObject.put("responseId", getResponseId(responseTime));
            headerJsonObject.put("responseTime", responseTime);
            headerJsonObject.put("subCode", StringUtils.EMPTY);
            headerJsonObject.put("subMsg", StringUtils.EMPTY);
            // 报文体字符串
            String bodyStr = StringUtils.defaultIfBlank(res.getString("body"), StringUtils.EMPTY);
            String encryptData = HuaXingSM4Utils.encryptData_ECB(bodyStr.trim(), secretKey, true);
            encryptData = Base64.encodeBase64String(encryptData.getBytes(ENCODE_UTF_8));
            res.put("body", encryptData);
            //签名原文
            String srcData = encryptData + headerJsonObject.getString("requestId") + headerJsonObject.getString("responseId");
            String signData = sign(userId, srcData, getPrivateKey(privateString));
            signData = Base64.encodeBase64String(signData.getBytes(ENCODE_UTF_8));
            headerJsonObject.put("signData", signData);
            byte[] md = new byte[32];
            HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
            sm3.update(bodyStr.getBytes(), 0, bodyStr.getBytes().length);
            sm3.doFinal(md, 0);
            String bodySm3Value = new String(Hex.encode(md)).toUpperCase();
            headerJsonObject.put("reserve", bodySm3Value);
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("{} 产品推送响应数据 验签错误 {}", PROJECT_NAME, res.toJSONString(), e);
            headerJsonObject.put("errorCode", HuaXingCodeEnum.PARAM_SIGN_ERROR.getCode());
            headerJsonObject.put("errorMsg", HuaXingCodeEnum.PARAM_SIGN_ERROR.getMsg());
        }
        return res;
    }

    /**
     * 组装业务请求报文
     *
     * @param bodyString
     * @param secretKey
     * @param appId
     * @param userId
     * @param privateString
     * @return
     */
    public static JSONObject assembleGHBBusinessRequest(String bodyString, String secretKey, String appId, String userId, String privateString) {
        JSONObject request = new JSONObject();
        // body
        String encryptBodyString = HuaXingSM4Utils.encryptData_ECB(bodyString.trim(), secretKey, true);
        if (StringUtils.isNotBlank(encryptBodyString)) {
            encryptBodyString = Base64.encodeBase64String(encryptBodyString.getBytes(StandardCharsets.UTF_8));
        }
        // header
        JSONObject header = new JSONObject();
        header.put("appId", appId);
        String requestTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String requestId = requestTime + RandomUtils.nextInt(100000, 1000000);
        header.put("requestId", requestId);
        header.put("requestTime", requestTime);
        header.put("charset", ENCODE_UTF_8);
        // signData
        String srcData = encryptBodyString + requestId + requestTime;
        String signData = sign(userId, srcData, getPrivateKey(privateString));
        signData = Base64.encodeBase64String(signData.getBytes(StandardCharsets.UTF_8));
        header.put("signData", signData);
        // header的reserve
        HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
        sm3.update(bodyString.getBytes(), 0, bodyString.getBytes().length);
        byte[] md = new byte[32];
        sm3.doFinal(md, 0);
        String bodySm3Value = new String(Hex.encode(md)).toUpperCase();
        header.put("reserve", bodySm3Value);
        // 组装返回
        request.put("body", encryptBodyString);
        request.put("header", header);
        return request;
    }

    /**
     * 解析业务响应报文
     *
     * @param response
     * @param secretKey
     * @param userId
     * @param publicString
     * @return
     */
    public static JSONObject parseGHBBusinessResponse(JSONObject response, String secretKey, String userId, String publicString) {
        String responseJson = response.toJSONString();
        // 验证签名
        String encryptBodyString = response.getString("body");
        JSONObject header = response.getJSONObject("header");
        String originData = encryptBodyString + header.get("requestId") + header.get("responseId");
        String signData = header.getString("signData");
        signData = new String(Base64.decodeBase64(signData.getBytes(StandardCharsets.UTF_8)));
        if (!verify(userId, originData, signData, getPublicKey(publicString))) {
            LOGGER.error("{} 解析业务响应报文，签名错误，response=[{}]", PROJECT_NAME, responseJson);
            return null;
        }
        // 解密body
        String bodyString = null;
        String secretKeyOld = HuaXingParseUtil.getSecretKeyOld();
        try {
            bodyString = HuaXingSM4Utils.decryptData_ECB(new String(Base64.decodeBase64(encryptBodyString.getBytes(StandardCharsets.UTF_8))), secretKey, true);
            response.put("replaceSecretKey", !Objects.equals(secretKey, secretKeyOld));
        } catch (Exception e) {
            LOGGER.info("{} 解析业务响应报文，新秘钥解密异常，response=[{}], secretKey=[{}]", PROJECT_NAME, responseJson, secretKey, e);
            if (StringUtils.isNotBlank(secretKeyOld) && !Objects.equals(secretKey, secretKeyOld)) {
                try {
                    bodyString = HuaXingSM4Utils.decryptData_ECB(new String(Base64.decodeBase64(encryptBodyString.getBytes(StandardCharsets.UTF_8))), secretKeyOld, true);
                } catch (Exception ex) {
                    LOGGER.info("{} 解析业务响应报文，旧秘钥解密异常，response=[{}], secretKeyOld=[{}]", PROJECT_NAME, responseJson, secretKeyOld, e);
                }
            }
        }
        // 判空
        if (StringUtils.isBlank(bodyString)) {
            LOGGER.error("{} 解析业务响应报文，解密结果为空，response=[{}]", PROJECT_NAME, responseJson);
            return null;
        }
        // 验证reserve
        String reserve = header.getString("reserve");
        HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
        sm3.update(bodyString.trim().getBytes(), 0, bodyString.trim().getBytes().length);
        byte[] md = new byte[32];
        sm3.doFinal(md, 0);
        String realReserve = new String(Hex.encode(md)).toUpperCase();
        if (!realReserve.equalsIgnoreCase(reserve)) {
            LOGGER.error("{} 解析业务响应报文，reserve不对，response=[{}]", PROJECT_NAME, responseJson);
            return null;
        }
        // 解析body
        LOGGER.info("{} 解析业务响应报文，正常，response=[{}], body=[{}]", PROJECT_NAME, responseJson, bodyString);
        return JSONObject.parseObject(bodyString);
    }

    /**
     * 生成支付积分的body
     *
     * @param uid
     * @param orderNo
     * @param quantity
     * @param merchNum
     * @param secretKey
     * @param userId
     * @param privateString
     * @return
     */
    public static String generatePayCreditsBody(String uid, String orderNo, int quantity, String merchNum, String secretKey, String userId, String privateString) {
        JSONObject body = new JSONObject();
        String transTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        body.put("transTime", transTime);
        JSONObject params = new JSONObject();
        params.put("PTY_ID", uid);
        params.put("MERCH_NUM", merchNum);
        params.put("ORDER_NUM", orderNo);
        params.put("GROSS_QTTY", quantity);
        params.put("CNSM_TYP", "P");
        body.put("params", params);
        // signData
        String srcData = StringUtils.join(Lists.newArrayList(uid, merchNum, transTime, orderNo, quantity), "");
        String signData = Hex.toHexString(HuaXingGmUtil.signSm3WithSm2(srcData.getBytes(), userId.getBytes(), getPrivateKey(privateString))).toUpperCase();
        body.put("signData", signData);
        // 加密body
        return HuaXingSM4Utils.encryptData_ECB(body.toJSONString().trim(), secretKey, true);
    }

    /**
     * 生成支付的body
     *
     * @param request
     * @param merchNum
     * @param secretKey
     * @param userId
     * @param privateString
     * @return
     */
    public static String generatePayBody(GdhxChargeRequest request, String merchNum, String secretKey, String userId, String privateString,String mrchdTyp) {
        JSONObject body = new JSONObject();
        body.put("transTime", request.getTransTime());
        JSONObject params = new JSONObject();
        params.put("PTY_ID", request.getUid());
        params.put("MERCH_NUM", merchNum);
        //代表兑吧入口类型
        params.put("MRCHD_TYP",mrchdTyp);
        params.put("ORDER_NUM", request.getOrderNo());
        params.put("FREI_AMT", request.getFreight() == null ? 0 : request.getFreight());
        params.put("CONS_NAME",request.getConsName());
        params.put("MRCHD_STATI_INFO", getInfo(request.getInfos()));
        //0907新增字段
        params.put("MRCHD_INFO_SET",getGoodsInfo(request.getGoodsInfos()));
        body.put("params", params);
        LOGGER.info("{}params {}", PROJECT_NAME,params);
        int quantity = request.getInfos().stream().mapToInt(GdhxStatiInfo::getTotalValue).sum();
        BigDecimal cossQuantity = BigDecimal.valueOf(quantity).divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_UNNECESSARY);
        // signData
        String srcData = StringUtils.join(Lists.newArrayList(request.getUid(),"",  request.getTransTime(),merchNum, request.getOrderNo(),params.get("FREI_AMT"), request.getConsName(),"",cossQuantity,""), "&");
        LOGGER.info("{}签名原串 {}", PROJECT_NAME,srcData);
        String signData = Hex.toHexString(HuaXingGmUtil.signSm3WithSm2(srcData.getBytes(), userId.getBytes(), getPrivateKey(privateString))).toUpperCase();
        body.put("signData", signData);
        LOGGER.info("{}signData {}", PROJECT_NAME,signData);
        // 加密body
        return HuaXingSM4Utils.encryptData_ECB(body.toJSONString().trim(), secretKey, true);
    }

    private static List<JSONObject> getInfo(List<GdhxStatiInfo> infos) {
        ArrayList<JSONObject> result = new ArrayList<>();
        for (GdhxStatiInfo info : infos) {
            JSONObject item = new JSONObject()
                    .fluentPut("GROSS_QTTY", new BigDecimal(info.getTotalValue()).divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_UNNECESSARY))
                    .fluentPut("CNSM_TYP", info.getType());
            result.add(item);
        }
        return result;
    }

    private static List<JSONObject> getGoodsInfo(List<GdhxGoodsInfo> goodsInfos) {
        ArrayList<JSONObject> result = new ArrayList<>();
        for (GdhxGoodsInfo info : goodsInfos) {
            JSONObject item = new JSONObject()
                    .fluentPut("MRCHD_ENCD", info.getPrizeId())
                    .fluentPut("MRCHD_NAME", info.getPrizeName())
                    .fluentPut("PROVI_NAME",info.getProvider())
                    .fluentPut("MRCHD_QTTY",info.getPrizeNum())
                    .fluentPut("MRCHD_PRICE_SET_INFO",getPriceInfo(info.getPriceInfos()));
            result.add(item);
        }
        return result;
    }

    private static List<JSONObject> getPriceInfo(List<GdhxPriceInfo> prizeInfo) {
        ArrayList<JSONObject> result = new ArrayList<>();
        for (GdhxPriceInfo info : prizeInfo) {
            JSONObject item = new JSONObject()
                    .fluentPut("MRCHD_PRICE", new BigDecimal(info.getPrice()).divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_UNNECESSARY))
                    .fluentPut("CNSM_TYP", info.getType());
            result.add(item);
        }
        return result;
    }

    public static List<JSONObject> getRefundInfo(List<GdhxRefundStatInfo> infos) {
        ArrayList<JSONObject> result = new ArrayList<>();
        for (GdhxRefundStatInfo info : infos) {
            JSONObject item = new JSONObject()
                    .fluentPut("GROSS_QTTY", new BigDecimal(info.getTotalValue()).divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_UNNECESSARY).toString())
                    .fluentPut("CNSM_TYP", info.getType());
            result.add(item);
        }
        return result;
    }

    public static List<JSONObject> getRefundGoodsInfo(List<GdhxRefundStatInfo> infos) {
        ArrayList<JSONObject> result = new ArrayList<>();
        for (GdhxRefundStatInfo info : infos) {
            JSONObject item = new JSONObject()
                    .fluentPut("MRCHD_ENCD", info.getPrizeId())
                    .fluentPut("CNSM_TYP", info.getType())
                    .fluentPut("CRSPD_MRCHD_GROSS_VAL",new BigDecimal(info.getTotalValue()).divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_UNNECESSARY).toString());
            result.add(item);
        }
        return result;
    }
    /**
     * 解析银行主动通知报文
     *
     * @param publicString
     * @param userId
     * @param secretKey
     * @param param        返回报文
     * @return
     */
    public static JSONObject parseGHBResponseMsg(String publicString, String userId, String secretKey, JSONObject param) {
        LOGGER.info("test secretKey::: {}", secretKey);
        param.put("replaceSecretKey", false);
        String secretKeyOld = HuaXingParseUtil.getSecretKeyOld();
        // 报文头Json对象
        JSONObject headerJsonObject = param.getJSONObject("header");
        // 报文体字符串
        String bodyStr = param.getString("body");
        // 组装待验证原文：body+responseId+responseTime+errorCode+errorMsg
        String originData = bodyStr + headerJsonObject.getString("requestId") + headerJsonObject.getString("requestTime");
        // 获取请求报文头header里的签名数据signData
        String signData = headerJsonObject.getString("signData");
        try {
            signData = new String(Base64.decodeBase64(signData.getBytes(ENCODE_UTF_8)));
            if (verify(userId, originData, signData, getPublicKey(publicString))) {
                // System.out.println("验签成功！！ ");
            } else {
                LOGGER.error("{} 解析请求报文 验签失败 {}", PROJECT_NAME, param.toJSONString());
                return null;
            }
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("{} 解析请求报文 验签错误 {}", PROJECT_NAME, param.toJSONString(), e);
        }
        try {
            String bodyValue = HuaXingSM4Utils.decryptData_ECB(new String(Base64.decodeBase64(bodyStr.getBytes(ENCODE_UTF_8))), secretKey, true);
            //由于SM4解密用错误密钥能解密出来且是乱码，所以解密后需校验body明文的摘要是否相等
            String reserve = (String) headerJsonObject.get("reserve");
            byte[] md = new byte[32];
            HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
            sm3.update(bodyValue.trim().getBytes(), 0, bodyValue.trim().getBytes().length);
            sm3.doFinal(md, 0);
            String srcDataSm3 = new String(Hex.encode(md)).toUpperCase();
            if (!srcDataSm3.equalsIgnoreCase(reserve)) {
                throw new Exception("解密失败");
            }
            // 标志密钥变化了
            if (!StringUtils.equals(secretKey, secretKeyOld)) {
                param.put("replaceSecretKey", true);
            }
            // bodyStr = bodyValue;
            return JSONObject.parseObject(bodyValue);
        } catch (Exception e) {//首次解密失败尝试旧密钥
            if (StringUtils.isBlank(secretKeyOld)) {
                LOGGER.error("{} 解析请求报文 解密错误 {}", PROJECT_NAME, param.toJSONString(), e);
                return null;
            }
            try {
                String bodyValue = HuaXingSM4Utils.decryptData_ECB(new String(Base64.decodeBase64(bodyStr.getBytes(ENCODE_UTF_8))), secretKeyOld, true);
                String reserve = (String) headerJsonObject.get("reserve");
                byte[] md = new byte[32];
                HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
                sm3.update(bodyValue.trim().getBytes(), 0, bodyValue.trim().getBytes().length);
                sm3.doFinal(md, 0);
                String srcDataSm3 = new String(Hex.encode(md)).toUpperCase();
                if (!srcDataSm3.equalsIgnoreCase(reserve)) {
                    LOGGER.error("{} 解析请求报文 解密失败 old {}", PROJECT_NAME, param.toJSONString());
                    return null;
                }
                // bodyStr = bodyValue;
                return JSONObject.parseObject(bodyValue);
            } catch (Exception e1) {
                LOGGER.error("{} 解析请求报文 解密错误 old {}", PROJECT_NAME, param.toJSONString(), e1);
                return null;
            }
        }
    }

    /**
     * 申请秘钥组装报文
     *
     * @param publicString
     * @param privateString
     * @param param
     * @return
     */
    public static JSONObject getSecretKeyRequestMsg(String publicString, String privateString, String userId, JSONObject param) {
        try {
            // 报文头Json对象
            JSONObject headerJsonObject = param.getJSONObject("header");
            // 报文体字符串
            String bodyStr = StringUtils.defaultIfBlank(param.getString("body"), StringUtils.EMPTY);
            // SM2加密
            byte[] encodeData = HuaXingGmUtil.sm2Encrypt(bodyStr.getBytes(), getPublicKey(publicString));
            // 对加密数据进行BASE64编码
            String encodeBodyStr = Base64.encodeBase64String(encodeData);
            param.put("body", encodeBodyStr);
            //签名原文
            String srcData = encodeBodyStr + headerJsonObject.getString("requestId") + headerJsonObject.getString("requestTime");
            String signData = sign(userId, srcData, getPrivateKey(privateString));
            signData = Base64.encodeBase64String(signData.getBytes(ENCODE_UTF_8));
            headerJsonObject.put("signData", signData);
            byte[] md = new byte[32];
            HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
            sm3.update(bodyStr.getBytes(), 0, bodyStr.getBytes().length);
            sm3.doFinal(md, 0);
            String bodySm3Value = new String(Hex.encode(md)).toUpperCase();
            headerJsonObject.put("reserve", bodySm3Value);
        } catch (Exception e) {
            LOGGER.warn("{} 获取密钥请求报文失败 {}", PROJECT_NAME, param.toJSONString(), e);
        }
        return param;
    }

    /**
     * 解析秘钥组装报文
     *
     * @param publicString
     * @param privateString
     * @param userId
     * @param res
     * @return
     */
    public static JSONObject parseSecretKeyRequestMsg(String publicString, String privateString, String userId, JSONObject res) {
        // 返回报文
        // net.sf.json.JSONObject responseMsgJsonObj = net.sf.json.JSONObject.fromObject(msg);
        // 报文头Json对象
        JSONObject headerJsonObject = res.getJSONObject("header");
        // 报文体字符串
        String bodyStr = res.getString("body");
        // 组装待验证原文：body+responseId+responseTime+errorCode+errorMsg
        String originData = bodyStr + headerJsonObject.getString("requestId") + headerJsonObject.getString("responseId");
        // 获取请求报文头header里的签名数据signData
        String signData = headerJsonObject.getString("signData");
        try {
            signData = new String(Base64.decodeBase64(signData.getBytes(ENCODE_UTF_8)));
            if (verify(userId, originData, signData, getPublicKey(publicString))) {
                // System.out.println("验签成功！！ ");
            } else {
                LOGGER.error("{} 解析密钥请求报文 验签失败 {}", PROJECT_NAME, res.toJSONString());
                return null;
            }
        } catch (UnsupportedEncodingException e) {
            LOGGER.warn("{} 解析密钥请求报文 验签错误 {}", PROJECT_NAME, res.toJSONString(), e);
        }
        try {
            bodyStr = new String(HuaXingGmUtil.sm2Decrypt(Base64.decodeBase64(bodyStr.getBytes(ENCODE_UTF_8)), getPrivateKey(privateString)), ENCODE_UTF_8);
        } catch (Exception e) {
            LOGGER.error("{} 解析密钥请求报文 解密错误 {}", PROJECT_NAME, res.toJSONString(), e);
            return null;
        }
        return JSONObject.parseObject(bodyStr);
    }

    private static PublicKey getPublicKey(String publicString) {
        String string = (publicString.indexOf("04") == 0) ? publicString.substring(2) : publicString;
        return HuaXingGmUtil.getPublickeyFromXY(new BigInteger(string.substring(0, 64), 16), new BigInteger(string.substring(64, 128), 16));
    }

    private static BCECPrivateKey getPrivateKey(String privateString) {
        return HuaXingGmUtil.getPrivatekeyFromD(new BigInteger(privateString, 16));
    }

    /**
     * 签名
     *
     * @param srcData
     * @param bcecPrivateKey
     * @return
     */
    public static String sign(String userId, String srcData, BCECPrivateKey bcecPrivateKey) {
        String signData = null;
        byte[] md = new byte[32];
        HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
        sm3.update(srcData.getBytes(), 0, srcData.getBytes().length);
        sm3.doFinal(md, 0);
        srcData = new String(Hex.encode(md)).toUpperCase();
        byte[] sig = HuaXingGmUtil.signSm3WithSm2(srcData.getBytes(), userId.getBytes(), bcecPrivateKey);
        signData = Hex.toHexString(sig).toUpperCase();
        return signData;
    }

    /**
     * 验签
     *
     * @param userId
     * @param srcData
     * @param signData
     * @param publicKey
     * @return
     */
    private static boolean verify(String userId, String srcData, String signData, PublicKey publicKey) {
        byte[] md = new byte[32];
        HuaXingSM3Digest sm3 = new HuaXingSM3Digest();
        sm3.update(srcData.getBytes(), 0, srcData.getBytes().length);
        sm3.doFinal(md, 0);
        srcData = new String(Hex.encode(md)).toUpperCase();
        return HuaXingGmUtil.verifySm3WithSm2(srcData.getBytes(), userId.getBytes(), Hex.decode(signData), publicKey);
    }

    public static String urlEncoder(String url) {
        try {
            return URLEncoder.encode(url, ENCODE_UTF_8);
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("{} URLEncoder error {}", PROJECT_NAME, url, e);
            return StringUtils.EMPTY;
        }
    }

    public static String getRequestId(String respTime) {
        return respTime + getOrderId();
    }

    public static String getResponseId(String respTime) {
        return RES_ID_PREFIX + (respTime + getOrderId()).substring(3);
    }

    public static String getResponseTime() {
        return DateFormatUtils.format(System.currentTimeMillis(), RES_TIME_PATTERN);
    }

    public static Date parseDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(RES_TIME_PATTERN);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }

    public static int getRedisExpTimeSeconds(String dateStr) {
        Date applyTime = HuaXingParseUtil.parseDate(dateStr);
        Long expTimeMills = DateUtils.minutesAddOrSub(applyTime, 360).getTime();
        return (int) ((expTimeMills - System.currentTimeMillis()) / 1000);
    }

    public static String getOrderId() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

    public static String getSecretKeyOld() {
        return SECRET_KEY_OLD;
    }

    public static void setSecretKeyOld(String value) {
        SECRET_KEY_OLD = value;
    }

    public static String getNewRedisKey() {
        return RedisKeyFactory.K7000.toString() + "0001";
    }

    public static String getOldRedisKey() {
        return RedisKeyFactory.K7000.toString() + "0002";
    }
}
