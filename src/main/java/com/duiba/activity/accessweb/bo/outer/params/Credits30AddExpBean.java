package com.duiba.activity.accessweb.bo.outer.params;

import com.duiba.activity.accessweb.enums.AddExpTypeEnum;

/**
 * 积分商城3.0参与活动工具，游戏，插件加经验参数封装
 *
 * Created by <PERSON><PERSON><PERSON> on 2018/5/21.
 */
public class Credits30AddExpBean {

    private Long appId;

    private Long consumerId;

    /**
     * 子订单id
     */
    private String orderId;

    private AddExpTypeEnum expTypeEnum;

    public Long getAppId() {
        return appId;
    }

    public Credits30AddExpBean setAppId(Long appId) {
        this.appId = appId;
        return this;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public Credits30AddExpBean setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
        return this;
    }

    public String getOrderId() {
        return orderId;
    }

    public Credits30AddExpBean setOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    public AddExpTypeEnum getExpTypeEnum() {
        return expTypeEnum;
    }

    public Credits30AddExpBean setExpTypeEnum(AddExpTypeEnum expTypeEnum) {
        this.expTypeEnum = expTypeEnum;
        return this;
    }
}
