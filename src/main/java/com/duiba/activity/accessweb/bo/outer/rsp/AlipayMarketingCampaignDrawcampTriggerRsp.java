package com.duiba.activity.accessweb.bo.outer.rsp;

import com.alipay.api.AlipayResponse;
import com.alipay.api.internal.mapping.ApiField;

/**
 * Created by Administrator on 2017/11/28.
 */
public class AlipayMarketingCampaignDrawcampTriggerRsp extends AlipayResponse {
    private static final long serialVersionUID = 5014929763884891149L;
    @ApiField("camp_id")
    private String campId;
    @ApiField("camp_log_id")
    private String campLogId;
    @ApiField("extend_field")
    private String extendField;
    @ApiField("prize_flag")
    private String prizeFlag;
    @ApiField("prize_id")
    private String prizeId;
    @ApiField("prize_name")
    private String prizeName;
    @ApiField("trigger_result")
    private Boolean triggerResult;

    public void setCampId(String campId) {
        this.campId = campId;
    }

    public String getCampId() {
        return this.campId;
    }

    public void setCampLogId(String campLogId) {
        this.campLogId = campLogId;
    }

    public String getCampLogId() {
        return this.campLogId;
    }

    public void setExtendField(String extendField) {
        this.extendField = extendField;
    }

    public String getExtendField() {
        return this.extendField;
    }

    public void setPrizeFlag(String prizeFlag) {
        this.prizeFlag = prizeFlag;
    }

    public String getPrizeFlag() {
        return this.prizeFlag;
    }

    public void setPrizeId(String prizeId) {
        this.prizeId = prizeId;
    }

    public String getPrizeId() {
        return this.prizeId;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public String getPrizeName() {
        return this.prizeName;
    }

    public void setTriggerResult(Boolean triggerResult) {
        this.triggerResult = triggerResult;
    }

    public Boolean getTriggerResult() {
        return this.triggerResult;
    }


}
