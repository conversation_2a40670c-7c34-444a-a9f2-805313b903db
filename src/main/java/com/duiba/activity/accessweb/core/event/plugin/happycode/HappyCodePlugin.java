package com.duiba.activity.accessweb.core.event.plugin.happycode;

import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOrderDto;

/**
 * Created by hww on 2017/12/12
 */
public interface HappyCodePlugin {

    /**
     * 扣除开心码库存
     */
    void beforeStockComplete(HappyCodeOrderDto order, HappyCodePluginContext context);

    /**
     * 生产订单失败，回滚扣库存
     */
    void beforeStockCompleteException(HappyCodeOrderDto order, HappyCodePluginContext context);
}
