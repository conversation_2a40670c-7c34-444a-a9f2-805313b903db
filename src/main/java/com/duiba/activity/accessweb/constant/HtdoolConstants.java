package com.duiba.activity.accessweb.constant;
/** 
 * ClassName:HtdoolConstants.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年11月29日 下午6:46:01 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
public class HtdoolConstants {

	//改返回值废弃
	public static final int LOTTERY_CODE_0 = 0;//处理中
	public static final int LOTTERY_CODE_3 = -1;//处理失败
	public static final int LOTTERY_CODE_1 = 1;//谢谢参与
	public static final int LOTTERY_CODE_2 = 2;//处理完成
	
	public static final int LOTTERY_CODE_PROCESS = 0;//处理中
	public static final int LOTTERY_CODE_FAILE = -1;//处理失败
	public static final int LOTTERY_CODE_THANK = 1;//谢谢参与
	public static final int LOTTERY_CODE_SUCCESS = 2;//处理完成
	public static final int LOTTERY_CODE_AGAIN = 3;//再来一次
	public static final int LOTTERY_CODE_DEV_FAIL = 101;// 扣积分失败或者内部处理异常
	
	public static final int STATUS_OTHER = 0;//其他异常
	public static final int STATUS_CREDITS_NOT_ENOUGH = 1;//积分不足
	public static final int STATUS_EVERYDAY = 3;//今日没有抽奖次数
	public static final int STATUS_FOREVER = 4;//没有抽奖次数
	public static final int STATUS_EVERYDAY_REMAIN = 5;//页面显示     今日剩余
	public static final int STATUS_CREDITS_PER_TIME = 6;//<span>1</span>元宝/每次
	public static final int STATUS_FOREVER_REMAIN = 7;//页面显示     剩余
	public static final int STATUS_NOT_LOGIN_USER = 18;//未登录
	
	/**
	 * 全静态类静止实例化
	 */
	private HtdoolConstants() {
		throw new IllegalAccessError("Utility class");
	}
}
