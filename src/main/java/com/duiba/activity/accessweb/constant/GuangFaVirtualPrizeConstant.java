package com.duiba.activity.accessweb.constant;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "guangfa.prize")
public class GuangFaVirtualPrizeConstant {
    /**
     * 兑吧在广发的appid
     */
    private String guangFaAppId = "01202006160000020002";

    /**
     * 广发在兑吧的appid
     */
    private long appId = 83051;
    /**
     * 兑吧在广发的商户instId
     */
    private String instId = "0120200616000002";

    // 活动ID
    private String mabId = "200000011511";

    /**
     * 广发的发奖接口
     */
    private String sendPrizeUrl = "http://218.13.4.194:30041/gateway/API/lifeValue/getChinaLifeGamePrize/1.0.0";

    private String getTokenUrl = "http://218.13.4.194:30041/gateway/API/lifeValue/getAppTokenIssue/1.0.0";

    private String version = "1.0.0";

    private String privateKey = "a5apAVED0Y/Hl58RiATrDJfCBF4n4PJ3HAvatElUepo=";

    private String publicKey = "q4Ui1+KBJqAjkJARRQyk22ZAOxLO92jrO2RByOmKF9nBq8LF1xE5coZPqKUHhXgJuSZwaUGRzbAlahl9KsStSg==";

    private String cgbPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAESLqoLJz4CID5V5aqsGXB9k8kSzIMlLgsn7kWBY8+b0o5s/JTABvwFJJcynYPx5F4F2/APT3/B+o81AW6R/MRiA==";

    private String encryptKey = "1234567890ABCDEF";

    private String encoding = "UTF-8";

    private String productCode = "lifeValue";

    public void setAppId(long appId) {
        this.appId = appId;
    }

    public String getGetTokenUrl() {
        return getTokenUrl;
    }

    public void setGetTokenUrl(String getTokenUrl) {
        this.getTokenUrl = getTokenUrl;
    }

    public String getGuangFaAppId() {
        return guangFaAppId;
    }

    public void setGuangFaAppId(String guangFaAppId) {
        this.guangFaAppId = guangFaAppId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public String getEncryptKey() {
        return encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getCgbPublicKey() {
        return cgbPublicKey;
    }

    public void setCgbPublicKey(String cgbPublicKey) {
        this.cgbPublicKey = cgbPublicKey;
    }

    public String getSendPrizeUrl() {
        return sendPrizeUrl;
    }

    public void setSendPrizeUrl(String sendPrizeUrl) {
        this.sendPrizeUrl = sendPrizeUrl;
    }

    public String getMabId() {
        return mabId;
    }

    public void setMabId(String mabId) {
        this.mabId = mabId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }
}
