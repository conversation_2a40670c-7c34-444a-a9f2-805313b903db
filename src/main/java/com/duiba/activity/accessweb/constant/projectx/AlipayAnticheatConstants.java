package com.duiba.activity.accessweb.constant.projectx;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2019-12-04
 * @Email: <EMAIL>
 * @Description:
 */
@Configuration
@ConfigurationProperties(prefix = "projectx.anticheat.alipay")
public class AlipayAnticheatConstants {

    /**
     * projectID
     */
    private Set<String> projectIds;

    public Set<String> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(Set<String> projectIds) {
        this.projectIds = projectIds;
    }
}
