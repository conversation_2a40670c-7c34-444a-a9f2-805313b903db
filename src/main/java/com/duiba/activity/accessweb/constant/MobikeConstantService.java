package com.duiba.activity.accessweb.constant;

import cn.com.duiba.order.center.api.dto.OrdersDto;
import com.google.common.collect.ImmutableSet;

/**
 * 摩拜定制常量类
 * Created by zzy on 2017/9/28.
 */
public class MobikeConstantService {
    private MobikeConstantService(){
        //X Y
    }
    /**
     * 摩拜app_id包括正式和测试
     */
    public static final ImmutableSet<Long> MOBIKE_APP_IDS = ImmutableSet.of(33888L, 35830L);

    /**
     * 摩拜定制积分参数
     */
    public static final String MOBIKE_CREDITS_KEY = OrdersDto.MOBIKE_CREDITS;
}
