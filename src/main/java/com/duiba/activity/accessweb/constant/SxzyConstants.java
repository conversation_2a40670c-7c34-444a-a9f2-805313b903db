package com.duiba.activity.accessweb.constant;

import com.google.common.collect.Sets;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/12/26 15:46.
 */
@Configuration
@ConfigurationProperties(prefix = "aaw.sxzy")
public class SxzyConstants {
    /**
     * appId集合
     */
    private Set<Long> appIdSet = Sets.newHashSet();

    /**
     * 更新奖品状态的api url
     */
    private String updateAwardStatusApiUrl;

    public Set<Long> getAppIdSet() {
        return appIdSet;
    }

    public void setAppIdSet(Set<Long> appIdSet) {
        this.appIdSet = appIdSet;
    }

    public String getUpdateAwardStatusApiUrl() {
        return updateAwardStatusApiUrl;
    }

    public void setUpdateAwardStatusApiUrl(String updateAwardStatusApiUrl) {
        this.updateAwardStatusApiUrl = updateAwardStatusApiUrl;
    }
}
