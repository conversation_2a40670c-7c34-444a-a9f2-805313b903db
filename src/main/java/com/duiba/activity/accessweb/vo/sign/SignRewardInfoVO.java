package com.duiba.activity.accessweb.vo.sign;


import cn.com.duiba.sign.center.api.enums.creditssign.SignRewardTypeEnum;

import java.io.Serializable;

/**
 * 奖励信息
 * Created by xiaoxuda on 2017/12/18.
 */
public class SignRewardInfoVO implements Serializable {
    private static final long serialVersionUID = 6057159133619949351L;
    /**
     * 奖励类型
     */
    private SignRewardTypeEnum rwType;
    /**
     * 插件活动ID
     */
    private Long plActId;
    /**
     * 插件活动皮肤ID
     */
    private Long plActSkinId;
    /**
     * 插件活动皮肤是否开放给开发者编辑
     */
    private Boolean plSkinOpen2Dev;
    /**
     * 加抽奖次数
     */
    private Integer activityCount;
    /**
     * 加积分
     */
    private Integer credits;

    public SignRewardTypeEnum getRwType() {
        return rwType;
    }

    public void setRwType(SignRewardTypeEnum rwType) {
        this.rwType = rwType;
    }

    public Long getPlActId() {
        return plActId;
    }

    public void setPlActId(Long plActId) {
        this.plActId = plActId;
    }

    public Long getPlActSkinId() {
        return plActSkinId;
    }

    public void setPlActSkinId(Long plActSkinId) {
        this.plActSkinId = plActSkinId;
    }

    public Boolean getPlSkinOpen2Dev() {
        return plSkinOpen2Dev;
    }

    public void setPlSkinOpen2Dev(Boolean plSkinOpen2Dev) {
        this.plSkinOpen2Dev = plSkinOpen2Dev;
    }

    public Integer getActivityCount() {
        return activityCount;
    }

    public void setActivityCount(Integer activityCount) {
        this.activityCount = activityCount;
    }

    public Integer getCredits() {
        return credits;
    }

    public void setCredits(Integer credits) {
        this.credits = credits;
    }
}
