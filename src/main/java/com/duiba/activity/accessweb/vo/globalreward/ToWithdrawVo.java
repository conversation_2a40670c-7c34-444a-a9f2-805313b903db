package com.duiba.activity.accessweb.vo.globalreward;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/1 18:35.
 */
public class ToWithdrawVo {
    /**
     * 是否开启总帐户周期
     */
    private Boolean periodOpen = false;
    /**
     * 账户余额
     */
    private Long balance;
    /**
     * 本月剩余提现次数
     */
    private Integer leftTimes;
    /**
     * 单个用户提现额度限制：1-固定额度，2-账户余额至少多少，10-无限制
     */
    private Integer perAmountLimitType;
    /**
     * 限制额度（单位：分）
     */
    private Long perLimitAmount;
    /**
     * 获取提现次数类型按bit：低位第1位-连续打开积分商城多少天，低位第2位-每月固定多少次
     */
    private Integer gainTimesType;
    /**
     * 连续打开积分商城多少天加一次提现次数
     */
    private Integer visitMallDays;
    /**
     * 每月固定增加提现次数
     */
    private Integer addTimesMonthly;
    /**
     * 单个用户每月最多提现次数
     */
    private Integer perMaxTimesMonthly;
    /**
     * 红包清零时间点类型：1-每年最后一天，2-每月最后一天,3-自定义时间
     */
    private Integer clearTimeType;
    /**
     * 每日所有用户总提现限制额度（单位：分）
     */
    private Long maxAmountDaily;

    /**
     * 指定清零时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    private Date expireTime;

    private Long appId;

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Integer getLeftTimes() {
        return leftTimes;
    }

    public void setLeftTimes(Integer leftTimes) {
        this.leftTimes = leftTimes;
    }

    public Integer getPerAmountLimitType() {
        return perAmountLimitType;
    }

    public void setPerAmountLimitType(Integer perAmountLimitType) {
        this.perAmountLimitType = perAmountLimitType;
    }

    public Long getPerLimitAmount() {
        return perLimitAmount;
    }

    public void setPerLimitAmount(Long perLimitAmount) {
        this.perLimitAmount = perLimitAmount;
    }

    public Integer getGainTimesType() {
        return gainTimesType;
    }

    public void setGainTimesType(Integer gainTimesType) {
        this.gainTimesType = gainTimesType;
    }

    public Integer getVisitMallDays() {
        return visitMallDays;
    }

    public void setVisitMallDays(Integer visitMallDays) {
        this.visitMallDays = visitMallDays;
    }

    public Integer getAddTimesMonthly() {
        return addTimesMonthly;
    }

    public void setAddTimesMonthly(Integer addTimesMonthly) {
        this.addTimesMonthly = addTimesMonthly;
    }

    public Integer getPerMaxTimesMonthly() {
        return perMaxTimesMonthly;
    }

    public void setPerMaxTimesMonthly(Integer perMaxTimesMonthly) {
        this.perMaxTimesMonthly = perMaxTimesMonthly;
    }

    public Integer getClearTimeType() {
        return clearTimeType;
    }

    public void setClearTimeType(Integer clearTimeType) {
        this.clearTimeType = clearTimeType;
    }

    public Long getMaxAmountDaily() {
        return maxAmountDaily;
    }

    public void setMaxAmountDaily(Long maxAmountDaily) {
        this.maxAmountDaily = maxAmountDaily;
    }


    public Boolean getPeriodOpen() {
        return periodOpen;
    }

    public void setPeriodOpen(Boolean periodOpen) {
        this.periodOpen = periodOpen;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }
}
