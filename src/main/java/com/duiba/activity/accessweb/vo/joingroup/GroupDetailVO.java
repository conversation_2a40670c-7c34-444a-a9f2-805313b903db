package com.duiba.activity.accessweb.vo.joingroup;

import java.util.List;

/**
 * @Desc 拼团详情
 * <AUTHOR>
 * @Date 2018/9/25
 */
public class GroupDetailVO {
    private Long joinGroupConfigId;
    private Long joinGroupItemId;
    private Long joinGroupInfoId;
    private Long itemId;
    private String name;
    // 商品类型
    private String type;
    private String image;
    //原价
    private String originalPrice;
    //优惠价
    private String preferentialPrice;
    private int groupNumber;
    private int memberCount;
    private Integer groupStatus;
    private Long endTime;
    private int remainCount;
    private boolean joined;
    private List<MemberInfo> memberAvatarList;
    private Long winConsumerId;
    private String winConsumerName;
    private String winConsumerAvatar;

    public Long getJoinGroupConfigId() {
        return joinGroupConfigId;
    }

    public void setJoinGroupConfigId(Long joinGroupConfigId) {
        this.joinGroupConfigId = joinGroupConfigId;
    }

    public Long getJoinGroupItemId() {
        return joinGroupItemId;
    }

    public void setJoinGroupItemId(Long joinGroupItemId) {
        this.joinGroupItemId = joinGroupItemId;
    }

    public Long getJoinGroupInfoId() {
        return joinGroupInfoId;
    }

    public void setJoinGroupInfoId(Long joinGroupInfoId) {
        this.joinGroupInfoId = joinGroupInfoId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getPreferentialPrice() {
        return preferentialPrice;
    }

    public void setPreferentialPrice(String preferentialPrice) {
        this.preferentialPrice = preferentialPrice;
    }

    public int getGroupNumber() {
        return groupNumber;
    }

    public void setGroupNumber(int groupNumber) {
        this.groupNumber = groupNumber;
    }

    public int getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(int memberCount) {
        this.memberCount = memberCount;
    }

    public Integer getGroupStatus() {
        return groupStatus;
    }

    public void setGroupStatus(Integer groupStatus) {
        this.groupStatus = groupStatus;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public int getRemainCount() {
        return remainCount;
    }

    public void setRemainCount(int remainCount) {
        this.remainCount = remainCount;
    }

    public boolean isJoined() {
        return joined;
    }

    public void setJoined(boolean joined) {
        this.joined = joined;
    }

    public List<MemberInfo> getMemberAvatarList() {
        return memberAvatarList;
    }

    public void setMemberAvatarList(List<MemberInfo> memberAvatarList) {
        this.memberAvatarList = memberAvatarList;
    }

    public Long getWinConsumerId() {
        return winConsumerId;
    }

    public void setWinConsumerId(Long winConsumerId) {
        this.winConsumerId = winConsumerId;
    }

    public String getWinConsumerName() {
        return winConsumerName;
    }

    public void setWinConsumerName(String winConsumerName) {
        this.winConsumerName = winConsumerName;
    }

    public String getWinConsumerAvatar() {
        return winConsumerAvatar;
    }

    public void setWinConsumerAvatar(String winConsumerAvatar) {
        this.winConsumerAvatar = winConsumerAvatar;
    }

    public static class MemberInfo {
        private Long consumerId;
        private String avatar;
        private boolean owner;
        private boolean self;

        public Long getConsumerId() {
            return consumerId;
        }

        public void setConsumerId(Long consumerId) {
            this.consumerId = consumerId;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public boolean getOwner() {
            return owner;
        }

        public void setOwner(boolean owner) {
            this.owner = owner;
        }

        public boolean getSelf() {
            return self;
        }

        public void setSelf(boolean self) {
            this.self = self;
        }
    }
}
