package com.duiba.activity.accessweb.vo.understandlevel;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-10
 */
public class BaseConfigVO {

    /**
     * 是否登录
     */
    private Integer loginFlag;

    /**
     * 是否已参与
     */
    private Integer joinFlag;

    /**
     * 若存在助力码，判断是否已助力
     */
    private Integer assistFlag;

    /**
     * 是否自己给自己分享
     */
    private Integer selfShareFlag;

    private String nickname;

    private String avatar;

    private String actionNickname;

    private String actionAvatar;

    private Long appId;

    private Long id;

    private String actTitle;

    private String actRule;

    private Date startTime;

    private Date endTime;

    private String smallImage;

    private String bannerImage;

    private Integer actCredits;

    private String actCreditsUnitName;

    private String questionTitle;

    private String questionAssistTitle;

    private List<UnderstandOptionVO> optionList;

    private Integer selectNum;

    private String startButtonText;

    private Integer logoSwitch;

    private String logoImage;

    private String interfaceConfig;

    private String shareLinkImage;

    private String shareLinkTitle;

    private String shareLinkSubTitle;

    private Integer sharePosterSwitch;

    private String sharePosterImage;

    private String sharePosterTitle;

    private Integer pictureFlag;

    /**
     * 被分享者要展示的选项数量
     */
    private Integer assistShowOptionNum;

    /**
     * 画像开关，0：关闭，1：打开
     */
    private Integer pictureSwitch = 0;

    /**
     * 画像页按钮文案
     */
    private String pictureButtonText;

    /**
     * 海报的文案颜色（有画像时有值）
     */
    private String sharePosterTitleColor;

    /**
     * 海报的用户昵称颜色（有画像时有值）
     */
    private String sharePosterNicknameColor;

    public Integer getPictureFlag() {
        return pictureFlag;
    }

    public void setPictureFlag(Integer pictureFlag) {
        this.pictureFlag = pictureFlag;
    }

    public Integer getAssistShowOptionNum() {
        return assistShowOptionNum;
    }

    public void setAssistShowOptionNum(Integer assistShowOptionNum) {
        this.assistShowOptionNum = assistShowOptionNum;
    }

    public Integer getPictureSwitch() {
        return pictureSwitch;
    }

    public void setPictureSwitch(Integer pictureSwitch) {
        this.pictureSwitch = pictureSwitch;
    }

    public String getPictureButtonText() {
        return pictureButtonText;
    }

    public void setPictureButtonText(String pictureButtonText) {
        this.pictureButtonText = pictureButtonText;
    }

    public String getSharePosterTitleColor() {
        return sharePosterTitleColor;
    }

    public void setSharePosterTitleColor(String sharePosterTitleColor) {
        this.sharePosterTitleColor = sharePosterTitleColor;
    }

    public String getSharePosterNicknameColor() {
        return sharePosterNicknameColor;
    }

    public void setSharePosterNicknameColor(String sharePosterNicknameColor) {
        this.sharePosterNicknameColor = sharePosterNicknameColor;
    }

    public Integer getSelfShareFlag() {
        return selfShareFlag;
    }

    public void setSelfShareFlag(Integer selfShareFlag) {
        this.selfShareFlag = selfShareFlag;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getActCreditsUnitName() {
        return actCreditsUnitName;
    }

    public void setActCreditsUnitName(String actCreditsUnitName) {
        this.actCreditsUnitName = actCreditsUnitName;
    }

    public String getActionNickname() {
        return actionNickname;
    }

    public void setActionNickname(String actionNickname) {
        this.actionNickname = actionNickname;
    }

    public String getActionAvatar() {
        return actionAvatar;
    }

    public void setActionAvatar(String actionAvatar) {
        this.actionAvatar = actionAvatar;
    }

    public String getQuestionAssistTitle() {
        return questionAssistTitle;
    }

    public void setQuestionAssistTitle(String questionAssistTitle) {
        this.questionAssistTitle = questionAssistTitle;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getLoginFlag() {
        return loginFlag;
    }

    public void setLoginFlag(Integer loginFlag) {
        this.loginFlag = loginFlag;
    }

    public Integer getJoinFlag() {
        return joinFlag;
    }

    public void setJoinFlag(Integer joinFlag) {
        this.joinFlag = joinFlag;
    }

    public Integer getAssistFlag() {
        return assistFlag;
    }

    public void setAssistFlag(Integer assistFlag) {
        this.assistFlag = assistFlag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActTitle() {
        return actTitle;
    }

    public void setActTitle(String actTitle) {
        this.actTitle = actTitle;
    }

    public String getActRule() {
        return actRule;
    }

    public void setActRule(String actRule) {
        this.actRule = actRule;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getSmallImage() {
        return smallImage;
    }

    public void setSmallImage(String smallImage) {
        this.smallImage = smallImage;
    }

    public String getBannerImage() {
        return bannerImage;
    }

    public void setBannerImage(String bannerImage) {
        this.bannerImage = bannerImage;
    }

    public Integer getActCredits() {
        return actCredits;
    }

    public void setActCredits(Integer actCredits) {
        this.actCredits = actCredits;
    }

    public String getQuestionTitle() {
        return questionTitle;
    }

    public void setQuestionTitle(String questionTitle) {
        this.questionTitle = questionTitle;
    }

    public List<UnderstandOptionVO> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<UnderstandOptionVO> optionList) {
        this.optionList = optionList;
    }

    public Integer getSelectNum() {
        return selectNum;
    }

    public void setSelectNum(Integer selectNum) {
        this.selectNum = selectNum;
    }

    public String getStartButtonText() {
        return startButtonText;
    }

    public void setStartButtonText(String startButtonText) {
        this.startButtonText = startButtonText;
    }

    public Integer getLogoSwitch() {
        return logoSwitch;
    }

    public void setLogoSwitch(Integer logoSwitch) {
        this.logoSwitch = logoSwitch;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public String getInterfaceConfig() {
        return interfaceConfig;
    }

    public void setInterfaceConfig(String interfaceConfig) {
        this.interfaceConfig = interfaceConfig;
    }

    public String getShareLinkImage() {
        return shareLinkImage;
    }

    public void setShareLinkImage(String shareLinkImage) {
        this.shareLinkImage = shareLinkImage;
    }

    public String getShareLinkTitle() {
        return shareLinkTitle;
    }

    public void setShareLinkTitle(String shareLinkTitle) {
        this.shareLinkTitle = shareLinkTitle;
    }

    public String getShareLinkSubTitle() {
        return shareLinkSubTitle;
    }

    public void setShareLinkSubTitle(String shareLinkSubTitle) {
        this.shareLinkSubTitle = shareLinkSubTitle;
    }

    public Integer getSharePosterSwitch() {
        return sharePosterSwitch;
    }

    public void setSharePosterSwitch(Integer sharePosterSwitch) {
        this.sharePosterSwitch = sharePosterSwitch;
    }

    public String getSharePosterImage() {
        return sharePosterImage;
    }

    public void setSharePosterImage(String sharePosterImage) {
        this.sharePosterImage = sharePosterImage;
    }

    public String getSharePosterTitle() {
        return sharePosterTitle;
    }

    public void setSharePosterTitle(String sharePosterTitle) {
        this.sharePosterTitle = sharePosterTitle;
    }


}
