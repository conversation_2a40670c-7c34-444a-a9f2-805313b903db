package com.duiba.activity.accessweb.vo.qqmusic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @Author: fss
 * @Date: 2022/11/17 11
 * @Description:
 */
public class MusicResultBuilder {

    /**
     * 异常可重试结果返回
     */
    public static String errorCanRetryResult(String message) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("goodList", Lists.newArrayList());
        result.put("retcodes", Long.valueOf(ErrorCode.E200001.getErrorCode()));
        result.put("retMsg", message);
        return JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 异常不可重试结果返回
     */
    public static String errorResult(String message) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("goodList", Lists.newArrayList());
        result.put("retcodes", Long.valueOf(ErrorCode.E200002.getErrorCode()));
        result.put("retMsg", message);
        return JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 成功
     *
     * @param takePrizeGoodsVOS
     * @return
     */
    public static String successResult(List<TakePrizeGoodsVO> takePrizeGoodsVOS) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("goodList", takePrizeGoodsVOS);
        result.put("retcodes", Long.valueOf(ErrorCode.E200000.getErrorCode()));
        result.put("retMsg", StringUtils.EMPTY);
        return JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }
}
