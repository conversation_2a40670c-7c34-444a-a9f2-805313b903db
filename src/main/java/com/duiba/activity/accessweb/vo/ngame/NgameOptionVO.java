package com.duiba.activity.accessweb.vo.ngame;

import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameOptionsDto;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2016/12/14.
 */
public class NgameOptionVO {
	private static Logger log = LoggerFactory.getLogger(NgameOptionVO.class);
	private Long id;
	private Long gameConfigDuibaId;
	private Long itemId;
	private Long appItemId;
	private String name;
	private Integer payload;
	private String prizeType;
	private String facePrice;
	private Integer limitCount;
	private Integer hidden;
	private String logo;
	private String description;
	private Boolean deleted;
	private Date gmtCreate;
	private Date gmtModified;
	private String scope;
	private Integer scopeStart;
	private Integer scopeEnd;
	private Boolean autoOpen;
	private Integer remaining;
	private Integer guarantee; //奖品保底值开关
	private String scoreArea; //奖项中奖区间
	private Integer optionLimitCount; //奖品中奖次数限制
	private Integer rate; //奖品中奖几率
	private Integer limitTimeType;//奖项限制时间类型
	private Integer limitDays;//自定义限制时间

	public Integer getLimitTimeType() {
		return limitTimeType;
	}

	public void setLimitTimeType(Integer limitTimeType) {
		this.limitTimeType = limitTimeType;
	}

	public Integer getLimitDays() {
		return limitDays;
	}

	public void setLimitDays(Integer limitDays) {
		this.limitDays = limitDays;
	}

	private List<ConsumerRankVO> consumerRankVOs;

	public NgameOptionVO(DuibaNgameOptionsDto gameOptionDuiba) {
		this.id = gameOptionDuiba.getId();
		this.gameConfigDuibaId = gameOptionDuiba.getDuibaGameId();
		this.itemId = gameOptionDuiba.getItemId();
		this.name = gameOptionDuiba.getPrizeName();
		this.payload = gameOptionDuiba.getPayload();
		this.prizeType = gameOptionDuiba.getPrizeType();
		this.facePrice = gameOptionDuiba.getFacePrice();
		this.hidden = gameOptionDuiba.getHidden() ? 1 : 0;
		this.logo = gameOptionDuiba.getLogo();
		this.description = gameOptionDuiba.getDescription();
		this.deleted = gameOptionDuiba.getDeleted();
		this.gmtCreate = gameOptionDuiba.getGmtCreate();
		this.gmtModified = gameOptionDuiba.getGmtModified();
		this.autoOpen = gameOptionDuiba.getAutoOpen();
		this.remaining = gameOptionDuiba.getRemaining();
		this.guarantee = gameOptionDuiba.getGuarantee();
		this.scoreArea = gameOptionDuiba.getScoreArea();
		this.optionLimitCount = gameOptionDuiba.getOptionLimitCount();
		this.rate = gameOptionDuiba.getRate();
		String extJson = gameOptionDuiba.getExtJson();
		if(!StringUtils.isBlank(extJson)){
			try {
				JSONObject jsonObject = JSONObject.parseObject(extJson);
				this.limitTimeType = jsonObject.getInteger(DuibaNgameOptionsDto.EXT_JSON_KEY_LIMIT_TYPE);
				this.limitDays = jsonObject.getInteger(DuibaNgameOptionsDto.EXT_JSON_KEY_LIMIT_DAYS);
			} catch (Exception e) {
				log.error("json转换错误:{}", extJson, e);
			}
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getGameConfigDuibaId() {
		return gameConfigDuibaId;
	}

	public void setGameConfigDuibaId(Long gameConfigDuibaId) {
		this.gameConfigDuibaId = gameConfigDuibaId;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public Long getAppItemId() {
		return appItemId;
	}

	public void setAppItemId(Long appItemId) {
		this.appItemId = appItemId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getPayload() {
		return payload;
	}

	public void setPayload(Integer payload) {
		this.payload = payload;
	}

	public String getPrizeType() {
		return prizeType;
	}

	public void setPrizeType(String prizeType) {
		this.prizeType = prizeType;
	}

	public String getFacePrice() {
		return facePrice;
	}

	public void setFacePrice(String facePrice) {
		this.facePrice = facePrice;
	}

	public Integer getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(Integer limitCount) {
		this.limitCount = limitCount;
	}

	public Integer getHidden() {
		return hidden;
	}

	public void setHidden(Integer hidden) {
		this.hidden = hidden;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}

	public String getScope() {
		return scope;
	}

	public void setScope(String scope) {
		this.scope = scope;
	}

	public Integer getScopeEnd() {
		return scopeEnd;
	}

	public void setScopeEnd(Integer scopeEnd) {
		this.scopeEnd = scopeEnd;
	}

	public Integer getScopeStart() {
		return scopeStart;
	}

	public void setScopeStart(Integer scopeStart) {
		this.scopeStart = scopeStart;
	}

	public List<ConsumerRankVO> getConsumerRankVOs() {
		return consumerRankVOs;
	}

	public void setConsumerRankVOs(List<ConsumerRankVO> consumerRankVOs) {
		this.consumerRankVOs = consumerRankVOs;
	}

	public Boolean getAutoOpen() {
		return autoOpen;
	}

	public void setAutoOpen(Boolean autoOpen) {
		this.autoOpen = autoOpen;
	}

	public Integer getRemaining() {
		return remaining;
	}

	public void setRemaining(Integer remaining) {
		this.remaining = remaining;
	}


	public Integer getGuarantee() {
		return guarantee;
	}

	public void setGuarantee(Integer guarantee) {
		this.guarantee = guarantee;
	}

	public String getScoreArea() {
		return scoreArea;
	}

	public void setScoreArea(String scoreArea) {
		this.scoreArea = scoreArea;
	}

	public Integer getOptionLimitCount() {
		return optionLimitCount;
	}

	public void setOptionLimitCount(Integer optionLimitCount) {
		this.optionLimitCount = optionLimitCount;
	}

	public Integer getRate() {
		return rate;
	}

	public void setRate(Integer rate) {
		this.rate = rate;
	}

}
