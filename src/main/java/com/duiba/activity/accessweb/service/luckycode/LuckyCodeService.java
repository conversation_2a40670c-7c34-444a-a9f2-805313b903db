package com.duiba.activity.accessweb.service.luckycode;

import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.vo.luckycode.IndexInfoVO;
import com.duiba.activity.accessweb.vo.luckycode.PrizeStatusVO;
import com.duiba.activity.accessweb.vo.luckycode.SubmitVO;
import com.duiba.activity.accessweb.vo.luckycode.UserAwardRecordVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 幸运码活动服务类
 *
 * <AUTHOR>
 * @Date 2019/3/20
 */
public interface LuckyCodeService {

    /**
     * 获取首页信息
     *
     * @param consumer
     * @param activityId
     * @param app
     * @param positions 未知数位置，用于辅助测试
     * @return
     */
    IndexInfoVO getIndexInfo(ConsumerDto consumer, Long activityId, AppSimpleDto app, int[] positions);

    /**
     * 获取预览信息
     *
     * @param activityId
     * @return
     */
    IndexInfoVO getPreviewIndexInfo(Long activityId);

    /**
     * 获取用户中奖信息
     *
     * @param consumerId
     * @param activityId
     * @return
     */
    List<PrizeStatusVO> getUserWiningPrizes(Long consumerId, Long activityId);


    /**
     * 获取前几名中奖用户
     *
     * @param prizeId
     * @param limit
     * @return
     */
    List<UserAwardRecordVO> getPrizeTopLimitWiningRecords(Long prizeId, Integer limit);

    /**
     * 查看未知数
     *
     * @param consumer
     * @param app
     * @param activityId
     * @param orderId
     * @param codeIndex
     * @param rates 流程概率配置，用于辅助测试
     * @return
     */
    SubmitVO submit(ConsumerDto consumer, AppSimpleDto app, Long activityId, Long orderId, Integer codeIndex, int[] rates);

    /**
     * 扣积分
     *
     * @param consumer
     * @param activityId
     * @param isOpen
     * @return
     */
    Long deductCredits(ConsumerDto consumer, AppSimpleDto app, Long activityId, boolean isOpen, HttpServletRequest request);

    /**
     * 领奖
     *
     * @param activityId
     * @param consumer
     * @param app
     */
    void takePrize(Long activityId, ConsumerDto consumer, AppSimpleDto app, HttpServletRequest request);
}
