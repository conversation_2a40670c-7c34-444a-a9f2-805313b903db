package com.duiba.activity.accessweb.service.activityshare.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.DuibaSingleLotteryDto;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteDuibaSingleLotteryServiceNew;
import com.duiba.activity.accessweb.service.activityshare.ActivityBannerProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zzy on 2017/9/21.
 */
@Service
public class DuibaSingleLotteryBannerProvider implements ActivityBannerProvider {
    @Autowired
    private RemoteDuibaSingleLotteryServiceNew remoteDuibaSingleLotteryServiceNew;

    @Override
    public boolean isHdtool() {
        return false;
    }

    @Override
    public int getActivityType() {
        return OperatingActivityDto.TypeDuibaSingleLottery;
    }

    @Override
    public String getBannerImg(Long activityId) {
        DuibaSingleLotteryDto activity = remoteDuibaSingleLotteryServiceNew.find(activityId);
        if (null == activity) {
            return "";
        }
        return activity.getBannerImage();
    }
}
