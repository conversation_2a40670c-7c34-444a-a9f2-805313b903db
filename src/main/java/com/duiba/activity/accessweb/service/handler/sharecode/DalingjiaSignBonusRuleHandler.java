package com.duiba.activity.accessweb.service.handler.sharecode;

import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountActionTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountSubTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.sharecode.RuleTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteConsumerAccountService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountAmountModifyRequest;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountModifyResponse;
import cn.com.duiba.api.enums.RedisKeySpace;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.DalingjiaConstant;
import com.duiba.activity.accessweb.enums.DalingjiaHashKeyForTaskTypeEnum;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.service.activity.sharecode.ResolveParameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 达令家养成二期-分享助力成功，分享者获得牛草
 * @Author: fxr
 * @Date: 2019/10/10
 */
@Service
public class DalingjiaSignBonusRuleHandler implements BonusRuleHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DalingjiaSignBonusRuleHandler.class);

    /**
     * 任务类型（助力）
     * 详见activity-custom-web DalingJiaSignPetTaskEnum
     */
    private static final String HELP_TYPE = "assistance";

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RemoteConsumerAccountService remoteConsumerAccountService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private DalingjiaConstant dalingjiaConstant;

    @PostConstruct
    public void init() {
        BonusRuleManager.register(this);
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.DALINGJIA_SIGN_PET.getCode();
    }

    @Override
    public Result<String> resolve(ResolveParameter resolveParameter) {
        // 查询分享者当前分享情况
        String helpInfoKey = getHelpInfoKey(resolveParameter.getActivityId(), resolveParameter.getConsumerId(), HELP_TYPE);
        // 结果信息
        String resultMsg;
        // 用户当日累计获得牛草次数回滚标记
        Boolean rollBackFlag = null;
        try {
            Long countNow = incriHash(helpInfoKey, DalingjiaHashKeyForTaskTypeEnum.REWARD_TIMES, 1L);
            rollBackFlag = true;
            if (countNow.compareTo(dalingjiaConstant.getRewardTimesLimitDaily()) > 0) {
                resultMsg = "助力成功,未获得牛草(用户当日获得牛草次数达到上限)";
            } else {
                // 用户当日累计获得牛草次数未达到上限，发放牛草并累计发放数量
                AccountModifyResponse response = accountModify(resolveParameter);
                // 牛草入账失败，获得牛草次数（计数回滚)
                if (response == null || !response.isSuccess()) {
                    String errorMsg = Objects.isNull(response) ? "响应为空" : response.getErrorMsg();
                    resultMsg = "助力成功,未获得牛草(牛草入账请求提交异常:" + (errorMsg) + ")";
                    LOGGER.info("〖Message〗: 达令家养成二期-助力成功,牛草入账异常:{};〖Parameters〗: [resolveParameter={}] ...", errorMsg, JSONObject.toJSONString(resolveParameter));
                } else {
                    // 用户当日累计获得牛草数量（单位：g）(计数)
                    incriHash(helpInfoKey, DalingjiaHashKeyForTaskTypeEnum.REWARD_COUNT, response.getChangeMoney());
                    // 用户离线期间获得牛草数量（单位：g）(计数)（此值在手机端访问后，可选择重置计数器）
                    incriHash(helpInfoKey, DalingjiaHashKeyForTaskTypeEnum.DURATION_REWARD_COUNT, response.getChangeMoney());
                    resultMsg = "助力成功,获得牛草(" + response.getChangeMoney() + "g)";
                    rollBackFlag = false;
                }
            }
        } catch (Exception e) {
            resultMsg = "助力成功,未获得牛草(业务异常:" + e.getMessage() + ")";
            LOGGER.error("〖Message〗: 达令家养成二期-助力定制逻辑处理异常;〖Parameters〗: [resolveParameter={}] ...", JSONObject.toJSONString(resolveParameter), e);
        } finally {
            if (Objects.nonNull(rollBackFlag) && rollBackFlag) {
                incriHash(helpInfoKey, DalingjiaHashKeyForTaskTypeEnum.REWARD_TIMES, -1L);
            }
            // 累计助力次数
            incriHash(helpInfoKey, DalingjiaHashKeyForTaskTypeEnum.TIMES, 1L);
        }
        return ResultBuilder.success(resultMsg);
    }


    /**
     * 发送用户任务奖励
     *
     * @param resolveParameter
     * @throws BizException
     */
    private AccountModifyResponse accountModify(ResolveParameter resolveParameter) throws BizException {
        // 分享者（被助力者）
        ConsumerDto inviter = remoteConsumerService.find(resolveParameter.getInviter());
        // 助力者 (被邀请者)
        ConsumerDto invitee = remoteConsumerService.find(resolveParameter.getInvitee());
        AccountAmountModifyRequest accountRequest = new AccountAmountModifyRequest();
        accountRequest.setAppId(inviter.getAppId());
        accountRequest.setConsumerId(inviter.getId());
        accountRequest.setPartnerUserId(inviter.getPartnerUserId());
        accountRequest.setAccountType(AccountTypeEnum.PET_FEEDER);
        accountRequest.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
        accountRequest.setBizType(AccountBizTypeEnum.SIGN);
        accountRequest.setBizId("HELPFROM_" + resolveParameter.getInvitee() + "_" + System.currentTimeMillis());
        accountRequest.setBizDescription("好友助力获得");
        accountRequest.setChangeMoney(dalingjiaConstant.getAwardConfByVip(inviter.getVipLevel(), invitee.getVipLevel()));
        accountRequest.setRelId(resolveParameter.getActivityId());
        accountRequest.setAccActType(AccountActionTypeEnum.ACTION_IN);
        accountRequest.setNeedLog(true);
        return remoteConsumerAccountService.accountModify(accountRequest);
    }

    /**
     * 达令家养成二期-用户日常任务完成数量记录&任务奖励记录 Rediskey
     *
     * @param signId     养成签到id
     * @param consumerId 用户id
     * @param type       任务type
     * @return
     */
    private String getHelpInfoKey(Long signId, Long consumerId, String type) {
        return RedisKeySpace.K140.toString() + signId + "_" + consumerId + "_" + type + "_" + LocalDate.now().toString();
    }

    private Long incriHash(String helpInfoKey, DalingjiaHashKeyForTaskTypeEnum rewardcount, long i) {
        Long increment = opsForHash().increment(helpInfoKey, rewardcount.getName(), i);
        stringRedisTemplate.expire(helpInfoKey, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
        return increment;
    }

    private HashOperations<String, String, String> opsForHash() {
        return stringRedisTemplate.opsForHash();
    }
}
