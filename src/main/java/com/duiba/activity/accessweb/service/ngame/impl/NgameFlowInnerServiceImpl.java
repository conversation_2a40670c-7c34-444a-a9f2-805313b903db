package com.duiba.activity.accessweb.service.ngame.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameAppSpecifyDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameBrickDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameOptionsDto;
import cn.com.duiba.activity.center.api.dto.ngame.ManualWinParamDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameConsumerRecordDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersExtraDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameStockDto;
import cn.com.duiba.activity.center.api.enums.PrizeLimitTimeTypeEnum;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteActivityBlackList4DeveloperService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteDuibaNgameAppSpecifyService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteDuibaNgameOptionsService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameConsumerRecordService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameOrdersExtendService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameOrdersExtraService;
import cn.com.duiba.activity.center.api.remoteservice.ngame_con.RemoteNgameOrdersConsumerService;
import cn.com.duiba.api.bo.subcredits.SubCreditsMsgDto;
import cn.com.duiba.api.enums.subcredits.SubCreditsOuterType;
import cn.com.duiba.api.enums.subcredits.SubCreditsType;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.perftest.PerfTestContext;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.CreditConsumeParams;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppTradingLimitService;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsCouponDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemCouponGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.paycenter.dto.DuibaRemainingMoneyDto;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaRemainingMoneyService;
import cn.com.duiba.prize.center.api.enums.DuibaPrizeTypeEnum;
import cn.com.duiba.thirdparty.api.RemoteNotifyDeveloperService;
import cn.com.duiba.thirdparty.dto.CreditsMessageDto;
import cn.com.duiba.thirdparty.dto.NotifyQueueDto;
import cn.com.duiba.thirdparty.enums.CallbackChannelTypeEnum;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.EmbedConstant;
import com.duiba.activity.accessweb.constant.LuckyBagUserTypeConstants;
import com.duiba.activity.accessweb.constant.RedisKeys;
import com.duiba.activity.accessweb.core.event.DuibaEventsDispatcher;
import com.duiba.activity.accessweb.core.event.ng.NGConsumerCreditsEvent;
import com.duiba.activity.accessweb.core.event.ng.NGDuibaEventsDispatcher;
import com.duiba.activity.accessweb.core.event.plugin.DuibaPluginEventsDispatcher;
import com.duiba.activity.accessweb.exception.AccessActivityException;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.exception.StatusException;
import com.duiba.activity.accessweb.mq.ConsumeCreditsCallback;
import com.duiba.activity.accessweb.mq.OnsMessageTopic;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.mq.producer.RocketMQMsgProducer;
import com.duiba.activity.accessweb.service.ActivityTakePrizeService;
import com.duiba.activity.accessweb.service.ActualPriceCalculateService;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.TimeLimitService;
import com.duiba.activity.accessweb.service.activity.ActivityKvService;
import com.duiba.activity.accessweb.service.bizid.BizIdPoolService;
import com.duiba.activity.accessweb.service.exchange.ExchangeNoticeService;
import com.duiba.activity.accessweb.service.hdtool.service.FrequentExchangeLimitService;
import com.duiba.activity.accessweb.service.hdtool.service.ItemCheckService;
import com.duiba.activity.accessweb.service.luckbug.AppSlotCacheService;
import com.duiba.activity.accessweb.service.luckbug.LuckBagRequest;
import com.duiba.activity.accessweb.service.luckbug.LuckBagService;
import com.duiba.activity.accessweb.service.luckbug.impl.NgameLuckBugCallback;
import com.duiba.activity.accessweb.service.ngame.NgameCacheKeyService;
import com.duiba.activity.accessweb.service.ngame.NgameCommonService;
import com.duiba.activity.accessweb.service.ngame.NgameFlowInnerService;
import com.duiba.activity.accessweb.service.ngame.NgameRankConfig;
import com.duiba.activity.accessweb.service.ngame.NgameRankService;
import com.duiba.activity.accessweb.service.ngame.NgameService;
import com.duiba.activity.accessweb.service.ngame.NgameStockService;
import com.duiba.activity.accessweb.service.ngame.NgameTakePrizeInnerService;
import com.duiba.activity.accessweb.service.ngame.plugin.NgameOrderPlugin;
import com.duiba.activity.accessweb.service.ngame.plugin.NgameOrdersEvent;
import com.duiba.activity.accessweb.service.order.VirtualCreator;
import com.duiba.activity.accessweb.tool.AppIdConstant;
import com.duiba.activity.accessweb.tool.AssembleTool;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.Environment;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.tool.TaojinbiTool;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.duiba.activity.accessweb.vo.TakePrizeVo;
import com.duiba.activity.accessweb.vo.ngame.NgameOptionVO;
import com.duiba.activity.accessweb.vo.ngame.NgameParams;
import com.google.common.base.Objects;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableMultiset;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimaps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhuzhiyong on 2016/12/15.
 */
@Service
public class NgameFlowInnerServiceImpl implements NgameFlowInnerService {
    private static final Logger log = LoggerFactory.getLogger(NgameFlowInnerServiceImpl.class);
    private static final String OPTION_SEPARATOR = "##";
    private static final String OPTION_GROUP_PREFIX = "unique";
    /** 即开奖支持的奖项集合，包含优惠券，虚拟商品，话费充值，支付宝充值，QB充值，实物奖品 */
    private static final ImmutableMultiset<String> PRIZE_TYPE = ImmutableMultiset.of(
            NgameOrdersDto.PrizeTypeCoupon,
            NgameOrdersDto.PrizeTypeVirtual,
            NgameOrdersDto.PrizeTypePhonebill,
            NgameOrdersDto.PrizeTypeAlipay,
            NgameOrdersDto.PrizeTypeQB,
            NgameOrdersDto.PrizeTypeObject,
            NgameOrdersDto.PrizeTypeCollectGoods);
    /** 需要预扣库存奖项集合*/
    private static final ImmutableMultiset<String> RESUME_STOCK_TYPE = ImmutableMultiset.of(
            NgameOrdersDto.PrizeTypeCoupon,
            NgameOrdersDto.PrizeTypeVirtual,
            NgameOrdersDto.PrizeTypeObject,
            NgameOrdersDto.PrizeTypeCollectGoods);
    /** 需要扣appItem库存奖项集合*/
    private static final ImmutableMultiset<String> ITEM_STOCK_TYPE = ImmutableMultiset.of(
            ItemDto.TypeCollectGoods,
            ItemDto.TypeVirtual,
            ItemDto.TypeCoupon,
            ItemDto.TypeObject);
    /** 直冲类商品集合，话费充值，支付宝充值，QB充值 */
    private static final ImmutableMultiset<String> PAY_ITEM_TYPE = ImmutableMultiset.of(
            NgameOrdersDto.PrizeTypePhonebill,
            NgameOrdersDto.PrizeTypeAlipay,
            NgameOrdersDto.PrizeTypeQB);

    @Autowired
    private FrequentExchangeLimitService frequentExchangeLimitService;
    @Autowired
    private RemoteDuibaNgameAppSpecifyService remoteDuibaNgameAppSpecifyService;
    @Autowired
    private RemoteAppTradingLimitService remoteAppTradingLimitService;
    @Autowired
    private NgameService ngameService;
    @Autowired
    private NgameCommonService ngameCommonService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private RemoteNgameOrdersConsumerService remoteNgameOrdersConsumerService;
    @Autowired
    private RocketMqMessageTopic rocketMqMessageTopic;
    @Autowired
    private RemoteNotifyDeveloperService remoteNotifyDeveloperService;
    @Autowired
    private RemoteDuibaNgameOptionsService remoteDuibaNgameOptionsService;
    @Autowired
    private NgameStockService ngameStockService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private TimeLimitService timeLimitService;
    @Autowired
    private ActualPriceCalculateService actualPriceCalculateService;
    @Autowired
    private RemoteNgameConsumerRecordService remoteNgameConsumerRecordService;
    @Autowired
    private RemoteDuibaRemainingMoneyService remoteDuibaRemainingMoneyService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private ExchangeNoticeService exchangeNoticeService;
    @Autowired
    private NgameRankService ngameRankService;
    @Autowired
    private RemoteNgameOrdersExtraService remoteNgameOrdersExtraService;
    @Autowired
    private RemoteItemCouponGoodsService remoteItemCouponGoodsService;
    @Autowired
    private NgameTakePrizeInnerService ngameTakePrizeInnerService;
    @Autowired
    private RemoteNgameOrdersExtendService remoteNgameOrdersExtendService;
    @Autowired
    private LuckBagService luckBagService;
    @javax.annotation.Resource(name = "ngameConsumeCreditsCallback")
    private ConsumeCreditsCallback consumeCreditsCallback;
    @Autowired
    private ExecutorService executorService;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
    @Autowired
    private TaojinbiTool taojinbiTool;
    @Autowired
    private ActivityTakePrizeService activityTakePrizeService;
    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;
    @Autowired
    private RemoteActivityBlackList4DeveloperService remoteActivityBlackList4DeveloperService;
    @Autowired
    private ActivityKvService activityKvService;
    @Autowired
    private ItemCheckService itemCheckService;

    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private NgameCacheKeyService ngameCacheKeyService;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private VirtualCreator virtualCreator;
    @Autowired
    private AppSlotCacheService appSlotCacheService;
    @Autowired
    private BizIdPoolService bizIdPoolService;


    @Autowired
    private RocketMQMsgProducer rocketMQMsgProducer;

    /**
     * app游戏定向信息本地缓存
     */
    private static final Cache<String, Optional<DuibaNgameAppSpecifyDto>> NGAME_APP_SPECIFY = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(100).build();

    /**
     * app游戏黑名单信息本地缓存
     */
    private static final Cache<String, Boolean> NGAME_APP_BLACK = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(100).build();

    /**
     * 游戏奖项
     */
    private static final Cache<Long,  Optional<List<DuibaNgameOptionsDto>>> NGAME_AUTO_OPTION = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(100).build();

    // 不自动发奖的固定duibaNgameId
    public static final List<Long> NOT_AUTO_OPEN_PRIZE_NGAMES = Lists.newArrayList(1877L,1919L);

    @Override
    public void checkPermission(ConsumerDto consumer, AppSimpleDto app, OperatingActivityDto operatingActivityDto,
                                DuibaNgameDto duibaNgameDto) throws StatusException {
        // 未登录
        if (ConsumerDto.NOTLOGINUSERID.equals(consumer.getPartnerUserId())) {
            throw new StatusException(StatusException.CodeOtherException, "请先登录");
        }
        // 预览用户
        if (ConsumerDto.PREVIEWUSERID.equals(consumer.getPartnerUserId())) {
            throw new StatusException(StatusException.CodeOtherException, "预览用户");
        }
        // 用户访问活动权限验证
        if (operatingActivityDto == null || duibaNgameDto == null || !operatingActivityDto.getAppId().equals(consumer.getAppId())) {
            throw new StatusException(StatusException.CodeOtherException, "无权访问");
        }
        // 活动结束
        if (operatingActivityDto.getDeleted() || operatingActivityDto.getStatus() != OperatingActivityDto.StatusIntOpen) {
            throw new StatusException(StatusException.CodeOtherException, "活动已结束");
        }
        // 后台关闭
        if (duibaNgameDto.getGameStatus() != DuibaNgameDto.STATUS_OPEN) {
            throw new StatusException(StatusException.CodeOtherException, "活动已结束");
        }
        // 活动已开奖
        if (duibaNgameDto.getIsOpenPrize()) {
            throw new StatusException(StatusException.CodeOtherException, "活动已结束");
        }
        // 自动关闭时间验证
        if (duibaNgameDto.getAutoOffDate() != null && duibaNgameDto.getAutoOffDate().before(new Date())) {
            throw new StatusException(StatusException.CodeOtherException, "活动已结束");
        }
        // 用户在200ms内频繁下单限制
        if (!PerfTestContext.isCurrentInPerfTestMode() && !frequentExchangeLimitService.canPassMillis(consumer.getId(), 200)) {
            throw new StatusException(StatusException.CodeFrequentExchange);
        }
        //校验黑名单和定向
        checkBlackListAndDirect(app, duibaNgameDto);
        // APP交易数限制
        checkAppTradingLimit(app);
    }

    /**
     * APP交易数限制
     * @param app
     * @throws StatusException
     */
    private void checkAppTradingLimit(AppSimpleDto app) throws StatusException {
        if (!app.isAppSwitch(AppSimpleDto.SwitchCloseAppTradingLimit)) {
            DubboResult<Integer> dubboResult = remoteAppTradingLimitService.getTodayTradingCount(app.getId());
            if (!dubboResult.isSuccess()) {
                log.warn("[Dubbo]Failed to getTodayTradingCount. code={}, msg={}, appid={}", dubboResult.getReturnCode(), dubboResult.getMsg(), app.getId());
                return;
            }
            if (dubboResult.getResult() >= 50) {
                throw new StatusException(StatusException.CodeAppTradingLimit);
            }
        }
    }

    /**
     * 校验黑名单和定向
     * @param app
     * @param duibaNgameDto
     * @throws StatusException
     */
    private void checkBlackListAndDirect(AppSimpleDto app, DuibaNgameDto duibaNgameDto) throws StatusException {
        // 黑名单限制
        if (duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_DEV_BLACKLIST)) {
            String blackKey = duibaNgameDto.getId()+"_"+app.getDeveloperId();
            boolean result = false;
            try {
                result = NGAME_APP_BLACK.get(blackKey, new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                        DubboResult<Boolean> dubboResult = remoteActivityBlackList4DeveloperService
                                .isExistBlackByActivityIdAndActivityTypeAndDeveloperId(duibaNgameDto.getId()
                                        , OperatingActivityDto.TypeDuibaNgame, app.getDeveloperId());
                        if(dubboResult.isSuccess() && dubboResult.getResult()){
                            return Boolean.TRUE;
                        }else{
                            return Boolean.FALSE;
                        }
                    }
                });
            } catch (Exception e) {
                log.error("获取app黑名单本地缓存信息失败",e);
            }

            if (result) {
                throw new StatusException(StatusException.CodeOtherException, "无法参与");
            }
        }
        // 定向APP
        if (duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_DIRECT)) {
            String specKey = app.getId()+"_"+duibaNgameDto.getId();
            DuibaNgameAppSpecifyDto tas = null;
            try {
                tas = NGAME_APP_SPECIFY.get(specKey, new Callable<Optional<DuibaNgameAppSpecifyDto>>() {
                    @Override
                    public Optional<DuibaNgameAppSpecifyDto> call() throws Exception {
                        return Optional.ofNullable(remoteDuibaNgameAppSpecifyService.findByGameConfigAndAppId(app.getId(), duibaNgameDto.getId()));
                    }
                }).orElse(null);
            } catch (Exception e) {
                log.error("获取app定向本地缓存信息失败",e);
            }
            if (null == tas) {
                throw new StatusException(StatusException.CodeOtherException, "无法参与");
            }
        }
    }

    @Override
    public NgameOrdersDto createOrder(ConsumerDto consumer, AppSimpleDto app, OperatingActivityDto operatingActivityDto,
                                      DuibaNgameDto duibaNgameDto, String ip, Long credits) throws StatusException {
        String limitScope = operatingActivityDto.getLimitScope();
        String freeScope = operatingActivityDto.getFreeScope();
        if (ngameService.isDuibaNgame(operatingActivityDto)) {
            limitScope = duibaNgameDto.getLimitScope();
            freeScope = duibaNgameDto.getFreeScope();
        }
        Long needCredits = ngameService.getCredits(app, operatingActivityDto, duibaNgameDto);
        //定制需求,前端传入需要扣的积分数,防止影响别的游戏,与原来应该扣得积分做比较取大者
        if (credits != null && credits > needCredits) {
            needCredits = credits;
        }
        // 是否免费
        boolean free = ngameService.isFree(consumer.getId(), operatingActivityDto, duibaNgameDto, app);
        //检验积分体系
        checkCreditsSystem(consumer, app, duibaNgameDto, freeScope, free);
        if (free) {
            needCredits = 0L;
        }
        // 积分不足
        if (consumer.getCredits() == null || (needCredits > 0 && consumer.getCredits() < needCredits)) {
            throw new StatusException(StatusException.CodeCreditsNotEnough, commonService.getUnitName4Show(app) + "不足，多赚点再来哦");
        }
        // 用户抽奖限制
        boolean canTakeOrder = ngameService.canTakeOrder(consumer.getId(), operatingActivityDto, duibaNgameDto,app);
        if (!canTakeOrder) {
            throw new StatusException(StatusException.CodeOtherException, limitScope.equals(DuibaNgameDto.LimitTypeEveryday) ? "今日游戏次数已用完" : "游戏次数已用完");
        }
        //确保传入credits参数时，必定扣积分
        if (credits != null && credits > needCredits) {
            needCredits = credits;
        }
        NgameOrdersDto order = new NgameOrdersDto(true);
        order.setToken(NgameOrdersDto.generateOrderToken());
        order.setAppId(consumer.getAppId());
        order.setConsumerId(consumer.getId());
        order.setPartnerUserId(consumer.getPartnerUserId());
        order.setOperatingActivityId(operatingActivityDto.getId());
        Long duibaNgameId = duibaNgameDto.getId();
        order.setDuibaNgameId(duibaNgameId);
        order.setGameType(operatingActivityDto.getType().toString());
        order.setCredits(needCredits);
        order.setIp(ip);
        order.setOrderStatus(NgameOrdersDto.StatusCreate);
        order.setExchangeStatus(NgameOrdersDto.ExchangeStatusCreate);
        // 记录用户参与次数
        ngameCommonService.addJoinNum(duibaNgameDto.getLimitScope(), duibaNgameDto.getLimitCount()
                , duibaNgameDto.getFreeScope(), duibaNgameDto.getFreeLimit(), consumer.getId(), order.getOperatingActivityId(), order.getCredits());
        order = remoteNgameOrdersConsumerService.insert(order);
        // 活动参与人数 +1
        remoteOperatingActivityServiceNew.increaseJoinNum(operatingActivityDto.getId());
        // 扣除本地积分
        if (needCredits > 0) {
            remoteConsumerService.decrementCredits(consumer.getId(), needCredits);
        }
        // 订单创建事件
        DuibaEventsDispatcher.get().dispatchEvent(new NgameOrdersEvent(NgameOrdersEvent.NgameOrdersEventType.OnOrderCreate, order));
        DuibaPluginEventsDispatcher.get().triggerAfterNgameOrderCreate(order, RequestLocal.getRequest());
        return order;
    }

    /**
     * 检验积分体系
     * @param consumer
     * @param app
     * @param duibaNgameDto
     * @param freeScope
     * @param free
     * @throws StatusException
     */
    private void checkCreditsSystem(ConsumerDto consumer, AppSimpleDto app, DuibaNgameDto duibaNgameDto, String freeScope, boolean free) throws StatusException {
        //此次兑换不是免费而且开启无积分体系
        if (duibaNgameDto.getFreeLimit() != null && !free && app.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType)) {
            if (consumer.getPartnerUserId() != null && consumer.getPartnerUserId().startsWith(ConsumerDto.NOTLOGINUSERID_GEN_NEW)) {
                throw new StatusException(StatusException.CodeOtherException, "请先登录");
            } else {
                throw new StatusException(StatusException.CodeOtherException, freeScope.equals(DuibaNgameDto.LimitTypeEveryday) ? "今日次数已用完" : "永久次数已用完");
            }
        }
    }

    @Override
    public void asyncConsumeCredits(ConsumerDto consumer, AppSimpleDto app, Long gameOrderId, Long credits, RequestParams requestParams, DuibaNgameDto duibaNgameDto) {//NOSONAR

        // 针对淘金币定制，需要携带access_token, mixUserNick
        String accessKey = null;
        String mixUserNick = null;
        String token = null;
        boolean isTaoJinBi = Objects.equal(AppIdConstant.TAOJINBI,app.getId());
        if (isTaoJinBi) {
            accessKey = advancedCacheClient.get(RedisKeys.CHW_PREFIX+consumer.getPartnerUserId());
            mixUserNick = remoteConsumerService.findExtra(consumer.getId(), "mixUserNick").getResult();
            try {
                token = taojinbiTool.getToken(mixUserNick, requestParams.getUserAgent(), requestParams.getIp());
            } catch (Exception e) {
                log.warn("", e);
            }
        }

        if (isTaoJinBi && (accessKey==null||mixUserNick==null||token==null)) {
            // 回补积分
            if (credits!=null&&credits>0) {
                remoteConsumerService.increaseCredits(consumer.getId(), credits);
            }
            log.warn("用户昵称或token失效, accessKey={}, mixUserNick={}, token={}", accessKey, mixUserNick, token);
            throw new AccessActivityRuntimeException("用户昵称或token失效，请重新进入活动");
        }
        Map<String, String> params = new HashMap<>();
        try {
            final Long consumerId = consumer.getId();
            final Long ngameOrderId = gameOrderId;
            NgameOrdersDto gameOrder = remoteNgameOrdersConsumerService.find(consumerId, gameOrderId);
            // 判断订单状态，非创建状态的什么也不做
            if (gameOrder.getOrderStatus() != NgameOrdersDto.StatusCreate) {
                return;
            }
            // 如果消耗积分为0， 不需要扣积分
            if (gameOrder.getCredits() == 0&& !AppIdConstant.FREE_SUB_CREIDS.contains(app.getId())) {
                executorService.submit(() -> consumeCreditsCallback.onSuccess(consumerId, ngameOrderId, null));
                return;
            }

            String transfer = requestParams.getTransfer();
            CreditConsumeParams p = new CreditConsumeParams();
            p.setAppKey(app.getAppKey());
            p.setCredits(gameOrder.getCredits());
            p.setOrderNum(NgameOrdersDto.generateOrderNum(gameOrderId));
            p.setUid(consumer.getPartnerUserId());
            p.setTimestamp(new Date());
            p.setType(SubCreditsOuterType.GAME.getCode());
            p.setIp(gameOrder.getIp());
            p.setFacePrice(0);
            p.setActualPrice(0);
            if (StringUtils.isNotBlank(transfer)) {
                p.setTransfer(transfer);
            }
            if (AppIdConstant.isOppoApp(app.getId())) {
                p.setDescription(duibaNgameDto.getTitle());
            } else {
                setDesc(p, duibaNgameDto);
            }
            Map<String, String> map = p.toRequestMap(app.getAppSecret());

            SubCreditsMsgDto subCreditsMsgDto = new SubCreditsMsgDto();
            subCreditsMsgDto.setAppId(app.getId());
            subCreditsMsgDto.setConsumerId(consumerId);
            subCreditsMsgDto.setHttpType(SubCreditsMsgDto.HTTP_GET);
//            subCreditsMsgDto.setHttpUrl(url);
            subCreditsMsgDto.setRelationId(gameOrderId.toString());
            subCreditsMsgDto.setRelationType(SubCreditsType.NGAME_LOTTERY);
//            subCreditsMsgDto.setRelationType("ngame");
            subCreditsMsgDto.setTimestamp(System.currentTimeMillis());
            String url = AssembleTool.assembleUrl(app.getCreditsConsumeRequestUrl(), map);
            params.put("url", url);
            params.put("consumerId", consumerId.toString());
            params.put("ngameOrderId", gameOrderId.toString());
            params.put("credits", String.valueOf(credits)); // null->"null"
            params.put("accessKey", accessKey);
            params.put("mixUserNick", mixUserNick);
            params.put("token", token);
            if (StringUtils.isNotBlank(transfer)) {
                params.put("transfer", transfer);
            }
            Long duibaNgameId = gameOrder.getDuibaNgameId();
            params.put("duibaNgameId",String.valueOf(duibaNgameId));
            params.put(CreditsMessageDto.MESSAGE_CHANNEL_TYPE_KEY,CallbackChannelTypeEnum.ROCKETMQ.getType());
            subCreditsMsgDto.setParams(params);

            // 必须在组装url之后 AssembleTool.assembleUrl
            map.put("appSecret", app.getAppSecret());
            subCreditsMsgDto.setAuthParams(map);

            subCreditsMsgDto.setCreditsConsumeRequestUrl(app.getCreditsConsumeRequestUrl());
            subCreditsMsgDto.setCreditConsumeParams(p);
            subCreditsMsgDto.setAppSecret(app.getAppSecret());
            subCreditsMsgDto.setCallbackTopic(rocketMqMessageTopic.getActivitySubCreditsCallbackNew());
            subCreditsMsgDto.setCallbackTag(OnsMessageTopic.TAG_NGAME_SUB_CREDITS);
            subCreditsMsgDto.setCallbackKey(NgameOrdersDto.generateOrderNum(gameOrderId));

            //提交扣积分请求
            submitSubCredits(gameOrderId, subCreditsMsgDto);
        } catch (Exception e) {
            consumeCreditsCallback.onFail(consumer.getId(), gameOrderId, credits, params, e);
        }
    }

    private void setDesc(CreditConsumeParams p, DuibaNgameDto duibaNgameDto) {
        if (duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_IS_COIN_PUSHER_GAME)) {
            p.setDescription("游戏," + duibaNgameDto.getTitle());
        } else {
            p.setDescription("游戏活动");
        }
    }

    /**
     * 提交扣积分请求
     * @param gameOrderId
     * @param creditsMessage
     */
   /* @Deprecated
    private void submitSubCredits(Long gameOrderId, CreditsMessage creditsMessage) {
        DubboResult<Boolean> dubboResult = remoteCreditsService.submitSubCredits(creditsMessage, rocketMqMessageTopic.getActivitySubCreditsCallback(),
                OnsMessageTopic.TAG_NGAME_SUB_CREDITS, NgameOrdersDto.generateOrderNum(gameOrderId));
        if (!dubboResult.isSuccess()) {
            log.warn("[Dubbo]remoteCreditsService.submitSubCredits failed, code={}, msg={}", dubboResult.getReturnCode(), dubboResult.getMsg());
            throw new AccessActivityRuntimeException("提交扣积分请求失败:" + dubboResult.getMsg());
        }
        if (!dubboResult.getResult()) {
            throw new AccessActivityRuntimeException("提交扣积分请求失败:result=false");
        }
    }*/

    /**
     * 新版扣积分请求
     * */
    private void submitSubCredits(Long gameOrderId, SubCreditsMsgDto subCreditsMsgDto) {

        String msgId=rocketMQMsgProducer.sendMsgWithGzip(rocketMqMessageTopic.getActivitySubCreditsNew()
                ,OnsMessageTopic.TAG_NGAME_SUB_CREDITS, NgameOrdersDto.generateOrderNum(gameOrderId)
                , JSON.toJSONString(subCreditsMsgDto));

        if (StringUtils.isBlank(msgId)) {
            log.warn("ngame send mq SubCredits failed, orderId:{}",gameOrderId);
            throw new AccessActivityRuntimeException("新游戏提交扣积分请求失败。" );
        }
    }


    @Override
    public void notifyOrderConsumerSuccess(Long consumerId, Long gameOrderId, String transfer) {
        NgameOrdersDto game = null;
        try {
            game = remoteNgameOrdersConsumerService.find(consumerId, gameOrderId);
            DuibaEventsDispatcher.get().dispatchEvent(new NgameOrdersEvent(NgameOrdersEvent.NgameOrdersEventType.OnOrderConsumeSuccess, game));
            Map<String, Object> params = new HashMap<>();
            params.put(NGConsumerCreditsEvent.PARAM_CREDITS, game.getCredits());
            params.put(NGConsumerCreditsEvent.PARAM_APPID, game.getAppId());
            NGDuibaEventsDispatcher.getInstance().dispatchConsumeCreditsEvent(new NGConsumerCreditsEvent(NGConsumerCreditsEvent.TYPE_NGAME, true, params));
        } catch (Exception e) {
            log.error("notifyOrderConsumerSuccess", e);
        } finally {
            notifyDeveloper(game, transfer);
        }
    }

    @Override
    public void validGameData(NgameOrdersDto gameOrder, NgameParams gameParams,
                              DuibaNgameDto duibaNgameDto) throws StatusException {
        if (gameOrder.getOrderStatus() != NgameOrdersDto.StatusConsumeSuccess) {
            throw new StatusException(StatusException.CodeOtherException, "订单状态不正确");
        }
        if (StringUtils.isEmpty(gameParams.getToken())) {
            throw new StatusException(StatusException.CodeOtherException, "token不能为Empty");
        }
        if (!gameParams.getToken().equals(gameOrder.getToken())) {
            throw new StatusException(StatusException.CodeOtherException, "token不正确");
        }
        long gameTime = new Date().getTime() - gameOrder.getGmtCreate().getTime();
        long gameTimeOut = (long) DuibaNgameDto.santa_max_time * 1000;

        if (!PerfTestContext.isCurrentInPerfTestMode() && !Environment.isDaily()) {
            DuibaNgameBrickDto brick = ngameCommonService.findDuibaNgameBrickDto(duibaNgameDto.getDuibaGameBrickId());
            if (brick != null && brick.getTimeLimit() != null) {
                gameTimeOut = brick.getTimeLimit() * 60 * 1000;
            }
            if (gameTime > gameTimeOut) {
                throw new StatusException(StatusException.CodeOtherException, "游戏超时了！下次稍微玩快一点哦！");
            }
        }
        if (StringUtils.isEmpty(gameParams.getTotalScore())) {
            throw new StatusException(StatusException.CodeOtherException, "分数错误");
        }
        if (!NumberUtils.isDigits(gameParams.getTotalScore())) {
            throw new StatusException(StatusException.CodeOtherException, "分数错误");
        }
    }

    @Override
    public List<NgameOptionVO> getOneAutoOpenPrize(Long consumerId, OperatingActivityDto operatingActivity) throws StatusException {
        if (ngameService.isDuibaNgame(operatingActivity)) {
            Long gameConfigDuibaId = operatingActivity.getActivityId();
            List<DuibaNgameOptionsDto> autoOpenOptions = Lists.newArrayList();
            try {
                autoOpenOptions = NGAME_AUTO_OPTION.get(gameConfigDuibaId, new Callable<Optional<List<DuibaNgameOptionsDto>>>() {
                    @Override
                    public Optional<List<DuibaNgameOptionsDto>> call() throws Exception {
                        return  Optional.ofNullable(remoteDuibaNgameOptionsService.findByAutoOpenDesc(gameConfigDuibaId, true));
                    }
                }).orElse(Lists.newArrayList());
            } catch (Exception e) {
                log.error("获取游戏自动开奖本地缓存信息失败",e);
            }
            List<NgameOptionVO> options = new ArrayList<>();
            for (DuibaNgameOptionsDto god : autoOpenOptions) {
                options.add(new NgameOptionVO(god));
            }
            if (CollectionUtils.isNotEmpty(autoOpenOptions)) {
                //查询中奖纪录，并打乱奖项排序
                NgameConsumerRecordDto gcr = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(consumerId, gameConfigDuibaId);
                Collections.shuffle(options);
                //如果用户第一次参与，或者没有中奖纪录。直接返回奖项列表
                if (gcr == null || StringUtils.isBlank(gcr.getAutoOpenPrizeId())) {
                    return options;
                }
                //将查询所有奖项存入map，减少数据库查询
                Map<Long, DuibaNgameOptionsDto> optionsInfo = Maps.uniqueIndex(autoOpenOptions, DuibaNgameOptionsDto::getId);
                //遍历中奖信息，并将已中奖所属分组存入winPrizeGroup
                String[] prizeIds = gcr.getAutoOpenPrizeId().split(",");
                Set<String> prizeIdsSet = Sets.newHashSet(prizeIds);
                List<String> winPrizeGroup = getWinPrizeGroup(optionsInfo, prizeIdsSet);
                //剔除已经获得过的奖品奖
                removeGotAutoPrize(options, winPrizeGroup);
                return options;
            }
            return null;
        }
        throw new AccessActivityRuntimeException("不是兑吧活动");
    }

    private List<String> getWinPrizeGroup(Map<Long, DuibaNgameOptionsDto> optionsInfo, Set<String> prizeIdsSet) {
        List<String> winPrizeGroup = Lists.newArrayList();
        for (String pid : prizeIdsSet) {
            if (StringUtils.isBlank(pid)) {
                continue;
            }
            DuibaNgameOptionsDto pdto = optionsInfo.get(Long.valueOf(pid));
            if (pdto == null) {
                pdto = remoteDuibaNgameOptionsService.find(Long.valueOf(pid));
            }
            StringBuilder sb = new StringBuilder();
            //判断奖项名称格式,是否为规定格式  名称##01
            if (pdto.getPrizeName() != null && pdto.getPrizeName().split(OPTION_SEPARATOR).length == 2) {
                sb.append(pdto.getPrizeName().split(OPTION_SEPARATOR)[1]);
            } else {
                sb.append(OPTION_GROUP_PREFIX);
                sb.append(pid);
            }
            winPrizeGroup.add(sb.toString());
        }
        return winPrizeGroup;
    }

    /**
     * 剔除已经获得过的奖品奖
     * @param options
     * @param winPrizeGroup
     */
    private void removeGotAutoPrize(List<NgameOptionVO> options, List<String> winPrizeGroup) {
        Iterator<NgameOptionVO> it = options.iterator();
        // 剔除掉已经中奖的奖项
        while (it.hasNext()) {
            NgameOptionVO opt = it.next();
            if (opt.getName() != null && !NgameOrdersDto.PrizeTypeLuckBag.equals(opt.getPrizeType())) {
                StringBuilder sb = new StringBuilder();
                //判断奖项名称格式,是否为规定格式  名称##01
                if (opt.getName().split(OPTION_SEPARATOR).length == 2) {
                    sb.append(opt.getName().split(OPTION_SEPARATOR)[1]);
                } else {
                    sb.append(OPTION_GROUP_PREFIX);
                    sb.append(opt.getId().toString());
                }
                if (winPrizeGroup.contains(sb.toString())) {
                    it.remove();
                }
            }
        }
    }

    @Override
    public boolean validOptionCheck(OperatingActivityDto operatingActivityDO, NgameOptionVO option,
                                    NgameOrdersDto gameOrder) throws StatusException {
        // 谢谢参与
        if (NgameOrdersDto.PrizeTypeThanks.equals(option.getPrizeType()) || NgameOrdersDto.PrizeTypeLuckBag.equals(option.getPrizeType())) {
            return true;
        }
        // 奖项状态验证
        if (option.getDeleted()) {
            log.info("奖项删除状态，出奖失败，奖项id={}", option.getId());
            return false;
        }
        //多奖项验证
        // 奖项剩余个数验证
        Integer remaining = 0;
        if (ngameService.isDuibaNgame(operatingActivityDO)) {
            NgameStockDto stock = ngameStockService.getGameOptionDuibaRemaining(option.getId());
            remaining = stock.getStock();
        }
        if (remaining <= 0) {
            log.info("奖项库存小于等于0，出奖失败，奖项id={}", option.getId());
            return false;
        }
        return true;
    }

    @Override
    public boolean validItemKeyCheck(OperatingActivityDto operatingActivityDO, NgameOptionVO option,
                                     NgameOrdersDto gameOrder, AppSimpleDto app) throws StatusException {
        // 谢谢参与
        if (NgameOrdersDto.PrizeTypeThanks.equals(option.getPrizeType()) || NgameOrdersDto.PrizeTypeLuckBag.equals(option.getPrizeType())) {
            return true;
        }
        // 获取ItemKey 如果开发者仓库删除，itemKeyDto.getAppItem 为 null
        DubboResult<ItemKeyDto> dubboResult = remoteItemKeyService.findItemKey(option.getAppItemId(), option.getItemId(), gameOrder.getAppId());
        if (!dubboResult.isSuccess()) {
            log.warn("[Dubbo]remoteItemKeyService.findItemKey failed, msg={}", dubboResult.getMsg());
            return false;
        }
        boolean isDuiba = ngameService.isDuibaNgame(operatingActivityDO);
        ItemKeyDto itemKeyDto = dubboResult.getResult();
        if (itemKeyDto == null) {
            log.info("奖项item不存在，出奖失败，奖项id={}", option.getId());
            return false;
        }
        //商品删除校验
        if (checkItemDeleted(option, itemKeyDto)) {
            return false;
        }
        // 限时，限量，会员等级限制，兑换限制，日期限制等有限制的，都不中奖
        if (checkItemLimit(option, app, itemKeyDto)) {
            return false;
        }
        // 奖项总库存验证
        if (checkOptionStock(option, itemKeyDto)) {
            return false;
        }
        Date now = new Date();
        // 优惠券批次校验
        if (checkCoupon(option, itemKeyDto, now)) {
            return false;
        }
        // 失效验证
        if (itemKeyDto.getAppItem() != null && AppItemDto.SubStatusExpired.equals(itemKeyDto.getAppItem().getSubStatus())) {
            log.info("商品已失效，出奖失败，奖项id={}", option.getId());
            return false;
        }
        // 实物自动下架失效过期验证，自动关闭
        if (checkObject(option, itemKeyDto, now)) {
            return false;
        }
        // 虚拟商品校验
        if (checkVirtual(gameOrder.getPartnerUserId(),option, app, itemKeyDto, now, isDuiba)) {
            return false;
        }

        String itemDtoType = itemKeyDto.getItemDtoType();
        Integer actualPrice = getActualPrice(option, itemKeyDto, itemDtoType,app.getId());

        //非兑吧活动,计算开发者预算
        if (checkActualPrice(option, itemDtoType, actualPrice)) {
            return false;
        }
        return true;
    }

    /**
     * 非兑吧活动,计算开发者预算
     * @param option
     * @param itemDtoType
     * @param actualPrice
     * @return
     */
    private boolean checkActualPrice(NgameOptionVO option, String itemDtoType, Integer actualPrice) {
        if (actualPrice != null && actualPrice > 0) {
            //兑吧活动  兑吧付费直充
            if (PAY_ITEM_TYPE.contains(itemDtoType)) {
                DuibaRemainingMoneyDto duibaM = remoteDuibaRemainingMoneyService.findRecord();
                if (duibaM == null || duibaM.getMoney() < actualPrice) {
                    log.info("兑吧付费直充余额不足，出奖失败，奖项id={}", option.getId());
                    return true;
                }
            }
        }
        return false;
    }

    private Integer getActualPrice(NgameOptionVO option, ItemKeyDto itemKeyDto, String itemDtoType,Long appId) {
        Integer actualPrice = null;
        if (PAY_ITEM_TYPE.contains(itemDtoType)) {
            actualPrice = Integer.valueOf(option.getFacePrice());
        } else if (ItemDto.TypeObject.equals(itemDtoType) || ItemDto.TypeCoupon.equals(itemDtoType)) {
            actualPrice = actualPriceCalculateService.calculateNormalActualPrice(itemKeyDto,appId);
        }
        return actualPrice;
    }

    /**
     * 校验限时，限量，会员等级限制，兑换限制，日期限制等有限制
     * @param option
     * @param app
     * @param itemKeyDto
     * @return
     */
    private boolean checkItemLimit(NgameOptionVO option, AppSimpleDto app, ItemKeyDto itemKeyDto) {
        if (timeLimitService.isLimitItemKey(itemKeyDto)) {
            log.info("兑吧商品删除、禁用状态，出奖失败，奖项id={}", option.getId());
            return true;
        }
        // APP兑换限制只能测试专用商品中奖
        if (itemKeyDto.getAppItem() != null && !app.isAppSwitch(AppSimpleDto.SwitchCloseAppTradingLimit)) {
            if (null != itemKeyDto.getItem() && itemKeyDto.getItem().getType().equals(ItemDto.TypeCoupon)) {
                if (!itemKeyDto.getItem().getId().equals(31605l)) {
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 奖项总库存验证
     * @param option
     * @param itemKeyDto
     * @return
     */
    private boolean checkOptionStock(NgameOptionVO option, ItemKeyDto itemKeyDto) {
        if (RESUME_STOCK_TYPE.contains(option.getPrizeType())) {
            DubboResult<Long> dubboResultStock = remoteItemKeyService.findStock(itemKeyDto);
            if (!dubboResultStock.isSuccess()) {
                log.warn("[Dubbo]{}, msg={}", "remoteItemKeyService.findStock", dubboResultStock.getMsg());
                return true;
            }
            if (dubboResultStock.getResult().longValue() <= 0) {
                log.info("奖项总库存不足，出奖失败，奖项id={}", option.getId());
                return true;
            }
        }
        return false;
    }

    /**
     * 虚拟商品校验
     * @param option
     * @param app
     * @param itemKeyDto
     * @param now
     * @param isDuiba
     * @return
     */
    private boolean checkVirtual(String partnerUserId,NgameOptionVO option, AppSimpleDto app, ItemKeyDto itemKeyDto, Date now, boolean isDuiba) {
        if (!ItemDto.TypeVirtual.equals(itemKeyDto.getItemDtoType())) {
            return false;
        }
        //校验是否是未登录用户
        if (ConsumerDto.isNotLoginUser(partnerUserId)) {
            return true;
        }

        if (StringUtils.isBlank(app.getVirtualExchangeUrl())) {
            log.info("虚拟商品没有配置兑换接口，出奖失败，奖项id={}", option.getId());
            return true;
        } else if (isDuiba) {
            if (null == itemKeyDto.getItem() || itemKeyDto.getItem().getDeleted()) {
                log.info("兑吧虚拟商品已删除，出奖失败，奖项id={}", option.getId());
                return true;
            }
            // 自动关闭时间验证
            if (itemKeyDto.getItem().getAutoOffDate() != null && now.after(itemKeyDto.getItem().getAutoOffDate())) {
                log.info("兑吧虚拟商品已过期，出奖失败，奖项id={}", option.getId());
                return true;
            }
            //验证是否定向正确
            if (!itemCheckService.getVirtualOwnerAppIds(itemKeyDto.getItem()).contains(String.valueOf(app.getId()))) {
                log.info("兑吧虚拟商品定向不正确，出奖失败，奖项id={}", option.getId());
                return true;
            }
        }

        return false;
    }



    /**
     * 优惠券批次校验
     * @param option
     * @param itemKeyDto
     * @param now
     * @return
     */
    private boolean checkCoupon(NgameOptionVO option, ItemKeyDto itemKeyDto, Date now) {
        if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())) {
            if (itemKeyDto.isItemMode() || itemKeyDto.isDuibaAppItemMode()) {
                if (itemKeyDto.getItem().getValidEndDate() != null && now.after(itemKeyDto.getItem().getValidEndDate())) {
                    log.info("优惠券超出有效期，出奖失败，奖项id={}", option.getId());
                    return true;
                }
                if (itemKeyDto.getItem().getAutoOffDate() != null && now.after(itemKeyDto.getItem().getAutoOffDate())) {
                    log.info("优惠券超出有效期，出奖失败，奖项id={}", option.getId());
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 实物自动下架失效过期验证，自动关闭
     * @param option
     * @param itemKeyDto
     * @param now
     * @return
     */
    private boolean checkObject(NgameOptionVO option, ItemKeyDto itemKeyDto, Date now) {
        if (ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType())) {
            if (itemKeyDto.isItemMode() || itemKeyDto.isDuibaAppItemMode()) {
                if (itemKeyDto.getItem().getValidEndDate() != null && now.after(itemKeyDto.getItem().getValidEndDate())) {
                    log.info("实物超出有效期，出奖失败，奖项id={}", option.getId());
                    return true;
                }
                if (itemKeyDto.getItem().getAutoOffDate() != null && now.after(itemKeyDto.getItem().getAutoOffDate())) {
                    log.info("实物超出有效期，出奖失败，奖项id={}", option.getId());
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 商品删除校验
     * @param option
     * @param itemKeyDto
     * @return
     */
    private boolean checkItemDeleted(NgameOptionVO option, ItemKeyDto itemKeyDto) {
        String itemKeyDtoType = itemKeyDto.getItemDtoType();
        if (!PAY_ITEM_TYPE.contains(itemKeyDtoType) &&
                itemKeyDto.getAppItem() != null &&
                itemKeyDto.getAppItem().getDeleted()) {
            log.info("开发者商品删除状态，出奖失败，奖项id={}", option.getId());
            return true;
        }
        // 兑吧商品，禁用或删除验证
        if (itemKeyDto.getItem() != null && (!itemKeyDto.getItem().getEnable() || itemKeyDto.getItem().getDeleted())) {
            log.info("兑吧商品删除、禁用状态，出奖失败，奖项id={}", option.getId());
            return true;
        }
        return false;
    }

    @Override
    public void doAutoOpenPrize(OperatingActivityDto operatingActivityDto, DuibaNgameDto duibaNgameDto, NgameOptionVO option,
                                NgameOrdersDto ngameOrdersDto, NgameParams gameParams, Long developerId, RequestParams requestParams) throws StatusException {

        NgameOrderPlugin.NgamePluginContext context = new NgameOrderPlugin.NgamePluginContext();
        try {

            Long gameOrderExtraId;
            ItemKeyDto key = commonService.findItemKeyDto(option.getAppItemId(), option.getItemId(), ngameOrdersDto.getAppId());
            // 根据奖项类型发奖
            String prizeType = option.getPrizeType();
            if (PRIZE_TYPE.contains(prizeType)) {
                // 扣奖项库存，商品库存，商品定向库存，商品预分配库存
                context.setAttribute(NgameOrderPlugin.NgamePluginContext.ITEM_KEY, key);
                DuibaPluginEventsDispatcher.get().triggerBeforeNgameStockComplete(ngameOrdersDto, option, context);
                //获取优惠券码，若奖品不是优惠券此处获取null
                Long couponId = (Long) context.getAttribute(NgameOrderPlugin.NgamePluginContext.COUPONID);
                // 记录即开奖订单信息
                NgameOrdersExtraDto gameOrdersExtraDto = insertNgameOrdersExtraDto(ngameOrdersDto, option, couponId);
                gameOrderExtraId = gameOrdersExtraDto.getId();
                // 更新子订单为待开奖状态

                if (Objects.equal(prizeType, NgameOrdersDto.PrizeTypeCoupon)) {
                    //优惠券自动发奖
                    doCouponAutoPrize(option, ngameOrdersDto, developerId, gameOrderExtraId, key, gameOrdersExtraDto,operatingActivityDto.getTitle(), requestParams);
                    updateNgameOrderSuccess(ngameOrdersDto, gameParams, gameOrderExtraId);
                } else if (Objects.equal(prizeType, NgameOrdersDto.PrizeTypeVirtual)) {
                    //虚拟商品自动发奖
                    doVirtualAutoPrize(option, ngameOrdersDto, key,operatingActivityDto.getTitle(),requestParams,gameParams, gameOrderExtraId);
                } else if(Objects.equal(prizeType, NgameOrdersDto.PrizeTypeCollectGoods)){
                    //集卡
                    activityKvService.createCollectGoodsInfo(option.getItemId(),ngameOrdersDto.getConsumerId());
                    updateNgameOrderSuccess(ngameOrdersDto, gameParams, gameOrderExtraId);
                } else{
                    //其余奖品生成兑换记录
                    ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
                    record.setConsumerId(ngameOrdersDto.getConsumerId());
                    record.setType(ConsumerExchangeRecordDto.TypeNgame);
                    record.setRelationId(ngameOrdersDto.getId());
                    record.setAppId(ngameOrdersDto.getAppId());
                    record.setOrigin(operatingActivityDto.getId() * 100);
                    //设置活动名称
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("trueActivityTitle", operatingActivityDto.getTitle());
                    record.setJson(
                            jsonObject.toJSONString()
                    );

                    record.setPrizeName(option.getName());
                    record.setItemId(option.getItemId());
                    record.setAppItemId(option.getAppItemId());

                    DubboResult<ConsumerExchangeRecordDto> dubboResult = remoteConsumerExchangeRecordService.insert(record);
                    if (!dubboResult.isSuccess()) {
                        throw new AccessActivityRuntimeException("插入兑换记录失败:" + dubboResult.getMsg());
                    }
                    updateNgameOrderSuccess(ngameOrdersDto, gameParams, gameOrderExtraId);
                }
                updateConsumerOptions(ngameOrdersDto, option);
            } else if (option.getPrizeType().equals(NgameOrdersDto.PrizeTypeLuckBag)) {
                //生成即开奖订单
                NgameOrdersExtraDto gameOrdersExtraDto = insertNgameOrdersExtraDto(ngameOrdersDto, option, null);
                ngameOrdersDto = remoteNgameOrdersConsumerService.find(ngameOrdersDto.getConsumerId(), ngameOrdersDto.getId());
                //请求福袋
                takeLuckBagPrize(ngameOrdersDto, duibaNgameDto, developerId, gameOrdersExtraDto.getId(), requestParams);
                updateConsumerOptions(ngameOrdersDto, option);
                remoteNgameOrdersExtendService.updateGameData(ngameOrdersDto.getId(), gameParams.getGameDataJson());
            }
            //领奖后更新子订单
            updateOrderAfterTakePrize(option, ngameOrdersDto, prizeType);
            //校验是否需要将bizId放入bizPool中
            checkAndPushBizId(ngameOrdersDto, prizeType);
        } catch (Exception e) {
            // 返还奖项库存，商品库存，商品定向库存，商品预分配库存
            DuibaPluginEventsDispatcher.get().triggerNgameStockCompleteException(ngameOrdersDto, option, e, context);
            // 出现异常，置为订单完成
            remoteNgameOrdersConsumerService.updateExchangeStatusToWaitOpenAndExtraId(ngameOrdersDto.getConsumerId(), ngameOrdersDto.getId(), gameParams.getGameDataJson(), null);
            log.error("error and force make complete", e);
        } finally {
            doOrderSuccess(ngameOrdersDto);
        }
    }

    /**
     * 校验并保存有效bizId
     * @param ngameOrdersDto
     * @param prizeType
     */
    private void checkAndPushBizId(NgameOrdersDto ngameOrdersDto, String prizeType) {
        //类型不支持
        if (!DuibaPrizeTypeEnum.isAccessToBizPoolType(prizeType)) {
            return;
        }
        //不是定制app
        if (!AppIdConstant.BIZ_POOL.contains(ngameOrdersDto.getAppId())) {
            return;
        }
        //无效bizId
        if (StringUtils.isBlank(ngameOrdersDto.getDeveloperBizId())) {
            return;
        }
        bizIdPoolService.insert(ngameOrdersDto.getConsumerId(), ngameOrdersDto.getDeveloperBizId());
    }

    /**
     * 游戏虚拟商品流程自动领奖之后再更新订单状态
     * @param takePrize
     * @param record
     * @return
     * @throws AccessActivityException
     */
    @Override
    public OrdersDto takeVirtualPrize(TakePrizeVo takePrize, ConsumerExchangeRecordDto record, String account) throws AccessActivityException {
        activityTakePrizeService.checkTakePrizeItemKey(takePrize.getItemKeyDto());

        OrdersDto order = null;
        try{
            String bizParams = PriceDegreeDto.getBizParams(takePrize.getFacePrice(),account);
            order = virtualCreator.innerCreateOrder(takePrize,bizParams);
            //更新子订单状态
            if (order == null) {
                return null;
            }
            //更新子订单中 主订单号
            activityTakePrizeService.updateMainOrderId(takePrize,order,record);
        } catch (Exception e) {
            // 回滚  领奖状态
            if (ConsumerExchangeRecordDto.TypeNgame == takePrize.getRecordType().intValue()) {
                remoteNgameOrdersConsumerService.rollbackTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
            }
            log.warn("领奖失败", e);
            throw new AccessActivityRuntimeException(CommonConstants.MSG_SERVER_EXCEPTION_LJSB, e);
        }
        return order;
    }

    private void updateNgameOrderSuccess(NgameOrdersDto ngameOrdersDto, NgameParams gameParams, Long gameOrderExtraId) {
        int ret = remoteNgameOrdersConsumerService.updateExchangeStatusToWaitOpenAndExtraId(ngameOrdersDto.getConsumerId(), ngameOrdersDto.getId(), gameParams.getGameDataJson(), gameOrderExtraId);
        if (ret < 1) {
            throw new AccessActivityRuntimeException("更新订单待开奖状态失败");
        }
    }

    /**
     * 更新每日排行榜
     * @param game
     * @param gameScore
     * @param isDesc
     * @param consumerId
     */
    @Override
    public void updateDailyRank(DuibaNgameDto game, long gameScore, boolean isDesc, Long consumerId) {
        String json = game.getExtendJson();
        JSONObject extendConfig = JSONObject.parseObject(json);
        if (extendConfig == null) {
            return;
        }
        NgameRankConfig config = ngameRankService.getDailyRankConfig(game.getId(), isDesc);
        Long oldScore = config.getScore(String.valueOf(consumerId));
        Integer dailyRankType = extendConfig.getInteger("dailyRankType");
        long newScore;
        if (oldScore == null) {
            newScore = gameScore;
        } else if (Objects.equal(dailyRankType, 1)){
            newScore = oldScore + gameScore;
        } else if (Objects.equal(dailyRankType, 0)) {
            if ((isDesc && oldScore > gameScore) || (!isDesc && oldScore < gameScore)) {
                return;
            }
            newScore = gameScore;
        } else {
            return;
        }
        config.add(String.valueOf(consumerId), newScore);
    }


    /**
     * 更新每周排行榜
     * @param game
     * @param gameScore
     * @param isDesc
     * @param consumerId
     */
    @Override
    public void  updateWeeklyRank(DuibaNgameDto game, long gameScore, boolean isDesc, Long consumerId) {
        String json = game.getExtendJson();
        JSONObject extendConfig = JSONObject.parseObject(json);
        if (extendConfig == null) {
            return;
        }
        NgameRankConfig config = ngameRankService.getWeeklyRankConfig(game.getId(), isDesc);
        Long oldScore = config.getScore(String.valueOf(consumerId));
        long newScore;
        if (oldScore == null) {
            newScore = gameScore;
        } else if ((isDesc && oldScore > gameScore) || (!isDesc && oldScore < gameScore)) {
            return;
        } else {
            newScore = gameScore;
        }
        config.add(String.valueOf(consumerId), newScore);
    }

    /**
     * 更新月排行榜数据
     * @param game
     * @param gameScore
     * @param isDesc
     * @param consumerId
     */
    @Override
    public void updateMonthlyRank(DuibaNgameDto game, long gameScore, boolean isDesc, Long consumerId) {
        String json = game.getExtendJson();
        JSONObject extendConfig = JSONObject.parseObject(json);
        if (extendConfig == null) {
            return;
        }
        NgameRankConfig config = ngameRankService.getMonthlyRankConfig(game.getId(), isDesc);
        Long oldScore = config.getScore(String.valueOf(consumerId));
        long newScore;
        if (oldScore == null) {
            newScore = gameScore;
        } else if ((isDesc && oldScore > gameScore) || (!isDesc && oldScore < gameScore)) {
            return;
        } else {
            newScore = gameScore;
        }
        config.add(String.valueOf(consumerId), newScore);
    }

    /**
     * 领奖后更新子订单
     * @param option
     * @param ngameOrdersDto
     * @param prizeType
     */
    private void updateOrderAfterTakePrize(NgameOptionVO option, NgameOrdersDto ngameOrdersDto, String prizeType) {
        if (!Objects.equal(NgameOrdersDto.PrizeTypeLuckBag, prizeType) && !Objects.equal(NgameOrdersDto.PrizeTypeVirtual, prizeType)) {
            //非福袋及虚拟商品，更新订单为待领取状态
            int statusRet = remoteNgameOrdersConsumerService.updateManualOpenPrizeExchangeStatusToWait(
                    ngameOrdersDto.getConsumerId(),
                    ngameOrdersDto.getId(),
                    option.getItemId(),
                    null,
                    option.getId(),
                    option.getPrizeType(),
                    option.getName(),
                    option.getFacePrice(),
                    null);
            if (statusRet < 1) {
                throw new AccessActivityRuntimeException("更新订单待领奖失败");
            }
        }
    }

    /**
     * 虚拟商品自动发奖
     * @param option
     * @param ngameOrdersDto
     * @param key
     * @throws AccessActivityException
     */
    private void doVirtualAutoPrize(NgameOptionVO option, NgameOrdersDto ngameOrdersDto, ItemKeyDto key,String title
            , RequestParams requestParams,NgameParams gameParams,Long gameOrderExtraId) throws AccessActivityException {
        String facePrice = remoteDuibaItemGoodsService.getJsonValue(option.getItemId(), "itemJson").getResult();
        ngameOrdersDto.setPrizeFacePrice(facePrice);

        // 用户兑换记录
        ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
        record.setConsumerId(ngameOrdersDto.getConsumerId());
        record.setType(ConsumerExchangeRecordDto.TypeNgame);
        record.setRelationId(ngameOrdersDto.getId());
        record.setOrigin(ngameOrdersDto.getOperatingActivityId() * 100);
        record.setAppId(ngameOrdersDto.getAppId());

        record.setPrizeName(option.getName());
        record.setItemId(option.getItemId());
        record.setAppItemId(option.getAppItemId());

        //设置活动名称
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("trueActivityTitle", title);
        record.setJson(
                jsonObject.toJSONString()
        );

        ConsumerExchangeRecordDto recordDto = remoteConsumerExchangeRecordService.insert(record).getResult();
        exchangeNoticeService.increConsumerNotice(ngameOrdersDto.getConsumerId());
        //查询商品信息
        cn.com.duiba.order.center.api.dto.RequestParams params = new cn.com.duiba.order.center.api.dto.RequestParams();
        params.setIp(ngameOrdersDto.getIp());
        params.setConsumerId(ngameOrdersDto.getConsumerId());
        params.setCookies(requestParams.getCookies());
        params.setUserAgent(requestParams.getUserAgent());
        AppSimpleDto app = commonService.findAppDto(ngameOrdersDto.getAppId());
        TakePrizeVo takePrize =
                new TakePrizeVo(app, key,
                        BeanUtils.copy(ngameOrdersDto,OrdersVO.class),
                        ConsumerExchangeRecordDto.TypeNgame,params,
                        OrdersDto.ChargeModeNgame,
                        OrdersDto.RelationTypeNgame,OrdersDto.SubOrderTypeNgame);
        takePrize.setIsduiActivity(true);
        takePrize.setPrizeType(option.getPrizeType());
        takePrize.setFacePrice(ngameOrdersDto.getPrizeFacePrice());
        if (AppIdConstant.BIZ_POOL.contains(app.getId()) && StringUtils.isBlank(takePrize.getDeveloperBizId())) {
            String bizId = bizIdPoolService.selectOne(ngameOrdersDto.getConsumerId());
            if (StringUtils.isBlank(bizId)) {
                throw new  AccessActivityRuntimeException("领奖失败，bizId不能为空");
            }
            takePrize.setDeveloperBizId(bizId);
        }

        int ret = remoteNgameOrdersConsumerService.updateExchangeStatusToWaitOpenAndExtraId(ngameOrdersDto.getConsumerId()
                , ngameOrdersDto.getId(), gameParams.getGameDataJson(), gameOrderExtraId);
        if (ret < 1) {
            throw new AccessActivityRuntimeException("更新订单待开奖状态失败");
        }
        int statusRetWait = remoteNgameOrdersConsumerService.updateManualOpenPrizeExchangeStatusToWait(ngameOrdersDto.getConsumerId()
                , ngameOrdersDto.getId(), option.getItemId(), null, option.getId(), option.getPrizeType()
                , option.getName(), facePrice, null);
        if (statusRetWait < 1) {
            throw new AccessActivityRuntimeException("更新订单待领奖失败");
        }
        // 定制，活动1877（端外）、1919（端内未登）不自动发奖，需要手动开奖
        if(!NOT_AUTO_OPEN_PRIZE_NGAMES.contains(ngameOrdersDto.getDuibaNgameId())){
            takeVirtualPrize(takePrize, recordDto, null);
            //更改领奖状态
            ret = remoteNgameOrdersConsumerService.doTakeVirtualPrize(takePrize.getConsumerId(),takePrize.getOrderId());

            if (ret <= 0) {
                throw new AccessActivityRuntimeException(CommonConstants.MSG_SERVER_EXCEPTION_YJLJ);
            }
        }
    }

    /**
     * 优惠券自动发奖
     * @param ngameOrdersDto
     * @param developerId
     * @param gameOrderExtraId
     * @param key
     * @param gameOrdersExtraDto
     */
    private void doCouponAutoPrize(NgameOptionVO option, NgameOrdersDto ngameOrdersDto, Long developerId
            , Long gameOrderExtraId, ItemKeyDto key, NgameOrdersExtraDto gameOrdersExtraDto,String title
            , RequestParams requestParams) {
        OrdersDto order = ngameTakePrizeInnerService.takeAutoOpenCouponPrize(ngameOrdersDto
                , gameOrdersExtraDto, developerId, requestParams);
        if (order != null) {
            remoteNgameOrdersExtraService.updateOrderId(gameOrderExtraId, order.getId(), order.getOrderNum());
            ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
            record.setConsumerId(ngameOrdersDto.getConsumerId());
            record.setType(ConsumerExchangeRecordDto.TypeNgame);
            record.setOrderId(order.getId());
            record.setRelationId(ngameOrdersDto.getId());
            record.setAppId(order.getAppId());
            record.setOrigin(ngameOrdersDto.getOperatingActivityId() * 100);
            //设置活动名称
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("trueActivityTitle", title);
            record.setJson(
                    jsonObject.toJSONString()
            );

            DubboResult<GoodsCouponDto> goodsCouponDtoDubboResult = remoteItemCouponGoodsService.findCoupon(key, order.getCouponId());
            GoodsCouponDto couponDto = goodsCouponDtoDubboResult.getResult();
            if (!goodsCouponDtoDubboResult.isSuccess()) {
                // 只是查询过期时间，这里不抛异常以免流程异常结束
                log.warn("[Dubbo]{} failed, msg={}", "remoteItemCouponGoodsService.findCoupon", goodsCouponDtoDubboResult.getMsg());
            } else if (couponDto != null) {
                record.setOverDue(couponDto.getOverDue());
            }

            record.setPrizeName(option.getName());
            record.setItemId(option.getItemId());
            record.setAppItemId(option.getAppItemId());

            DubboResult<ConsumerExchangeRecordDto> dubboResult = remoteConsumerExchangeRecordService.insert(record);
            if (!dubboResult.isSuccess()) {
                throw new AccessActivityRuntimeException("插入兑换记录失败:" + dubboResult.getMsg());
            }
            // 优惠券中奖红点数+1
            final long xcons = ngameOrdersDto.getConsumerId();
            executorService.submit(() -> asyncIncreaseConsumerNotice(xcons));
        }
    }

    /**
     * 异步增加用户优惠券中奖红点数
     * @param consumerId
     */
    private void asyncIncreaseConsumerNotice(Long consumerId) {
        try {
            exchangeNoticeService.increConsumerNotice(consumerId);
        } catch (Exception e) {
            log.error("asyncIncreaseConsumerNotice", e);
        }
    }

    /**
     * 福袋开奖
     * @param ngameOrdersDto
     * @param gameDto
     * @param developerId
     * @param gameOrderExtraId
     */
    private void takeLuckBagPrize(NgameOrdersDto ngameOrdersDto, DuibaNgameDto gameDto, Long developerId, Long gameOrderExtraId, RequestParams requestParams){
        LuckBagRequest luckBagRequest = new LuckBagRequest();
        luckBagRequest.setOrderId(NgameOrdersDto.PREFIX_ORDER + ngameOrdersDto.getId());
        luckBagRequest.setOrderIdLong(ngameOrdersDto.getId());
        luckBagRequest.setConsumerId(ngameOrdersDto.getConsumerId());
        luckBagRequest.setOperatingActivityId(ngameOrdersDto.getOperatingActivityId());
        luckBagRequest.setAppId(ngameOrdersDto.getAppId());
        luckBagRequest.setIp(requestParams.getIp());
        luckBagRequest.setUa( requestParams.getOs());
        luckBagRequest.setOrderExtraId(gameOrderExtraId);
        luckBagRequest.setTag(ngameCommonService.findNgameTag(gameDto.getId()));
        luckBagRequest.setButtonType(EmbedConstant.ButtonType_Hdtool);
        luckBagRequest.setInfoType(EmbedConstant.InfoType_Ad);
        luckBagRequest.setInfo(String.valueOf(ngameOrdersDto.getId()));
        luckBagRequest.setOs(requestParams.getOs());
        luckBagRequest.setUserAgent(requestParams.getUserAgent());
        luckBagRequest.setDeveloperId(developerId);
        luckBagRequest.setUserType(LuckyBagUserTypeConstants.NORMAL_ACTIVITY_USER_TYPE);
        luckBagRequest.setActivityType(OperatingActivityDto.TypeDuibaNgame);
        luckBagRequest.setActivityId(ngameOrdersDto.getDuibaNgameId());
        luckBagRequest.setProxy(requestParams.isProxy());
        luckBagRequest.setSlotId(appSlotCacheService.getSlotIdWithDefaultType(ngameOrdersDto.getAppId(),gameDto.getChannelType()));
        luckBagService.obtainAdvert(luckBagRequest, new NgameLuckBugCallback());
    }

    /**
     * 新增即开奖游戏订单
     * @param gameOrder
     * @param option
     * @param couponId
     * @return
     */
    private NgameOrdersExtraDto insertNgameOrdersExtraDto(NgameOrdersDto gameOrder, NgameOptionVO option, Long couponId) {
        NgameOrdersExtraDto gameOrdersExtraDto = new NgameOrdersExtraDto(true);
        gameOrdersExtraDto.setGameOrderId(gameOrder.getId());
        gameOrdersExtraDto.setItemId(option.getItemId());
        gameOrdersExtraDto.setAppItemId(option.getAppItemId());
        gameOrdersExtraDto.setPrizeId(option.getId());
        gameOrdersExtraDto.setPrizeType(option.getPrizeType());
        gameOrdersExtraDto.setPrizeName(option.getName());
        gameOrdersExtraDto.setPrizeFacePrice(option.getFacePrice());
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, 1);
        Date overdueDate = cal.getTime();
        gameOrdersExtraDto.setPrizeOverdueDate(overdueDate);
        gameOrdersExtraDto.setCouponId(couponId);
        return remoteNgameOrdersExtraService.insert(gameOrdersExtraDto);
    }



    @Override
    public void doManualOpenPrize(OperatingActivityDto operatingActivityDO, DuibaNgameDto
            duibaNgameDto, NgameOrdersDto gameOrder, NgameParams gameParams) throws StatusException {
        try {

            // 更新订单待开奖状态
            int ret = remoteNgameOrdersConsumerService.updateExchangeStatusToWaitOpen(gameOrder.getConsumerId(), gameOrder.getId(), gameParams.getGameDataJson());
            if (ret < 1) {
                throw new AccessActivityRuntimeException("更新订单待开奖状态失败");
            }
        } finally {
            doOrderSuccess(gameOrder);
        }
    }

    private void doOrderSuccess(NgameOrdersDto gameOrder) {
        try {
            if (gameOrder.getOrderStatus() == NgameOrdersDto.StatusSuccess) {
                DuibaEventsDispatcher.get().dispatchEvent(new NgameOrdersEvent(NgameOrdersEvent.NgameOrdersEventType.OnOrderSuccess, gameOrder));
            }
        } catch (Exception e) {
            log.error("doOrderSuccess", e);
        }
    }

    public void notifyDeveloper(NgameOrdersDto gameOrder, String transfer) {
        try {
            if (gameOrder == null) {
                return;
            }
            if (gameOrder.getCredits() == null || gameOrder.getCredits() < 0 || AppIdConstant.NO_NOTIFY.contains(gameOrder.getAppId())) {
                return;
            }
            if (gameOrder.getCredits()==0 && !AppIdConstant.FREE_SUB_CREIDS.contains(gameOrder.getAppId())) {
                return;
            }
            if (gameOrder.getOrderStatus() == NgameOrdersDto.StatusCreate) {
                return;
            }

            ConsumerDto consumer = remoteConsumerService.find(gameOrder.getConsumerId());
            NotifyQueueDto notifyQueueDto = new NotifyQueueDto();
            notifyQueueDto.setAppId(gameOrder.getAppId());
            notifyQueueDto.setConsumerId(gameOrder.getConsumerId());
            notifyQueueDto.setDeveloperBizId(gameOrder.getDeveloperBizId());
            notifyQueueDto.setDuibaOrderNum(NgameOrdersDto.generateOrderNum(gameOrder.getId()));
            if (gameOrder.getError4developer() != null) {
                notifyQueueDto.setError4developer(gameOrder.getError4developer());
            }
            if (StringUtils.isNotBlank(transfer)) {
                notifyQueueDto.setTransfer(transfer);
            }
            notifyQueueDto.setNextTime(new Date());
            notifyQueueDto.setPartnerUserId(consumer.getPartnerUserId());
            notifyQueueDto.setRelationId(gameOrder.getId());
            notifyQueueDto.setRelationType(NotifyQueueDto.RT_NGAMEORDER);
            notifyQueueDto.setResult(NgameOrdersDto.StatusConsumeSuccess == gameOrder.getOrderStatus() ||NgameOrdersDto.StatusSuccess == gameOrder.getOrderStatus());
            notifyQueueDto.setTimes(0);
            remoteNotifyDeveloperService.notifyDeveloper(notifyQueueDto);
        } catch (Exception e) {
            log.error("notifyDeveloper error", e);
        }
    }

    @Override
    public void notifyOrderFail(Long consumerId, Long gameOrderId, String transfer) {
        try {
            NgameOrdersDto game = remoteNgameOrdersConsumerService.find(consumerId, gameOrderId);
            if (game == null) {
                return;
            }
            DuibaEventsDispatcher.get().dispatchEvent(new NgameOrdersEvent(NgameOrdersEvent.NgameOrdersEventType.OnOrderFail, game));
            notifyDeveloper(game, transfer);
        } catch (Exception e) {
            log.error("notifyOrderFail", e);
        }
    }

    /**
     * 游戏皮肤按分数升序排名，更新用户游戏记录，最高分（数值最小）
     * @param gameScore
     * @param gameOrder
     */
    @Override
    public void updateConsumerMinScore(long gameScore, NgameOrdersDto gameOrder,String title) {
        NgameConsumerRecordDto gameRecord = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(gameOrder.getConsumerId(), gameOrder.getDuibaNgameId());
        if (gameRecord == null) {
            NgameConsumerRecordDto maxScoreRecord = new NgameConsumerRecordDto(true);
            maxScoreRecord.setAppId(gameOrder.getAppId());
            maxScoreRecord.setConsumerId(gameOrder.getConsumerId());
            maxScoreRecord.setPartnerUserId(gameOrder.getPartnerUserId());
            maxScoreRecord.setOperatingActivityId(gameOrder.getOperatingActivityId());
            maxScoreRecord.setDuibaNgameId(gameOrder.getDuibaNgameId());
            maxScoreRecord.setMaxScore(gameScore);
            maxScoreRecord.setShareStatus(NgameConsumerRecordDto.share_status_0);
            maxScoreRecord.setTotalScore(maxScoreRecord.getMaxScore());
            maxScoreRecord.setIsGivePrize(false);
            maxScoreRecord.setGameOrdersId(gameOrder.getId());
            maxScoreRecord.setCheat(false);
            maxScoreRecord.setMaxScoreDate(new Date());
            maxScoreRecord = remoteNgameConsumerRecordService.insert(maxScoreRecord);
            //添加一条用户游戏记录
            addGameRecord(maxScoreRecord.getId(), gameOrder.getConsumerId(),title);
            this.updateRankByMaxScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), false, maxScoreRecord);
        } else if (gameScore < gameRecord.getMaxScore()) {
            gameRecord.setMaxScore(gameScore);
            remoteNgameConsumerRecordService.updateScore(gameRecord.getId(), gameScore, null, gameOrder.getId());
            this.updateRankByMaxScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), false, gameRecord);
        }
    }

    /**
     * 游戏皮肤按分数降序排名，更新用户游戏记录（最高分/总分）
     * @param gameScore
     * @param gameOrder
     * @param duibaNgameDto
     */
    @Override
    public void updateConsumerMaxScore(long gameScore, NgameOrdersDto gameOrder, DuibaNgameDto duibaNgameDto,String title) {
        NgameConsumerRecordDto gameRecord = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(gameOrder.getConsumerId(), gameOrder.getDuibaNgameId());
        boolean isTotal = duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_TOTAL_SCORE);

        if (gameRecord == null) {
            gameRecord = new NgameConsumerRecordDto(true);
            gameRecord.setAppId(gameOrder.getAppId());
            gameRecord.setConsumerId(gameOrder.getConsumerId());
            gameRecord.setPartnerUserId(gameOrder.getPartnerUserId());
            gameRecord.setOperatingActivityId(gameOrder.getOperatingActivityId());
            gameRecord.setDuibaNgameId(gameOrder.getDuibaNgameId());
            gameRecord.setMaxScore(gameScore);
            gameRecord.setShareStatus(NgameConsumerRecordDto.share_status_0);
            gameRecord.setTotalScore(gameScore);
            gameRecord.setIsGivePrize(false);
            gameRecord.setGameOrdersId(gameOrder.getId());
            gameRecord.setCheat(false);
            gameRecord.setMaxScoreDate(new Date());
            gameRecord = remoteNgameConsumerRecordService.insert(gameRecord);
            //添加一条用户游戏记录
            addGameRecord(gameRecord.getId(), gameOrder.getConsumerId(),title);
        } else {
            if (gameScore > gameRecord.getMaxScore()) {
                gameRecord.setMaxScore(gameScore);
                gameRecord.setMaxScoreDate(new Date());
            }
            if (isTotal) {
                gameRecord.setMaxScoreDate(new Date());
            }
            gameRecord.setTotalScore(gameRecord.getTotalScore() + gameScore);
            remoteNgameConsumerRecordService.updateMaxScoreAndTotal(gameRecord);
        }
        if (isTotal) {
            this.updateRankByTotalScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), true, gameRecord);
        } else {
            this.updateRankByMaxScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), true, gameRecord);
        }

    }

    /**
     * 游戏皮肤按分数降序排名，更新用户游戏记录（最高分/总分），记录表中记录当日最高分/总分，排行榜逻辑不变
     * @param gameScore
     * @param gameOrder
     * @param duibaNgameDto
     */
    @Override
    public void updateDailyConsumerMaxScore(long gameScore, NgameOrdersDto gameOrder, DuibaNgameDto duibaNgameDto, String title) {
        NgameConsumerRecordDto gameRecord = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(gameOrder.getConsumerId(), gameOrder.getDuibaNgameId());
        boolean isTotal = duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_TOTAL_SCORE);

        if (gameRecord == null) {
            gameRecord = new NgameConsumerRecordDto(true);
            gameRecord.setAppId(gameOrder.getAppId());
            gameRecord.setConsumerId(gameOrder.getConsumerId());
            gameRecord.setPartnerUserId(gameOrder.getPartnerUserId());
            gameRecord.setOperatingActivityId(gameOrder.getOperatingActivityId());
            gameRecord.setDuibaNgameId(gameOrder.getDuibaNgameId());
            gameRecord.setMaxScore(gameScore);
            gameRecord.setShareStatus(NgameConsumerRecordDto.share_status_0);
            gameRecord.setTotalScore(gameScore);
            gameRecord.setIsGivePrize(false);
            gameRecord.setGameOrdersId(gameOrder.getId());
            gameRecord.setCheat(false);
            gameRecord.setMaxScoreDate(new Date());
            gameRecord = remoteNgameConsumerRecordService.insert(gameRecord);
            //添加一条用户游戏记录
            addGameRecord(gameRecord.getId(), gameOrder.getConsumerId(),title);
        } else {
            if (DateUtil.isToday(gameRecord.getMaxScoreDate())) {
                if (gameScore > gameRecord.getMaxScore()) {
                    gameRecord.setMaxScore(gameScore);
                    gameRecord.setMaxScoreDate(new Date());
                }
                if (isTotal) {
                    gameRecord.setMaxScoreDate(new Date());
                }
            } else {
                gameRecord.setMaxScore(gameScore);
                gameRecord.setTotalScore(0L);
                gameRecord.setMaxScoreDate(new Date());
            }
            gameRecord.setTotalScore(gameRecord.getTotalScore() + gameScore);
            remoteNgameConsumerRecordService.updateMaxScoreAndTotal(gameRecord);
        }
        if (isTotal) {
            this.updateRankByTotalScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), true, gameRecord);
        } else {
            this.updateRankByMaxScore(gameOrder.getDuibaNgameId(), gameOrder.getConsumerId(), true, gameRecord);
        }

    }

    /**
     * 更新中奖信息
     */
    private void updateConsumerOptions(NgameOrdersDto gameOrder, NgameOptionVO optionVO) {
        NgameConsumerRecordDto gameRecord = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(gameOrder.getConsumerId(), gameOrder.getDuibaNgameId());
        if (gameRecord == null
                || optionVO == null
                || Objects.equal(optionVO.getPrizeType(), ItemDto.LuckyBag)) {
            return;
        }
        String prizeIds = setAutoPrizeIds(gameRecord, optionVO);
        remoteNgameConsumerRecordService.updateAutoOpenPrizeId(gameRecord.getId(), prizeIds);
    }

    /**
     * 添加一条游戏记录
     *
     * @param gameRecordId
     * @param consumerId
     */
    private void addGameRecord(Long gameRecordId, Long consumerId,String title) {
        ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
        record.setConsumerId(consumerId);
        record.setType(ConsumerExchangeRecordDto.TypeNgameRecord);
        record.setRelationId(gameRecordId);
        record.setOverDue(null);//游戏记录无需过期时间
        Date now = new Date();
        record.setGmtCreate(now);
        record.setGmtModified(now);
        //设置活动名称
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("trueActivityTitle", title);
        record.setJson(
                jsonObject.toJSONString()
        );

        DubboResult<ConsumerExchangeRecordDto> dubboResult = remoteConsumerExchangeRecordService.insert(record);
        if (!dubboResult.isSuccess()) {
            throw new AccessActivityRuntimeException("插入兑换记录失败:" + dubboResult.getMsg());
        }
        exchangeNoticeService.increConsumerNotice(consumerId);
    }

    /**
     * 通过最好成绩排名
     * @param gameId
     * @param consumerId
     * @param isDesc
     * @param gameRecord
     */
    private void updateRankByMaxScore(long gameId, long consumerId, boolean isDesc, NgameConsumerRecordDto gameRecord) {
        try {
            if (gameRecord != null) {
                NgameRankConfig rank = ngameRankService.getTotalRankConfig(gameId, isDesc);
                rank.add(String.valueOf(consumerId), gameRecord.getMaxScore());
            }
        } catch (Exception e) {
            log.error("updateRank", e);
        }
    }

    /**
     * 通过总分成绩排名
     * @param gameId
     * @param consumerId
     * @param isDesc
     * @param gameRecord
     */
    private void updateRankByTotalScore(long gameId, long consumerId, boolean isDesc, NgameConsumerRecordDto gameRecord) {
        try {
            if (gameRecord != null) {
                NgameRankConfig rank = ngameRankService.getTotalRankConfig(gameId, isDesc);
                rank.add(String.valueOf(consumerId), gameRecord.getTotalScore());
            }
        } catch (Exception e) {
            log.error("updateRank", e);
        }
    }

    private String setAutoPrizeIds(NgameConsumerRecordDto gameRecord, NgameOptionVO optionVO) {
        String prizeIds = gameRecord.getAutoOpenPrizeId();
        if (optionVO != null && StringUtils.isNotBlank(gameRecord.getAutoOpenPrizeId())) {
            prizeIds = gameRecord.getAutoOpenPrizeId() + "," + optionVO.getId();
        } else if (optionVO != null && optionVO.getId() != null) {
            prizeIds = optionVO.getId().toString();
        }
        return prizeIds;
    }

    @Override
    public void doNgameOpenPrize(List<ManualWinParamDto> wins) {
        for (ManualWinParamDto win : wins) {
            try {
                NgameOrdersDto gameOrder = getOrGenerateNgameOrder(win.getCid(), win.getNgameOrderId());
                DuibaNgameOptionsDto optionsDto = remoteDuibaNgameOptionsService.find(win.getOptionId());
                NgameOptionVO option = new NgameOptionVO(optionsDto);
                NgameOrderPlugin.NgamePluginContext context = new NgameOrderPlugin.NgamePluginContext();
                ItemKeyDto key = null;
                // 扣奖项库存，商品库存，商品定向库存，商品预分配库存
                try {
                    if (ITEM_STOCK_TYPE.contains(option.getPrizeType()) ) {
                        key = commonService.findItemKeyDto(option.getAppItemId(), option.getItemId(), gameOrder.getAppId());
                        context.setAttribute(NgameOrderPlugin.NgamePluginContext.ITEM_KEY, key);
                    }
                    DuibaPluginEventsDispatcher.get().triggerBeforeNgameStockComplete(gameOrder, option, context);
                    Long couponId = (Long) context.getAttribute(NgameOrderPlugin.NgamePluginContext.COUPONID);

                    gameOrder.setCouponId(couponId);

                    // 5.更新订单奖项
                    String facePrice = option.getFacePrice();
                    //判断是否是兑吧活动工具的虚拟商品
                    if (Objects.equal(option.getPrizeType(), HdtoolOrdersDto.PrizeTypeVirtual)) {
                        facePrice = remoteDuibaItemGoodsService.getJsonValue(option.getItemId(), "itemJson").getResult();
                    }
                    //判断是否是集卡
                    if(HdtoolOrdersDto.PrizeTypeCollectGoods.equals(option.getPrizeType())){
                        activityKvService.createCollectGoodsInfo(option.getItemId(),gameOrder.getConsumerId());
                    }
                    gameOrder.setPrizeFacePrice(facePrice);
                    int statusRet = remoteNgameOrdersConsumerService.updateManualOpenPrizeExchangeStatusToWait(win.getCid()
                            , gameOrder.getId(), option.getItemId(), null, option.getId()
                            , option.getPrizeType(), option.getName(), facePrice, couponId);
                    if (statusRet < 1) {
                        throw new AccessActivityRuntimeException("更新订单待领奖失败");
                    }
                } catch (Exception e) {
                    // 返还奖项库存，商品库存，商品定向库存，商品预分配库存
                    log.info("游戏手动开奖失败，库存回滚奖项id={}", option.getId());
                    DuibaPluginEventsDispatcher.get().triggerNgameStockCompleteException(gameOrder, option, e, context);
                }
                // 用户兑换记录
                createConsumerRecordForManualPrize(gameOrder, option, key);
            } catch (Exception e) {
                log.warn("failed to open prize", e);
            }
        }
    }

    /**
     * 手动开奖生成兑换记录
     * @param gameOrder
     * @param option
     * @param key
     */
    private void createConsumerRecordForManualPrize(NgameOrdersDto gameOrder, NgameOptionVO option, ItemKeyDto key) {
        if (!option.getPrizeType().equals(NgameOrdersDto.PrizeTypeThanks)&&!option.getPrizeType().equals(NgameOrdersDto.PrizeTypeCollectGoods)) {
            ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
            record.setConsumerId(gameOrder.getConsumerId());
            record.setType(ConsumerExchangeRecordDto.TypeNgame);
            record.setRelationId(gameOrder.getId());
            record.setOrigin(gameOrder.getOperatingActivityId() * 100);
            record.setAppId(gameOrder.getAppId());
            if (ItemDto.TypeCoupon.equals(option.getPrizeType())) {
                GoodsCouponDto goodsCouponDto = remoteItemCouponGoodsService.findCoupon(key, gameOrder.getCouponId()).getResult();
                if (goodsCouponDto != null) {
                    record.setOverDue(goodsCouponDto.getOverDue());
                }
            }
            //通过id查询活动
            OperatingActivityDto operatingDto = activityCacheService.getOperatingDto(gameOrder.getOperatingActivityId());
            if(operatingDto != null){
                //设置活动名称
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("trueActivityTitle", operatingDto.getTitle());
                record.setJson(
                        jsonObject.toJSONString()
                );
            }
            record.setPrizeName(option.getName());
            record.setItemId(option.getItemId());
            record.setAppItemId(option.getAppItemId());
            remoteConsumerExchangeRecordService.insert(record);
            exchangeNoticeService.increConsumerNotice(gameOrder.getConsumerId());
        }
    }

    @Override
    public List<NgameOptionVO> getAutoOpenPrizeNewRule(DuibaNgameDto duibaNgameDto,NgameOrdersDto gameOrder, OperatingActivityDto operatingActivity) throws StatusException {
        if (!ngameService.isDuibaNgame(operatingActivity)) {
            throw new AccessActivityRuntimeException("不是兑吧活动");
        }
        Long gameConfigDuibaId = operatingActivity.getActivityId();
        //本地缓存奖项
        List<DuibaNgameOptionsDto> autoOpenOptions = Lists.newArrayList();
        try {
            autoOpenOptions = NGAME_AUTO_OPTION.get(gameConfigDuibaId, new Callable<Optional<List<DuibaNgameOptionsDto>>>() {
                @Override
                public Optional<List<DuibaNgameOptionsDto>> call() throws Exception {
                    return  Optional.ofNullable(remoteDuibaNgameOptionsService.findByAutoOpenDesc(gameConfigDuibaId, true));
                }
            }).orElse(Lists.newArrayList());
        } catch (Exception e) {
            log.error("获取游戏自动开奖本地缓存信息失败",e);
        }

        if(CollectionUtils.isEmpty(autoOpenOptions)) {
            return null;
        }
        List<NgameOptionVO> options = Lists.newArrayList();
        for (DuibaNgameOptionsDto option : autoOpenOptions) {
            if (StringUtils.isBlank(option.getScoreArea()) || isInArea(option, gameOrder.getScore())) {
                options.add(new NgameOptionVO(option));
            }
        }
        if (CollectionUtils.isEmpty(options)) {
            return null;
        }
        //查询中奖纪录
        NgameConsumerRecordDto gcr = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(gameOrder.getConsumerId(), gameConfigDuibaId);
        //如果用户第一次参与，或者没有中奖纪录。直接返回奖项列表
        if (gcr == null || StringUtils.isBlank(gcr.getAutoOpenPrizeId())) {
            return options;
        }
        List<String> prizeIds = Arrays.asList(gcr.getAutoOpenPrizeId().split(","));
        if (CollectionUtils.isEmpty(prizeIds)) {
            return options;
        }
        ImmutableListMultimap<Long, String> map = Multimaps.index(prizeIds, (s) -> NumberUtils.isNumber(s) ? Long.valueOf(s) : 0L);
        //校验中奖限制
        checkOptionLimit( duibaNgameDto, options, map,gcr.getConsumerId());
        return options;
    }

    /**
     * 校验中奖限制
     * @param options
     * @param map
     */
    private void checkOptionLimit(DuibaNgameDto duibaNgameDto,List<NgameOptionVO> options, ImmutableListMultimap<Long, String> map,Long consumerId) {

        Set<Long> idSet=Sets.newHashSet();
        boolean winOptionByNgameMutiCheck=canWinOptionByNgameMutiCheck(duibaNgameDto,idSet,map);

        Iterator<NgameOptionVO> iterator = options.iterator();
        while (iterator.hasNext()) {
            NgameOptionVO option = iterator.next();
            List<String> time = map.get(option.getId());

            //多奖项验证
            if(!winOptionByNgameMutiCheck&&idSet.contains(option.getId())){
                iterator.remove();
                continue;
            }

            if (CollectionUtils.isEmpty(time)) {
                continue;
            }
            if((option.getLimitTimeType() == null
                    || option.getLimitTimeType() == PrizeLimitTimeTypeEnum.FOREVER.getValue())
                    && option.getOptionLimitCount() != null
                    && time.size() >= option.getOptionLimitCount()){
                iterator.remove();
            }
        }
        removeOption(consumerId, options);
    }

    private void removeOption(Long consumerId, List<NgameOptionVO> options) {
        Iterator<NgameOptionVO> iterator = options.iterator();
        while (iterator.hasNext()) {
            NgameOptionVO option = iterator.next();
            Integer limitTimeType = option.getLimitTimeType();
            Long time = null;
            if(limitTimeType != null && limitTimeType == PrizeLimitTimeTypeEnum.EVERY_DAY.getValue()){
                time = redisAtomicClient.getLong(ngameCacheKeyService.generateEveryDayOptionLimitKey(consumerId,option.getId()));
            }
            else if(limitTimeType != null && limitTimeType == PrizeLimitTimeTypeEnum.CUSTOM.getValue())
            {
                time = redisAtomicClient.getLong(ngameCacheKeyService.generateCustomOptionLimitKey(consumerId,option.getId()));
            }
            if(time == null){
                time = 0L;
            }
            if(option.getOptionLimitCount() != null && time >= option.getOptionLimitCount()){
                iterator.remove();
            }
        }
    }


    private int getMutiPrizeTotalTimes(ImmutableListMultimap<Long, String> map,Set<Long> idSet){
        int totalTimes=0;
        for(Long id:idSet){
            totalTimes+= map.get(id).size();
        }
        return totalTimes;
    }
    private boolean canWinOptionByNgameMutiCheck(DuibaNgameDto duibaNgameDto, Set<Long> idSet,ImmutableListMultimap<Long, String> map){
        if ( duibaNgameDto == null || !duibaNgameDto.isOpenSwitch(DuibaNgameDto.SWITCHES_NEW_OPEN_MUTLI_PRIZE_LIMIT)) {
            return true;//不参加验证
        }
        String extJson = duibaNgameDto.getExtendJson();
        if (org.apache.commons.lang3.StringUtils.isBlank(extJson)) {
            return true;
        }

        JSONObject jsonObject = JSONObject.parseObject(extJson);

        String limitPrizeIds = jsonObject.getString(DuibaNgameDto.EXT_JSON_KEY_MUTLI_PRIZE_LIMIT_IDS);
        List<Long> ids = JSONObject.parseArray(limitPrizeIds, Long.class);
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        //赋值，多奖项奖品id
        idSet.addAll(ids);

        Integer mutliPrizeLimitCount = jsonObject.getInteger(DuibaNgameDto.EXT_JSON_KEY_MUTLI_PRIZE_LIMIT_COUNT);
        if (mutliPrizeLimitCount == null) {
            return true;
        }else if(getMutiPrizeTotalTimes(map,idSet)>=mutliPrizeLimitCount){
            return false;
        }
        return true;
    }

    @Override
    public NgameOptionVO getAutoOpenOptionByRate(List<NgameOptionVO> options) {
        if (CollectionUtils.isEmpty(options)) {
            return null;
        }
        BigDecimal temp = BigDecimal.ZERO;
        // 中奖的随机数(0 到 10000)之间的随机数
        BigDecimal randomNumber = new BigDecimal((int)(Math.random() * 10000));
        // 中奖奖项
        for (NgameOptionVO option : options) {
            temp = temp.add(new BigDecimal(option.getRate()));
            if (randomNumber.compareTo(temp) < 0) {
                return option;
            }
        }
        return null;
    }

    @Override
    public NgameOptionVO getRandomLuckBag(List<NgameOptionVO> luckOptions) {
        if (CollectionUtils.isEmpty(luckOptions)) {
            return null;
        }
        int index = (int) (Math.random() * luckOptions.size());
        return luckOptions.get(index);
    }

    /**
     * 判断游戏分数能否获取当前奖项
     */
    private boolean isInArea(DuibaNgameOptionsDto option, Long score) {
        try {
            String scoreArea = option.getScoreArea();
            String [] area = scoreArea.split(",");
            if (Long.valueOf(area[0]) <= score && score <= Long.valueOf(area[1])) {
                return true;
            }
        } catch (Exception e) {
            //若抛出异常，当次校验返回false，不影响其他奖项正常中奖
            log.error("判断奖项分数区间出错,奖项id=[{}],错误信息:", option.getId(), e);
        }
        return false;
    }

    /**
     * 获取或创建游戏子订单
     * 1.原有逻辑为大奖会更新获得最高分的订单
     * 2.在即开奖支持所有奖品之后，如果最高分订单获得过其他奖品，会导致手动开奖奖品无法更新成功
     * 3.解决方案：若订单未有奖品信息，则使用原有订单；若当前订单已经获取过奖品，则重新创建一条带有标识符的订单
     * 4.标识符：ngame_orders.develop_biz_id = duiba_manual_open_ngame
     */
    private NgameOrdersDto getOrGenerateNgameOrder(Long consumerId, Long orderId) {
        NgameOrdersDto ordersDto = remoteNgameOrdersConsumerService.find(consumerId, orderId);
        /*
         *  同时符合以下三个条件，返回原订单进行开奖
         *  1.订单状态为完成订
         *  2.单领奖状态为待开奖
         *  3.奖品类型为空
         * */
        if (Objects.equal(ordersDto.getOrderStatus(), NgameOrdersDto.StatusSuccess)
                && Objects.equal(ordersDto.getExchangeStatus(), NgameOrdersDto.ExchangeStatusWaitOpen)
                && StringUtils.isEmpty(ordersDto.getPrizeType())) {
            return ordersDto;
        }
        //复制并生成一个特殊订单
        NgameOrdersDto order = new NgameOrdersDto(true);
        order.setAppId(ordersDto.getAppId());
        order.setConsumerId(ordersDto.getConsumerId());
        order.setPartnerUserId(ordersDto.getPartnerUserId());
        order.setOperatingActivityId(ordersDto.getOperatingActivityId());
        order.setDuibaNgameId(ordersDto.getDuibaNgameId());
        order.setCredits(0L);
        order.setOrderStatus(NgameOrdersDto.StatusSuccess);
        order.setExchangeStatus(NgameOrdersDto.ExchangeStatusWaitOpen);
        order.setDeveloperBizId(NgameOrdersDto.BIZ_ID_FOR_MANAUAL_OPEN);
        order.setIp(ordersDto.getIp());
        NgameOrdersDto resultOrder = remoteNgameOrdersConsumerService.insert(order);
        log.info("生成特殊表示游戏子订单，订单id={}", resultOrder.getId());
        //更新nGame用户中奖记录表
        NgameConsumerRecordDto record = remoteNgameConsumerRecordService.findRecordByConIdAndNgameId(consumerId, ordersDto.getDuibaNgameId());
        record.setGameOrdersId(resultOrder.getId());
        remoteNgameConsumerRecordService.updateGameOrdersId(record);
        return resultOrder;
    }
}
