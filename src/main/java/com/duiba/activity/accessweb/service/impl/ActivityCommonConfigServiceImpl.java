package com.duiba.activity.accessweb.service.impl;

import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.service.ActivityCommonConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Created by hww on 2018/1/13
 */
@Service
public class ActivityCommonConfigServiceImpl implements ActivityCommonConfigService {

    @Resource(name = "redisTemplate")
    private AdvancedCacheClient redisClient;

    @Override
    public String getRole(Integer type, Long id, Long consumerId) {
        if (null == type || id == null || consumerId == null) {
            return null;
        }
        String key = RedisKeyFactory.K063.toString() + type + id + consumerId;
        return redisClient.get(key);
    }

    @Override
    public void setRole(Integer type, Long id, Long consumerId, String role) {
        if (null == type || id == null || consumerId == null || StringUtils.isBlank(role)) {
            return;
        }
        String key = RedisKeyFactory.K063.toString() + type + id + consumerId;
        redisClient.set(key, role, 30, TimeUnit.DAYS);
    }
}
