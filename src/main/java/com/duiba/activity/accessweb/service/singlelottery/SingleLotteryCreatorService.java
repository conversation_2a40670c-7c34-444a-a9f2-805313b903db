/**
 * Project Name:activity-access-web
 * File Name:SingleLotteryCreatorService.java
 * Package Name:com.duiba.activity.accessweb.service.singlelottery
 * Date:2017年1月23日下午1:39:20
 * Copyright (c) 2017, duiba.com.cn All Rights Reserved.
 *
*/

package com.duiba.activity.accessweb.service.singlelottery;

import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.exception.StatusException;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * ClassName:SingleLotteryCreatorService <br/>
 * Date:     2017年1月23日 下午1:39:20 <br/>
 * <AUTHOR>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public interface SingleLotteryCreatorService {

    /**
     * 创建单品抽奖订单
     * createOrder:(这里用一句话描述这个方法的作用). <br/>
     * 
     * <AUTHOR>
     * @param consumer
     * @param operatingActivityId
     * @param ip
     * @param request
     * @return
     * @throws Exception
     * @since JDK 1.6
     */
    SingleLotteryOrderDto createOrder(ConsumerDto consumer, long operatingActivityId, String ip, HttpServletRequest request) throws StatusException;
    
    /**
     * 扣积分,抽奖
     * asyncConsumerCredits:(这里用一句话描述这个方法的作用). <br/>
     * 
     * <AUTHOR>
     * @param consumerDO
     * @param app
     * @param order
     * @param map
     * @since JDK 1.6
     */
    void asyncConsumerCredits(ConsumerDto consumerDO, AppSimpleDto app, SingleLotteryOrderDto order, Map<String, String> map);
    
    /**
     * 
     * onCreditsSuccess:(这里用一句话描述这个方法的作用). <br/>
     *
     * <AUTHOR>
     * @param order
     * @since JDK 1.6
     */
    void onCreditsSuccess(Map<String,String> paramsMap
            ,String consumerId,String relationId, SingleLotteryOrderDto order);
}

