package com.duiba.activity.accessweb.service.wxcoupon;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.credits.sdk.AssembleTool;
import cn.com.duiba.wechat.server.api.dto.SessionBeanDto;
import cn.com.duiba.wechat.server.api.remoteservice.RemoteWeChatSelfMiniAppService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.config.WeChatAutoLoginConfig;
import com.duiba.activity.accessweb.tool.HttpUtil;
import com.duiba.activity.accessweb.tool.WxFavorCouponUtils;
import com.duiba.activity.accessweb.vo.wxcoupon.EncryptedDataCustomParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxAutoLoginService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WxAutoLoginService.class);

    @Autowired
    private WeChatAutoLoginConfig weChatAutoLoginConfig;

    @Autowired
    private RemoteWeChatSelfMiniAppService remoteWeChatSelfMiniAppService;

    public String generateCustomizationAutoLoginUrl(String encryptedData, String code) throws Exception {
        encryptedData = URLDecoder.decode(encryptedData, "utf-8");
        String jsonStr = WxFavorCouponUtils.decryptMsg(encryptedData, weChatAutoLoginConfig.getAesKey());
        if (StringUtils.isBlank(jsonStr)) {
            throw new BizException("授权失败");
        }
        EncryptedDataCustomParam dataParam = JSON.parseObject(jsonStr, EncryptedDataCustomParam.class);

        // 获取openId
        String openId;
        if (code.startsWith("lyj")) {
            openId = code;
        } else {
            openId = getOpenId(code);
        }

        // 获取orderId
        Map<String, String> map = new HashMap<>();
        map.put("openId", openId);
        map.put("orderId", dataParam.getOrderId());

        String sign = WxFavorCouponUtils.encryptMsg(JSONObject.toJSONString(map), weChatAutoLoginConfig.getAesKey());
        Map<String, String> requestParam = new HashMap<>();
        requestParam.put("sign", sign);

        // 从星速台生成免登url
        String projectUrl = weChatAutoLoginConfig.getProjectUrl();
        if (StringUtils.isNotBlank(dataParam.getProjectUrl())){
            projectUrl = dataParam.getProjectUrl();
        }
        String url = AssembleTool.assembleUrl(projectUrl, requestParam);
        String response = HttpUtil.sendGet(url);

        JSONObject result = JSONObject.parseObject(response, JSONObject.class);
        Boolean success = result.getBoolean("success");
        if (!success) {
            String message = result.getString("message");
            throw new BizException(message);
        }
        return result.getString("data");
    }

    public String getOpenId(String code) throws Exception {
        SessionBeanDto sessionBeanDto = remoteWeChatSelfMiniAppService.jscode2session(weChatAutoLoginConfig.getMiniAppid(), weChatAutoLoginConfig.getMiniAppSecret(), code);
        LOGGER.info("微信立减金 获取用户openid code={} sessionBeanDto={}", code, JSON.toJSONString(sessionBeanDto));
        if (sessionBeanDto == null || StringUtils.isBlank(sessionBeanDto.getOpenid())) {
            LOGGER.warn("微信立减金 获取用户openid失败 code={}", code);
            throw new BizException("授权失败");
        }
        return sessionBeanDto.getOpenid();
    }

}
