package com.duiba.activity.accessweb.service.mobai.impl;

import cn.com.duiba.activity.common.center.api.params.UserInviteParam;
import cn.com.duiba.wolf.utils.DateUtils;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.activity.sharecode.ShareCodeService;
import com.duiba.activity.accessweb.service.mobai.MobaiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @description: 摩拜服务
 * @author: zhengwei
 * @date: 2018-09-12 10:32
 */
@Service
public class MobaiServiceImpl implements MobaiService {
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ShareCodeService shareCodeService;

    /**
     * 结束时间
     */
    private static final Date end = DateUtils.getDayDate("2018-10-02");

    /**
     * 输入分享码领奖前校验
     *
     * @param userInviteParam
     * @return
     */
    @Override
    public Result checkShareCode(UserInviteParam userInviteParam) {
        //判断活动是否结束
        if (System.currentTimeMillis() > end.getTime()) {
            return ResultBuilder.fail(ResultCode.C100102);
        }
        //校验分享码
        Result result = shareCodeService.checkShareCode(userInviteParam);
        if (!result.getSuccess()) {
            return result;
        }
        //判断该分享码是否已兑换过
        String key = buildKey(userInviteParam);
        if (redisTemplate.opsForSet().isMember(key, userInviteParam.getShareCode())) {
            return ResultBuilder.fail(ResultCode.C100004);
        }
        //分享码校验通过,加入缓存
        redisTemplate.opsForSet().add(key, userInviteParam.getShareCode());
        redisTemplate.expireAt(key, end);
        return result;
    }

    /**
     * 构建key
     *
     * @param param
     * @return
     */
    private String buildKey(UserInviteParam param) {
        return new StringBuilder().append(RedisKeyFactory.K800.toString())
                .append(param.getAppId())
                .append("_")
                .append(param.getActivityId())
                .append("_")
                .append(param.getInviteConsumerId()).toString();
    }
}
