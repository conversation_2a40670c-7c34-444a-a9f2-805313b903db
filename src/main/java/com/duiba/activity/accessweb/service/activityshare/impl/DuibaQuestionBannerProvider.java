package com.duiba.activity.accessweb.service.activityshare.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerDto;
import cn.com.duiba.activity.center.api.remoteservice.game.RemoteDuibaQuestionAnswerServiceNew;
import com.duiba.activity.accessweb.service.activityshare.ActivityBannerProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zzy on 2017/9/21.
 */
@Service
public class DuibaQuestionBannerProvider implements ActivityBannerProvider {

    @Autowired
    private RemoteDuibaQuestionAnswerServiceNew remoteDuibaQuestionAnswerServiceNew;

    @Override
    public boolean isHdtool() {
        return false;
    }

    @Override
    public int getActivityType() {
        return OperatingActivityDto.TypeActivityAccessQuestionAnswer;
    }

    @Override
    public String getBannerImg(Long activityId) {
        DuibaQuestionAnswerDto activity = remoteDuibaQuestionAnswerServiceNew.find(activityId);
        if (null == activity) {
            return "";
        }
        return activity.getBanner();
    }
}
