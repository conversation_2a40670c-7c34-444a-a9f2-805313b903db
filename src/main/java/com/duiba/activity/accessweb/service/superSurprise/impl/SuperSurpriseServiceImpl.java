package com.duiba.activity.accessweb.service.superSurprise.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityOptionsDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.RankListProvidePrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseConfigDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseJoinRecordDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseJoinRecordQuery;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRankListOptionDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRebirthQueryDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseRebirthRecordDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityOptionsService;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.superSurprise.RemoteSuperSurpriseService;
import cn.com.duiba.activity.common.center.api.dto.visittime.OpActivityVisitTimesDto;
import cn.com.duiba.activity.common.center.api.remoteservice.visittime.RemoteOpActivityVisitTimesService;
import cn.com.duiba.api.bo.mq.MqDataParam;
import cn.com.duiba.api.enums.ActivityOrderStutsEnum;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.api.enums.RedisKeySpace;
import cn.com.duiba.api.tools.DuibaHdtoolUtil;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.app.AppExtraLargeFieldDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.plugin.center.api.dto.ActivityOrderDto;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.remoteservice.plugin.RemoteCreditsPluginService;
import cn.com.duiba.plugin.center.api.request.credits.SubCreditsRequest;
import cn.com.duiba.plugin.center.api.response.CreditsPluginResponse;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.entity.Pair;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.config.SuperSupriseConfig;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.enums.superSurprise.LimitScope;
import com.duiba.activity.accessweb.enums.superSurprise.RankListProvidePrizeStatus;
import com.duiba.activity.accessweb.enums.superSurprise.RebirthResult;
import com.duiba.activity.accessweb.enums.superSurprise.RebirthStatus;
import com.duiba.activity.accessweb.enums.superSurprise.RebirthType;
import com.duiba.activity.accessweb.enums.superSurprise.RecordCreditsStatus;
import com.duiba.activity.accessweb.enums.superSurprise.RecordExchangeStatus;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.mq.RocketMqService;
import com.duiba.activity.accessweb.service.market.MarketTempService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.superSurprise.SuperSurpriseService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.MD5;
import com.duiba.activity.accessweb.util.superSurprise.RsaUtil;
import com.duiba.activity.accessweb.vo.changying.GiftRecordRequest;
import com.duiba.activity.accessweb.vo.superSurprise.GameResultVO;
import com.duiba.activity.accessweb.vo.superSurprise.RankInfoVO;
import com.duiba.activity.accessweb.vo.superSurprise.RankSimpleInfo;
import com.duiba.activity.accessweb.vo.superSurprise.RebirthResultVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseInfoVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseJoinRecordStatusVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseJoinResultVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseRankListProvideRecordVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseRebirthResultVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseRebirthVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseScoreSubmitVO;
import com.duiba.activity.accessweb.vo.superSurprise.SuperSurpriseSimplePrizeVO;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by zhangyongjie on 2022/3/24 5:00 下午
 */
@Service
public class SuperSurpriseServiceImpl implements SuperSurpriseService {
    private static final Logger logger = LoggerFactory.getLogger(SuperSurpriseServiceImpl.class);


    private com.github.benmanes.caffeine.cache.Cache<Long, List<SuperSurpriseRankListDto>> rankCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(20)
            .maximumSize(200)
            .build();

    private com.github.benmanes.caffeine.cache.Cache<String, Integer> consumerRankCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(200)
            .maximumSize(1000)
            .build();

    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;

    @Autowired
    private RemoteSuperSurpriseService remoteSuperSurpriseService;

    @Autowired
    private MarketTempService marketTempService;

    @Autowired
    private RemoteConsumerService remoteConsumerService;

    @Autowired
    private RemoteOperatingActivityOptionsService remoteOperatingActivityOptionsService;

    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;

    @Autowired
    private RemoteCreditsPluginService remoteCreditsPluginService;

    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private RiskService riskService;

    @Autowired
    private RocketMqMessageTopic rocketMqMessageTopic;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String,Long>  redisTemplate;

    @Autowired
    private RocketMqService rocketMqService;

    @Autowired
    private RemoteOpActivityVisitTimesService remoteOpActivityVisitTimesService;

    @Autowired
    private RemoteAppService remoteAppService;

    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;

    @Autowired
    private SuperSupriseConfig superSupriseConfig;

    @Autowired
    private DeveloperCacheService developerCacheService;


    private BiConsumer<SuperSurpriseInfoVO,SuperSurpriseConfigDto> LIMIT_FILLER = (info,config) ->{
        SuperSurpriseJoinRecordQuery query = new SuperSurpriseJoinRecordQuery();
        query.setActivityId(info.getId());
        query.setAppId(info.getAppId());
        query.setConsumerId(RequestLocal.getCid());
        if(info.getFreeLimitTimes() != null){
            if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(info.getFreeLimitTimesScope()))){
                Date startTime = DateUtils.getDayStartTime(new Date());
                query.setStartTime(startTime);
            }

            query.setFree(0);
            Integer usedFreeTimes = remoteSuperSurpriseService.countUserJoinTimes(query);
            info.setUsedFreeJoinTimes(usedFreeTimes);
            int leftFreeTimes = config.getFreeLimitTimes() - usedFreeTimes;
            info.setRemainFreeJoinTimes(leftFreeTimes >= 0?leftFreeTimes:0);
        }

        if(info.getLimitTimes() != null){
            if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(info.getLimitTimesScope()))){
                Date startTime = DateUtils.getDayStartTime(new Date());
                query.setStartTime(startTime);
            }
            query.setFree(null);
            query.setStatusList(Lists.newArrayList(RecordCreditsStatus.NONE.getCode(),RecordCreditsStatus.SUCCESS.getCode()));
            Integer usedTimes = remoteSuperSurpriseService.countUserJoinTimes(query);
            info.setUsedJoinTimes(usedTimes);
            int leftJoinTimes = config.getLimitTimes() - usedTimes;
            info.setRemainJoinTimes(leftJoinTimes >= 0?leftJoinTimes:0);
        }


    };


    private Consumer<SuperSurpriseInfoVO> PRIZE_INFO_HANDLER = info ->{
        if(!BooleanUtils.isTrue(info.getEnableRankListPrize())){
            return;
        }
        OperatingActivityDto operatingActivity = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(info.getAppId(), info.getId(), OperatingActivityDto.TypeSuperSurprise);

        List<SuperSurpriseRankListOptionDto> optionDtos = remoteSuperSurpriseService.findByOperatingActivityId(operatingActivity.getId());

        List<OperatingActivityOptionsDto> realOption = remoteOperatingActivityOptionsService.findByIds(optionDtos.stream().map(SuperSurpriseRankListOptionDto::getOptionId).collect(Collectors.toList()));
        info.setRankPrizeInfo(realOption.stream().map(x ->{
            SuperSurpriseSimplePrizeVO superSurpriseSimplePrizeVO = new SuperSurpriseSimplePrizeVO();
            superSurpriseSimplePrizeVO.setPrizeName(x.getName());
            superSurpriseSimplePrizeVO.setPrizeImage(x.getLogo());
            return  superSurpriseSimplePrizeVO;
        }).limit(4).collect(Collectors.toList()));

    };


    /**
     * 排行榜发奖
     */
    private BiConsumer<SuperSurpriseConfigDto,OperatingActivityDto> RANK_LIST_PRIZE_DEALER = (configDto, op) -> {


        Long consumerId = RequestLocal.getCid();
        try(RedisLock lock = redisAtomicClient.getLock(getRankListPrizeOpenKey(configDto.getId(),consumerId),5)){
            if(lock == null){
                return;
            }
            Date now = new Date();
            if(!BooleanUtils.isTrue(configDto.getEnableRankListPrize())){
                return;
            }

            if(now.before(configDto.getRankStartTime()) || now.after(configDto.getEndTime())){
                return;
            }

            String joinRankKey = RedisKeySpace.K6010.toString() + configDto.getId() + "_" + RequestLocal.getCid();

            redisTemplate.expireAt(joinRankKey,configDto.getEndTime());
            //防止多次进入发奖逻辑
            if(redisTemplate.opsForValue().increment(joinRankKey,1) > 1){
                return;
            }
            Integer selfRank = remoteSuperSurpriseService.getSelfRank(configDto.getId(), consumerId) + 1;
            SuperSurpriseRankListDto selfRankRecord = remoteSuperSurpriseService.getSelfRankRecord(configDto.getId(), consumerId);
            if(Objects.isNull(selfRankRecord)){
                //用户如果没有记录直接返回
                String key = RedisKeySpace.K6007.toString() + op.getId() + "_" + RequestLocal.getCid();
                stringRedisTemplate.opsForValue().set(key,"notJoin");
                stringRedisTemplate.expireAt(key,configDto.getEndTime());
                return;
            }

            SuperSurpriseRankListOptionDto superSurpriseRankListOptionDto = remoteSuperSurpriseService.findByActivityIdAndRankRange(op.getId(), selfRank);
            if(superSurpriseRankListOptionDto == null){
                String key = RedisKeySpace.K6007.toString() + op.getId() + "_" + RequestLocal.getCid();
                stringRedisTemplate.opsForValue().set(key,"notWin");
                stringRedisTemplate.expireAt(key,configDto.getEndTime());
                return;
            }

            RankListProvidePrizeRecordDto existRecord = remoteSuperSurpriseService.getRankProvidePrizeRecordByActIdAndTypeAndCid(configDto.getId(), ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode().toString(), consumerId);
            if(Objects.nonNull(existRecord)){
                return;
            }

            String orderNum = createSubOrder(configDto, op.getId(), 0L);
            //创建发奖记录
            RankListProvidePrizeRecordDto record = new RankListProvidePrizeRecordDto();
            record.setActivityId(configDto.getId());
            record.setActivityType(ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode().toString());
            record.setAppId(RequestLocal.getAppId());
            record.setConsumerId(consumerId);
            record.setConsumerRank(selfRank);
            record.setConsumerScore(selfRankRecord.getScore());
            record.setOptionId(superSurpriseRankListOptionDto.getOptionId());
            record.setOrderNum(orderNum);
            record.setExchangeStatus(RankListProvidePrizeStatus.INIT.getCode());
            Long providePrizeRecordId = remoteSuperSurpriseService.saveRankProvidePrizeRecord(record);
            //去发奖
            rocketMqService.takePrize(getMqDataParam(RequestLocal.getConsumerDO(),op,orderNum,RequestParams.parse(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()),configDto,providePrizeRecordId,Boolean.FALSE),RocketMqMessageTopic.SUPER_SURPRISE_RANK_PRIZE_TAG);

        }catch (Exception e){
            logger.error("【天降好礼】排行榜开奖失败,activityId={},consumerId={}",configDto.getId(),consumerId,e);
        }


    };


    /**
     * 校验参与次数
     * @param configDto
     * @return
     * @throws BizException
     */
    private boolean checkLimitTimeForJoin(SuperSurpriseConfigDto configDto) throws BizException {
        Integer freeLimitTimes = configDto.getFreeLimitTimes();
        Integer limitTimes = configDto.getLimitTimes();
        Integer freeLimitTimesScope = configDto.getFreeLimitTimesScope();
        Integer limitTimesScope = configDto.getLimitTimesScope();
        SuperSurpriseJoinRecordQuery query = new SuperSurpriseJoinRecordQuery();
        query.setActivityId(configDto.getId());
        query.setAppId(configDto.getAppId());
        query.setConsumerId(RequestLocal.getCid());
        if(freeLimitTimes == null && limitTimes == null){
            //第一种情况，没配置任何限制，那么除非没开积分开关，否则不是免费参与
            return false;
        }else if (freeLimitTimes != null && limitTimes == null){
            //第二种情况 免费开关开了 参与限制开关没开，那么只要没超过免费限制就可以免费参与
            if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(freeLimitTimesScope))){
                Date startTime = DateUtils.getDayStartTime(new Date());
                query.setStartTime(startTime);
            }

            query.setFree(0);
            Integer usedFreeTimes = remoteSuperSurpriseService.countUserJoinTimes(query);
            return usedFreeTimes < freeLimitTimes;
        }else if(freeLimitTimes == null && limitTimes != null){
            //第二种情况 免费开关没开 参与限制开关开了，那么除非没开积分开关，否则不是免费参与，如果参与次数达到上限，那么直接报错
            if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(limitTimesScope))){
                Date startTime = DateUtils.getDayStartTime(new Date());
                query.setStartTime(startTime);
            }
            query.setFree(null);
            query.setStatusList(Lists.newArrayList(RecordCreditsStatus.NONE.getCode(),RecordCreditsStatus.SUCCESS.getCode()));

            Integer usedJoinTimes = remoteSuperSurpriseService.countUserJoinTimes(query);

            if(usedJoinTimes >= limitTimes){
                throw new BizException("参与次数已达上限");
            }
            return false;
        }else if(freeLimitTimes != null && limitTimes != null){
                if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(freeLimitTimesScope))){
                    Date startTime = DateUtils.getDayStartTime(new Date());
                    query.setStartTime(startTime);
                }

                query.setFree(0);
                Integer usedFreeTimes = remoteSuperSurpriseService.countUserJoinTimes(query);

                if(LimitScope.EVERY_DAY.equals(LimitScope.getByCode(limitTimesScope))){
                    Date startTime = DateUtils.getDayStartTime(new Date());
                    query.setStartTime(startTime);
                }
                query.setFree(null);
                query.setStatusList(Lists.newArrayList(RecordCreditsStatus.NONE.getCode(),RecordCreditsStatus.SUCCESS.getCode()));
                Integer usedLimitTimes = remoteSuperSurpriseService.countUserJoinTimes(query);

                if(usedLimitTimes >= limitTimes){
                    throw new BizException("参与次数已达上限");
                }
                return usedFreeTimes < freeLimitTimes;
        }
        return false;

    }

    private String getRankListPrizeOpenKey(Long activityId,Long consumerId){
        return RedisKeySpace.K6006.toString() + activityId +"_" + consumerId;
    }



    @Override
    public ModelAndView index(Long id) throws BizException {
        ModelAndView mv = new ModelAndView("/superSurprise/index");
        SuperSurpriseConfigDto superSurpriseConfig = remoteSuperSurpriseService.getById(id);
        if(Objects.isNull(superSurpriseConfig)){
            throw new BizException("活动配置不存在");
        }
        if (!Objects.equals(RequestLocal.getAppId(), superSurpriseConfig.getAppId())) {
            throw new BizException("无权访问");
        }

        OperatingActivityDto operatingActivity = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(superSurpriseConfig.getAppId(), superSurpriseConfig.getId(), OperatingActivityDto.TypeSuperSurprise);
        if(OperatingActivityDto.StatusIntOpen != operatingActivity.getStatus() || new Date().after(superSurpriseConfig.getEndTime())){
            throw new BizException("活动已结束");
        }

        Long appId = RequestLocal.getAppId();
        mv.addObject("appId", appId);
        mv.addObject("opId",operatingActivity.getId());
        mv.addObject("activityId",superSurpriseConfig.getId());
        mv.addObject("title", operatingActivity.getTitle());
        mv.addObject("activityCode", marketTempService.getMarketTempActivityCode(ActivityUniformityTypeEnum.SUPER_SURPRISE));

        // 查询并填充唤登链接
        AppSimpleDto app = developerCacheService.getById(appId);
        String loginProgram = developerCacheService.getLoginCode(app);
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        mv.addObject("loginProgram", loginProgram);
        // 登录状态
        mv.addObject("isLogin", consumerDto !=null && !consumerDto.isNotLoginUser());
        // 唤起登录开关
        AppExtraLargeFieldDto appExtraLargeFieldDto = developerCacheService.findAppShareConfCode(appId);
        mv.addObject("openLogin", appExtraLargeFieldDto != null && appExtraLargeFieldDto.isShareSwitch(AppExtraLargeFieldDto.SwitchDevCallUp) && StringUtils.isNotBlank(loginProgram));

        AccessLogExUtil.putAccessLogExPair(true,ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode(),null, PageBizTypeEnum.SUPER_SURPRISE_INDEX,
                superSurpriseConfig.getId(),
                operatingActivity.getId(),
                ActivityUniformityTypeEnum.SUPER_SURPRISE,
                consumerDto.isNotLoginUser(),consumerDto.getCredits(),null,null,null,null);
        riskService.visitRiskLog(operatingActivity);
        //排行榜异步开奖
        RANK_LIST_PRIZE_DEALER.accept(superSurpriseConfig,operatingActivity);
        return mv;
    }

    @Override
    public SuperSurpriseInfoVO getActivityInfo(Long id) throws BizException {
        SuperSurpriseConfigDto surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(id))
                .orElseThrow(() -> new BizException("活动不存在"));

        SuperSurpriseInfoVO result = BeanUtils.copy(surpriseConfigDto, SuperSurpriseInfoVO.class);
        //前端一定要用这个字段，问就是以前也是这个字段，所以重新赋值一遍
        result.setInterfaceConfig(result.getStyleConfig());
        OperatingActivityDto operatingActivityDto = Optional.ofNullable(remoteOperatingActivityServiceNew
                .findByAppIdAndActivityIdAndType(RequestLocal.getAppId(),
                        surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise)).orElseThrow(() -> new BizException("活动不存在"));
        ConsumerDto consumerDto = remoteConsumerService.find(RequestLocal.getCid());
        result.setUserCurrentCredits(consumerDto.getCredits());

        AppSimpleDto app = remoteAppService.getSimpleApp(RequestLocal.getAppId()).getResult();
        result.setUnitName(app.getUnitName());
        //处理游戏限制次数
        LIMIT_FILLER.accept(result,surpriseConfigDto);

        //处理排行榜奖品
        PRIZE_INFO_HANDLER.accept(result);

        String key = RedisKeySpace.K6007.toString() + operatingActivityDto.getId() + "_" + RequestLocal.getCid();
        String mark = stringRedisTemplate.opsForValue().get(key);

        if(StringUtils.isNotBlank(mark)){
            Integer selfRank = remoteSuperSurpriseService.getSelfRank(surpriseConfigDto.getId(), RequestLocal.getCid());
            //弹窗展示直接销毁
            if("notWin".equals(mark)){
                //如果是参与名次未中奖
                result.setPopupNotWin(true);
                result.setFinalRank(selfRank + 1);
            }else if("notJoin".equals(mark)){
                result.setPopupNotJoin(true);
                result.setFinalRank(-1);
            }else {
                result.setPopup(true);
                result.setFinalRank(selfRank + 1);
            }
            stringRedisTemplate.delete(key);

        }

        String newGuideKey = getNewGuideKey(surpriseConfigDto.getId(),RequestLocal.getCid());
        //是否需要新手引导
        if(stringRedisTemplate.opsForValue().setIfAbsent(newGuideKey, "guide")){
            result.setNeedNewGuide(true);
            stringRedisTemplate.expireAt(newGuideKey,surpriseConfigDto.getEndTime());
        }

        return result;
    }

    private String getNewGuideKey(Long activityId, Long consumerId){
        return RedisKeySpace.K6011.toString() + activityId + "_" + consumerId;
    }

    @Override
    public SuperSurpriseJoinResultVO doJoin(Long id) throws BizException {
        SuperSurpriseConfigDto  surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(id)).orElseThrow(() -> new BizException("活动不存在"));
        OperatingActivityDto op = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(surpriseConfigDto.getAppId(), surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise);
        //活动校验
        activityGeneralCheck(surpriseConfigDto,op);

        try(RedisLock lock = redisAtomicClient.getLock(RedisKeySpace.K6001.toString() + id + "_" + RequestLocal.getCid(),30)){
            if(lock == null){
                throw new BizException("操作过于频繁，请稍后再试");
            }

            //风控
            StormEngineResultDto riskResult = riskService.joinRisk(op.getId(), surpriseConfigDto.getId(), op.getType());
            if (Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())) {
                logger.info("【天降好礼】用户参与遭到风控拦截 用户id = {},uid = {}, appid = {}, 活动id ={}, 活动类型 = {}",RequestLocal.getCid(),RequestLocal.getPartnerUserId(),RequestLocal.getAppId(),op.getId(),op.getType());
                throw new BizException(riskResult.getCopy());
            }


            //1.校验用户参与次数
            boolean joinFree = checkLimitTimeForJoin(surpriseConfigDto);

            if(joinFree){
                return doFreeJoin(surpriseConfigDto, op.getId());
            }

            //判断消耗积分参与开关是否开启,没开启的话直接不消耗积分参与
            if(!BooleanUtils.isTrue(surpriseConfigDto.getEnableCredits())){
                return doFreeJoin(surpriseConfigDto, op.getId());
            }

            ConsumerDto consumerDto = remoteConsumerService.find(RequestLocal.getCid());
            if(surpriseConfigDto.getConsumeCredits() > consumerDto.getCredits()){
                throw new BizException("积分不足");
            }
            Pair<Long, SuperSurpriseJoinRecordDto> pair = createJoinRecord(surpriseConfigDto, surpriseConfigDto.getConsumeCredits(), null);

            String orderNum = doSubCredits(surpriseConfigDto,
                    op.getId(),
                    "参与活动扣积分",
                    pair.getKey(),
                    rocketMqMessageTopic.getBusinessSubCreditsCallback(),
                    RocketMqMessageTopic.SUPER_SURPRISE_JOIN_SUB_CREDITS_TAG, surpriseConfigDto.getConsumeCredits());
            SuperSurpriseJoinRecordDto record = pair.getValue();
            record.setId(pair.getKey());
            record.setOrderNum(orderNum);
            remoteSuperSurpriseService.updateJoinRecordById(record);
            SuperSurpriseJoinResultVO resultVO = new SuperSurpriseJoinResultVO();
            resultVO.setRecordId(pair.getKey());
            resultVO.setRecordStatus(record.getConsumeCreditsStatus());
            accessLogJoin(surpriseConfigDto.getId(), op.getId(), orderNum, surpriseConfigDto.getConsumeCredits(), PageBizTypeEnum.SUPER_SURPRISE_JOIN);
            return resultVO;



        }catch (BizException e){
            throw  e;

        }catch (Exception e){
            logger.error("【天降好礼】活动参与失败, consumerId={},activityId={}",RequestLocal.getCid(),surpriseConfigDto.getId(),e);
            throw new BizException("系统异常");
        }

    }

    /**
     * 发起扣积分请求
     * @param surpriseConfigDto 活动配置
     * @param opId 入库活动id
     * @param description 描述
     * @param businessId 需要透传的业务id
     * @param callBackTopic 回调topic
     * @param callBackTag 回调tag
     * @return 订单号
     * @throws BizException
     */
    private String doSubCredits(SuperSurpriseConfigDto surpriseConfigDto,
                                Long opId,
                                String description,
                                Long businessId,
                                String callBackTopic,
                                String callBackTag,Long credits) throws BizException {
        SubCreditsRequest subCreditsRequest = new SubCreditsRequest();
        subCreditsRequest.setAppId(surpriseConfigDto.getAppId());
        subCreditsRequest.setConsumerId(RequestLocal.getCid());
        subCreditsRequest.setPartnerUserId(RequestLocal.getPartnerUserId());
        subCreditsRequest.setCredits(credits);
        subCreditsRequest.setActivityId(surpriseConfigDto.getId());
        subCreditsRequest.setOperatingActivityId(opId);
        subCreditsRequest.setActivityType(ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode().toString());
        subCreditsRequest.setDescription(description);
        subCreditsRequest.setTransfer(businessId.toString());
        subCreditsRequest.setActivityTitle(surpriseConfigDto.getActivityTitle());
        subCreditsRequest.setCallbackTopic(callBackTopic);
        subCreditsRequest.setCallbackTag(callBackTag);
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        subCreditsRequest.setRequestParams(RequestParams.parse(request));

        CreditsPluginResponse creditsPluginResponse = remoteCreditsPluginService.subCredits(subCreditsRequest);
        if(!creditsPluginResponse.isSuccess()){
            throw new BizException(creditsPluginResponse.getErrorMsg());
        }
        return creditsPluginResponse.getOrderNum();
    }


    @Override
    public SuperSurpriseJoinRecordStatusVO getJoinRecordInfoById(Long recordId) throws BizException {
        SuperSurpriseJoinRecordDto recordDto = Optional.ofNullable(remoteSuperSurpriseService.getJoinRecordById(recordId)).orElseThrow(() -> new BizException("参与记录不存在"));
        if(!RequestLocal.getCid().equals(recordDto.getConsumerId())){
            throw new BizException("无权查看");
        }

        SuperSurpriseJoinRecordStatusVO result = new SuperSurpriseJoinRecordStatusVO();
        result.setConsumeCreditsStatus(recordDto.getConsumeCreditsStatus());
        result.setExchangeStatus(recordDto.getExchangeStatus());
        if(RecordCreditsStatus.FAIL.getCode() == recordDto.getConsumeCreditsStatus()){
            ActivityOrderDto orderDto = remoteActivityOrderService.findByOrderNum(recordDto.getOrderNum());
            result.setErrorMessage(orderDto.getError4consumer());
        }
        if(recordDto.getPrizeId() != null){
            OperatingActivityOptionsDto option = remoteOperatingActivityOptionsService.findOptionById(recordDto.getPrizeId());
            SuperSurpriseSimplePrizeVO prizeVO = new SuperSurpriseSimplePrizeVO();
            prizeVO.setPrizeId(option.getId());
            prizeVO.setPrizeName(option.getName());
            prizeVO.setPrizeImage(option.getLogo());
            result.setPrizeInfo(prizeVO);
        }
        return result;
    }

    @Override
    public SuperSurpriseRebirthResultVO doRebirth(SuperSurpriseRebirthVO superSurpriseRebirthVO) throws BizException {
        SuperSurpriseConfigDto surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(superSurpriseRebirthVO.getActivityId())).orElseThrow(() -> new BizException("活动配置不存在"));
        OperatingActivityDto op = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(surpriseConfigDto.getAppId(), surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise);
        activityGeneralCheck(surpriseConfigDto,op);
        try(RedisLock lock = redisAtomicClient.getLock(RedisKeySpace.K6002.toString() + surpriseConfigDto.getId() + RequestLocal.getCid(),5)){
            if(lock == null){
                throw new BizException("操作过于频繁，请稍后再试");
            }

            if(!BooleanUtils.isTrue(surpriseConfigDto.getEnableRebirth())){
                throw new BizException("活动未开启复活机制");
            }

            Long recordId = superSurpriseRebirthVO.getRecordId();
            //校验对应的参与记录
            SuperSurpriseJoinRecordDto joinRecord = remoteSuperSurpriseService.getJoinRecordById(recordId);
            if(joinRecord == null){
                throw new BizException("参与记录不存在");
            }
            //校验参与记录是否合法
            if(!joinRecord.getConsumerId().equals(RequestLocal.getCid())){
                throw new BizException("非法请求");
            }
            SuperSurpriseRebirthResultVO result = new SuperSurpriseRebirthResultVO();

            //如果是概率复活
            if(RebirthType.RATE.equals(superSurpriseRebirthVO.getType())){
                Boolean enableRateRebirth = surpriseConfigDto.getEnableRateRebirth();
                result.setType(RebirthType.RATE);
                result.setResult(RebirthResult.FAIL.getCode());
                if(BooleanUtils.isTrue(enableRateRebirth)){

                    String rebirthRate = surpriseConfigDto.getRebirthRate();
                    Integer rebirthRateLimitTimes = surpriseConfigDto.getRebirthRateLimitTimes();
                    BigDecimal rate = new BigDecimal(rebirthRate);
                    BigDecimal randomNumber = BigDecimal.valueOf(Math.random() * 100).setScale(4, BigDecimal.ROUND_HALF_UP);
                    Long rebirthRecordId = null;
                    if(randomNumber.compareTo(rate) < 0){
                        //查询用户是否已到达概率复活次数上限
                        SuperSurpriseRebirthQueryDto queryDto = new SuperSurpriseRebirthQueryDto();
                        queryDto.setAppId(surpriseConfigDto.getAppId());
                        queryDto.setConsumerId(RequestLocal.getCid());
                        queryDto.setUseCredits(RebirthType.RATE.getCode());
                        queryDto.setJoinRecordId(joinRecord.getId());
                        Integer usedCount = remoteSuperSurpriseService.countRebirthRecordByCondition(queryDto);
                        result.setRemainTimes(rebirthRateLimitTimes - usedCount);
                        if(usedCount < rebirthRateLimitTimes){
                            rebirthRecordId = doSaveRebirthRecord(surpriseConfigDto, RebirthType.RATE, joinRecord.getId(), 0L,RebirthStatus.SUCCESS).getId();
                            result.setResult(RebirthResult.SUCCESS.getCode());
                            result.setRebirthRecordId(rebirthRecordId);
                            result.setRemainTimes(rebirthRateLimitTimes - usedCount - 1);
                        }
                        //如果已经超出了限制，就不再插入复活记录了
                    }else {
                        rebirthRecordId = doSaveRebirthRecord(surpriseConfigDto, RebirthType.RATE, joinRecord.getId(), 0L,RebirthStatus.FAIL).getId();
                        result.setRebirthRecordId(rebirthRecordId);
                    }

                }
            }else {
                //积分复活
                Boolean enableCreditsRebirth = surpriseConfigDto.getEnableCreditsRebirth();
                result.setType(RebirthType.CREDITS);
                result.setResult(RebirthResult.FAIL.getCode());
                if(BooleanUtils.isTrue(enableCreditsRebirth)){
                    Integer rebirthCreditsLimitTimes = surpriseConfigDto.getRebirthCreditsLimitTimes();
                    Long rebirthCredits = surpriseConfigDto.getRebirthCredits();

                    ConsumerDto consumerDto = remoteConsumerService.find(RequestLocal.getCid());
                    if(rebirthCredits > consumerDto.getCredits()){
                        throw new BizException("积分不足");
                    }
                    //查询用户是否已到达积分复活次数上限
                    SuperSurpriseRebirthQueryDto queryDto = new SuperSurpriseRebirthQueryDto();
                    queryDto.setAppId(surpriseConfigDto.getAppId());
                    queryDto.setConsumerId(RequestLocal.getCid());
                    queryDto.setUseCredits(RebirthType.CREDITS.getCode());
                    queryDto.setJoinRecordId(joinRecord.getId());
                    queryDto.setRebirthStatus(RebirthStatus.SUCCESS.getCode());
                    Integer usedCount = remoteSuperSurpriseService.countRebirthRecordByCondition(queryDto);
                    result.setRemainTimes(rebirthCreditsLimitTimes - usedCount);
                    if(usedCount < rebirthCreditsLimitTimes){
                        //保存复活记录
                        SuperSurpriseRebirthRecordDto rebirthRecord = doSaveRebirthRecord(surpriseConfigDto, RebirthType.CREDITS, joinRecord.getId(), rebirthCredits,RebirthStatus.PROCESSING);

                        String orderNum = doSubCredits(surpriseConfigDto,
                                op.getId(),
                                "复活消耗积分",
                                rebirthRecord.getId(),
                                rocketMqMessageTopic.getBusinessSubCreditsCallback(),
                                RocketMqMessageTopic.SUPER_SURPRISE_REBIRTH_SUB_CREDITS_TAG,
                                surpriseConfigDto.getRebirthCredits());
                        rebirthRecord.setOrderNum(orderNum);
                        remoteSuperSurpriseService.updateRebirthRecordById(rebirthRecord);
                        result.setRemainTimes(rebirthCreditsLimitTimes - usedCount - 1);
                        result.setResult(RebirthResult.PROCESSING.getCode());
                        result.setRebirthRecordId(rebirthRecord.getId());
                        accessLogJoin(surpriseConfigDto.getId(),op.getId(),orderNum,surpriseConfigDto.getRebirthCredits(),PageBizTypeEnum.SUPER_SURPRISE_REBIRTH);

                    }
                }


            }

            return result;

        }catch (BizException e){
            throw e;
        }catch (Exception e){
            logger.error("【天降好礼】用户游戏复活异常, activityId={},consumerId={}",surpriseConfigDto.getId(),RequestLocal.getCid(),e);
            throw new BizException("系统异常");
        }
    }

    @Override
    public RebirthResultVO queryRebirthStatus(Long rebirthRecordId) throws BizException {
        SuperSurpriseRebirthRecordDto recordDto = Optional.ofNullable(remoteSuperSurpriseService.findRebirthRecordById(rebirthRecordId)).orElseThrow(() -> new BizException("复活记录不存在"));
        if(!RequestLocal.getCid().equals(recordDto.getConsumerId())){
            throw new BizException("非法请求");
        }
        RebirthResultVO rebirthResultVO = new RebirthResultVO();
        rebirthResultVO.setStatus(recordDto.getRebirthStatus());
        if(RebirthStatus.FAIL.getCode() == recordDto.getRebirthStatus()){
            ActivityOrderDto orderDto = remoteActivityOrderService.findByOrderNum(recordDto.getOrderNum());
            rebirthResultVO.setErrorMessage(orderDto.getError4consumer());
        }
        return rebirthResultVO;
    }

    @Override
    public GameResultVO submitScore(SuperSurpriseScoreSubmitVO superSurpriseScoreSubmitVO) throws BizException {
        SuperSurpriseConfigDto surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(superSurpriseScoreSubmitVO.getActivityId())).orElseThrow(() -> new BizException("活动配置不存在"));
        OperatingActivityDto op = remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(surpriseConfigDto.getAppId(), surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise);
        activityGeneralCheck(surpriseConfigDto,op);

        SuperSurpriseJoinRecordDto joinRecordDto = Optional.ofNullable(remoteSuperSurpriseService.getJoinRecordById(superSurpriseScoreSubmitVO.getRecordId())).orElseThrow(() -> new BizException("参与记录不存在"));
        if(!RequestLocal.getCid().equals(joinRecordDto.getConsumerId()) || !RequestLocal.getAppId().equals(surpriseConfigDto.getAppId())
                || !joinRecordDto.getActivityId().equals(surpriseConfigDto.getId())) {
            throw new BizException("无权操作");
        }

        try(RedisLock lock = redisAtomicClient.getLock(RedisKeySpace.K6003.toString() + superSurpriseScoreSubmitVO.getRecordId() + "_" + RequestLocal.getCid(),  5)){
            if(lock == null){
                throw new BizException("操作过于频繁，请稍后再试");
            }

            //风控
            StormEngineResultDto riskResult = riskService.joinRisk(op.getId(), surpriseConfigDto.getId(), op.getType());
            if (Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())) {
                logger.info("【天降好礼】用户提交分数遭到风控拦截 用户id = {},uid = {}, appid = {}, 活动id ={}, 活动类型 = {}",RequestLocal.getCid(),RequestLocal.getPartnerUserId(),RequestLocal.getAppId(),op.getId(),op.getType());
                throw new BizException(riskResult.getCopy());
            }

            //判断是否同一条参与记录重复提交
            String multiSubmitCheckKey = getMultiSubmitCheckKey(joinRecordDto.getId(), surpriseConfigDto.getId());
            if(redisTemplate.opsForValue().increment(multiSubmitCheckKey, 1) > 1){
                throw new BizException("请勿重复提交");
            }
            redisTemplate.expireAt(multiSubmitCheckKey,surpriseConfigDto.getEndTime());

            // 判断扣积分是否成功
            if (!Objects.equals(joinRecordDto.getConsumeCreditsStatus(), RecordCreditsStatus.NONE.getCode()) && !Objects.equals(joinRecordDto.getConsumeCreditsStatus(), RecordCreditsStatus.SUCCESS.getCode())) {
                throw new BizException("扣积分失败不可参与");
            }

            String decryptScore = RsaUtil.decrypt(superSurpriseScoreSubmitVO.getScore());
            if(!NumberUtils.isNumber(decryptScore)){
                throw new BizException("分数格式错误");
            }
            //判断分数范围是否合理
            checkScore(decryptScore, surpriseConfigDto.getGamePeriod());

            //验证签名
            String sign = superSurpriseScoreSubmitVO.getSign();
            String timestamp = superSurpriseScoreSubmitVO.getTimestamp();
            checkSign(decryptScore, sign, timestamp);

            //验证时间
            checkReqTime(timestamp, joinRecordDto);

            Long score = Long.valueOf(decryptScore);
            GameResultVO result = new GameResultVO();
            result.setCurrentScore(score);
            if(BooleanUtils.isTrue(surpriseConfigDto.getEnableRankListPrize())){
                //如果活动开启了排行榜，那么需要保存用户排行榜记录
                SuperSurpriseRankListDto selfRankRecord = remoteSuperSurpriseService.getSelfRankRecord(surpriseConfigDto.getId(), RequestLocal.getCid());
                if(Objects.isNull(selfRankRecord)){
                    //如果还没有排行榜记录，则初始化一条
                    SuperSurpriseRankListDto superSurpriseRankListDto = new SuperSurpriseRankListDto();
                    superSurpriseRankListDto.setActivityId(surpriseConfigDto.getId());
                    superSurpriseRankListDto.setAppId(surpriseConfigDto.getAppId());
                    superSurpriseRankListDto.setConsumerId(RequestLocal.getCid());
                    superSurpriseRankListDto.setPartnerUserId(RequestLocal.getPartnerUserId());
                    superSurpriseRankListDto.setScore(score);
                    remoteSuperSurpriseService.saveRankList(superSurpriseRankListDto);


                    Integer selfRank = remoteSuperSurpriseService.getSelfRank(surpriseConfigDto.getId(), RequestLocal.getCid()) + 1;

                    //保存用户最佳排名
                    String consumerBestRankKey = getConsumerBestRankKey(surpriseConfigDto.getId());
                    boolean needExpire = redisTemplate.hasKey(consumerBestRankKey);
                    redisTemplate.opsForHash().put(consumerBestRankKey,RequestLocal.getCid(),selfRank);
                    //设置过期时间
                    if(!needExpire){
                        redisTemplate.expireAt(consumerBestRankKey,surpriseConfigDto.getEndTime());
                    }
                    result.setBestRank(selfRank);
                    result.setBestScore(score);
                }else{
                    String extraInfo = surpriseConfigDto.getExtraInfo();
                    JSONObject exObj = JSON.parseObject(extraInfo);

                    //累计分数时
                    JSONObject rankConfig;
                    if (exObj != null && (rankConfig = exObj.getJSONObject("rank")) != null
                            && rankConfig.getInteger("rankType") == 2) {

                        //如果本次提交分数比原分数高，则更新记录
                        selfRankRecord.setScore(score);
                        remoteSuperSurpriseService.increaseRankScore(selfRankRecord);
                        Integer newRank = remoteSuperSurpriseService.getSelfRank(surpriseConfigDto.getId(), RequestLocal.getCid()) + 1;
                        //更新最佳排名
                        redisTemplate.opsForHash().put(getConsumerBestRankKey(surpriseConfigDto.getId()),RequestLocal.getCid(),newRank);
                        result.setBestRank(newRank);
                        result.setBestScore(score);
                    }else {
                        if(score > selfRankRecord.getScore()){
                            //如果本次提交分数比原分数高，则更新记录
                            selfRankRecord.setScore(score);
                            remoteSuperSurpriseService.updateRank(selfRankRecord);

                            Integer newRank = remoteSuperSurpriseService.getSelfRank(surpriseConfigDto.getId(), RequestLocal.getCid()) + 1;
                            //更新最佳排名
                            redisTemplate.opsForHash().put(getConsumerBestRankKey(surpriseConfigDto.getId()),RequestLocal.getCid(),newRank);

                            result.setBestRank(newRank);
                            result.setBestScore(score);
                        }else{
                            //否则取出redis保存的最佳排名
                            Integer  bestRank = (Integer) redisTemplate.opsForHash().get(getConsumerBestRankKey(surpriseConfigDto.getId()), RequestLocal.getCid());
                            result.setBestRank(bestRank);
                            result.setBestScore(selfRankRecord.getScore());
                        }
                    }
                }

            }
            //更新参与记录分数
            joinRecordDto.setScore(score);
            remoteSuperSurpriseService.updateJoinRecordById(joinRecordDto);

            //判断是否去开奖
            if(BooleanUtils.isTrue(surpriseConfigDto.getEnableRatePrize())){
                //先把数据库记录更新成开奖中
                if(remoteSuperSurpriseService.updateExchangeStatusById(joinRecordDto.getId(),RecordExchangeStatus.EXCHANGE_PROCESS.getCode()) != 1){
                    throw new BizException("参与记录状态异常");
                }
                OpActivityVisitTimesDto visitTimesDto = remoteOpActivityVisitTimesService.findByOpActivityId(op.getId());
                //访问记录初始化，避免prize-center报错
                if(Objects.isNull(visitTimesDto)){
                    rocketMqService.addVisitTime(op.getId());
                }

                //发送mq消息到prize-center
                rocketMqService.takePrize(getMqDataParam(RequestLocal.getConsumerDO(),op,joinRecordDto.getOrderNum(),RequestParams.parse(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()),surpriseConfigDto, joinRecordDto.getId(),Boolean.TRUE),RocketMqMessageTopic.SUPER_SURPRISE_RATE_PRIZE_TAG);
            }
            result.setRecordId(joinRecordDto.getId());

            //常盈定制通知
            notifyChangYin(score, joinRecordDto,RequestLocal.getAppId());
            return result;


        }catch (BizException e){
            throw e;
        }catch (Exception e){
            logger.error("【天降好礼】分数提交异常",e);
            throw new BizException("系统异常");
        }

    }



    private void notifyChangYin(Long score, SuperSurpriseJoinRecordDto joinRecordDto,Long appId) throws BizException {
        if(!superSupriseConfig.getChangYingAppIds().contains(appId)){
            return;
        }
        long timeStamp = System.currentTimeMillis();
        GiftRecordRequest giftRecordRequest = new GiftRecordRequest();
        giftRecordRequest.setScore(score.intValue());
        giftRecordRequest.setRecordId(joinRecordDto.getId());
        giftRecordRequest.setUid(RequestLocal.getPartnerUserId());
        giftRecordRequest.setTimestamp(timeStamp);
        notifyChangeYin(RequestLocal.getConsumerAppDO(),giftRecordRequest);
    }

    private void checkScore(String decryptScore, Integer gamePeriod) throws BizException {
        int n = Integer.valueOf(decryptScore);
        int limit = gamePeriod * superSupriseConfig.getScorePerSec();
        if (n < 0 || n > limit) {
            logger.info("天降好礼 分数非法 score={} cid={} limit={} gamePeriod={}", decryptScore, RequestLocal.getCid(), limit, gamePeriod);
            throw new BizException("分数非法");
        }
    }

    private void checkReqTime(String timestamp, SuperSurpriseJoinRecordDto joinRecordDto) throws BizException {
        if (Math.abs(Long.valueOf(timestamp) - System.currentTimeMillis()) > 60000) {
            logger.info("天降好礼 提交分数 时间超时 timestamp={} cid={} joinRecordDto={}", timestamp, RequestLocal.getCid(), JSON.toJSONString(joinRecordDto));
            throw new BizException("请求非法");
        }
    }

    private void checkSign(String decryptScore, String sign, String timestamp) throws BizException {
        String m = decryptScore + timestamp + Long.toHexString(Long.valueOf(timestamp));
        String mySign = "";
        try {
            mySign = MD5.md5(m);
        } catch (Exception e) {
            logger.warn("天降好礼 提交分数 验签异常 decryptScore={} sign={} timestamp={} cid={}", decryptScore, sign, timestamp, RequestLocal.getCid(), e);
            throw new BizException("系统异常");
        }
        if (!StringUtils.equals(sign, mySign)) {
            logger.info("天降好礼 提交分数 验签失败 decryptScore={} sign={} timestamp={} cid={}", decryptScore, sign, timestamp, RequestLocal.getCid());
            throw new BizException("非法请求");
        }

    }

    @Override
    public SuperSurpriseRankListProvideRecordVO queryRankListProvideRecord(Long activityId) throws BizException {
        SuperSurpriseConfigDto surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(activityId)).orElseThrow(() -> new BizException("活动配置不存在"));
        OperatingActivityDto op = Optional.ofNullable(remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(surpriseConfigDto.getAppId(), surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise)).orElseThrow(() -> new BizException("活动不存在"));
        timeCheck(surpriseConfigDto,op);

        RankListProvidePrizeRecordDto record = remoteSuperSurpriseService.getRankProvidePrizeRecordByActIdAndTypeAndCid(activityId, ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode().toString(), RequestLocal.getCid());
        if(record == null){
            //这种情况一般不存在
            return null;
        }
        SuperSurpriseRankListProvideRecordVO result = BeanUtils.copy(record, SuperSurpriseRankListProvideRecordVO.class);

        Long optionId = record.getOptionId();
        OperatingActivityOptionsDto option = remoteOperatingActivityOptionsService.findOptionById(optionId);


        if(Objects.nonNull(option)){
            SuperSurpriseSimplePrizeVO prizeInfo = new SuperSurpriseSimplePrizeVO();
            DubboResult<ConsumerExchangeRecordDto> exchangeRecordResult = remoteConsumerExchangeRecordService.findByRelationIdAndType(Long.valueOf(record.getOrderNum()), ConsumerExchangeRecordDto.TypeSuperSurprise, RequestLocal.getCid());
            ConsumerExchangeRecordDto exchangeRecordDto  = exchangeRecordResult.getResult();
            if(exchangeRecordResult.isSuccess() && Objects.nonNull(exchangeRecordDto)){
                String url = "";
                if (exchangeRecordDto.getOrderId() != null) {
                    url = "/crecord/recordDetailNew?orderId=" + exchangeRecordDto.getOrderId() + "&after=1&dbnewopen";
                } else {
                    url = "/activity/takePrizeNew?recordId=" + exchangeRecordDto.getId() + "&dpm=1.26.0.1&dcm=102.9972.0.0&dbnewopen";
                }
                prizeInfo.setUrl(url);
            }
            prizeInfo.setPrizeId(optionId);
            prizeInfo.setPrizeName(option.getName());
            prizeInfo.setPrizeImage(option.getLogo());
            result.setPrizeInfo(prizeInfo);
        }
        return result;
    }

    @Override
    public RankInfoVO queryRankList(Long activityId) throws BizException {
        SuperSurpriseConfigDto surpriseConfigDto = Optional.ofNullable(remoteSuperSurpriseService.getById(activityId)).orElseThrow(() -> new BizException("活动配置不存在"));
        OperatingActivityDto op = Optional.ofNullable(remoteOperatingActivityServiceNew.findByAppIdAndActivityIdAndType(surpriseConfigDto.getAppId(), surpriseConfigDto.getId(), OperatingActivityDto.TypeSuperSurprise)).orElseThrow(() -> new BizException("活动不存在"));

        timeCheck(surpriseConfigDto,op);
        RankInfoVO result = new RankInfoVO();
        List<SuperSurpriseRankListDto> rankInfo =  rankCache.get(activityId, aid -> remoteSuperSurpriseService.getRankInfo(aid));

        List<RankSimpleInfo> rank = rankInfo.stream().map(x -> {
            RankSimpleInfo info = new RankSimpleInfo();
            info.setRank(x.getRank());
            info.setUid(x.getPartnerUserId());
            info.setScore(x.getScore());
            return info;
        }).collect(Collectors.toList());
        result.setRankList(rank);
        result.setMyUid(RequestLocal.getPartnerUserId());
        SuperSurpriseRankListDto selfRankRecord = remoteSuperSurpriseService.getSelfRankRecord(activityId, RequestLocal.getCid());

        JSONObject extInfo = JSON.parseObject(surpriseConfigDto.getExtraInfo());
        JSONObject rankTypeConfig ;
        result.setRankType(1);
        if (extInfo!=null && (rankTypeConfig = extInfo.getJSONObject("rank")) != null) {
            result.setRankType(rankTypeConfig.getInteger("rankType"));
        }
        if(Objects.isNull(selfRankRecord)){
            return result;
        }

        Integer selfRank = consumerRankCache.get(activityId + "_" + RequestLocal.getCid(), key -> remoteSuperSurpriseService.getSelfRank(activityId, RequestLocal.getCid()));

        result.setMyRank(selfRank + 1);
        result.setMyScore(selfRankRecord.getScore());
        return result;
    }

    @Override
    public void insertRankForTest(Long activityId, Long appId, Integer number) throws InterruptedException {
        for(int i = 0; i<number;i++){
            Long cid = Long.valueOf(i+1) * 1010;
            SuperSurpriseRankListDto superSurpriseRankListDto = new SuperSurpriseRankListDto();
            superSurpriseRankListDto.setActivityId(activityId);
            superSurpriseRankListDto.setAppId(appId);
            superSurpriseRankListDto.setConsumerId(cid);
            superSurpriseRankListDto.setPartnerUserId("zhangTest" + i);
            superSurpriseRankListDto.setScore(Long.valueOf(2000 - i));
            remoteSuperSurpriseService.saveRankList(superSurpriseRankListDto);
            Thread.sleep(100);
        }
    }

    @Override
    public void notifyChangeYin(AppSimpleDto app, GiftRecordRequest record) throws BizException {
        logger.info("notifyChangeYin:{}", JSON.toJSONString(record));
        long timestamp = record.getTimestamp();
        Map<String,String> data = Maps.newHashMap();
        data.put("timestamp", timestamp+"");
        data.put("uid", record.getUid());
        data.put("appKey", app.getAppKey());
        data.put("score",record.getScore().toString() );
        data.put("joinEndTime", timestamp+"");
        data.put("recordId", record.getRecordId().toString());
        data.put("appSecret", app.getAppSecret());
        String sign = SignTool.sign(data);
        data.remove("appSecret");
        data.put("sign", sign);
        logger.info("notifyChangeYin data :{}", JSON.toJSONString(data));


        try {
            String url = SpringEnvironmentUtils.isProdEnv() ? superSupriseConfig.getProdChangYingUrl() : superSupriseConfig.getChangYingUrl();

            String respBody = HttpUtil.createPost(url).timeout(5000)
                    .body(JSONObject.toJSONString(data))
                    .header("Content-Type", "application/json")
                    .execute().body();
            logger.info("notifyChangeYin response={}", respBody);
        } catch (Exception e) {
            logger.warn("notifyChangeYin息异常", e);
        }


    }


    public MqDataParam getMqDataParam(ConsumerDto consumer, OperatingActivityDto operatingActivity, String orderNum, RequestParams requestParams,SuperSurpriseConfigDto configDto, Long joinRecordId,Boolean ratePrize) {
        MqDataParam param = new MqDataParam();
        param.setAppId(consumer.getAppId());
        param.setConsumerId(consumer.getId());
        param.setOpId(operatingActivity.getId());
        param.setDuibaId(null);
        param.setPartnerUserId(consumer.getPartnerUserId());
        param.setIp(requestParams.getIp());
        param.setProxy(requestParams.isProxy());
        param.setSubOrderId(null);
        param.setOrderNum(orderNum);
        param.setTransfer(requestParams.getTransfer());
        param.setActivityType(MqDataParam.ACTIVITY_TYPE_HD_TOOL);
        param.setActivityUniformityType(ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode());
        param.setBusiness(false);
        param.setTitle(operatingActivity.getTitle());
        param.setOs(requestParams.getOs());
        param.setSoltId(requestParams.getSlotId());
        param.setOpType(operatingActivity.getType());
        param.setSubType(operatingActivity.getSubType());
        param.setUserAgent(requestParams.getUserAgent());
        param.setTokenId(requestParams.getCookies("tokenId"));
        param.setDeap(requestParams.getDeap());
        param.setChannelType(0);
        param.setRatePrize(ratePrize);
        param.setSuperSurpriseRecordId(joinRecordId);

        JSONObject extJson = new JSONObject();
        String extraInfo = configDto.getExtraInfo();
        if(StringUtils.isNotBlank(extraInfo)){
            JSONObject extra = JSON.parseObject(extraInfo);
            JSONObject ml;
            if((ml = extra.getJSONObject("ml")) != null){
                if(BooleanUtils.isTrue(ml.getBoolean("enableMultiPrizeLimit"))){
                    String limitCount = ml.getString("limitCount");
                    String limitScope = ml.getString("limitScope");
                    String limitDay = ml.getString("limitDay");
                    extJson.put(DuibaHdtoolUtil.EXT_JSON_KEY_MUTLI_PRIZE_LIMIT_COUNT,limitCount);
                    extJson.put(DuibaHdtoolUtil.EXT_JSON_KEY_MULTI_PRIZE_LIMIT_TYPE,limitScope);
                    extJson.put(DuibaHdtoolUtil.EXT_JSON_KEY_MULTI_PRIZE_LIMIT_DAYS,limitDay);
                }
            }
        }
        param.setExtendJson(extJson.toJSONString());

        Map<String, String> cookiesParams = Maps.newHashMap();
        RequestParams.getCookiesForMap(requestParams.getCookies(), cookiesParams);
        param.setCookiesParams(JSON.toJSONString(cookiesParams));
        return param;
    }

    /**
     * 获取分数重复提交key
     * @param joinRecordId
     * @param configId
     * @return
     */
    private String getMultiSubmitCheckKey(Long joinRecordId,Long configId){
        return RedisKeySpace.K6005.toString() + joinRecordId + "_" + configId;
    }

    /**
     * 获取排行榜最佳排名key
     *
     * @param activityId
     * @return
     */
    private String getConsumerBestRankKey(Long activityId){
        return RedisKeySpace.K6004.toString() + activityId;
    }





    /**
     * 保存复活记录
     * @param configDto 活动配置
     * @param type 复活类型
     * @param joinRecordId 参与记录id
     * @param credits 消耗积分
     * @return 复活记录id
     */
    private SuperSurpriseRebirthRecordDto doSaveRebirthRecord(SuperSurpriseConfigDto configDto, RebirthType type, Long joinRecordId,Long credits,RebirthStatus status){
        SuperSurpriseRebirthRecordDto superSurpriseRebirthRecordDto = new SuperSurpriseRebirthRecordDto();
        superSurpriseRebirthRecordDto.setActivityId(configDto.getId());
        superSurpriseRebirthRecordDto.setAppId(configDto.getAppId());
        superSurpriseRebirthRecordDto.setConsumerId(RequestLocal.getCid());
        superSurpriseRebirthRecordDto.setJoinRecordId(joinRecordId);
        superSurpriseRebirthRecordDto.setUseCredits(type.getCode());
        superSurpriseRebirthRecordDto.setConsumeCredits(credits);
        superSurpriseRebirthRecordDto.setRebirthStatus(status.getCode());
        Long rebirthRecordId = remoteSuperSurpriseService.saveRebirthRecord(superSurpriseRebirthRecordDto);
        superSurpriseRebirthRecordDto.setId(rebirthRecordId);
        return superSurpriseRebirthRecordDto;
    }

    //天降好礼参与日志
    private void accessLogJoin(Long id, Long opId, String orderNum,Long credits,PageBizTypeEnum pageBizType) {
        try {
            AccessLogFilter.putExPair("suc", 1);
            AccessLogFilter.putExPair("id", id);
            AccessLogFilter.putExPair("activityid", opId);
            AccessLogFilter.putExPair("activitytype", OperatingActivityDto.TypeSuperSurprise);

            AccessLogFilter.putExPair("orderNum", orderNum);
            AccessLogFilter.putExPair("loginStatus", 1);
            AccessLogFilter.putExPair("userCredits", RequestLocal.getConsumerDO().getCredits());
            AccessLogFilter.putExPair("pageBizId", pageBizType.getBizPageId());
//            AccessLogFilter.putExPair("firstSignDay", RequestLocal.getConsumerDO().getGmtCreate());
            AccessLogFilter.putExPair("Credits",credits);

        } catch (Exception e) {
            logger.error("【天降好礼】参与日志埋点异常,id=" + id, e);
        }
    }

    private void activityGeneralCheck(SuperSurpriseConfigDto surpriseConfigDto, OperatingActivityDto op) throws BizException {
        timeCheck(surpriseConfigDto,op);
        if(BooleanUtils.isTrue(surpriseConfigDto.getEnableRankListPrize())){
            if(new Date().after(surpriseConfigDto.getRankStartTime())){
                throw new BizException(ErrorCode.E4000001.getDesc()).withCode(ErrorCode.E4000001.getErrorCode());
            }
        }

    }

    private void timeCheck(SuperSurpriseConfigDto surpriseConfigDto, OperatingActivityDto op) throws BizException {
        if(!RequestLocal.getAppId().equals(surpriseConfigDto.getAppId())){
            throw new BizException("无权进入");
        }
        if(OperatingActivityDto.StatusIntOpen != op.getStatus()){
            throw new BizException("活动未发布");
        }

        Date startTime = surpriseConfigDto.getStartTime();
        Date endTime = surpriseConfigDto.getEndTime();

        Date now = new Date();
        if(now.before(startTime)){
            throw new BizException("活动未开始");
        }

        if(now.after(endTime)){
            throw new BizException("活动已结束");
        }

    }

    private SuperSurpriseJoinResultVO doFreeJoin(SuperSurpriseConfigDto surpriseConfigDto,Long opId) throws BizException {
        SuperSurpriseJoinResultVO resultVO = new SuperSurpriseJoinResultVO();
        //如果可以免费参与，就不用检验总次数限制
        //1.生成扣积分数为0的子订单
        String orderNum = createSubOrder(surpriseConfigDto, opId,0L);

        //2.生成参与记录
        Pair<Long, SuperSurpriseJoinRecordDto> pair = createJoinRecord(surpriseConfigDto, 0L, orderNum);
        resultVO.setRecordId(pair.getKey());
        resultVO.setRecordStatus(pair.getValue().getConsumeCreditsStatus());
        accessLogJoin(surpriseConfigDto.getId(),opId,orderNum,0L,PageBizTypeEnum.SUPER_SURPRISE_JOIN);
        return resultVO;
    }

    /**
     * 创建参与记录
     * @param surpriseConfigDto
     * @param credits
     * @param orderNum
     * @return
     */
    private Pair<Long,SuperSurpriseJoinRecordDto> createJoinRecord(SuperSurpriseConfigDto surpriseConfigDto, Long credits, String orderNum){
        SuperSurpriseJoinRecordDto recordDto = new SuperSurpriseJoinRecordDto();
        recordDto.setAppId(surpriseConfigDto.getAppId());
        recordDto.setActivityId(surpriseConfigDto.getId());
        recordDto.setConsumeCredits(credits);
        recordDto.setConsumerId(RequestLocal.getCid());
        recordDto.setOrderNum(orderNum);
        recordDto.setConsumeCreditsStatus(credits == 0?RecordCreditsStatus.NONE.getCode():RecordCreditsStatus.PROCESSING.getCode());
        if(BooleanUtils.isTrue(surpriseConfigDto.getEnableRatePrize())){
            recordDto.setExchangeStatus(RecordExchangeStatus.INIT.getCode());
        }else {
            recordDto.setExchangeStatus(RecordExchangeStatus.NONE.getCode());
        }
        Long recordId = remoteSuperSurpriseService.saveJoinRecord(recordDto);
        return new Pair<>(recordId,recordDto);
    }

    private String createSubOrder(SuperSurpriseConfigDto surpriseConfigDto,Long opId,Long credits) throws BizException {
        ActivityOrderDto orderDto = new ActivityOrderDto();
        orderDto.setAppId(surpriseConfigDto.getAppId());
        orderDto.setConsumerId(RequestLocal.getCid());
        orderDto.setPartnerUserId(RequestLocal.getPartnerUserId());


        orderDto.setDuibaActivityId(surpriseConfigDto.getId());
        orderDto.setAppActivityId(opId);
        orderDto.setActivityType(ActivityUniformityTypeEnum.SUPER_SURPRISE.getCode().toString());
        orderDto.setConsumeCredits(credits);

        orderDto.setConsumeCreditsStatus(credits == 0 ?ActivityOrderStutsEnum.CONSUME_CREDITS_SUCCESS.getCode():ActivityOrderStutsEnum.CONSUME_CREDITS_PROCESSING.getCode());
        // 初始化时设置发奖状态为0 此条订单可能会用于发奖
        orderDto.setExchangeStatus(ActivityOrderStutsEnum.EXCHANGE_NONE.getCode());

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        orderDto.setIp(RequestTool.getIpAddr(request));
        return  remoteActivityOrderService.createOrder(orderDto);
    }




}