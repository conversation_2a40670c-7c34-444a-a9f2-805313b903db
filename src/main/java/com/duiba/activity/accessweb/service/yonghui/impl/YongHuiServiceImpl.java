package com.duiba.activity.accessweb.service.yonghui.impl;

import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.common.center.api.dto.sharecode.ShareCodeTimesConfigDto;
import cn.com.duiba.activity.common.center.api.enums.ShareCodeActivityTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.sharecode.TimesConfigEnum;
import cn.com.duiba.activity.common.center.api.params.UserShareCodeParam;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteUserShareCodeService;
import cn.com.duiba.activity.common.center.api.rsp.sharecode.InviteResponseDto;
import cn.com.duiba.api.enums.RedisKeySpace;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByActivityRequest;
import cn.com.duiba.plugin.center.api.response.PlaceOrderResponse;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import com.alibaba.fastjson.JSON;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.activity.sharecode.ActivtyShareCodeService;
import com.duiba.activity.accessweb.service.plugin.ActivityPluginService;
import com.duiba.activity.accessweb.service.yonghui.YongHuiService;
import com.duiba.activity.accessweb.vo.activity.ActivityShareCodeResultVO;
import com.duiba.activity.accessweb.vo.yonghui.HelpRecordVO;
import com.duiba.activity.accessweb.vo.yonghui.YongHuiHelpHomeVO;
import com.duiba.activity.accessweb.vo.yonghui.YongHuiHelpResultVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/1/28 18:43
 */
@Service
public class YongHuiServiceImpl implements YongHuiService {

    private static final String HELPED = "1";

    @Autowired
    private RemoteUserShareCodeService remoteUserShareCodeService;

    @Autowired
    private ActivtyShareCodeService activtyShareCodeService;

    @Autowired
    private ActivityPluginService activityPluginService;

    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;

    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    @Autowired
    private GoodsCacheService goodsCacheService;

    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;

    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    private static final Logger logger = LoggerFactory.getLogger(YongHuiServiceImpl.class);

    @Override
    public YongHuiHelpResultVO getUserHelpLimitTimes(ConsumerDto consumer, Long activityId, Long duibaActivityId) {
        YongHuiHelpResultVO helpResult = new YongHuiHelpResultVO();
        Pair<Integer, Integer> limitTimes = getShareCodeUsedLimitTimes(consumer, activityId, duibaActivityId);
        helpResult.setInviteLimitTimes(limitTimes.getValue0());
        helpResult.setBeInvitedLimitTimes(limitTimes.getValue1());
        return helpResult;
    }

    @Override
    public YongHuiHelpHomeVO getHelpHome(ConsumerDto consumer, Long activityId, Long duibaActivityId) {
        YongHuiHelpHomeVO vo = new YongHuiHelpHomeVO();
        YongHuiHelpResultVO helpResult = getUserHelpLimitTimes(consumer, activityId, duibaActivityId);
        vo.setHelpLimitTimes(helpResult.getInviteLimitTimes());
        String helpStatus = advancedCacheClient.get(getHelpStatusRedisKey(consumer.getId()));
        Long dayShareTimes = redisAtomicClient.getLong(getDayShareTimesRedisKey(consumer.getId()));
        vo.setDayShareTimes(dayShareTimes == null ? 0 : dayShareTimes);
        if (StringUtils.isNotBlank(helpStatus) && helpStatus.startsWith(HELPED)) {
            // 查询到有助力记录后立马置0，防止下一次进入主页还有提示
            advancedCacheClient.set(getHelpStatusRedisKey(consumer.getId()), "0", 1, TimeUnit.DAYS);
            String[] cardType = helpStatus.split(",");
            String[] bonusCard = Arrays.copyOfRange(cardType, 1, cardType.length);
            List<YongHuiHelpHomeVO.InviteeHelp> bonusCards = Lists.newArrayList();
            for (String s : bonusCard) {
                YongHuiHelpHomeVO.InviteeHelp inviteeHelp = new YongHuiHelpHomeVO.InviteeHelp();
                inviteeHelp.setNickname(s.substring(0, s.indexOf(':')));
                inviteeHelp.setItemId(s.substring(s.indexOf(':') + 1));
                bonusCards.add(inviteeHelp);
            }
            vo.setBonusCard(bonusCards);
            vo.setHelpStatus(bonusCard.length > 0 ? 1 : 0);
        } else {
            vo.setHelpStatus(0);
        }
        return vo;
    }

    @Override
    public List<HelpRecordVO> getHelpRecord(Long inviteConsumerId) {
        /**
         * consumerId:cardId:System.currentTimeMillis()
         */
        String helpRecord = advancedCacheClient.get(getHelpRecordRedisKey(inviteConsumerId));
        if (StringUtils.isBlank(helpRecord)) {
            return Collections.emptyList();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd HH:mm");
        List<HelpRecordVO> resultList = Lists.newArrayList();
        //"consumerId:cardId:System.currentTimeMillis()"
        String[] helpRecordArray = helpRecord.split(",");
        for (String str : helpRecordArray) {
            String[] result = str.split(":");
            try {
                HelpRecordVO vo = new HelpRecordVO();
                vo.setConsumerId(Long.parseLong(result[0]));
                vo.setItemId(Long.parseLong(result[1]));
                vo.setDate(dateFormat.format(new Date(Long.parseLong(result[2]))));
                resultList.add(vo);
            } catch (NumberFormatException e) {
                logger.error("数据转换异常", e);
            }
        }
        resultList.sort((r1,r2) -> r2.getDate().compareToIgnoreCase(r1.getDate()));
        // 获取用户信息
        List<Long> ids = resultList.stream().map(HelpRecordVO::getConsumerId).collect(Collectors.toList());
        DubboResult<List<ConsumerExtraDto>> co = remoteConsumerExtraService.findAllByConsumerIds(ids);
        List<ConsumerExtraDto> ce = co.getResult();
        Map<Long, ConsumerExtraDto> consumerExtraDtoMap = Maps.uniqueIndex(ce, ConsumerExtraDto::getConsumerId);
        resultList.forEach(i -> {
            if (consumerExtraDtoMap.get(i.getConsumerId()) != null) {
                i.setAvatar(consumerExtraDtoMap.get(i.getConsumerId()).getAvatar());
                i.setNickname(consumerExtraDtoMap.get(i.getConsumerId()).getNickname());
            }
        });
        List<Long> itemIds = resultList.stream().map(HelpRecordVO::getItemId).collect(Collectors.toList());
        List<ItemDto> items = goodsCacheService.getItemsByIds(itemIds);
        Map<Long, ItemDto> itemBrief = Maps.uniqueIndex(items, ItemDto::getId);
        resultList.forEach(r -> {
            if (itemBrief.get(r.getItemId()) != null) {
                r.setPrizeName(itemBrief.get(r.getItemId()).getName());
            }
        });
        return resultList;
    }

    @Override
    public void submitHelpBonus(Long inviteConsumerId, Long consumerId, String bonusResult) {
        String nickname = "好友";
        DubboResult<ConsumerExtraDto> extResult = remoteConsumerExtraService.findByConsumerId(consumerId);
        if (extResult != null && extResult.isSuccess()) {
            nickname = extResult.getResult().getNickname();
        }
        String bonusCard = advancedCacheClient.get(getHelpStatusRedisKey(inviteConsumerId));
        if (bonusCard == null) {
            bonusCard = "1";
        }
        String helpRecord = advancedCacheClient.get(getHelpRecordRedisKey(inviteConsumerId));
        if (StringUtils.isNotBlank(bonusResult)) {
            Long cardId = JSON.parseObject(bonusResult).getLong("itemId");
            bonusCard = bonusCard.concat("," + nickname + ":" + cardId);
            String helpRecordResult = consumerId + ":" + cardId + ":" + System.currentTimeMillis();
            if (helpRecord == null) {
                advancedCacheClient.set(getHelpRecordRedisKey(inviteConsumerId), helpRecordResult, 10, TimeUnit.DAYS);
            } else {
                helpRecord = helpRecord.concat("," + helpRecordResult);
                advancedCacheClient.set(getHelpRecordRedisKey(inviteConsumerId), helpRecord, 10, TimeUnit.DAYS);
            }
        }
        advancedCacheClient.set(getHelpStatusRedisKey(inviteConsumerId), bonusCard, 10, TimeUnit.DAYS);
    }

    private String getHelpStatusRedisKey(Long consumerId) {
        return RedisKeySpace.K023.toString() + consumerId;
    }

    private String getHelpRecordRedisKey(Long consumerId) {
        return RedisKeyFactory.K098.toString() + consumerId;
    }

    private String getDayShareTimesRedisKey(Long consumerId) {
        return RedisKeyFactory.K097 + consumerId.toString();
    }


    private Pair<Integer, Integer> getShareCodeUsedLimitTimes(ConsumerDto consumer, Long activityId, Long duibaActivityId) {
        ActivityShareCodeResultVO shareCodeResult = activtyShareCodeService.getShareCode(consumer, activityId, ShareCodeActivityTypeEnum.HDTOOL.getCode());
        if(!shareCodeResult.getSuccess()) {
            return Pair.with(0, 0);
        }
        UserShareCodeParam param = new UserShareCodeParam();
        param.setActivityId(activityId);
        param.setAppId(consumer.getAppId());
        param.setConsumerId(consumer.getId());
        param.setShareCodeActivityTypeEnum(ShareCodeActivityTypeEnum.HDTOOL);
        try {
            InviteResponseDto resp = remoteUserShareCodeService.getShareCodeUsedTimes(param, shareCodeResult.getShareCode(), TimesConfigEnum.EVERYDAY_EVERYDAY);
            ShareCodeTimesConfigDto timesConfig = activtyShareCodeService.getShareCodeTimesConfig(duibaActivityId, ShareCodeActivityTypeEnum.HDTOOL.getCode());
            Integer[] limitTimes = new Integer[2];
            Optional.ofNullable(timesConfig.getInviteTimes()).ifPresent(e -> limitTimes[0] = (e - resp.getInviteTimes()));
            Optional.ofNullable(timesConfig.getBeInvitedTimes()).ifPresent(e -> limitTimes[1] = (e - resp.getBeInvitedTimes()));
            return Pair.fromArray(limitTimes);
        } catch (BizException e) {
            logger.error("通过邀请码获取邀请记录出错", e);
        }
        return Pair.with(0, 0);
    }

    @Override
    public String doJoin(RequestParams params, ConsumerDto consumer, Long activityId) throws BizException {
        ActivityPluginDto plugin = activityPluginService.findById(activityId);
        if (plugin == null) {
            throw new BizException(ResultCode.C100015.getDescription()).withCode(ResultCode.C100015.getCode());
        }
        if (!Objects.equals(plugin.getStatus(), ActivityPluginDto.STATUS_OPEN)) {
            throw new BizException(ResultCode.C100008.getDescription()).withCode(ResultCode.C100008.getCode());
        }
        PlaceOrderByActivityRequest activityRequest = new PlaceOrderByActivityRequest();
        activityRequest.setAppId(consumer.getAppId());
        activityRequest.setActivityId(activityId);
        activityRequest.setActivityType("plugdraw");
        activityRequest.setConsumerId(consumer.getId());
        activityRequest.setPartnerUserId(consumer.getPartnerUserId());
        activityRequest.setRequestParams(params);
        try {
            PlaceOrderResponse placeOrderResponse = remoteActivityOrderService.placeOrder(activityRequest);
            if(placeOrderResponse.isSuccess()){
                return placeOrderResponse.getOrderNum();
            }
        }catch (Exception e){
            logger.info("调用插件异常", e);
        }
        return null;
    }

}
