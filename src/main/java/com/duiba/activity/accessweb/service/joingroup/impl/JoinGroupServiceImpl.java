package com.duiba.activity.accessweb.service.joingroup.impl;

import cn.com.duiba.activity.center.api.dto.fakeuser.FakeUserDto;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupConfigDto;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupInfoDto;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupItemDto;
import cn.com.duiba.activity.center.api.dto.joingroup.JoinGroupRecordDto;
import cn.com.duiba.activity.center.api.enums.JoinGroupCheatTypeEnum;
import cn.com.duiba.activity.center.api.enums.JoinGroupStatusEnum;
import cn.com.duiba.activity.center.api.enums.LimitScopeEnum;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import cn.com.duiba.activity.center.api.params.JoinGroupRecordPageParam;
import cn.com.duiba.activity.center.api.remoteservice.fakeuser.RemoteFakeUserService;
import cn.com.duiba.activity.center.api.remoteservice.joingroup.RemoteJoinGroupInfoAppService;
import cn.com.duiba.activity.center.api.remoteservice.joingroup.RemoteJoinGroupItemAppService;
import cn.com.duiba.activity.center.api.remoteservice.joingroup.RemoteJoinGroupRecordAppService;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.util.ItemCommUtils;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByItemRequest;
import cn.com.duiba.plugin.center.api.response.PlaceOrderResponse;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.duiba.activity.accessweb.cache.ConsumerCacheService;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.constant.ShowConstant;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.enums.FollowOfficalAccountEnum;
import com.duiba.activity.accessweb.service.joingroup.JoinGroupService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.AssembleTool;
import com.duiba.activity.accessweb.tool.CookieUtil;
import com.duiba.activity.accessweb.tool.HttpUtil;
import com.duiba.activity.accessweb.tool.MoneyFormatUtil;
import com.duiba.activity.accessweb.vo.joingroup.GroupDetailVO;
import com.duiba.activity.accessweb.vo.joingroup.GroupItemDetailVO;
import com.duiba.activity.accessweb.vo.joingroup.GroupItemListVO;
import com.duiba.activity.accessweb.vo.joingroup.GroupItemVO;
import com.duiba.activity.accessweb.vo.joingroup.JoinGroupResultVO;
import com.duiba.activity.accessweb.vo.joingroup.MyGroupInfoVO;
import com.duiba.activity.accessweb.vo.joingroup.MyGroupListVO;
import com.duiba.activity.accessweb.vo.joingroup.OpenGroupResultVO;
import com.duiba.activity.accessweb.vo.joingroup.OtherGroupInfoVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 18/9/14
 * @description 0元拼团
 */
@Service
public class JoinGroupServiceImpl extends JoinGroupCacheUtil implements JoinGroupService {

	private static final Logger logger = LoggerFactory.getLogger(JoinGroupServiceImpl.class);

	@Resource
	private RemoteJoinGroupRecordAppService remoteJoinGroupRecordAppService;
	@Resource
	private RemoteJoinGroupItemAppService remoteJoinGroupItemAppService;
	@Resource
	private RemoteJoinGroupInfoAppService remoteJoinGroupInfoAppService;
	@Resource
	private RemoteConsumerService remoteConsumerService;
	@Resource
	private ConsumerCacheService consumerCacheService;
	@Resource
	private GoodsCacheService goodsCacheService;
	@Resource(name = "stringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RemoteFakeUserService remoteFakeUserService;
    @Resource
	private RemoteActivityOrderService remoteActivityOrderService;
	@Autowired
	private ShowConstant showConstant;
	@Autowired
	private RemoteItemKeyService remoteItemKeyService;
	@Autowired
	private RiskService riskService;

	Random random = new Random();

    @Override
    public List<Long> getItemIds(Long actListId) throws BizException {
        JoinGroupConfigDto joinGroupConfigDto = getConfigByActListId(actListId);
        if (joinGroupConfigDto == null) {
            throw new BizException("关联拼团活动配置不存在");
        }
        return joinGroupConfigDto.getJoinGroupItemList().stream().map(JoinGroupItemDto::getId).collect(Collectors.toList());
    }

    @Override
    public GroupItemListVO getItemList(Long appId, Long actListId, int pageNo, int pageSize) throws BizException {
	    JoinGroupConfigDto joinGroupConfigDto = getConfigByActListId(actListId);
	    if (joinGroupConfigDto == null) {
            throw new BizException("关联拼团活动配置不存在");
        }

		List<JoinGroupItemDto> joinGroupItemDtos = joinGroupConfigDto.getJoinGroupItemList();
        if (CollectionUtils.isEmpty(joinGroupItemDtos)) {
            throw new BizException(("未配置拼团商品"));
        }

        //分页
        int offset = pageSize * (pageNo - 1);
        if (offset >= joinGroupItemDtos.size()) {
            return null;
        }
        int endOffset = offset + pageSize;
        joinGroupItemDtos = joinGroupItemDtos.subList(offset, endOffset > joinGroupItemDtos.size() ? joinGroupItemDtos.size() : endOffset);

        //商品id - 商品信息，linkedHashMap保持原序
		Map<Long, ItemInfo> itemMap = new LinkedHashMap<>(joinGroupItemDtos.size());
	    //需要批量查询团信息的商品id列表
        List<Long> groupInfoQueryItemIds = new ArrayList<>(joinGroupItemDtos.size());
        //商品兑换项id - 商品id列表映射
        Map<Long, List<Long>> itemIdMap = new LinkedHashMap<>(joinGroupItemDtos.size());

        //获取拼团信息
		for (JoinGroupItemDto joinGroupItemDto : joinGroupItemDtos) {
            //预生成商品信息
            ItemInfo itemInfo = new ItemInfo();
            itemMap.put(joinGroupItemDto.getId(), itemInfo);
            itemInfo.joinGroupConfigDto = joinGroupConfigDto;
            itemInfo.joinGroupItemDto = joinGroupItemDto;

            //从缓存中获取
			List<JoinGroupInfoDto> joinGroupInfoDtos =
                    getGroupInfosByItemIdAndStatus(appId, joinGroupItemDto.getId(), JoinGroupStatusEnum.SUCCESS, false);
            //如果缓存不存在，则将商品id加入查询列表
            if (joinGroupInfoDtos == null) {
                groupInfoQueryItemIds.add(joinGroupItemDto.getId());
            } else {
                itemInfo.joinGroupInfoDtos = joinGroupInfoDtos;
            }
            itemIdMap.computeIfAbsent(joinGroupItemDto.getItemId(), k -> new ArrayList<>(5))
					.add(joinGroupItemDto.getId());
		}

        //查询兑换项商品信息
        findGroupInfos(appId, itemMap, itemIdMap, groupInfoQueryItemIds);

        //查询拼团用户信息
        findGroupUsers(appId, itemMap);

        GroupItemListVO vo = new GroupItemListVO();
        vo.setJoinGroupConfigId(joinGroupConfigDto.getId());
        vo.setRelateActList(joinGroupConfigDto.getRelateActList());
        vo.setRelateActDetail(joinGroupConfigDto.getRelateActDetail());
        vo.setJoinGroupItemList(itemMap.values().stream().map(ItemInfo::toItemVO).collect(Collectors.toList()));
        return vo;
	}

    /**
     * 查询兑换项商品信息
     *
     * @param itemMap
     * @param itemIdMap
     * @param groupInfoQueryItemIds
     */
    private void findGroupInfos(Long appId, Map<Long, ItemInfo> itemMap, Map<Long, List<Long>> itemIdMap, List<Long> groupInfoQueryItemIds) {
        if (!groupInfoQueryItemIds.isEmpty()) {
            List<JoinGroupInfoDto> joinGroupInfoDtos =
                    remoteJoinGroupInfoAppService.getByItemIdsAndStatus(appId, groupInfoQueryItemIds, JoinGroupStatusEnum.SUCCESS.getCode());
            //批量查询，遍历封装
            for (JoinGroupInfoDto joinGroupInfoDto : joinGroupInfoDtos) {
                itemMap.get(joinGroupInfoDto.getJoinGroupItemId()).joinGroupInfoDtos.add(joinGroupInfoDto);
            }
            //设置缓存
            for (Long itemId : groupInfoQueryItemIds) {
                setItemGroupInfosCache(appId, itemId, JoinGroupStatusEnum.SUCCESS, itemMap.get(itemId).joinGroupInfoDtos);
            }
        }

        //批量查询兑换项列表，遍历封装
        for (ItemDto itemDto : goodsCacheService.getItemsByIds(Lists.newArrayList(itemIdMap.keySet()))) {
            for (Long itemId : itemIdMap.get(itemDto.getId())) {
                itemMap.get(itemId).itemDto = itemDto;
            }
        }

    }

    /**
     * 查询拼团用户信息
     *
     * @param itemMap 商品信息映射表
     */
    private void findGroupUsers(Long appId, Map<Long, ItemInfo> itemMap) {
        //需要批量查询拼团记录的团信息id列表
        List<Long> recordQueryGroupInfoIds = new ArrayList<>(itemMap.size());
        //需要批量查询用户信息的团对应商品id列表
        List<Long> userQueryItemIds = new ArrayList<>(itemMap.size());
        for (Map.Entry<Long, ItemInfo> entry : itemMap.entrySet()) {
            Long itemId = entry.getKey();
            ItemInfo itemInfo = entry.getValue();
            List<JoinGroupInfoDto> groupInfoDtos = itemInfo.joinGroupInfoDtos;
            //从缓存中获取用户列表
            UserAdaptor[] userAdaptors = getUserAdaptorsByItemId(appId, itemId, false);
            //随机获取一个团id
            if (userAdaptors == null) {
                JoinGroupInfoDto joinGroupInfoDto = randomJoinGroupInfo(appId, groupInfoDtos);
                if (joinGroupInfoDto != null) {
                    recordQueryGroupInfoIds.add(joinGroupInfoDto.getId());
                    userQueryItemIds.add(itemId);
                }
            } else {
                itemInfo.userAdaptors = userAdaptors;
            }
        }

        //用户id - 商品id列表映射（同一个用户可能同时参与不同商品下的拼团）
        Map<Long, List<Long>> userItemsMap = Maps.newLinkedHashMap();
        //假用户id - 商品id列表映射（同一个用户可能同时参与不同商品下的拼团）
        Map<Long, List<Long>> fakeUserItemsMap = Maps.newLinkedHashMap();
        //商品id - 用户排序信息映射
        Map<Long, UserIndexHolder> itemUsersMap = new LinkedHashMap<>(itemMap.size());
        //按团信息id批量分组查询，遍历封装
        if (!recordQueryGroupInfoIds.isEmpty()) {
            List<JoinGroupRecordDto> joinGroupRecordDtos =
                    remoteJoinGroupRecordAppService.getByGroupIds(recordQueryGroupInfoIds, null);
            for (JoinGroupRecordDto joinGroupRecordDto : joinGroupRecordDtos) {
                boolean isFakeUser = Objects.equals(joinGroupRecordDto.getFakeType(), YesOrNoEnum.YES.getCode());
                Map<Long, List<Long>> tmp = isFakeUser ? fakeUserItemsMap : userItemsMap;
                tmp.computeIfAbsent(joinGroupRecordDto.getConsumerId(), k -> new ArrayList<>(5))
                        .add(joinGroupRecordDto.getJoinGroupItemId());
                itemUsersMap.computeIfAbsent(joinGroupRecordDto.getJoinGroupItemId(), k -> new UserIndexHolder())
                        .addUser(joinGroupRecordDto.getConsumerId(), isFakeUser);
            }
        }

        //初始化用户数组
        for (Map.Entry<Long, UserIndexHolder> entry : itemUsersMap.entrySet()) {
            itemMap.get(entry.getKey()).userAdaptors = new UserAdaptor[entry.getValue().size()];
        }

        //查找正常用户
        findGroupNormalUsers(itemMap, itemUsersMap, userItemsMap);
        //查找假用户
        findGroupFakeUsers(itemMap, itemUsersMap, fakeUserItemsMap);

        //设置缓存
        for (int i = 0, size = userQueryItemIds.size(); i < size; i++) {
            setGroupUserAdaptorsCache(recordQueryGroupInfoIds.get(i), JoinGroupStatusEnum.SUCCESS, itemMap.get(userQueryItemIds.get(i)).userAdaptors);
        }
    }

    /**
     * 查找正常用户
     *
     * @param itemMap
     * @param itemUsersMap
     * @param userItemsMap
     */
    private void findGroupNormalUsers(Map<Long, ItemInfo> itemMap, Map<Long, UserIndexHolder> itemUsersMap, Map<Long, List<Long>> userItemsMap) {
        //批量查找用户信息，遍历封装
        if (!userItemsMap.isEmpty()) {
            List<ConsumerExtraDto> consumerExtraDtos =
		            consumerCacheService.getConsumerExtraListCache(Lists.newArrayList(userItemsMap.keySet()));
            for (ConsumerExtraDto consumerExtraDto : consumerExtraDtos) {
                for (Long itemId : userItemsMap.get(consumerExtraDto.getConsumerId())) {
                    int index = itemUsersMap.get(itemId).getIndex(consumerExtraDto.getConsumerId(), false);
                    itemMap.get(itemId).userAdaptors[index] = new NormalUserAdaptor(consumerExtraDto);
                }
            }
        }
    }

    /**
     * 查找假用户
     *
     * @param itemMap
     * @param itemUsersMap
     * @param fakeUserItemsMap
     */
    private void findGroupFakeUsers(Map<Long, ItemInfo> itemMap, Map<Long, UserIndexHolder> itemUsersMap, Map<Long, List<Long>> fakeUserItemsMap) {
        //批量查找假用户信息，遍历封装
        if (!fakeUserItemsMap.isEmpty()) {
            List<FakeUserDto> fakeUserDtos =
                    remoteFakeUserService.getByIds(Lists.newArrayList(fakeUserItemsMap.keySet()));
            for (FakeUserDto fakeUserDto : fakeUserDtos) {
                for (Long itemId : fakeUserItemsMap.get(fakeUserDto.getId())) {
                    int index = itemUsersMap.get(itemId).getIndex(fakeUserDto.getId(), true);
                    itemMap.get(itemId).userAdaptors[index] = new FakeUserAdaptor(fakeUserDto);
                }
            }
        }
    }

	@Override
    public GroupItemDetailVO getItemDetail(Long appId, Long joinGroupItemId) throws BizException {
		JoinGroupItemDto joinGroupItemDto = getJoinGroupItemById(joinGroupItemId);
        if (joinGroupItemDto == null) {
			throw new BizException("指定奖品配置不存在");
		}

        JoinGroupConfigDto joinGroupConfigDto = getConfigById(joinGroupItemDto.getJoinGroupConfigId());
        if (joinGroupConfigDto == null) {
            throw new BizException("未找到对应的拼团配置信息");
        }

		ItemInfo itemInfo = new ItemInfo();
        itemInfo.joinGroupConfigDto = joinGroupConfigDto;
		itemInfo.joinGroupItemDto = joinGroupItemDto;
        itemInfo.joinGroupInfoDtos = getGroupInfosByItemIdAndStatus(appId, joinGroupItemId, JoinGroupStatusEnum.SUCCESS, true);
        itemInfo.userAdaptors = getUserAdaptorsByItemId(appId, joinGroupItemId, true);
		itemInfo.itemDto = goodsCacheService.findById(joinGroupItemDto.getItemId());
		return itemInfo.toItemDetailVO(showConstant.getShowAppId().equals(appId));
	}

	@Override
	public GroupDetailVO getGroupDetail(Long groupId, Long consumerId) throws BizException {
		JoinGroupInfoDto joinGroupInfoDto = getGroupById(groupId);
		if (joinGroupInfoDto == null) {
			throw new BizException("指定拼团不存在");
		}

		JoinGroupItemDto joinGroupItemDto = getJoinGroupItemById(joinGroupInfoDto.getJoinGroupItemId());
		if (joinGroupItemDto == null) {
			throw new BizException("未找到对应的拼团商品信息");
		}

        JoinGroupConfigDto joinGroupConfigDto = getConfigById(joinGroupItemDto.getJoinGroupConfigId());
        if (joinGroupConfigDto == null) {
            throw new BizException("未找到对应的拼团配置信息");
        }

        JoinGroupStatusEnum statusEnum = JoinGroupStatusEnum.getByCode(joinGroupInfoDto.getGroupStatus());
        //当状态为失败时更新数据库，加库存
        if (statusEnum == JoinGroupStatusEnum.UNDER_WAY
                && Objects.equals(joinGroupInfoDto.getCheatType(), YesOrNoEnum.NO.getCode())
                && joinGroupInfoDto.getEndTime().before(new Date())) {
            remoteJoinGroupInfoAppService.modifyToFailure(Collections.singletonList(groupId));
            statusEnum = JoinGroupStatusEnum.FAILURE;
            joinGroupInfoDto.setGroupStatus(statusEnum.getCode());
        }

		//判断是否独角秀
		Boolean isShow = showConstant.getShowAppId().equals(joinGroupInfoDto.getAppId());
		if(isShow && statusEnum == JoinGroupStatusEnum.UNDER_WAY
				&& Objects.equals(joinGroupInfoDto.getCheatType(), YesOrNoEnum.YES.getCode())
				&& joinGroupInfoDto.getEndTime().before(new Date())){
			remoteJoinGroupInfoAppService.modifyToFailure(Collections.singletonList(groupId));
			statusEnum = JoinGroupStatusEnum.FAILURE;
			joinGroupInfoDto.setGroupStatus(statusEnum.getCode());
		}

        GroupInfo groupInfo = new GroupInfo();
        groupInfo.joinGroupConfigDto = joinGroupConfigDto;
        groupInfo.joinGroupItemDto = joinGroupItemDto;
        groupInfo.joinGroupInfoDto = joinGroupInfoDto;
        groupInfo.itemDto = goodsCacheService.findById(joinGroupItemDto.getItemId());
        groupInfo.joinGroupRecordDtos = getGroupRecordsByGroupIdAndStatus(groupId, statusEnum, true);
        groupInfo.userAdaptors =
                JoinGroupStatusEnum.UNDER_WAY == statusEnum
                        ? getUserAdaptorsByRecords(groupInfo.joinGroupRecordDtos)
                        : getUserAdaptorsByGroupIdAndStatus(groupId, statusEnum, true);
		groupInfo.selfConsumerId = consumerId;
        return groupInfo.toGroupDetailVO(isShow);
    }

    private UserAdaptor[] getUserAdaptorsByItemId(Long appId, Long joinGroupItemId, boolean loadIfNull) {
        JoinGroupInfoDto joinGroupInfoDto = getRandomGroupByItemId(appId, joinGroupItemId, loadIfNull);
        if (joinGroupInfoDto == null) {
            return loadIfNull ? new UserAdaptor[0] : null;
        }
        return getUserAdaptorsByGroupIdAndStatus(joinGroupInfoDto.getId(), JoinGroupStatusEnum.SUCCESS, loadIfNull);
	}

    @Override
    public List<OtherGroupInfoVO> getOtherGroupList(Long actDetailId, Long appId, Long groupId)
			throws BizException {
		// 根据拼团活动id获取拼团活动配置信息
        JoinGroupConfigDto joinGroupConfigDto = getConfigByActDetailId(actDetailId);
		if(null == joinGroupConfigDto){
			throw new BizException("拼团活动配置不存在");
		}
		Integer otherGroupCount = null == joinGroupConfigDto.getOtherGroupCount() ? 10 : joinGroupConfigDto.getOtherGroupCount();
		// 根据拼团活动配置id批量获取n个进行中的拼团活动
        List<JoinGroupInfoDto> joinGroupList = getByAppConfigIdAndStatus(appId, joinGroupConfigDto.getId(),
				JoinGroupStatusEnum.UNDER_WAY.getCode(), otherGroupCount, groupId);
		if(CollectionUtils.isEmpty(joinGroupList)){
			return new ArrayList<>();
		}
		// 获取各个团已参与人数
		List<String> keys = new ArrayList<>();
		joinGroupList.forEach(joinGroupInfoDto -> {
            keys.add(keyForGroupJoinNumber(appId, joinGroupConfigDto.getId(), joinGroupInfoDto.getId()));
		});
		List<String> joinNumberList = stringRedisTemplate.opsForValue().multiGet(keys);
		// 获取各个团的团长头像
		Map<Long, String> consumerAvatarMap = getOwnerAvatarMap(joinGroupList);
		List<OtherGroupInfoVO> otherGroupInfoVOList = new ArrayList<>();
		for(int i = 0; i < joinGroupList.size(); i++){
			JoinGroupInfoDto joinGroup = joinGroupList.get(i);
			if(null == joinGroup.getGroupNumber()){
				logger.warn("团{}的成团人数为空了，团信息：{}", joinGroup.getId(), JSON.toJSON(joinGroup));
				throw new BizException("团数据异常");
			}
			// 获取已参团人数
			Integer joinNumber = StringUtils.isBlank(joinNumberList.get(i)) ? 0 : Integer.valueOf(joinNumberList.get(i));
			OtherGroupInfoVO otherGroupInfoVO = new OtherGroupInfoVO();
			otherGroupInfoVO.setGroupId(joinGroup.getId());
			otherGroupInfoVO.setRemainCount(joinGroup.getGroupNumber() - joinNumber);
            otherGroupInfoVO.setAvatar(consumerAvatarMap.get(joinGroup.getId()));
            otherGroupInfoVOList.add(otherGroupInfoVO);
		}
		return otherGroupInfoVOList;
	}

	// 获取各个团团长头像
	private Map<Long, String> getOwnerAvatarMap(List<JoinGroupInfoDto> joinGroupList){
    	if(CollectionUtils.isEmpty(joinGroupList)){
    		return new HashMap<>();
	    }
		List<Long> groupIdList = joinGroupList.stream().map(x->x.getId()).collect(Collectors.toList());
    	// 获取各个团的团长
		List<JoinGroupRecordDto> ownerList = getOwnersByGroupIds(groupIdList);
		if(CollectionUtils.isEmpty(ownerList)){
			return new HashMap<>();
		}
		List<Long> consumerIdList = ownerList.stream().map(x->x.getConsumerId()).collect(Collectors.toList());
		List<ConsumerExtraDto> consumerExtraList = consumerCacheService.getConsumerExtraListCache(consumerIdList);
		if(CollectionUtils.isEmpty(consumerExtraList)){
			return new HashMap<>();
		}
		Map<Long, String> consumerAvatarMap = new HashMap<>(consumerExtraList.size());
		consumerExtraList.forEach(consumerExtraDto -> {
			if(StringUtils.isNotBlank(consumerExtraDto.getAvatar())){
				consumerAvatarMap.put(consumerExtraDto.getConsumerId(), consumerExtraDto.getAvatar());
			}
		});
		if(MapUtils.isEmpty(consumerAvatarMap)){
			return new HashMap<>();
		}
		Map<Long, String> groupOwnerMap = new HashMap<>(ownerList.size());
		ownerList.forEach(joinGroupRecordDto -> {
			String avatar = consumerAvatarMap.get(joinGroupRecordDto.getConsumerId());
			if(StringUtils.isNotBlank(avatar)){
				groupOwnerMap.put(joinGroupRecordDto.getGroupInfoId(), avatar);
			}
		});
		return groupOwnerMap;
	}

    @Override
    public MyGroupListVO getGroupList(Long actListId, Integer pageNo, Integer pageSize, Long appId, Long consumerId) throws BizException {
		MyGroupListVO myGroupListVO = new MyGroupListVO();
        myGroupListVO.setRelateActList(actListId);
		// 根据自定义活动id获取关联的拼团活动信息
        JoinGroupConfigDto joinGroupConfigDto = getConfigByActListId(actListId);
		if(null == joinGroupConfigDto){
			throw new BizException("没有对应的拼团活动配置");
		}
		myGroupListVO.setJoinGroupConfigId(joinGroupConfigDto.getId());
		myGroupListVO.setRelateActDetail(joinGroupConfigDto.getRelateActDetail());
		// 根据拼团活动id和用户id分页获取用户的参团列表
		JoinGroupRecordPageParam joinGroupRecordPageParam = new JoinGroupRecordPageParam();
		joinGroupRecordPageParam.setAppId(appId);
		joinGroupRecordPageParam.setJoinGroupConfigId(joinGroupConfigDto.getId());
		joinGroupRecordPageParam.setConsumerId(consumerId);
		joinGroupRecordPageParam.setPageNum(pageNo);
		joinGroupRecordPageParam.setPageSize(pageSize);
		List<JoinGroupRecordDto> recordList = remoteJoinGroupRecordAppService.getPageByAppConfigIdAndUserId(joinGroupRecordPageParam);
		if(CollectionUtils.isEmpty(recordList)){
			return myGroupListVO;
		}
		// 根据团id list批量获取团信息
		List<Long> groupIdList = recordList.stream().map(x->x.getGroupInfoId()).collect(Collectors.toList());
		List<JoinGroupInfoDto> groupList = remoteJoinGroupInfoAppService.getByIdList(groupIdList);
		if(CollectionUtils.isEmpty(groupList)){
			throw new BizException("对应的拼团信息不存在");
		}
		// 将已过结束时间的进行中拼团，设为失败
	    List<Long> failureGroupIds = new ArrayList<>();
	    for(JoinGroupInfoDto joinGroupInfoDto: groupList){
	    	if(JoinGroupStatusEnum.UNDER_WAY.getCode().equals(joinGroupInfoDto.getGroupStatus()) && joinGroupInfoDto.getEndTime().before(new Date())){
	    		failureGroupIds.add(joinGroupConfigDto.getId());
			    joinGroupInfoDto.setGroupStatus(JoinGroupStatusEnum.FAILURE.getCode());
		    }
	    }
	    if(CollectionUtils.isNotEmpty(failureGroupIds)){
	    	remoteJoinGroupInfoAppService.modifyToFailure(failureGroupIds);
	    }
		// 组装返回对象
		myGroupListVO.setGroupList(getMyGroupInfoList(recordList, groupList));
		return myGroupListVO;
	}

	// 组装我的拼团列表
	private List<MyGroupInfoVO> getMyGroupInfoList(List<JoinGroupRecordDto> recordList, List<JoinGroupInfoDto> groupList) throws BizException{
		Map<Long, JoinGroupInfoDto> groupMap = groupList.stream().collect(Collectors.toMap(x->x.getId(), x->x));
		Map<Long, JoinGroupItemDto> joinGroupItemMap = new HashMap<>();
		Map<Long, ItemDto> itemIdMap = new HashMap<>();
		// 根据团商品id批量获取拼团商品信息
		List<Long> joinGroupItemIdList = groupList.stream().map(x->x.getJoinGroupItemId()).collect(Collectors.toList());
		List<JoinGroupItemDto> joinGroupItemList = getJoinGroupItemListByIds(joinGroupItemIdList);
		if(CollectionUtils.isNotEmpty(joinGroupItemList)){
			joinGroupItemMap = joinGroupItemList.stream().collect(Collectors.toMap(x->x.getId(), x->x));
			// 根据拼团商品批量获取兑换项的信息
			List<Long> itemIdList = joinGroupItemList.stream().map(x->x.getItemId()).collect(Collectors.toList());
			List<ItemDto> itemList = goodsCacheService.getItemsByIds(itemIdList);
			if(CollectionUtils.isNotEmpty(itemList)){
				itemIdMap = itemList.stream().collect(Collectors.toMap(x->x.getId(), x->x));
			}
		}
		List<MyGroupInfoVO> myGroupInfoVOList = new ArrayList<>();
		try{
			for(JoinGroupRecordDto joinGroupRecordDto: recordList){
				JoinGroupInfoDto joinGroup = groupMap.get(joinGroupRecordDto.getGroupInfoId());
				JoinGroupItemDto joinGroupItem = joinGroupItemMap.get(joinGroup.getJoinGroupItemId());
				if (null == joinGroupItem) {
					continue;
				}
				ItemDto item = itemIdMap.get(joinGroupItem.getItemId());

				String itemName = ObjectUtils.defaultIfNull(joinGroupItem.getItemName(), null != item ? item.getName() : null);
				String type = ObjectUtils.defaultIfNull(joinGroupItem.getPrizeType(), null != item ? item.getType() : null);

				MyGroupInfoVO myGroupInfoVO = new MyGroupInfoVO();
				myGroupInfoVO.setGroupId(joinGroup.getId());
				myGroupInfoVO.setItemName(itemName);
				myGroupInfoVO.setType(type);
				if (ItemCommUtils.isDegree(type)) { // 价格：市场价、优惠价
					// 如果是直充类，取拼团商品的facePrize
					myGroupInfoVO.setOriginalPrice(MoneyFormatUtil.parseLongToString(joinGroupItem.getFacePrice(), 2));
					myGroupInfoVO.setPreferentialPrice(MoneyFormatUtil.parseLongToString(joinGroupItem.getFacePrice(), 2));
				}else if(null != item){
					myGroupInfoVO.setImage(item.getSmallImage());  // 图片取缩略图
					// 非直充类，取兑换项的marketPrice和facePrize
					myGroupInfoVO.setOriginalPrice(MoneyFormatUtil.parseIntToString(item.getMarketPrice(), 2));
					myGroupInfoVO.setPreferentialPrice(MoneyFormatUtil.parseIntToString(item.getFacePrice(), 2));
				}
				myGroupInfoVO.setGroupStatus(joinGroup.getGroupStatus());
				myGroupInfoVO.setCanExchange(YesOrNoEnum.YES.getCode().equals(joinGroupRecordDto.getWinStatus()));
				myGroupInfoVO.setRemainTime(getRemainTime(joinGroup.getEndTime()));

				myGroupInfoVOList.add(myGroupInfoVO);
			}
		}catch (Exception e){
			logger.warn("0元拼团，我的拼团页，数据异常，拼团记录：{} ", JSON.toJSON(recordList), e);
			throw new BizException("数据异常");
		}
		return myGroupInfoVOList;
	}

	// 获取结束剩余时间时间戳
	private Long getRemainTime(Date endTime){
		Long nowMillis = Calendar.getInstance().getTimeInMillis();
		Long endTimeMillis = endTime.getTime();
		if(nowMillis.compareTo(endTimeMillis) >= 0){
			return 0L;
		}
		return endTimeMillis - nowMillis;
	}

    @Override
    public JoinGroupResultVO joinGroup(Long groupId, Long appId, ConsumerDto consumerDto) throws BizException {
        // 获取团信息
        JoinGroupInfoDto joinGroupInfoDto = getAndCheckGroupInfo(groupId);

	    AccessLogExUtil.putAccessLogExPair(true, 500, 2, PageBizTypeEnum.ACT_JOIN, joinGroupInfoDto.getJoinGroupConfigId(), null,
			    ActivityUniformityTypeEnum.Group, consumerDto.isNotLoginUser(),
			    consumerDto.getCredits(), null, null, null, null);
	    // 判断是否已参与该团
	    List<JoinGroupRecordDto> joinRecords = remoteJoinGroupRecordAppService.getByGroupIdAndConsumerId(groupId, consumerDto.getId());
	    if(CollectionUtils.isNotEmpty(joinRecords)){
	    	throw new BizException("你已参与过该团").withCode(ErrorCode.E0200105.getErrorCode());
	    }
		//独角秀定制逻辑
		if(showConstant.getShowAppId().equals(joinGroupInfoDto.getAppId())){
			//是否已经参与过其他团(非团长),不论是否进行中
			JoinGroupRecordPageParam joinGroupRecordPageParam = new JoinGroupRecordPageParam();
			joinGroupRecordPageParam.setConsumerId(consumerDto.getId());
			joinGroupRecordPageParam.setAppId(appId);
			joinGroupRecordPageParam.setJoinGroupConfigId(joinGroupInfoDto.getJoinGroupConfigId());
			joinGroupRecordPageParam.setOwnerType(YesOrNoEnum.NO.getCode());
			//查询出参与的记录(非团长)
			List<JoinGroupRecordDto> joinGroupRecordDtos = remoteJoinGroupRecordAppService.getPageByAppConfigIdAndUserId(joinGroupRecordPageParam);

			//已经参加过其他团
			if(CollectionUtils.isNotEmpty(joinGroupRecordDtos)){
				throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
			}
			joinGroupRecordPageParam.setOwnerType(YesOrNoEnum.YES.getCode());
			joinGroupRecordPageParam.setJoinGroupItemId(joinGroupInfoDto.getJoinGroupItemId());
			//查询出此商品开团的记录(团长)
			List<JoinGroupRecordDto> joinGroupItemRecordList = remoteJoinGroupRecordAppService.getPageByAppConfigIdAndUserId(joinGroupRecordPageParam);
			if(CollectionUtils.isNotEmpty(joinGroupItemRecordList)){
				throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
			}
		}
	    // 获取拼团活动配置
        Long joinAward = checkJoinConfig(joinGroupInfoDto.getJoinGroupConfigId());
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(null, joinGroupInfoDto.getJoinGroupConfigId(), ActivityUniformityTypeEnum.Group.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}

        // 判断参团人数是否已满
	    String joinNumberKey = keyForGroupJoinNumber(appId, joinGroupInfoDto.getJoinGroupConfigId(), groupId);
	    String joinNumberStr = stringRedisTemplate.opsForValue().get(joinNumberKey);
	    Integer joinNumber = StringUtils.isBlank(joinNumberStr) ? 0 : Integer.valueOf(joinNumberStr);
	    if (joinNumber.compareTo(joinGroupInfoDto.getGroupNumber()) >= 0) {
		    throw new BizException("团人数已满").withCode(ErrorCode.E0200104.getErrorCode());
	    }
	    // 根据拼团商品id获取拼团商品信息
	    JoinGroupItemDto joinGroupItemDto = checkJoinGroupItem(appId, joinGroupInfoDto.getJoinGroupItemId(), consumerDto.getId());
	    // 可参团，redis中参团人数加1
        stringRedisTemplate.opsForValue().increment(joinNumberKey, 1);
        Integer addNumber = 1;
        joinNumber = joinNumber + 1;
        Boolean addFakeUser = false;
        // 是否需要作弊
        if (Objects.equals(YesOrNoEnum.YES.getCode(), joinGroupInfoDto.getCheatType())
                && Objects.equals(joinNumber, joinGroupInfoDto.getCheatSequence() - 1)) {
            // 达到假用户序号，redis中参团人数+1，
            stringRedisTemplate.opsForValue().increment(joinNumberKey, 1);
            addNumber = 2;
            joinNumber = joinNumber + 1;
            addFakeUser = true;
        }
        // 增加用户参与记录
        try {
            addJoinGroupRecord(appId, consumerDto.getId(), joinGroupInfoDto, addFakeUser);
        } catch (BizException e) {
            // 回滚，参团人数扣减
            joinGroupRollBack(joinNumberKey, addNumber);
        } catch (Exception e){
        	logger.warn("参团异常：", e);
	        // 回滚，参团人数扣减
	        joinGroupRollBack(joinNumberKey, addNumber);
        }
        // 组装返回数据
        JoinGroupResultVO joinGroupResultVO = new JoinGroupResultVO();
        joinGroupResultVO.setJoinAward(joinAward);
        // 判断是否达到成团人数，如果达到成团人数，进行发奖
        if (Objects.equals(joinNumber, joinGroupInfoDto.getGroupNumber())) {
	        joinSuccessOpenPrize(joinGroupInfoDto, appId, joinGroupItemDto);
        }
        return joinGroupResultVO;
    }

	/**
	 * 增加参团记录失败，已参团人数回滚
	 */
	private void joinGroupRollBack(String joinNumberKey, Integer addNumber) throws BizException{
	    Long expiredTime = stringRedisTemplate.getExpire(joinNumberKey);
	    String newNumberStr = stringRedisTemplate.opsForValue().get(joinNumberKey);
	    Integer newJoinNumber = StringUtils.isBlank(newNumberStr) ? 0 : Integer.valueOf(newNumberStr) - addNumber;
	    stringRedisTemplate.opsForValue().set(joinNumberKey, String.valueOf(newJoinNumber), expiredTime, TimeUnit.MILLISECONDS);
	    throw new BizException("参团异常，请重试");
    }

    /**
     * 校验拼团状态
     */
    private JoinGroupInfoDto getAndCheckGroupInfo(Long groupId) throws BizException {
        JoinGroupInfoDto joinGroupInfoDto = getGroupById(groupId);
        if (null == joinGroupInfoDto) {
            throw new BizException("团不存在");
        }
        if (Objects.equals(JoinGroupStatusEnum.FAILURE.getCode(), joinGroupInfoDto.getGroupStatus())) {
            throw new BizException("团不是进行中状态").withCode(ErrorCode.E0200103.getErrorCode());
        }
        return joinGroupInfoDto;
    }

    /**
     * 校验拼团配置，并返回参团奖励
     */
    private Long checkJoinConfig(Long joinGroupConfigId) throws BizException {
        JoinGroupConfigDto joinGroupConfigDto = getConfigById(joinGroupConfigId);
        if (null == joinGroupConfigDto) {
            throw new BizException("拼团活动不存在");
        }
        if (!Objects.equals(YesOrNoEnum.YES.getCode(), joinGroupConfigDto.getActivityStatus())) {
            throw new BizException("拼团活动不可用");
        }
        return joinGroupConfigDto.getJoinAward();
    }

    /**
     * 校验拼团商品，并返回兑换项id
     */
    private JoinGroupItemDto checkJoinGroupItem(Long appId, Long joinGroupItemId, Long consumerId) throws BizException {
        JoinGroupItemDto joinGroupItemDto = getJoinGroupItemById(joinGroupItemId);
        if (null == joinGroupItemDto) {
            throw new BizException("拼团商品不存在");
        }
        // 获取用户该商品的参团记录，判断是否超过限制
        Date recordCreate = null;
        if (Objects.equals(LimitScopeEnum.EVERY_DAY.getId(), joinGroupItemDto.getJoinLimitType())) {
            recordCreate = DateUtils.getDayDate(new Date());
        }
	    Integer count = remoteJoinGroupRecordAppService.getCountByItemAndUserIdAndType(appId, joinGroupItemId, consumerId, YesOrNoEnum.NO.getCode(), recordCreate);
	    if(ObjectUtils.compare(count, joinGroupItemDto.getJoinLimit()) >= 0) {
		    if (Objects.equals(LimitScopeEnum.EVERY_DAY.getId(), joinGroupItemDto.getJoinLimitType())) {
			    throw new BizException(ErrorCode.E0200105.getDesc()).withCode(ErrorCode.E0200105.getErrorCode());
		    }else {
			    throw new BizException(ErrorCode.E0200107.getDesc()).withCode(ErrorCode.E0200107.getErrorCode());
		    }
	    }
	    if(remoteJoinGroupRecordAppService.checkHasUnderWayGroup(appId, joinGroupItemId, consumerId, YesOrNoEnum.NO.getCode())){
		    // 已参加该商品的团，不能同时参加多个
		    throw new BizException(ErrorCode.E0200109.getDesc()).withCode(ErrorCode.E0200109.getErrorCode());
	    }
        return joinGroupItemDto;
	}

	/**
	 * 拼团人数满了，发奖逻辑
	 */
	private void joinSuccessOpenPrize(JoinGroupInfoDto joinGroupInfoDto, Long appId, JoinGroupItemDto joinGroupItemDto) {
		// 获取所有参团信息
		List<JoinGroupRecordDto> joinGroupRecordDtoList = remoteJoinGroupRecordAppService.getByGroupId(joinGroupInfoDto.getId());
		Boolean isShow = showConstant.getShowAppId().equals(joinGroupInfoDto.getAppId());
		//独角秀定制逻辑
		Long ownerCid = null;
		if(isShow){
			//找出团长
			JoinGroupRecordDto joinGroupRecordDto = joinGroupRecordDtoList.stream().filter(x -> Objects.equals(YesOrNoEnum.YES.getCode(),x.getOwnerType())).findAny().orElse(null);
			ownerCid = joinGroupRecordDto.getConsumerId();
			//移除掉团长
			joinGroupRecordDtoList.remove(joinGroupRecordDto);
		}
		if(CollectionUtils.isEmpty(joinGroupRecordDtoList)){
			logger.warn("0元拼团-开奖异常，参团记录为空，团信息：{}", JSON.toJSON(joinGroupInfoDto));
			return;
		}
		JoinGroupRecordDto winRecord;
		String winOrderNum = null;
		if(YesOrNoEnum.YES.getCode().equals(joinGroupInfoDto.getCheatType())){
			// 如果作弊，中奖人为假用户，不真实出奖
			List<JoinGroupRecordDto> jakeRecords = joinGroupRecordDtoList.stream()
			                                                             .filter(x->YesOrNoEnum.YES.getCode().equals(x.getFakeType()))
			                                                             .collect(Collectors.toList());
			if(CollectionUtils.isEmpty(jakeRecords)){
				logger.warn("0元拼团-开奖异常，假用户为空，团信息：{}", JSON.toJSON(joinGroupInfoDto));
				return;
			}
			winRecord = jakeRecords.get(0);
			//独角兽的团长一定中奖
			if(isShow){
				openPrize(ownerCid, joinGroupItemDto, appId);
				ConsumerDto consumerDto = remoteConsumerService.find(ownerCid);
				notice(consumerDto.getPartnerUserId(),joinGroupItemDto.getItemName());
			}
		}else{
			// 如果不作弊，随机选出中奖人，让其中奖
			Integer winNumber = random.nextInt(joinGroupRecordDtoList.size());
			winRecord = joinGroupRecordDtoList.get(winNumber);
			winOrderNum = openPrize(winRecord.getConsumerId(), joinGroupItemDto, appId);
			//独角秀需要给团长也发奖
			if(isShow){
				openPrize(ownerCid, joinGroupItemDto, appId);
				List<Long> cids = Arrays.asList(ownerCid,winRecord.getConsumerId());
				List<ConsumerDto> consumerDtos = remoteConsumerService.findAllByIds(cids);
				List<String> uids = consumerDtos.stream().map(x -> x.getPartnerUserId()).collect(Collectors.toList());
				notice(String.join(",", uids),joinGroupItemDto.getItemName());
			}
		}
		// 更新中奖的参与记录状态
		remoteJoinGroupRecordAppService.modifyStatusForWin(winRecord.getId(), winOrderNum);
		// 更新团信息
		remoteJoinGroupInfoAppService.modifyToSuccess(joinGroupInfoDto.getId(), winRecord.getConsumerId());
	}

	private void notice(String uids,String itemName){
		//通知开发者消息
		Map<String,String> paramsMap = new HashMap<>();
		paramsMap.put("uids",uids);
		paramsMap.put("prizeInformation",itemName);
		String url = AssembleTool.assembleUrl(showConstant.getPrizeNoticeUrl(),paramsMap);
		String responseStr = HttpUtil.sendGet(url);
		logger.info("通知开发者开奖,url={},responseStr={}",url,responseStr);
		if(!"ok".equals(responseStr)){
			logger.warn("通知开发者开奖信息失败,url={},responseStr={}",url,responseStr);
		}
	}

	/**
	 * 发奖
	 */
	private String openPrize(Long winConsumerId, JoinGroupItemDto joinGroupItemDto, Long appId){
		ConsumerDto winConsumer = remoteConsumerService.find(winConsumerId);
		if(null == winConsumer){
			logger.warn("0元开团-开奖异常，获取中奖用户信息为空，中奖用户id：{}", winConsumerId);
			return null;
		}
		String winOrderNum = null;
		// 发奖
		PlaceOrderByItemRequest placeOrderByItemRequest = new PlaceOrderByItemRequest();
		placeOrderByItemRequest.setAppId(appId);
		placeOrderByItemRequest.setConsumerId(winConsumer.getId());
		placeOrderByItemRequest.setPartnerUserId(winConsumer.getPartnerUserId());
		placeOrderByItemRequest.setItemId(joinGroupItemDto.getItemId());
		placeOrderByItemRequest.setItemType(joinGroupItemDto.getPrizeType());
		if(ItemCommUtils.isDegree(joinGroupItemDto.getPrizeType())){
			placeOrderByItemRequest.setFacePrice(null != joinGroupItemDto.getFacePrice() ? joinGroupItemDto.getFacePrice().toString(): null);
		}
		try{
			PlaceOrderResponse placeOrderResponse = remoteActivityOrderService.placeOrder(placeOrderByItemRequest);
			if(!placeOrderResponse.isSuccess()){
				logger.warn("0元你开团-开奖失败，开奖返回信息：{}", JSON.toJSON(placeOrderResponse));
			}
			winOrderNum = placeOrderResponse.getOrderNum();
		}catch (BizException e){
			logger.warn("0元开团-开奖异常，中奖用户：{}", JSON.toJSON(winConsumer), e);
		}
		return winOrderNum;
	}

	@Override public OpenGroupResultVO openGroup(Long joinGroupItemId, Long appId, Long consumerId) throws BizException {
		// 根据拼团商品id获取拼团商品信息
		JoinGroupItemDto joinGroupItemDto = getJoinGroupItemById(joinGroupItemId);
		// 校验拼团商品、兑换项库存

		//需要扣减的库存
		Integer compareRemain = 1;
		//独角秀定制,扣减库存2
		boolean isShow = showConstant.getShowAppId().equals(appId);
		if (isShow){
			compareRemain = 2;
		}
		checkItem(appId,joinGroupItemDto,compareRemain);
		// 获取拼团活动配置信息
		Long joinGroupConfigId = joinGroupItemDto.getJoinGroupConfigId();
		JoinGroupConfigDto joinGroupConfigDto = getConfigById(joinGroupConfigId);
        if (!Objects.equals(YesOrNoEnum.YES.getCode(), joinGroupConfigDto.getActivityStatus())) {
            throw new BizException("拼团活动不是可用状态");
		}
		// 校验是否超过商品的开团限制
		checkOpenLimit(appId, consumerId, joinGroupItemDto);
        // 是否需要作弊
		Boolean needCheat = needCheat(joinGroupItemDto.getCheatRuleType(), joinGroupItemDto.getCheatPercent());
		//风控
		StormEngineResultDto riskReuslt = riskService.joinRisk(null, joinGroupConfigId, ActivityUniformityTypeEnum.Group.getCode());
		if(Objects.equals(riskReuslt.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskReuslt.getCopy());
		}
		Integer cheatSequence = null;
		if(needCheat){
			// 计算假用户插入位置序号
			cheatSequence = countCheatSequence(joinGroupItemDto.getGroupNumber());
   			if(isShow && null != joinGroupItemDto.getRemaining()){
				//扣去一个库存
				remoteJoinGroupItemAppService.modifyToSubRemaining(joinGroupItemId, 1);
			}
		}else if(null != joinGroupItemDto.getRemaining()){
			// 库存不是无限，预扣库存，商品剩余数量剪去相应的数量
			Integer subResult = remoteJoinGroupItemAppService.modifyToSubRemaining(joinGroupItemId,compareRemain);
			if(null == subResult || subResult.compareTo(0) <= 0){
				logger.info("0元拼团-开团，扣减商品{}数量失败", joinGroupItemId);
				throw new BizException("扣减商品数量失败");
			}
		}
		OpenGroupResultVO openGroupResultVO = new OpenGroupResultVO();
		try{
			// 新增团信息
			JoinGroupInfoDto joinGroupInfoDto = new JoinGroupInfoDto();
			joinGroupInfoDto.setAppId(appId);
			joinGroupInfoDto.setJoinGroupConfigId(joinGroupConfigId);
			joinGroupInfoDto.setJoinGroupItemId(joinGroupItemId);
			joinGroupInfoDto.setGroupNumber(joinGroupItemDto.getGroupNumber());
			joinGroupInfoDto.setGroupStatus(JoinGroupStatusEnum.UNDER_WAY.getCode());
			joinGroupInfoDto.setCheatType(needCheat ? YesOrNoEnum.YES.getCode(): YesOrNoEnum.NO.getCode());
			joinGroupInfoDto.setCheatSequence(cheatSequence);
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.SECOND, joinGroupItemDto.getExpiredTime().intValue());
			joinGroupInfoDto.setEndTime(calendar.getTime());
			Long groupId = remoteJoinGroupInfoAppService.add(joinGroupInfoDto);
			if(null == groupId){
				logger.info("0元拼团-开团，新增团信息失败，团信息：{}", JSON.toJSON(joinGroupConfigDto));
				throw new BizException("新增团信息失败");
			}
			openGroupResultVO.setJoinGroupInfoId(groupId);
			// 增加用户参团记录，并设为团长
			JoinGroupRecordDto joinGroupRecordDto = new JoinGroupRecordDto();
			joinGroupRecordDto.setAppId(appId);
			joinGroupRecordDto.setJoinGroupConfigId(joinGroupConfigId);
			joinGroupRecordDto.setJoinGroupItemId(joinGroupItemId);
			joinGroupRecordDto.setGroupInfoId(groupId);
			joinGroupRecordDto.setConsumerId(consumerId);
			joinGroupRecordDto.setOwnerType(YesOrNoEnum.YES.getCode());
			joinGroupRecordDto.setFakeType(YesOrNoEnum.NO.getCode());
			int addRecordResult = remoteJoinGroupRecordAppService.add(joinGroupRecordDto);
			if(addRecordResult <= 0){
				logger.info("0元拼团-开团，新增开团记录失败，开团记录：{}", JSON.toJSON(joinGroupRecordDto));
				throw new BizException("新增开团记录失败");
			}
			// redis中新增该团参与人数记录，过期时间为该团结束时间+24小时
			Long expiredTime = calendar.getTimeInMillis() + 24 * 60 * 60 * 1000;
			stringRedisTemplate.opsForValue().set(keyForGroupJoinNumber(appId, joinGroupConfigId, groupId), "1", expiredTime, TimeUnit.MILLISECONDS);
		}catch (BizException e){
			logger.warn("开团异常：", e);
			addRemaining(joinGroupItemId, compareRemain, isShow, needCheat);
			throw new BizException(e.getMessage());
		}
		openGroupResultVO.setOpenAward(joinGroupConfigDto.getOpenAward());
		return openGroupResultVO;
	}

	/**
	 * 加库存
	 * @param joinGroupItemId
	 * @param compareRemain
	 * @param isShow
	 * @param needCheat
     */
	private void addRemaining(Long joinGroupItemId, Integer compareRemain, boolean isShow, Boolean needCheat) {
		if(!needCheat){
            // 预扣库存加回来
            remoteJoinGroupItemAppService.modifyToAddRemaining(joinGroupItemId, compareRemain);
        }
		// 预扣库存加回来
		if(needCheat && isShow){
            remoteJoinGroupItemAppService.modifyToAddRemaining(joinGroupItemId, 1);
        }
	}

	/**
	 * 校验是否超过商品的开团限制
	 */
	private void checkOpenLimit(Long appId, Long consumerId, JoinGroupItemDto joinGroupItemDto) throws BizException{
		// 获取用户该商品的开团记录，判断是否超过限制
		Date recordCreate = null;
		if(Objects.equals(LimitScopeEnum.EVERY_DAY.getId(), joinGroupItemDto.getOpenLimitType())){
			recordCreate = DateUtils.getDayDate(new Date());
		}
		Integer count = remoteJoinGroupRecordAppService.getCountByItemAndUserIdAndType(appId, joinGroupItemDto.getId(), consumerId, YesOrNoEnum.YES.getCode(), recordCreate);
		if(ObjectUtils.compare(count, joinGroupItemDto.getOpenLimit()) >= 0){
			if(Objects.equals(LimitScopeEnum.EVERY_DAY.getId(), joinGroupItemDto.getOpenLimitType())){
				throw new BizException(ErrorCode.E0200102.getDesc()).withCode(ErrorCode.E0200102.getErrorCode());
			}else{
				throw new BizException(ErrorCode.E0200106.getDesc()).withCode(ErrorCode.E0200106.getErrorCode());
			}
		}
		if(remoteJoinGroupRecordAppService.checkHasUnderWayGroup(appId, joinGroupItemDto.getId(), consumerId, YesOrNoEnum.YES.getCode())){
			// 已开该商品的团，不能同时开多个
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		//独角秀开团判断
		if (showConstant.getShowAppId().equals(appId)
				&& remoteJoinGroupRecordAppService.
				checkHasUnderWayGroup(appId, joinGroupItemDto.getId(), consumerId, YesOrNoEnum.NO.getCode())){
			throw new BizException(ErrorCode.E0200109.getDesc()).withCode(ErrorCode.E0200109.getErrorCode());
		}
	}

	/**
	 * 判断是否需要作弊
	 */
	private Boolean needCheat(Integer cheatRuleType, Integer cheatPercent){
		if(Objects.equals(JoinGroupCheatTypeEnum.NONE.getCode(), cheatRuleType)){
			return false;
		}else if(Objects.equals(JoinGroupCheatTypeEnum.RANDOM_GROUP.getCode(), cheatRuleType)){
			if(null == cheatPercent){
				return false;
			}
			Integer number = random.nextInt(100) + 1;
			if(cheatPercent.compareTo(number) < 0){
				return false;
			}
		}
		return true;
	}

	/**
	 * 计算假用户插入位置
	 */
	private Integer countCheatSequence(Integer groupNumber){
		Integer total = groupNumber - 2;
		return random.nextInt(total) + 3;
	}

	/**
	 * 开团校验-校验拼团商品、兑换项
	 */
	private void checkItem(Long appId,JoinGroupItemDto joinGroupItemDto,Integer compareRemain) throws BizException{
		if(null == joinGroupItemDto){
			throw new BizException("拼团商品不存在");
		}
		if(null != joinGroupItemDto.getRemaining() && joinGroupItemDto.getRemaining().compareTo(compareRemain - 1) <= 0){ // 如果为null，则无数量限制
			logger.info("0元拼团-开团，商品剩余数量不够，商品信息：{}", JSON.toJSON(joinGroupItemDto));
			throw new BizException("商品剩余数量不够").withCode(ErrorCode.E0200101.getErrorCode());
		}

		if(ItemCommUtils.isDegree(joinGroupItemDto.getPrizeType())){
			// 直充类/多档位类型，不校验兑换项总库存
			return;
		}
		// 获取兑换项
		//DubboResult<ItemDto> itemDtoDubboResult = remoteDuibaItemGoodsService.find(joinGroupItemDto.getItemId());

		DubboResult<ItemKeyDto> dubboResult = remoteItemKeyService.findItemKey(null, joinGroupItemDto.getItemId(), appId);

		if(null == dubboResult || !dubboResult.isSuccess() || null == dubboResult.getResult()){
			throw new BizException("兑换项不存在");
		}
		ItemDto itemDto = dubboResult.getResult().getItem();
		if(!itemDto.isDegree() && (null == itemDto.getRemaining() || itemDto.getRemaining().compareTo(compareRemain - 1) <= 0)){
			// 如果不是直充类，判断兑换项总库存
			logger.info("0元拼团-开团，兑换项剩余总库存不够，商品信息：{}，兑换项信息：{}", JSON.toJSON(joinGroupItemDto), JSON.toJSON(itemDto));
			throw new BizException("兑换项剩余总库存不够").withCode(ErrorCode.E0200101.getErrorCode());
		}
	}

	private class ItemInfo {
		JoinGroupConfigDto joinGroupConfigDto;
        JoinGroupItemDto joinGroupItemDto;
        ItemDto itemDto;
        List<JoinGroupInfoDto> joinGroupInfoDtos = Lists.newArrayList();
        UserAdaptor[] userAdaptors = new UserAdaptor[0];

		GroupItemVO toItemVO() {
			GroupItemVO vo = new GroupItemVO();
			wrapperItemVO(vo,false);
			return vo;
		}

		GroupItemDetailVO toItemDetailVO(Boolean isShow) {
			GroupItemDetailVO vo = new GroupItemDetailVO();
			wrapperItemVO(vo,isShow);
			vo.setJoinGroupConfigId(joinGroupConfigDto.getId());
			if (itemDto != null) {
				vo.setDescription(itemDto.getDescription());
				if (!itemDto.isDegree()) {
					vo.setOriginalPrice(MoneyFormatUtil.parseIntToString(itemDto.getMarketPrice(), 2));
					vo.setPreferentialPrice(MoneyFormatUtil.parseIntToString(itemDto.getFacePrice(), 2));
				}
			}
			if (itemDto == null || itemDto.isDegree()) {
				vo.setOriginalPrice(MoneyFormatUtil.parseLongToString(joinGroupItemDto.getFacePrice(), 2));
				vo.setPreferentialPrice(MoneyFormatUtil.parseLongToString(joinGroupItemDto.getFacePrice(), 2));
			}
			return vo;
		}

		void wrapperItemVO(GroupItemVO vo,Boolean isDetail) {
            vo.setJoinGroupItemId(joinGroupItemDto.getId());
            vo.setItemId(joinGroupItemDto.getItemId());
            vo.setMemberCount(joinGroupInfoDtos.stream().mapToInt(JoinGroupInfoDto::getGroupNumber).sum());
			vo.setName(joinGroupItemDto.getItemName());
			//用户头像最多返回10个
			List<String> avatars = new ArrayList<>(10);
			for (UserAdaptor userAdaptor : userAdaptors) {
				if (userAdaptor != null && StringUtils.isNotBlank(userAdaptor.getAvatar())) {
					avatars.add(userAdaptor.getAvatar());
                    if (avatars.size() >= 10) {
						break;
					}
				}
			}
			vo.setMemberAvatarList(avatars);

            if (itemDto != null) {
                vo.setType(itemDto.getType());
                if (StringUtils.isBlank(vo.getName())) {
					vo.setName(itemDto.getName());
				}
                vo.setImage(isDetail ? itemDto.getMultiImage() : itemDto.getSmallImage());
            }
            if (joinGroupItemDto.getPrizeType() != null) {
                vo.setType(joinGroupItemDto.getPrizeType());
            }
		}

	}

    private class GroupInfo extends ItemInfo {
        JoinGroupInfoDto joinGroupInfoDto;
        List<JoinGroupRecordDto> joinGroupRecordDtos = Lists.newArrayList();
        Long ownerConsumerId;
        Long selfConsumerId;

		GroupDetailVO toGroupDetailVO(Boolean isShow) {
			GroupDetailVO vo = new GroupDetailVO();
			vo.setJoinGroupConfigId(joinGroupConfigDto.getId());
			vo.setJoinGroupItemId(joinGroupItemDto.getId());
			vo.setJoinGroupInfoId(joinGroupInfoDto.getId());
			vo.setItemId(joinGroupItemDto.getItemId());
			vo.setName(joinGroupItemDto.getItemName());
			if (itemDto != null) {
				if (StringUtils.isBlank(vo.getName())) {
					vo.setName(itemDto.getName());
				}
				vo.setType(itemDto.getType());
				vo.setImage(getImage(isShow));
				if (!itemDto.isDegree()) {
					vo.setOriginalPrice(MoneyFormatUtil.parseIntToString(itemDto.getMarketPrice(), 2));
					vo.setPreferentialPrice(MoneyFormatUtil.parseIntToString(itemDto.getFacePrice(), 2));
				}
			}
			if (itemDto == null || itemDto.isDegree()) {
				vo.setOriginalPrice(MoneyFormatUtil.parseLongToString(joinGroupItemDto.getFacePrice(), 2));
				vo.setPreferentialPrice(MoneyFormatUtil.parseLongToString(joinGroupItemDto.getFacePrice(), 2));
			}

			if (joinGroupItemDto.getPrizeType() != null) {
			    vo.setType(joinGroupItemDto.getPrizeType());
            }

			vo.setGroupNumber(joinGroupInfoDto.getGroupNumber());
            vo.setMemberCount(joinGroupRecordDtos.size());
			vo.setGroupStatus(joinGroupInfoDto.getGroupStatus());
            vo.setEndTime(joinGroupInfoDto.getEndTime().getTime() - new Date().getTime());
			vo.setRemainCount(joinGroupInfoDto.getGroupNumber() - joinGroupRecordDtos.size());
            vo.setWinConsumerId(joinGroupInfoDto.getWinConsumerId());
            //如果团长id不存在，则从记录列表中获取
            if (ownerConsumerId == null) {
                for (JoinGroupRecordDto joinGroupRecordDto : joinGroupRecordDtos) {
                    if (Objects.equals(joinGroupRecordDto.getOwnerType(), YesOrNoEnum.YES.getCode())) {
                        ownerConsumerId = joinGroupRecordDto.getConsumerId();
                        break;
                    }
                }
            }

			wrapConsumerInfo(vo);
			return vo;
		}

		private String getImage(Boolean isShow) {
			return isShow ? itemDto.getMultiImage() : itemDto.getSmallImage();
		}

		void wrapConsumerInfo(GroupDetailVO vo) {
            List<GroupDetailVO.MemberInfo> memberInfos = new ArrayList<>(userAdaptors.length);
            int index = 0;
			for (UserAdaptor userAdaptor : userAdaptors) {
				GroupDetailVO.MemberInfo memberInfo = new GroupDetailVO.MemberInfo();
				Long consumerId = null;
                if (userAdaptor != null) {
                    consumerId = userAdaptor.getConsumerId();
                    memberInfo.setAvatar(userAdaptor.getAvatar());
                    if (Objects.equals(consumerId, joinGroupInfoDto.getWinConsumerId())) {
                        vo.setWinConsumerName(userAdaptor.getNickname());
                        vo.setWinConsumerAvatar(userAdaptor.getAvatar());
                    }
                } else if (userAdaptors.length == joinGroupRecordDtos.size()){
                    consumerId = joinGroupRecordDtos.get(index).getConsumerId();
                }
                memberInfo.setConsumerId(consumerId);
                memberInfo.setOwner(Objects.equals(consumerId, ownerConsumerId));
                memberInfo.setSelf(Objects.equals(consumerId, selfConsumerId));
                if (memberInfo.getSelf()) {
                    vo.setJoined(true);
                }
                memberInfos.add(memberInfo);
                index++;
			}
			vo.setMemberAvatarList(memberInfos);
		}
    }

    /**
     * 参团，增加参团记录
     */
    private void addJoinGroupRecord(Long appId, Long consumerId, JoinGroupInfoDto joinGroupInfoDto, Boolean addFakeUser) throws BizException {
        JoinGroupRecordDto fakeUserRecord = null;
        if (addFakeUser) {
            fakeUserRecord = new JoinGroupRecordDto();
            fakeUserRecord.setAppId(appId);
            fakeUserRecord.setJoinGroupConfigId(joinGroupInfoDto.getJoinGroupConfigId());
            fakeUserRecord.setJoinGroupItemId(joinGroupInfoDto.getJoinGroupItemId());
            fakeUserRecord.setGroupInfoId(joinGroupInfoDto.getId());
            FakeUserDto fakeUserDto = remoteFakeUserService.getByBizId(joinGroupInfoDto.getId());
            if (null == fakeUserDto) {
                logger.info("参团获取假用户异常，假用户为空了");
                throw new BizException("参团异常，请重试");
            }
            fakeUserRecord.setConsumerId(fakeUserDto.getId());
            fakeUserRecord.setOwnerType(YesOrNoEnum.NO.getCode());
            fakeUserRecord.setFakeType(YesOrNoEnum.YES.getCode());
        }
        JoinGroupRecordDto joinGroupRecordDto = new JoinGroupRecordDto();
        joinGroupRecordDto.setAppId(appId);
        joinGroupRecordDto.setJoinGroupConfigId(joinGroupInfoDto.getJoinGroupConfigId());
        joinGroupRecordDto.setJoinGroupItemId(joinGroupInfoDto.getJoinGroupItemId());
        joinGroupRecordDto.setGroupInfoId(joinGroupInfoDto.getId());
        joinGroupRecordDto.setConsumerId(consumerId);
        joinGroupRecordDto.setOwnerType(YesOrNoEnum.NO.getCode());
        joinGroupRecordDto.setFakeType(YesOrNoEnum.NO.getCode());
        remoteJoinGroupRecordAppService.add(joinGroupRecordDto);
        if (null != fakeUserRecord) {
            remoteJoinGroupRecordAppService.add(fakeUserRecord);
        }
    }
}
