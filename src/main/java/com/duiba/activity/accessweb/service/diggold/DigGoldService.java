package com.duiba.activity.accessweb.service.diggold;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.vo.diggold.DigGoldIndexVO;
import com.duiba.activity.accessweb.vo.diggold.DigGoldTakePrizeVO;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: lufeng
 * @Description: 挖金矿活动服务接口
 * @Date: Created in 2019/7/4
 */
public interface DigGoldService {

    /**
     * 通过活动主键id查找活动首页信息
     * @param id
     * @return
     */
    DigGoldIndexVO getConfigAndPrizes(Long id, AppSimpleDto appSimpleDto) throws BizException;

    /**
     * 扣积分接口，返回订单号
     * @param id
     * @param consumerDto
     * @return
     */
    String doJoin(HttpServletRequest httpServletRequest, Long id, ConsumerDto consumerDto, AppSimpleDto appSimpleDto) throws BizException;

    /**
     * 查询扣积分结果
     * @param orderNum
     * @return
     * @throws BizException
     */
    Integer getOrderStatus(Long id, String orderNum) throws BizException;

    /**
     * 提交分数
     * @param id
     * @param consumerDto
     * @param orderNum
     * @param score
     * @param sign
     * @return
     */
    boolean doSubmit(Long id, ConsumerDto consumerDto, String orderNum, Integer score, String sign, AppSimpleDto appSimpleDto, HttpServletRequest request, OperatingActivityDto operatingActivityDto) throws Exception;

    /**
     * 获取出奖结果
     * @param orderNum
     * @return
     */
    DigGoldTakePrizeVO getPrizeResult(Long id, String orderNum, Long appId) throws BizException;

}
