package com.duiba.activity.accessweb.service.handler.sharecode;

import cn.com.duiba.activity.common.center.api.enums.sharecode.RuleTypeEnum;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.ShuqiConstant;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.service.AppInfoService;
import com.duiba.activity.accessweb.service.activity.sharecode.ResolveParameter;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.util.Collections;
import java.util.Map;

/**
 * Created by fangdong on 2019/03/14
 */
@Service
public class ShuqiReadCardBonusRuleHandler implements InitializingBean, BonusRuleHandler {
    private static final Logger log = LoggerFactory.getLogger(ShuqiReadCardBonusRuleHandler.class);

    @Resource
    private AppInfoService appInfoService;
    @Resource
    private ShuqiConstant shuqiConstant;
    @Resource
    private RemoteConsumerService remoteConsumerService;
    @Resource
    private RestTemplateBuilder restTemplateBuilder;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        BonusRuleManager.register(this);
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.SHUQI_READ_CARD.getCode();
    }

    @Override
    public Result<String> resolve(ResolveParameter param) {
        if (null == param || null == param.getConsumerId()) {
            return ResultBuilder.fail("奖励用户为空");
        }

        try {
            return this.doResolve(param);
        } catch (AccessActivityRuntimeException e) {
            log.warn("书旗阅读卡发奖异常, param={}", JSON.toJSONString(param), e);
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e) {
            log.error("书旗阅读卡发奖异常, param={}", JSON.toJSONString(param), e);
            return ResultBuilder.fail("书旗阅读卡发奖未知异常");
        }
    }

    /**
     * 发奖
     * @param resolveParameter 参数
     * @return 发奖结果
     */
    private Result<String> doResolve(ResolveParameter resolveParameter) {
        AppSimpleDto app = appInfoService.findAppByAppId(shuqiConstant.getShuqiAppId());
        if (app == null) {
            throw new AccessActivityRuntimeException("书旗app不存在");
        }

        if (SpringEnvironmentUtils.isDevEnv() || SpringEnvironmentUtils.isTestEnv()) {
            return ResultBuilder.success();
        }

        // 获取接口请求参数
        MultiValueMap<String, String> requestParam = this.getRequestParam(resolveParameter, app);

        // 调用开发者接口发阅读卡
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(shuqiConstant.getShuqiReadCardSendUrl()).queryParams(requestParam);
        String responseStr = restTemplate.getForObject(uriBuilder.toUriString(), String.class, requestParam);
        JSONObject response = JSON.parseObject(responseStr);

        String status = response.getString("status");
        if (!"ok".equalsIgnoreCase(status) && !"success".equalsIgnoreCase(status)) {
            String errorMessage = response.getString("errorMessage");
            log.error("书旗发阅读卡接口调用失败, param={}, response={}, msg={}", requestParam, response, errorMessage);
            throw new AccessActivityRuntimeException("书旗发阅读卡接口调用失败, " + StringUtils.left(errorMessage, 50));
        }

        JSONObject data = new JSONObject();
        data.put("bizId", data.getString("supplierBizId"));
        return ResultBuilder.success(data.toString());
    }

    /**
     * 获取发阅读卡接口请求参数
     * @return 请求参数
     */
    private MultiValueMap<String, String> getRequestParam(ResolveParameter resolveParameter, AppSimpleDto app) {
        MultiValueMap<String, String> requestParam = new LinkedMultiValueMap<>();

        Map<String, String> signParam = Maps.newHashMap();
        String uid = remoteConsumerService.findPartnerUserIdByConsumerId(app.getId(), resolveParameter.getInviter());
        if (StringUtils.isEmpty(uid)) {
            log.error("用户不存在, cid={}", resolveParameter.getInviter());
            throw new AccessActivityRuntimeException("用户不存在, cid=" + resolveParameter.getInviter());
        }
        signParam.put("uid", uid);
        signParam.put("appKey", app.getAppKey());
        signParam.put("appSecret", app.getAppSecret());
        signParam.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        signParam.put("orderNum", String.valueOf(resolveParameter.getInviterBizId()));
        signParam.put("sign", SignTool.sign(signParam));


        for (Map.Entry<String, String> entry : signParam.entrySet()) {
            requestParam.put(entry.getKey(), Collections.singletonList(entry.getValue()));
        }
        requestParam.remove("appSecret");

        return requestParam;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.restTemplate = restTemplateBuilder.setConnectTimeout(5000).setReadTimeout(5000).build();
    }
}
