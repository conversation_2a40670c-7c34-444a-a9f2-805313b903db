package com.duiba.activity.accessweb.service.iqiyi;

import cn.com.duiba.activity.center.api.dto.iqiyi.HappyCodeDto;
import cn.com.duiba.boot.exception.BizException;
import com.duiba.activity.accessweb.vo.iqiyi.FutureHappyRecordVO;
import com.duiba.activity.accessweb.vo.iqiyi.MyHappyCodeRecordVO;
import com.duiba.activity.accessweb.vo.iqiyi.MyHappyCodeWithPhaseVO;
import com.duiba.activity.accessweb.vo.iqiyi.NowHappyRecordVO;
import com.duiba.activity.accessweb.vo.iqiyi.OldHappyRecordVO;
import com.duiba.activity.accessweb.vo.iqiyi.PopupVO;

import java.util.List;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2019/11/18
 */
public interface HappyCodeService {

    Integer myCardNum(Long consumerId);

    /**
     * 查询往期记录
     * @returns
     */
    List<OldHappyRecordVO> listOldHappyRecord(Long appId);

    /**
     * 查询当前记录
     * @returns
     */
    List<NowHappyRecordVO> listNowHappyRecord(Long appId);

    /**
     * 查询预告记录
     * @returns
     */
    List<FutureHappyRecordVO> listFutureHappyRecord(Long appId);

    /**
     * 参与夺宝
     * @param appId
     * @param itemId
     * @return
     * @throws BizException
     */
    String doJoin(Long appId, Long consumerId, Long itemId, Long activityId, String partnerUserId) throws BizException;

    /**
     * 我的夺宝码
     * @param appId
     * @return
     */
    List<MyHappyCodeWithPhaseVO> myHappyCode(Long appId, Long consumerId);

    /**
     * 我的夺宝记录
     * @param appId
     * @param consumerId
     * @return
     */
    List<MyHappyCodeRecordVO> myHappyCodeRecord(Long appId, Long consumerId);

    /**
     * 上一次开奖接口弹窗
     * @param appId
     * @param consumerId
     * @return
     */
    PopupVO popup(Long appId, Long consumerId);
}
