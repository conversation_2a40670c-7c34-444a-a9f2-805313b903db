package com.duiba.activity.accessweb.service.impl;

import cn.com.duiba.activity.center.api.dto.activity.MarketingHdtoolPrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.centscan.CentscanPrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldConfigDto;
import cn.com.duiba.activity.center.api.dto.diggold.DigGoldRecordDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersDto;
import cn.com.duiba.activity.center.api.dto.guess.GuessOrdersDto;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleConfigDto;
import cn.com.duiba.activity.center.api.dto.haggle.HaggleOpenRecordDto;
import cn.com.duiba.activity.center.api.dto.haggle.HagglePrizeDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOrderDto;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeItemBasicDto;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeItemPhaseDto;
import cn.com.duiba.activity.center.api.dto.happycodenew.HappyCodeUserRecordDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.hsbc.HsbcChanceOrderDto;
import cn.com.duiba.activity.center.api.dto.luckycode.LuckyCodeUserAwardRecordDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersDto;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.RankListProvidePrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.superSurprise.SuperSurpriseJoinRecordDto;
import cn.com.duiba.activity.center.api.dto.tlcb.TaskRecordDto;
import cn.com.duiba.activity.center.api.dto.underseagame.UnderseaGamePrizeRecordDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandLevelAssistRecordDto;
import cn.com.duiba.activity.center.api.dto.understandlevel.UnderstandLevelConfigDto;
import cn.com.duiba.activity.center.api.enums.LuckyCodeUserAwardStateEnum;
import cn.com.duiba.activity.center.api.enums.diggold.RecordExchangeStatusEnum;
import cn.com.duiba.activity.center.api.enums.haggle.RecordStatusEnum;
import cn.com.duiba.activity.center.api.enums.happycodenew.HappyRewardStatusEnum;
import cn.com.duiba.activity.center.api.enums.hsbc.HsbcChanceOrderUseStatusEnum;
import cn.com.duiba.activity.center.api.enums.tlcb.TaskStatusEnum;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteMarketingHdtoolPrizeRecordService;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.centscan.RemoteCentscanPrizeRecordService;
import cn.com.duiba.activity.center.api.remoteservice.diggold.RemoteDigGoldService;
import cn.com.duiba.activity.center.api.remoteservice.game.RemoteDuibaQuestionAnswerOrdersService;
import cn.com.duiba.activity.center.api.remoteservice.guess.RemoteGuessOrdersConsumerService;
import cn.com.duiba.activity.center.api.remoteservice.haggle.RemoteHaggleService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeOrderService;
import cn.com.duiba.activity.center.api.remoteservice.happycodenew.RemoteHappyCodeItemBasicService;
import cn.com.duiba.activity.center.api.remoteservice.happycodenew.RemoteHappyCodeItemPhaseService;
import cn.com.duiba.activity.center.api.remoteservice.happycodenew.RemoteHappyCodeUserRecordService;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteCreditsHdtoolOrdersService;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteDuibaHdtoolServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.hsbc.RemoteHsbcChanceOrderService;
import cn.com.duiba.activity.center.api.remoteservice.luckycode.RemoteLuckyCodeUserAwardRecordService;
import cn.com.duiba.activity.center.api.remoteservice.ngame_con.RemoteNgameOrdersConsumerService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrderTextChangeService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersSimpleService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersStatusChangeService;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryOrderService;
import cn.com.duiba.activity.center.api.remoteservice.superSurprise.RemoteSuperSurpriseService;
import cn.com.duiba.activity.center.api.remoteservice.tlcb.RemoteTaiLongTaskUnitService;
import cn.com.duiba.activity.center.api.remoteservice.underseagame.RemoteUnderseaGamePrizeRecordService;
import cn.com.duiba.activity.center.api.remoteservice.understandlevel.RemoteUnderstandLevelService;
import cn.com.duiba.anticheat.center.api.constant.RiskConstant;
import cn.com.duiba.anticheat.center.api.enums.ActRiskSenceEnum;
import cn.com.duiba.anticheat.center.api.enums.ActivityPosEnum;
import cn.com.duiba.anticheat.center.api.enums.RiskRoutBizEnum;
import cn.com.duiba.anticheat.center.api.enums.RuleSceneEnum;
import cn.com.duiba.anticheat.center.api.model.DuibaActivityModel;
import cn.com.duiba.anticheat.center.api.param.RiskRuleEngineParam;
import cn.com.duiba.anticheat.center.api.remoteservice.rules.RemoteDuibaAntiService;
import cn.com.duiba.api.bo.KeyValueDto;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PrizeTypeEnum;
import cn.com.duiba.api.enums.prize.PrizeRelTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ReceiveAddressDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteCustomAddressService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteReceiveAddressService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DeveloperDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteDeveloperService;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.DuibaStormEngineDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.remoteservice.RemoteStormrageEngineService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteAddrLimitService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteGoodsAppItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsBatchDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsCouponDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemBaseDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.enums.VirtualAccountFormatEnum;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemCouponGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemNewExtraService;
import cn.com.duiba.goods.center.api.remoteservice.tool.ItemKeyUtils;
import cn.com.duiba.goods.center.api.remoteservice.tool.UnitUtils;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.dto.RequestParams;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.paycenter.dto.DuibaRemainingMoneyDto;
import cn.com.duiba.paycenter.dto.RemainingMoneyDto;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaRemainingMoneyService;
import cn.com.duiba.paycenter.remoteservice.RemoteRemainingMoneyService;
import cn.com.duiba.prize.center.api.enums.ItemNewExtraEnum;
import cn.com.duiba.sign.center.api.dto.SignActivityDto;
import cn.com.duiba.sign.center.api.dto.SignOperatingDto;
import cn.com.duiba.sign.center.api.remoteservice.creditssign.RemoteSignActivityService;
import cn.com.duiba.sign.center.api.remoteservice.creditssign.RemoteSignOperatingService;
import cn.com.duiba.thirdparty.api.hsbc.RemoteHsbcBankServcie;
import cn.com.duiba.thirdparty.dto.hsbc.param.HsbcMatchPhoneParam;
import cn.com.duiba.tuia.union.star.center.api.remoteservice.domain.rsp.MobileUaInfoDTO;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.config.AbcConfig;
import com.duiba.activity.accessweb.config.CcbConfig;
import com.duiba.activity.accessweb.config.CommConfig;
import com.duiba.activity.accessweb.config.HsbcConfig;
import com.duiba.activity.accessweb.config.QingTingFmConfig;
import com.duiba.activity.accessweb.config.YspObjectCheckConfig;
import com.duiba.activity.accessweb.config.ZhrsConfig;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.RefreshConstant;
import com.duiba.activity.accessweb.enums.superSurprise.RecordExchangeStatus;
import com.duiba.activity.accessweb.exception.AccessActivityException;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.exception.StatusException;
import com.duiba.activity.accessweb.service.ActivityTakePrizeService;
import com.duiba.activity.accessweb.service.ActualPriceCalculateService;
import com.duiba.activity.accessweb.service.AlipayTakePrizeCheckService;
import com.duiba.activity.accessweb.service.AppRequestService;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.abc.ABCBankService;
import com.duiba.activity.accessweb.service.activity.happycode.ActivityHappyCodeService;
import com.duiba.activity.accessweb.service.hdtool.service.ItemCheckService;
import com.duiba.activity.accessweb.service.limit.AnticheatExchangeLimitService;
import com.duiba.activity.accessweb.service.order.AlipayOfficialCreator;
import com.duiba.activity.accessweb.service.order.CouponCreator;
import com.duiba.activity.accessweb.service.order.ObjectCreator;
import com.duiba.activity.accessweb.service.order.ObjectCreator.ObjectAddress;
import com.duiba.activity.accessweb.service.order.PhonebillCreator;
import com.duiba.activity.accessweb.service.order.QBCreator;
import com.duiba.activity.accessweb.service.order.VirtualCreator;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.verification.Impl.ObjectVerificationService;
import com.duiba.activity.accessweb.service.wxcoupon.WxFavorCouponService;
import com.duiba.activity.accessweb.tool.CatLogTool;
import com.duiba.activity.accessweb.tool.CookieUtil;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.Environment;
import com.duiba.activity.accessweb.tool.InnerLogUtil;
import com.duiba.activity.accessweb.tool.IqiyiRiskTool;
import com.duiba.activity.accessweb.tool.PatternValidator;
import com.duiba.activity.accessweb.util.StormrageUtil;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.duiba.activity.accessweb.vo.TakePrizeVo;
import com.duiba.activity.accessweb.vo.zhrs.ZhrsTaskPrizeRequest;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hazelcast.util.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * ClassName:ActivityTakePrizeServiceImpl.java <br/>
 * <AUTHOR>
 * @date 创建时间：2016年12月16日 下午5:16:08
 * @version 1.0
 * @parameter
 * @since   JDK 1.6
 */
@Service
public class ActivityTakePrizeServiceImpl implements ActivityTakePrizeService {

	private static Logger log = LoggerFactory.getLogger(ActivityTakePrizeServiceImpl.class);
	private final static String SWITCH_OPEN = "1";
	private final static String SWITCH_CLOSE = "0";

	@Autowired
	private RemoteCreditsHdtoolOrdersService remoteCreditsHdtoolOrdersService;

	@Resource
	private RemoteHsbcChanceOrderService remoteHsbcChanceOrderService;
	@Autowired
	private ActualPriceCalculateService actualPriceCalculateService;
	@Autowired
	private RemoteRemainingMoneyService remoteRemainingMoneyService;
	@Autowired
	private RemoteDuibaRemainingMoneyService remoteDuibaRemainingMoneyService;
	@Autowired
	private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
	@Autowired
	private RemoteDeveloperService remoteDeveloperService;
	@Autowired
	private AnticheatExchangeLimitService anticheatExchangeLimitService;
	@Autowired
	private VirtualCreator virtualCreator;
	@Autowired
	private CouponCreator couponCreator;
	@Autowired
	private ObjectCreator objectCreator;
	@Autowired
	private AlipayOfficialCreator alipayOfficialCreator;
	@Autowired
	private PhonebillCreator phonebillCreator;
	@Autowired
	private QBCreator qBCreator;
	@Autowired
	private RemoteActivityOrderService remoteActivityOrderService;
	@Autowired
	private CommonService commonService;
	@Autowired
	private AppRequestService appRequestService;
	@Autowired
	private RemoteItemCouponGoodsService remoteItemCouponGoodsService;
	@Autowired
	private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
	@Autowired
	private RemoteGuessOrdersConsumerService remoteGuessOrdersConsumerService;
	@Autowired
	private RemoteSingleLotteryOrderService remoteSingleLotteryOrderService;
	@Autowired
	private RemoteNgameOrdersConsumerService remoteNgameOrdersConsumerService;
	@Autowired
	private RemoteDuibaQuestionAnswerOrdersService remoteDuibaQuestionAnswerOrdersService;
	@Autowired
	private RemoteQuizzOrdersSimpleService remoteQuizzOrdersSimpleService;
	@Autowired
	private RemoteQuizzOrdersStatusChangeService remoteQuizzOrdersStatusChangeService;
	@Autowired
	private RemoteQuizzOrderTextChangeService remoteQuizzOrderTextChangeService;
	@Autowired
	private RemoteItemKeyService remoteItemKeyService;
	@Autowired
	private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;
	@Autowired
	private DomainService domainService;
	@Autowired
	private  IqiyiRiskTool  iqiyiRiskTool;
	@Autowired
	private RemoteHappyCodeOrderService remoteHappyCodeOrderService;
	@Autowired
	private ActivityHappyCodeService activityHappyCodeService;
	@Autowired
	private AlipayTakePrizeCheckService alipayTakePrizeCheckService;
	@Autowired
	private RemoteConsumerService remoteConsumerService;
	@Autowired
	private RemoteDuibaAntiService remoteDuibaAntiService;
	@Autowired
    private RemoteAppService remoteAppService;
    @Autowired
    private ItemCheckService itemCheckService;

	@Autowired
	private GoodsCacheService goodsCacheService;


    @Autowired
    private RemoteReceiveAddressService remoteReceiveAddressService;

    @Autowired
    private RemoteHappyCodeUserRecordService remoteHappyCodeUserRecordService;
    @Autowired
    private RemoteLuckyCodeUserAwardRecordService remoteLuckyCodeUserAwardRecordService;
    @Autowired
    private RemoteDigGoldService remoteDigGoldService;
	@Autowired
	private RemoteUnderstandLevelService remoteUnderstandLevelService;
    @Autowired
    private RemoteItemNewExtraService remoteItemNewExtraService;

    @Autowired
    private ABCBankService abcBankService;
    @Autowired
    private AbcConfig abcConfig;
	@Autowired
	private RemoteHaggleService remoteHaggleService;
	@Autowired
	private RemoteCentscanPrizeRecordService remoteCentscanPrizeRecordService;

	@Autowired
	private RefreshConstant refreshConstant;

	@Resource
	private CcbConfig config;

    @Resource
    private QingTingFmConfig qingTIngFmConfig;

	@Resource
	private CommConfig commConfig;

	@Autowired
	private RemoteDuibaHdtoolServiceNew remoteDuibaHdtoolServiceNew;

	@Autowired
	private RemoteGoodsAppItemExtraService remoteGoodsAppItemExtraService;
	@Autowired
	private RemoteUnderseaGamePrizeRecordService remoteUnderseaGamePrizeRecordService;
	@Autowired
	private ActivityCacheService activityCacheService;
	@Autowired
	private RemoteMarketingHdtoolPrizeRecordService remoteMarketingHdtoolPrizeRecordService;
	@Autowired
	private RemoteHappyCodeItemPhaseService remoteHappyCodeItemPhaseService;
	@Autowired
	private RemoteHappyCodeItemBasicService remoteHappyCodeItemBasicService;
	@Autowired
	private RemoteAddrLimitService remoteAddrLimitService;
	@Autowired
	private RemoteTaiLongTaskUnitService remoteTaiLongTaskUnitService;

	@Autowired
	private RemoteSuperSurpriseService remoteSuperSurpriseService;
	@Autowired
	private WxFavorCouponService wxFavorCouponService;

	@Autowired
	private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;

	@Autowired
	private RiskService riskService;

	@Autowired
	private ZhrsConfig zhrsConfig;
	@Resource
	private CloseableHttpClient httpClient;
	@Resource
	private ExecutorService executorService;
	@Resource
	private HsbcConfig hsbcConfig;
	@Resource
	private RemoteHsbcBankServcie remoteHsbcBankService;

	@Resource
	private YspObjectCheckConfig yspObjectCheckConfig;


	@Resource
	private ObjectVerificationService objectVerificationService;

	@Autowired
	private RemoteStormrageEngineService remoteStormrageEngineService;


	@Resource
	private RemoteSignOperatingService remoteSignOperatingService;
	@Resource
	private RemoteSignActivityService remoteSignActivityService;
	@Resource
	private RemoteCustomAddressService remoteCustomAddressService;


	private List<Long> IQIYI_APP_LIST = Lists.newArrayList(31819L, 28549L, 40445L);
	// 线上环境，陌陌appId
	private static final ImmutableList<Long> MOMO_APP_LIST_ONLINE = ImmutableList.of(48975L);
	// 测试环境，陌陌appId
	private static final ImmutableList<Long> MOMO_APP_LIST_DAILY = ImmutableList.of(1L);
	// 陌陌定制，用户扩展信息，身份证号字段名称
	private static final String ID_CARD = "idCard";

	// 通过通用发奖 由开发者承担直冲类奖品的活动类型
	private static final ImmutableSet<String> DEVELOPER_COST_ACTIVITY_LIST;

	public  final  static  String TRANSFER  =  "transfer";

	public static final String SPE_CHAT = "@";




	static {
		List<String> temp = Lists.newArrayList();
		// 积分夺宝
		temp.add(ActivityUniformityTypeEnum.HappyCode.getCode().toString());
		// 幸运码
		temp.add(ActivityUniformityTypeEnum.LUCKY_CODE.getCode().toString());
		// 邀好友赚佣金  全民赚佣金
		temp.add(ActivityUniformityTypeEnum.PyramidSpread.getCode().toString());
		// 拆红包
		temp.add(ActivityUniformityTypeEnum.OpenRedPacket.getCode().toString());
		// 无敌挖矿机
		temp.add(ActivityUniformityTypeEnum.MiningMachine.getCode().toString());
		// 拼拼乐
		temp.add(ActivityUniformityTypeEnum.HappyGroup.getCode().toString());
		// 0元拼团
		temp.add(ActivityUniformityTypeEnum.FreeGroupTool.getCode().toString());
		// 微信刮红包
		temp.add(ActivityUniformityTypeEnum.ScrapeRedPacket.getCode().toString());
		// 新人七天
		temp.add(ActivityUniformityTypeEnum.NewConsumer7DayPopup.getCode().toString());
		// 开发者集卡活动
		temp.add(ActivityUniformityTypeEnum.CollectCard.getCode().toString());
		//拉新签到活动
		temp.add(ActivityUniformityTypeEnum.SignForNew.getCode().toString());
		//拉新签到7天活动
		temp.add(PrizeRelTypeEnum.SIGN_FOR_NEW_7.getType());
		// 历史原因 开发者集卡活动 使用的是这个类型
		temp.add(cn.com.duiba.plugin.center.api.dto.ActivityOrderDto.TYPE_COLLECT_DEVELOPER);
		// 日历签到组件化 等同于 PrizeRelTypeEnum.SIGN_COMPONENT.getType();
		temp.add(ActivityUniformityTypeEnum.SIGN_CALENDAR_COMPONENT.getCode().toString());
        //夺宝签到
		temp.add(ActivityUniformityTypeEnum.SignTreasure.getCode().toString());
		//pk赛-独立活动
		temp.add(ActivityUniformityTypeEnum.PKH5.getCode().toString());
		DEVELOPER_COST_ACTIVITY_LIST = ImmutableSet.copyOf(temp);
	}

	@Override
	public OrdersVO findOrderInfo(ConsumerExchangeRecordDto record) { //NOSONAR
		//根据 record 中type  来查询 各种活动的 子订单信息
		//活动工具
		OrdersVO vo = null;
		if(ConsumerExchangeRecordDto.TypeHdtoolLottery == record.getType()){
			HdtoolOrdersDto order = remoteCreditsHdtoolOrdersService.find(record.getConsumerId(),record.getRelationId());
			vo =  BeanUtils.copy(order,OrdersVO.class);
			vo.setActivityId(order.getDuibaHdtoolId());
            vo.setActUniformType(ActivityUniformityTypeEnum.HDTool);
		}else if(ConsumerExchangeRecordDto.TypeSuperSurprise == record.getType()){
			vo = parseSuperSurprise(String.valueOf(record.getRelationId()));
		}else if(ConsumerExchangeRecordDto.TypePluginLottery == record.getType()){
			vo = parsePlugin(String.valueOf(record.getRelationId()));
		} else if(ConsumerExchangeRecordDto.TypeSign == record.getType()){
            vo = parseBarrageSign(String.valueOf(record.getRelationId()));
        } else if(ConsumerExchangeRecordDto.TypeGuess == record.getType()){
			GuessOrdersDto order = remoteGuessOrdersConsumerService.find(record.getConsumerId(),record.getRelationId());
			vo = BeanUtils.copy(order,OrdersVO.class);
            vo.setFacePrice(order.getPrizeFacePrice());
			vo.setActivityId(order.getDuibaGuessId());
		}else if(ConsumerExchangeRecordDto.TypeSingleLottery == record.getType()){
		    SingleLotteryOrderDto order = remoteSingleLotteryOrderService.find(record.getRelationId());
		    vo = BeanUtils.copy(order,OrdersVO.class);
		    if(ItemDto.TypeVirtual.equals(order.getPrizeType())) {
		        vo.setFacePrice(String.valueOf(order.getPrizeDegree()));
		    } else {
		        vo.setFacePrice(String.valueOf(order.getPrizeFacePrice()));
		    }
        }  else if (ConsumerExchangeRecordDto.TypeNgame == record.getType()) {
			NgameOrdersDto orderDO = remoteNgameOrdersConsumerService.find(record.getConsumerId(), record.getRelationId());
			vo = BeanUtils.copy(orderDO,OrdersVO.class);
			vo.setStatus(orderDO.getOrderStatus());
			vo.setFacePrice(orderDO.getPrizeFacePrice());
			vo.setActivityId(orderDO.getDuibaNgameId());
			vo.setActUniformType(ActivityUniformityTypeEnum.DuibaNgame);
		} else if (ConsumerExchangeRecordDto.TypeCreditGame == record.getType()) {
			ActivityOrderDto activityOrderDto = remoteActivityOrderService.findByOrderNum(String.valueOf(record.getRelationId())).getResult();
			vo =BeanUtils.copy(activityOrderDto,OrdersVO.class);
			vo.setPrizeType(activityOrderDto.getActivityOptionType());
			vo.setActivityId(activityOrderDto.getDuibaActivityId());
		}else if(ConsumerExchangeRecordDto.TypeQuestion == record.getType()){
			DuibaQuestionAnswerOrdersDto order = remoteDuibaQuestionAnswerOrdersService.find(record.getRelationId());
			vo = BeanUtils.copy(order,OrdersVO.class);
			vo.setFacePrice(order.getPrizeFacePrice());
			vo.setActivityId(order.getDuibaQuestionAnswerId());
			vo.setActUniformType(ActivityUniformityTypeEnum.QuestionAnswer);
		}else if(ConsumerExchangeRecordDto.TypeQuizz == record.getType()){
			QuizzOrdersDto order = remoteQuizzOrdersSimpleService.find(record.getConsumerId(), record.getRelationId());
			vo = BeanUtils.copy(order,OrdersVO.class);
			vo.setFacePrice(order.getPrizeFacePrice());
			vo.setActivityId(order.getDuibaQuizzId());
		} else if (ConsumerExchangeRecordDto.TYPE_HAPPY_CODE == record.getType()) {
			HappyCodeOrderDto order = remoteHappyCodeOrderService.findOrderById(record.getRelationId());
			vo = new OrdersVO();
			vo.setId(order.getId());
			vo.setConsumerId(order.getConsumerId());
			vo.setPrizeType(order.getOptionType());
			vo.setItemId(order.getItemId());
			vo.setExchangeStatus(transferHappyCodeOrderExchangeStatus(order.getExchangeStatus()));
			vo.setCouponId(findCouponId(order));
			vo.setDuibaActivityId(order.getPhaseId());
			String facePrice = String.valueOf(order.getFacePrice());
			if (Objects.equals(ItemDto.TypeVirtual, order.getOptionType())) {
				// 虚拟商品 需要使用商品身上的标识符
				String itemJson = remoteDuibaItemGoodsService.getJsonValue(order.getItemId(), "itemJson").getResult();
				if (StringUtils.isNotBlank(itemJson)) {
					facePrice = itemJson;
				}
			}
			vo.setFacePrice(facePrice);
		} else if (ConsumerExchangeRecordDto.TypeLuckyCode == record.getType()) {
            vo = createLuckyCodeOrderInfo(record);
		} else if (ConsumerExchangeRecordDto.TypeHappyCodeNew == record.getType()) {
			vo = createHappyCodeNewOrderInfo(record);
        } else if (ConsumerExchangeRecordDto.TypeDigGold == record.getType()) {
			vo = createDigGoldOrderInfo(record);
		} else if (ConsumerExchangeRecordDto.TypeHaggle == record.getType()) {
			vo = createHaggleOrderInfo(record);
		} else if (ConsumerExchangeRecordDto.TypeCentscan == record.getType()) {
			vo = createCentscanOrderInfo(record);
		} else if (ConsumerExchangeRecordDto.TypeUnderseaGame == record.getType()) {
			vo = createUnderseaGameOrderInfo(record);
		}else if (ConsumerExchangeRecordDto.TypeUnderstandLevel == record.getType()) {
			vo = createUnderstandLevelOrderInfo(record);
		} else if (ConsumerExchangeRecordDto.TypeTaiLongTaskUnit == record.getType()) {
			vo = createTaiLongTaskUnitOrderInfo(record);
		}


        return processOrdersVOResult(vo);
	}

	private OrdersVO createTaiLongTaskUnitOrderInfo(ConsumerExchangeRecordDto record) {
		TaskRecordDto taskRecord = remoteTaiLongTaskUnitService.findRecordById(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(taskRecord.getId());
		vo.setConsumerId(taskRecord.getConsumerId());
		vo.setPrizeType(taskRecord.getAppItemType());
		vo.setAppItemId(taskRecord.getAppItemId());
		vo.setPrizeId(taskRecord.getTaskId());
		vo.setActUniformType(ActivityUniformityTypeEnum.TAILONG_TASK_UNIT);
		if (TaskStatusEnum.RECEIVED.getStatus().equals(taskRecord.getTaskStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else if (TaskStatusEnum.FAIL.getStatus().equals(taskRecord.getTaskStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
		} else if (TaskStatusEnum.SUCCESS.getStatus().equals(taskRecord.getTaskStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeProcessing);
		}
		if (ItemDto.TypeCoupon.equals(taskRecord.getAppItemType())) {
			vo.setCouponId(Long.valueOf(taskRecord.getExtraInfo()));
		} else if (StringUtils.isNotBlank(taskRecord.getAppItemType())) {
			vo.setFacePrice(UnitUtils.transformYuan2Fen(taskRecord.getExtraInfo()).toString());
		}
		vo.setOperatingActivityId(taskRecord.getOperatingActivityId());
		return vo;
	}

	private OrdersVO createUnderstandLevelOrderInfo(ConsumerExchangeRecordDto record) {
		UnderstandLevelAssistRecordDto assistRecordDto = remoteUnderstandLevelService.getAssistRecordById(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(assistRecordDto.getId());
		vo.setConsumerId(assistRecordDto.getConsumerId());
		vo.setPrizeType(assistRecordDto.getPrizeType());
		vo.setAppItemId(assistRecordDto.getAppItemId());
		vo.setPrizeId(assistRecordDto.getPrizeId());
		vo.setActUniformType(ActivityUniformityTypeEnum.UNDERSTAND_LEVEL);
		if (RecordExchangeStatusEnum.NO_GET_PRIZE.getType().equals(assistRecordDto.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else if (RecordExchangeStatusEnum.GET_PRIZE_FAIL.getType().equals(assistRecordDto.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
		} else if (RecordExchangeStatusEnum.GET_PRIZE_SUC.getType().equals(assistRecordDto.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeProcessing);
		}
		if (ItemDto.TypeCoupon.equals(assistRecordDto.getPrizeType())) {
			vo.setCouponId(Long.valueOf(assistRecordDto.getPrizeValue()));
		} else if (ItemDto.TypeVirtual.equals(assistRecordDto.getPrizeType())) {
			vo.setFacePrice(assistRecordDto.getPrizeValue());
		} else if (StringUtils.isNotBlank(assistRecordDto.getPrizeValue())) {
			vo.setFacePrice(UnitUtils.transformYuan2Fen(assistRecordDto.getPrizeValue()).toString());
		}
		UnderstandLevelConfigDto configDto = remoteUnderstandLevelService.getBaseConfig(assistRecordDto.getConfigId());
		if (configDto != null) {
			vo.setOperatingActivityId(configDto.getOpId());
		}
		return vo;
	}

	@NotNull
	private OrdersVO createHappyCodeNewOrderInfo(ConsumerExchangeRecordDto record) {
		HappyCodeUserRecordDto order = remoteHappyCodeUserRecordService.findById(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(order.getId());
		vo.setConsumerId(order.getConsumerId());
		vo.setPrizeType(order.getPrizeType());
		vo.setAppItemId(order.getAppItemId());
		vo.setExchangeStatus(transferHappyCodeOrderExchangeStatusNew(order));
		vo.setActivityId(order.getActId());
		if (Objects.equals(ItemDto.TypeVirtual, order.getPrizeType())) {
            HappyCodeItemPhaseDto happyCodeItemPhaseDto = remoteHappyCodeItemPhaseService.findById(order.getPhaseId());
			HappyCodeItemBasicDto happyCodeItemBasicDto = remoteHappyCodeItemBasicService.findItemBasicById(happyCodeItemPhaseDto.getBasicId());
            vo.setFacePrice(happyCodeItemBasicDto.getMerchantCoding());
        }
		return vo;
	}

	private OrdersVO createLuckyCodeOrderInfo(ConsumerExchangeRecordDto record) {
        LuckyCodeUserAwardRecordDto order = remoteLuckyCodeUserAwardRecordService.findById(record.getRelationId());
        OrdersVO vo = new OrdersVO();
        vo.setId(order.getId());
        vo.setConsumerId(order.getConsumerId());
        vo.setPrizeType(order.getPrizeType());
        vo.setAppItemId(order.getAppItemId());
        vo.setPrizeId(order.getAwardConfigId());
        vo.setActUniformType(ActivityUniformityTypeEnum.LUCKY_CODE);
        if (LuckyCodeUserAwardStateEnum.FINISH_DRAW.getCode().equals(order.getState())) {
            vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
        } else if (LuckyCodeUserAwardStateEnum.FAIL_DRAW.getCode().equals(order.getState())) {
            vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
        } else if (LuckyCodeUserAwardStateEnum.SUCCESS_DRAW.getCode().equals(order.getState())) {
            vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
        } else {
            vo.setExchangeStatus(ActivityOrderDto.ExchangeProcessing);
        }
        vo.setOperatingActivityId(order.getOperatingActivityId());
        if (ItemDto.TypeCoupon.equals(order.getPrizeType())) {
            vo.setCouponId(Long.valueOf(order.getItemValue()));
        } else if (ItemDto.TypeVirtual.equals(order.getPrizeType())) {
            vo.setFacePrice(order.getItemValue());
		} else if (StringUtils.isNotBlank(order.getItemValue())) {
			try {
				vo.setFacePrice(UnitUtils.transformYuan2Fen(order.getItemValue()).toString());
			} catch (Exception e) {
				log.warn("createLuckyCodeOrderInfo:", e);
				vo.setFacePrice(StringUtils.EMPTY);
			}
		}
        return vo;
    }

	private OrdersVO createDigGoldOrderInfo(ConsumerExchangeRecordDto record) {
		DigGoldRecordDto order = remoteDigGoldService.getRecordById(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(order.getId());
		vo.setConsumerId(order.getConsumerId());
		vo.setPrizeType(order.getType());
		vo.setAppItemId(order.getAppItemId());
		vo.setPrizeId(order.getPrizeId());
		vo.setActUniformType(ActivityUniformityTypeEnum.DigGold);
		if (RecordExchangeStatusEnum.NO_GET_PRIZE.getType().equals(order.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else if (RecordExchangeStatusEnum.GET_PRIZE_FAIL.getType().equals(order.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
		} else if (RecordExchangeStatusEnum.GET_PRIZE_SUC.getType().equals(order.getExchangeStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeProcessing);
		}
		if (ItemDto.TypeCoupon.equals(order.getType())) {
			vo.setCouponId(Long.valueOf(order.getItemValue()));
		} else if (ItemDto.TypeVirtual.equals(order.getType())) {
			vo.setFacePrice(order.getItemValue());
		} else if (StringUtils.isNotBlank(order.getItemValue())) {
			vo.setFacePrice(UnitUtils.transformYuan2Fen(order.getItemValue()).toString());
		}
		DigGoldConfigDto configDto = remoteDigGoldService.getConfig(order.getConfigId());
		if (configDto != null) {
			vo.setOperatingActivityId(configDto.getOpId());
		}
		return vo;
	}

	private OrdersVO createHaggleOrderInfo(ConsumerExchangeRecordDto record) {
		HaggleOpenRecordDto haggleOpenRecordDto = remoteHaggleService.getOpenRecordById(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(haggleOpenRecordDto.getId());
		vo.setConsumerId(haggleOpenRecordDto.getConsumerId());
		Long prizeId = haggleOpenRecordDto.getPrizeId();
		vo.setPrizeId(prizeId);
		HagglePrizeDto hagglePrizeDto = remoteHaggleService.getPrizeById(prizeId);
		vo.setAppItemId(hagglePrizeDto.getAppItemId());
		ItemKeyDto itemKeyDto = remoteItemKeyService.getItemKey(hagglePrizeDto.getAppItemId()).getResult();
		vo.setPrizeType(itemKeyDto.getItemType());
		vo.setActUniformType(ActivityUniformityTypeEnum.Haggle);
		if (RecordStatusEnum.RECEIVED.getType().equals(haggleOpenRecordDto.getRecordStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else if (RecordStatusEnum.NORECEIVE.getType().equals(haggleOpenRecordDto.getRecordStatus())) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeProcessing);
		}
		HaggleConfigDto configDto = remoteHaggleService.getConfig(haggleOpenRecordDto.getConfigId());
		if (configDto != null) {
			vo.setOperatingActivityId(configDto.getOpId());
		}
		return vo;
	}

	private OrdersVO createCentscanOrderInfo(ConsumerExchangeRecordDto record) {
		CentscanPrizeRecordDto order = remoteCentscanPrizeRecordService.find(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(order.getId());
		vo.setConsumerId(order.getConsumerId());
		vo.setPrizeType(order.getPrizeType());
		vo.setAppItemId(order.getAppItemId());
		vo.setPrizeId(order.getAppItemId());
		vo.setActUniformType(ActivityUniformityTypeEnum.Centscan);
		if (CentscanPrizeRecordDto.STATUS_INITIAL == order.getProcessStatus()) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else if (CentscanPrizeRecordDto.STATUS_SUCCESS == order.getProcessStatus()) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
		}
		vo.setOperatingActivityId(order.getActivityId());
		JSONObject extra = JSON.parseObject(order.getExtraInfo());
		if (ItemDto.TypeCoupon.equals(vo.getPrizeType())) {
			vo.setCouponId(extra.getLong("couponId"));
		}
		return vo;
	}

	private OrdersVO createUnderseaGameOrderInfo(ConsumerExchangeRecordDto record) {
		UnderseaGamePrizeRecordDto order = remoteUnderseaGamePrizeRecordService.find(record.getRelationId());
		OrdersVO vo = new OrdersVO();
		vo.setId(order.getId());
		vo.setConsumerId(order.getConsumerId());
		vo.setPrizeType(order.getPrizeType());
		vo.setAppItemId(order.getAppItemId());
		vo.setPrizeId(order.getPrizeConfigId());
		vo.setActUniformType(ActivityUniformityTypeEnum.UNDERSEA_GAME);
		if (UnderseaGamePrizeRecordDto.STATUS_INIT == order.getPrizeStatus()) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		} else if (UnderseaGamePrizeRecordDto.STATUS_SUCCESS == order.getPrizeStatus()) {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeSuccess);
		} else {
			vo.setExchangeStatus(ActivityOrderDto.ExchangeFail);
		}
		vo.setOperatingActivityId(activityCacheService.findUnderseaGameConfigById(order.getConfigId()).getOpId());
		if (ItemDto.TypeCoupon.equals(vo.getPrizeType())) {
			vo.setCouponId(Long.valueOf(order.getPrizeValue()));
		} else if (ItemDto.TypeVirtual.equals(order.getPrizeType())) {
			vo.setFacePrice(order.getPrizeValue());
		} else if (StringUtils.isNotBlank(order.getPrizeValue())) {
			vo.setFacePrice(UnitUtils.transformYuan2Fen(order.getPrizeValue()).toString());
		}
		return vo;
	}

	private OrdersVO processOrdersVOResult(OrdersVO vo){
        if(vo == null){
            return null;
        }
        if(vo.getOperatingActivityId() != null){
            OperatingActivityDto opt = remoteOperatingActivityServiceNew.find(vo.getOperatingActivityId());
            if(opt != null){
                vo.setActivityId(opt.getActivityId());
                if(vo.getActUniformType() == null) {
                    vo.setActUniformType(ActivityUniformityTypeEnum.getByCode(opt.getType()));
                }
            }
        }
        return vo;
    }

    private OrdersVO parseSuperSurprise(String orderNum){
		OrdersVO vo = new OrdersVO();
		DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(orderNum);
		if(orderResult.isSuccess()){
			vo =BeanUtils.copy(orderResult.getResult(),OrdersVO.class);
			vo.setPrizeType(orderResult.getResult().getActivityOptionType());
			vo.setActivityId(orderResult.getResult().getAppActivityId());
			vo.setActUniformType(ActivityUniformityTypeEnum.SUPER_SURPRISE);
		}
		return vo;
	}

    private OrdersVO parsePlugin(String orderNum){
        OrdersVO vo = new OrdersVO();
        DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(orderNum);

        if(orderResult.isSuccess()){
			ActivityOrderDto activityOrderDto = orderResult.getResult();
			vo =BeanUtils.copy(orderResult.getResult(),OrdersVO.class);
            vo.setPrizeType(orderResult.getResult().getActivityOptionType());
            vo.setActivityId(orderResult.getResult().getDuibaActivityId());
            vo.setActUniformType(ActivityUniformityTypeEnum.Plugin);
			if (Objects.equals(ItemDto.TypeVirtual, orderResult.getResult().getActivityOptionType())) {
				// 虚拟商品 需要使用商品身上的标识符
				Long appItemId = orderResult.getResult().getAppItemId();
				if(appItemId!=null){
					ItemKeyDto itemKeyWithCache = goodsCacheService.findItemKeyWithCache(appItemId, activityOrderDto.getAppId());
					List<AppItemSkuDto> list = itemKeyWithCache.getAppItem().getAppItemSkuDtoList();
					log.info("itemKeyWithCache list:{}", JSON.toJSONString(list));
					vo.setFacePrice(CollectionUtils.isNotEmpty(list)? list.get(0).getMerchantCoding() : null);
				}
			}

        }
        return vo;
    }

    private OrdersVO parseBarrageSign(String orderNum){
        OrdersVO vo = new OrdersVO();
        DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(orderNum);
        if(orderResult.isSuccess()){
            vo =BeanUtils.copy(orderResult.getResult(),OrdersVO.class);
            vo.setPrizeType(orderResult.getResult().getActivityOptionType());
            vo.setActivityId(orderResult.getResult().getDuibaActivityId());
            vo.setActUniformType(ActivityUniformityTypeEnum.BarrageSign);
        }
        return vo;
    }

	/**
	 * 若奖项是优惠券 则获取couponId
	 * @param order
	 * @return
	 */
	private Long findCouponId(HappyCodeOrderDto order) {
		return Objects.equals(order.getOptionType(), ItemDto.TypeCoupon) ? activityHappyCodeService.findCouponId(order.getId()) : null;

	}

	/**
	 * 将开心码子订单状态领奖状态转化成activityorder定义的领奖状态
	 * @param exchangeStatus
	 * @return
	 */
	private Integer transferHappyCodeOrderExchangeStatus(Integer exchangeStatus) {
		switch (exchangeStatus) {
			case HappyCodeOrderDto.EXCHANGE_STATUS_WAIT : return ActivityOrderDto.ExchangeWait;
			case HappyCodeOrderDto.EXCHANGE_STATUS_DONE : return ActivityOrderDto.ExchangeSuccess;
			case HappyCodeOrderDto.EXCHANGE_STATUS_OVERDUE : return ActivityOrderDto.ExchangeOverdue;
			default: return exchangeStatus;
		}
	}

	/**
	 * 新版开心码
	 * 将开心码子订单状态领奖状态转化成activityorder定义的领奖状态
	 * @param order
	 * @return
	 */
	private Integer transferHappyCodeOrderExchangeStatusNew(HappyCodeUserRecordDto order) {
		HappyRewardStatusEnum exchangeStatus = order.getRewardStatus();
		if (HappyRewardStatusEnum.REWARD_STATUS_NOT.equals(exchangeStatus)){
			Date current = new Date();
			if (current.after(order.getPrizeExpireTime())){
				return ActivityOrderDto.ExchangeOverdue;
			}
			return ActivityOrderDto.ExchangeWait;
		} else if (HappyRewardStatusEnum.REWARD_STATUS_YES.equals(exchangeStatus)){
			return ActivityOrderDto.ExchangeSuccess;
		}
		return null;
	}

	@Override
	public ModelAndView takeModelAndView(AppSimpleDto app, ConsumerDto consumer, ConsumerExchangeRecordDto record, OrdersVO order) throws UnsupportedEncodingException {
		ItemKeyDto itemKey = commonService.findItemKeyDto(order.getAppItemId(),order.getItemId(),app.getId());
		//虚拟商品
		if(ItemDto.TypeVirtual.equals(order.getPrizeType())){
			return takeModelAndViewForVirtual(itemKey,order,record,app);
		//实物
		}else if (ItemDto.TypeObject.equals(order.getPrizeType())) {
			return takeModelAndViewForObject(itemKey,record,app,consumer,order);
		//优惠卷兑换页面
		}else if(ItemDto.TypeCoupon.equals(order.getPrizeType())){
			return takeModelAndViewForCounpon(itemKey,record,app,consumer);
		//支付宝兑换页面
		}else if(ItemDto.TypeAlipay.equals(order.getPrizeType())) {
			return takeModelAndViewForAlipay(itemKey,order,record,app,consumer);
		//话费
		}else if(ItemDto.TypePhonebill.equals(order.getPrizeType())) {
			return takeModelAndViewForPhonebill(itemKey,order,record,app,consumer);
		//Qb
		} else if(ItemDto.TypeQB.equals(order.getPrizeType())) {
			return takeModelAndViewForQb(itemKey,order,record,app,consumer);
		}
		log.error("msg={},order={}","奖品数据异常",JSONObject.toJSONString(order));
		return new ModelAndView("/error");
	}

	private ModelAndView takeModelAndViewForVirtual(ItemKeyDto itemKey,OrdersVO order, ConsumerExchangeRecordDto record,AppSimpleDto app) throws UnsupportedEncodingException {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		if (StringUtils.isBlank(app.getVirtualExchangeUrl())) {
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
			log.info("虚拟商品兑换详情页,APP未配置虚拟商品领奖接口appId={}", app.getId());
		}
		AppItemDto appItemDto = itemKey.getAppItem();
		if (null != appItemDto) {
			if (appItemDto.getDeleted()) {
				exText = CommonConstants.MSG_EXTEXT_YXJ;
				exEnable = false;
				log.info("虚拟商品兑换详情页,APP虚拟商品已删除appId={},appItemId:{}", app.getId(), appItemDto.getId());
			}
			if(!itemCheckService.checkPriceDegree(itemKey,order.getFacePrice())){
				exText = CommonConstants.MSG_EXTEXT_YXJ;
				exEnable = false;
				log.info("虚拟商品兑换详情页,APP虚拟商品档位不存在appId={},appItemId:{}", app.getId(), appItemDto.getId());
			}
		}

		ModelAndView model = new ModelAndView();
		//如果是虚拟商品需要填写用户名，并且此虚拟商品是微信立减金，重定向到兑吧小程序授权页面
		if (isNeedAccount(itemKey) && wxFavorCouponService.isWxFavorCoupon(itemKey)) {
			String degree = URLEncoder.encode(order.getFacePrice(), "utf-8");
			Long itemId = itemKey.getItem().getId();
			StringBuilder sb = new StringBuilder();
			sb.append("/aaw/wx/favor/coupon/detailIndex?recordId=").append(record.getId());
			sb.append("&itemId=").append(itemId);
			sb.append("&degree=").append(degree);
			return new ModelAndView(new RedirectView(sb.toString()));
		}

		//如果虚拟商品需要的填写用户名，则重定向跳到用户名输入页
		if (isNeedAccount(itemKey)) {
            String degree = URLEncoder.encode(order.getFacePrice(), "utf-8");
            Long itemId = itemKey.getAppItem() == null ? itemKey.getItem().getId() : itemKey.getAppItem().getId();
            String itemIdStr = itemKey.getAppItem() == null ? "&itemId=" : "&appItemId=";
            StringBuilder sb = new StringBuilder();
            sb.append("/activity/virtualInputUserName?recordId=").append(record.getId());
            sb.append(itemIdStr).append(itemId);
            sb.append("&degree=").append(degree);
            return new ModelAndView(new RedirectView(sb.toString()));
        }
		DubboResult<Long> stockResult = remoteItemKeyService.findStock(itemKey);
		model.setViewName("/takePrize/take_prize_virtual");
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		String  stockText = "少量";
		if(stockResult.isSuccess() && stockResult.getResult()!=null && stockResult.getResult()>100){
			stockText = "充足";
		}
		this.setModelProgrem(model,itemKey);
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject("app", app);
		model.addObject("stockText", stockText);
		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		if (itemKey.getAppItem() != null) {
			List<KeyValueDto> values = remoteItemNewExtraService.findValuesByAppItemIdAndPropNames(itemKey.getAppItem().getId(), Arrays.asList("developerLink", "btnText", "devLinkSwitch"));
			Map<String, String> name2Value = values.stream().collect(Collectors.toMap(KeyValueDto::getPropName, KeyValueDto::getPropValue, (e, r) -> e));
			String devLinkSwitch = Optional.ofNullable(name2Value.get("devLinkSwitch")).orElse(SWITCH_CLOSE);
			Boolean flag = SWITCH_OPEN.equals(devLinkSwitch);
			if (flag) {
				model.addObject("developerLink",name2Value.get("developerLink"));
				model.addObject("btnText",name2Value.get("btnText"));
			}
		}
		OrdersDto ordersDto = remoteConsumerOrderSimpleService.findById(order.getMainOrderId(), order.getConsumerId()).getResult();
		model.addObject("virtualStatus",Objects.nonNull(ordersDto) && Objects.equals(ordersDto.getStatus(),OrdersDto.StatusSuccess));
		return model;
	}

	private boolean isNeedAccount(ItemKeyDto itemKey) {
		return (itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeNeedUserName)) ||
					(itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemBaseDto.OpTypeNeedUserName));
	}

	private ModelAndView takeModelAndViewForObject(ItemKeyDto itemKey,ConsumerExchangeRecordDto record, AppSimpleDto app, ConsumerDto consumer, OrdersVO order) {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		Integer price = actualPriceCalculateService.calculateMinActualPrice(itemKey,app.getId());
		if (checkItemKeyEnbael(itemKey)){
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
		}
		RemainingMoneyDto money = remoteRemainingMoneyService.findByDeveloperId(app.getDeveloperId());
		if (null == money || price > money.getMoney() || !appRequestService.isMonthBudgetEnough(itemKey) || !appRequestService.isDayBudgetEnough(itemKey)) {
			exText = CommonConstants.MSG_EXTEXT_ZBKY;
			exEnable = false;
		}
		ModelAndView model = new ModelAndView("/takePrize/take_prize_object");
		this.setModelProgrem(model,itemKey);
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject(CommonConstants.CONSUMER, consumer);
		model.addObject("app", app);
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		//过期未领奖
		//央视影音定制，不设置过期时间 20200116
		if (!commConfig.isYangshiApp(app.getId()) && checkOverDue(order, record)) {
			exText = CommonConstants.MSG_EXTEXT_SX;
			exEnable = false;
		}
		model.addObject(CommonConstants.IFOBJECTCHECK,false);
		//央视频针对待领取定制
		if (yspObjectCheckConfig.getCheckAppIds().contains(consumer.getAppId())){
			Boolean isPass = objectVerificationService.ifPass(consumer.getAppId(), consumer.getPartnerUserId(), String.valueOf(record.getId()), itemKey.getAppItem().getId());
			// 这里pass true 代表通过不需要检验
			model.addObject(CommonConstants.IFOBJECTCHECK,!isPass);
			model.addObject(CommonConstants.PHONE,consumer.getPhone());
		}
		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		//实物类型扩展字段(兑吧商品专有):0普通实物:1话费流量实物:2Q币实物:3支付宝实物
		Integer objectTypeInt = ItemDto.ObjectTypeNormal;
		if(itemKey.isItemMode()){
			String objectType = remoteDuibaItemGoodsService.getJsonValue(itemKey.getItem().getId(), "objectType").getResult();
			if(StringUtils.isNotBlank(objectType)){
				objectTypeInt = Integer.parseInt(objectType);
			}
		}
		model.addObject("objectType", objectTypeInt);
		model.addObject("prizeItemType", builderPrizeItemType(itemKey));
		//增加实物中奖24小时领奖提示
		String tipsInfo = StringUtils.EMPTY;
        if (exEnable && isTipsRecordType(record)) {
			//定制文案
			tipsInfo =  setRevicedText(app.getId());
		}
		model.addObject("tipsInfo",tipsInfo);
		return model;
	}

	private String builderPrizeItemType(ItemKeyDto itemKey) {
		String prizeItemType = StringUtils.EMPTY;
		if (itemKey.isDuibaAppItemMode()) {
			prizeItemType = "1";
		} else if (itemKey.isSelfAppItemMode()) {
			prizeItemType = "2";
		}
		return prizeItemType;
	}


	private String setRevicedText(Long appId) {
		//央视影音定制，不设置过期时间 20200116
		if (commConfig.isYangshiApp(appId)) {
			return StringUtils.EMPTY;
		}
		Integer hour = commConfig.beLongToOppo(appId) ? commConfig.getOppoOrderExpireHour() : 24;
		return "请在中奖后" + hour + "小时内领取";
	}

	//过期未领奖
	private boolean checkOverDue(OrdersVO order, ConsumerExchangeRecordDto record) {
		return Objects.equals(order.getExchangeStatus(), ActivityOrderDto.ExchangeOverdue) || record.getOverDue() != null && record.getOverDue().before(new Date());
	}

	private boolean isTipsRecordType(ConsumerExchangeRecordDto record){
		if(null==record || null==record.getType()){
			return false;
		}
		int type=  record.getType();
		return type==ConsumerExchangeRecordDto.TypeHdtoolLottery
				||type == ConsumerExchangeRecordDto.TypeSign
				|| type== ConsumerExchangeRecordDto.TypePluginLottery;
	}

	private ModelAndView takeModelAndViewForCounpon(ItemKeyDto itemKey,ConsumerExchangeRecordDto record, AppSimpleDto app, ConsumerDto consumer) {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		Integer price = actualPriceCalculateService.calculateMinActualPrice(itemKey,app.getId());
		if (checkItemKeyEnbael(itemKey)){
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
		}
		RemainingMoneyDto money = remoteRemainingMoneyService.findByDeveloperId(app.getDeveloperId());
		if (null == money || price > money.getMoney() || !appRequestService.isMonthBudgetEnough(itemKey) || !appRequestService.isDayBudgetEnough(itemKey)) {
			exText = CommonConstants.MSG_EXTEXT_ZBKY;
			exEnable = false;
		}

		ModelAndView model = new ModelAndView("/takePrize/take_prize_coupon");
		this.setModelProgrem(model,itemKey);
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject(CommonConstants.CONSUMER, consumer);
		model.addObject("app", app);

		if (exEnable) {
			DubboResult<GoodsBatchDto> dubboResultBatch = remoteItemCouponGoodsService.findCurrentBatch(itemKey);
			if (!dubboResultBatch.isSuccess()) {
				log.warn("find coupon batch failed, msg={}", dubboResultBatch.getMsg());
				return new ModelAndView("/error");
			}
			model.addObject("remain", dubboResultBatch.getResult().getStock());
		}
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		return model;
	}

	private ModelAndView takeModelAndViewForAlipay(ItemKeyDto itemKey,OrdersVO order, ConsumerExchangeRecordDto record, AppSimpleDto app,ConsumerDto consumer) {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		if ((itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) && !itemKey.getItem().getEnable()){
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
		}
		if(checkFacePrice(order,exEnable,app,itemKey)){
			log.warn("checkFacePrice 账户没钱啦！！！order = [{}] record = [{}]",order.getId(),record.getId());
			exText = CommonConstants.MSG_EXTEXT_ZBKY;
			exEnable = false;
		}
		//校验对应兑换记录是否过期
        //央视影音定制，不设置过期时间 20200116
        if (!commConfig.isYangshiApp(app.getId()) && checkOverDue(record)) {
			exText = CommonConstants.MSG_EXTEXT_SX;
			exEnable = false;
		}
		ModelAndView model = new ModelAndView("/takePrize/take_prize_alipay");
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject(CommonConstants.CONSUMER, consumer);
		model.addObject("app", app);
		//设置用户uid
		model.addObject("uid",consumer.getPartnerUserId());
		//设置活动id相关信息
		model.addObject("duibaActivityId",order.getDuibaActivityId());
		model.addObject("operatingActivityId",order.getOperatingActivityId());
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		model.addObject("alipayName",  CommonConstants.MSG_EXTEXT_ZFBXJ + (Long.valueOf(order.getFacePrice()) / 100.0) + "元");
		// 设置身份证号
		DubboResult<String> dubboResult = remoteConsumerService.findExtra(consumer.getId(), ID_CARD);
		if(null != dubboResult && dubboResult.isSuccess()){
			model.addObject(ID_CARD, dubboResult.getResult());
		}
		// 设置金额
		model.addObject("price", Long.valueOf(order.getFacePrice()) / 100.0);
		return model;
	}

	private ModelAndView takeModelAndViewForPhonebill(ItemKeyDto itemKey,OrdersVO order, ConsumerExchangeRecordDto record, AppSimpleDto app,ConsumerDto consumer) {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		if ((itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) && !itemKey.getItem().getEnable()){
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
		}
		if(checkFacePrice(order,exEnable,app,itemKey)){
			log.warn("checkFacePrice 账户没钱啦！！！order = [{}] record = [{}]",order.getId(),record.getId());
			exText = CommonConstants.MSG_EXTEXT_ZBKY;
			exEnable = false;
		}
		//校验对应兑换记录是否过期
        //央视影音定制，不设置过期时间 20200116
        if (!commConfig.isYangshiApp(app.getId()) && checkOverDue(record)) {
			exText = CommonConstants.MSG_EXTEXT_SX;
			exEnable = false;
		}
		ModelAndView model = new ModelAndView("/takePrize/take_prize_phone");
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject(CommonConstants.CONSUMER, consumer);
		model.addObject("app", app);
		//设置用户uid
		model.addObject("uid",consumer.getPartnerUserId());
		//设置活动id相关信息
		model.addObject("duibaActivityId",order.getDuibaActivityId());
		model.addObject("operatingActivityId",order.getOperatingActivityId());
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		model.addObject("phoneBillName", "话费" + (Integer.valueOf(order.getFacePrice()) / 100) + "元");
		return model;
	}

	private ModelAndView takeModelAndViewForQb(ItemKeyDto itemKey, OrdersVO order,ConsumerExchangeRecordDto record, AppSimpleDto app,ConsumerDto consumer) {
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		if ((itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) && !itemKey.getItem().getEnable()){
			exText = CommonConstants.MSG_EXTEXT_YXJ;
			exEnable = false;
		}
		if(checkFacePrice(order,exEnable,app,itemKey)){
			log.warn("checkFacePrice 账户没钱啦！！！order = [{}] record = [{}]",order.getId(),record.getId());
			exText = CommonConstants.MSG_EXTEXT_ZBKY;
			exEnable = false;
		}
		//校验对应兑换记录是否过期
		if (checkOverDue(record)) {
			exText = CommonConstants.MSG_EXTEXT_SX;
			exEnable = false;
		}
		if(record.getOrderId() != null){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}
		ModelAndView model = new ModelAndView("/takePrize/take_prize_qb");
		model.addObject(CommonConstants.RECORDID, record.getId());
		model.addObject(CommonConstants.CONSUMER, consumer);
		model.addObject("app", app);

		model.addObject(CommonConstants.EXTEXT, exText);
		model.addObject(CommonConstants.EXENABLE, exEnable);
		model.addObject("qbName", "Q币" + (Integer.valueOf(order.getFacePrice()) / 100) + "个");
		return model;
	}

	private boolean checkOverDue(ConsumerExchangeRecordDto record) {
		return record.getOverDue() != null && record.getOverDue().before(new Date());
	}

	private boolean checkFacePrice(OrdersVO order, boolean exEnable, AppSimpleDto app, ItemKeyDto itemKey) {
		if (isDuiba(order.getOperatingActivityId()) && exEnable){
			if (order.getFacePrice() != null && Integer.valueOf(order.getFacePrice()) > 0){
				DuibaRemainingMoneyDto duibaM = remoteDuibaRemainingMoneyService.findRecord();
				if (duibaM ==null || duibaM.getMoney() < Integer.valueOf(order.getFacePrice())) {
					return true;
				}
			} else {
				return true;
			}
		}else{
			RemainingMoneyDto money = remoteRemainingMoneyService.findByDeveloperId(app.getDeveloperId());
			if(null == money || Integer.valueOf(order.getFacePrice()) > money.getMoney() || !appRequestService.isMonthBudgetEnough(itemKey) || !appRequestService.isDayBudgetEnough(itemKey)) {
				return true;
			}
		}
		return false;
	}

	//判断该商品是否可用
	private boolean checkItemKeyEnbael(ItemKeyDto itemKey) {
		if (itemKey.isItemMode()){
			if (!itemKey.getItem().getEnable()) {
				return true;
			}
		} else if (itemKey.isSelfAppItemMode()) {
			if (itemKey.getAppItem().getDeleted()) {
				return true;
			}
		} else if (itemKey.isDuibaAppItemMode() && !itemKey.getItem().getEnable()) {
			return true;
		}

		return false;
	}

    @Override
    public ModelAndView virtualInputUserName(AppSimpleDto app, ConsumerDto consumer, ConsumerExchangeRecordDto record,
                                             Long appItemId, Long itemId, String degree) throws AccessActivityException {
        ItemKeyDto itemKey = commonService.findItemKeyDto(appItemId, itemId, app.getId());
        if (null == itemKey) {
            throw new AccessActivityException(CommonConstants.MSG_EXTEXT_YXJ);
        }
		if (!ItemDto.TypeVirtual.equals(getItemType(itemKey))) {
            throw new AccessActivityException(CommonConstants.MSG_EXTEXT_SPYC);
        }

		DeveloperDto developer = remoteDeveloperService.getDeveloperById(app.getDeveloperId()).getResult();

		//直接根据兑换记录产生时间来判断是否失效
		String exText = CommonConstants.MSG_EXTEXT_MSLQ;
		boolean exEnable = true;
		Date now = new Date();
		if(null != record.getOrderId()){
			exText = CommonConstants.MSG_EXTEXT_YLJ;
			exEnable = false;
		}else if(!config.isCcbAppIds(app.getId()) && now.getTime() - record.getGmtCreate().getTime() > 24*3600*1000){
			// 建行 商品不走过期失效逻辑
			exText = CommonConstants.MSG_EXTEXT_SX;
			exEnable = false;
		}

        String title = "";
        if (!itemCheckService.checkPriceDegree(itemKey, degree)) {
            exText = CommonConstants.MSG_EXTEXT_ZBKY;
            exEnable = false;
        } else {
			title = getTitle(degree, itemKey, title);
		}


        ModelAndView model = new ModelAndView("/takePrize/virtual_input_account");
        model.addObject("poweredBy", developer.getSwitch(DeveloperDto.SWITCH_POWERED_BY));
        model.addObject(CommonConstants.TITLE, title);
        model.addObject(CommonConstants.EXTEXT, config.isCcbAppIds(app.getId()) ? "立即充值" : exText);
        model.addObject(CommonConstants.EXENABLE, exEnable);
		model.addObject(CommonConstants.CONSUMER, consumer);
        model.addObject("app", app);
        model.addObject(CommonConstants.RECORDID, record.getId());
        model.addObject("isCcb", config.isCcbAppIds(app.getId()));
        model.addObject("partnerUserId", consumer.getPartnerUserId());
		getQingTingFmModel(app, itemKey, model);
		return model;
    }

	private String getItemType(ItemKeyDto itemKey) {
		return itemKey.getAppItem() == null ? itemKey.getItem().getType() : itemKey.getAppItem().getType();
	}

	private String getTitle(String degree, ItemKeyDto itemKey, String title) {
		if (itemKey.isV1() || itemKey.getAppItem() == null) {
			title = itemKey.getAppItem() == null ? itemKey.getItem().getSubtitle() : itemKey.getAppItem().getTitle();
		} else if (itemKey.getAppItem() != null) {
			PriceDegreeDto priceDegree = new PriceDegreeDto(itemKey.getAppItem().getCustomPrice());
			title = priceDegree.getTitleByDegree(degree);
		}
		return title;
	}

	private void getQingTingFmModel(AppSimpleDto app, ItemKeyDto itemKey, ModelAndView model) {//NOSONAR
		//蜻蜓fm中奖输入框定制
		String accountTitle = "恭喜中奖";
		String accountPrompt = "请输入账号";
		Integer accountFormat = 0;//默认无格式
        boolean isQingting = qingTIngFmConfig.isQingTingAppIds(app.getId())
                && itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemBaseDto.OpTypeNeedUserName);
        model.addObject("isQingtingFm", isQingting);
		model.addObject("accountTitle", accountTitle);
		model.addObject("accountPrompt", accountPrompt);
		model.addObject("accountFormat", accountFormat);

		if (itemKey.getItem() == null && itemKey.getAppItem() == null) {
			return;
		}

		if((itemKey.isSelfAppItemMode() || itemKey.isDuibaAppItemMode()) && !itemKey.getAppItem().isOpTypeAppItem(ItemBaseDto.OpTypeNeedUserName)){
		    return;
        }

		if (itemKey.isItemMode() && !itemKey.getItem().isOpTypeItem(ItemBaseDto.OpTypeNeedUserName)) {
			return;
		}
        String accountPromptStr = "";
        String accountTitleStr = "";
		if(itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemBaseDto.OpTypeNeedUserName)){
			AppItemExtraDto appItemExtraDto = remoteGoodsAppItemExtraService.findByAppItemId(itemKey.getAppItem().getId()).getResult();
            accountPromptStr = appItemExtraDto.getPromptByKey(AppItemExtraDto.ACCOUNT_PROMPT);
            accountTitleStr = appItemExtraDto.getPromptByKey(AppItemExtraDto.ACCOUNT_TITLE);
			String accountFormatStr =  appItemExtraDto.getPromptByKey(AppItemExtraDto.ACCOUNT_FORMAT);
			if(StringUtils.isNotBlank(accountFormatStr)){
				accountFormat = Integer.parseInt(accountFormatStr);
			}
		}else if(itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemBaseDto.OpTypeNeedUserName)){
			List<KeyValueDto> itemAllApi = remoteItemNewExtraService.findItemAllApi(itemKey.getItem().getId());
			if (CollectionUtils.isEmpty(itemAllApi)) {
				return;
			}
			for (KeyValueDto keyValueDto : itemAllApi) {
				if ("accountButton".equals(keyValueDto.getPropName())) {
					JSONObject jsonObject = JSON.parseObject(keyValueDto.getPropValue());
					accountTitleStr = jsonObject.getString("accountTitle");
					accountPromptStr = jsonObject.getString("accountPrompt");
					Integer accountFormatInt = jsonObject.getInteger("accountFormat");
					if(accountFormatInt!=null){
						accountFormat = accountFormatInt;
					}
					break;
				}
			}
		}

        accountTitle = StringUtils.isNotBlank(accountTitleStr) ? accountTitleStr : accountTitle;
        accountPrompt = StringUtils.isNotBlank(accountPromptStr) ? accountPromptStr : accountPrompt;
		model.addObject("accountTitle", accountTitle);
		model.addObject("accountPrompt", accountPrompt);
		model.addObject("accountFormat", accountFormat);
	}

	private void setModelProgrem(ModelAndView model, ItemKeyDto itemKey) {
		//兑吧商品
		if(itemKey.isItemMode() || itemKey.isDuibaAppItemMode()){
			//图片
			if(itemKey.getItem().getMultiImage()!= null && itemKey.getItem().getMultiImage().length()>0){
				model.addObject(CommonConstants.MULTIIMAGE, itemKey.getItem().getMultiImage());
			}else if(itemKey.getItem().getImage()!= null && itemKey.getItem().getImage().length()>0){
				model.addObject(CommonConstants.MULTIIMAGE, itemKey.getItem().getImage());
			}
			//标题
			model.addObject(CommonConstants.TITLE, itemKey.getItem().getName());
			//市场价
			setFacePrice(model, itemKey);
			model.addObject(CommonConstants.ITEMID, itemKey.getItem().getId());
			model.addObject(CommonConstants.APPITEMID,null);
			model.addObject("description",itemKey.getItem().getDescription()==null?"":itemKey.getItem().getDescription());
		}else{
			//自有商品
			if(itemKey.getAppItem().getMultiImage()!= null && itemKey.getAppItem().getMultiImage().length()>0){
				model.addObject(CommonConstants.MULTIIMAGE, itemKey.getAppItem().getMultiImage());
			}
			//标题
			model.addObject(CommonConstants.TITLE, itemKey.getAppItem().getTitle());
			//市场价
			setItemFacePrice(model, itemKey);
			model.addObject(CommonConstants.ITEMID, null);
			model.addObject(CommonConstants.APPITEMID,itemKey.getAppItem().getId());
			model.addObject("description",itemKey.getAppItem().getDescription()==null?"":itemKey.getAppItem().getDescription());

		}
	}

	private void setItemFacePrice(ModelAndView model, ItemKeyDto itemKey) {
		if(itemKey.isV1()){
            if (itemKey.getAppItem().getAppItemSkuDtoList()!=null&&!itemKey.getAppItem().getAppItemSkuDtoList().isEmpty()&&itemKey.getAppItem().getAppItemSkuDtoList().get(0).getFacePrice()!=null) {
                setAppItemFacePrice(model, itemKey.getAppItem(), itemKey.getAppItem().getAppItemSkuDtoList().get(0).getFacePrice().intValue(), itemKey.getAppId());
            }
        }else{
            setAppItemFacePrice(model, itemKey.getAppItem(), itemKey.getAppItem().getFacePrice(), itemKey.getAppId());
        }
	}

	private void setFacePrice(ModelAndView model, ItemKeyDto itemKey) {
		if(itemKey.isV1()){
			setV1FacePrice(model, itemKey);
			return;
		}

		if (itemKey.getAppItem() != null) {
            setAppItemFacePrice(model, itemKey.getAppItem(), itemKey.getAppItem().getFacePrice(), itemKey.getAppId());
        } else if(itemKey.getItem().getMarketPrice()!=null){
            setItemFacePrice(model, itemKey.getItem(), itemKey.getItem().getMarketPrice(), itemKey.getAppId());
        }else{
            setItemFacePrice(model, itemKey.getItem(), itemKey.getItem().getFacePrice(), itemKey.getAppId());
        }
	}

	private void setV1FacePrice(ModelAndView model, ItemKeyDto itemKey) {
		if (itemKey.getAppItem() != null&&itemKey.getAppItem().getAppItemSkuDtoList()!=null
                &&!itemKey.getAppItem().getAppItemSkuDtoList().isEmpty()&&itemKey.getAppItem().getAppItemSkuDtoList().get(0).getFacePrice()!=null) {
            setAppItemFacePrice(model, itemKey.getAppItem(), itemKey.getAppItem().getAppItemSkuDtoList().get(0).getFacePrice().intValue(), itemKey.getAppId());
        } else if(itemKey.getItem()!=null&&itemKey.getItem().getItemSkuDtoList()!=null&&!itemKey.getItem().getItemSkuDtoList().isEmpty()
                &&itemKey.getItem().getItemSkuDtoList().get(0).getFacePrice()!=null){
            setItemFacePrice(model, itemKey.getItem(), itemKey.getItem().getItemSkuDtoList().get(0).getFacePrice().intValue(), itemKey.getAppId());
        }
	}

	//设置兑吧商品市场价值。区分元和积分	20181112 by lufeng
	private void setItemFacePrice(ModelAndView model, ItemDto item, Integer price, Long appId) {
		// 市面价值-->单位
		if (!item.isOpTypeItem(ItemDto.OpTypeCredits)) {
			model.addObject(CommonConstants.MARKETPRICE, price / 100 + "元");//元
		} else {
			DubboResult<AppSimpleDto> appSimpleDtoDubboResult = remoteAppService.getSimpleApp(appId);
			if (appSimpleDtoDubboResult != null
					&& appSimpleDtoDubboResult.isSuccess()
					&& appSimpleDtoDubboResult.getResult() != null
					&& StringUtils.isNotBlank(appSimpleDtoDubboResult.getResult().getUnitName())) {
				model.addObject(CommonConstants.MARKETPRICE, price + appSimpleDtoDubboResult.getResult().getUnitName());
			} else {
				model.addObject(CommonConstants.MARKETPRICE, price + "积分");//默认积分
			}
		}
	}

	//设置自有商品市场价值。区分元和积分	20181112 by lufeng
	private void setAppItemFacePrice(ModelAndView model, AppItemDto appItem, Integer price, Long appId) {
		// 市面价值-->单位
		if (!appItem.isOpTypeAppItem(ItemDto.OpTypeCredits)) {
			model.addObject(CommonConstants.MARKETPRICE, price / 100 + "元");//元
		} else {
			DubboResult<AppSimpleDto> appSimpleDtoDubboResult = remoteAppService.getSimpleApp(appId);
			if (appSimpleDtoDubboResult != null
					&& appSimpleDtoDubboResult.isSuccess()
					&& appSimpleDtoDubboResult.getResult() != null
					&& StringUtils.isNotBlank(appSimpleDtoDubboResult.getResult().getUnitName())) {
				model.addObject(CommonConstants.MARKETPRICE, price + appSimpleDtoDubboResult.getResult().getUnitName());
			} else {
				model.addObject(CommonConstants.MARKETPRICE, price + "积分");//默认积分
			}
		}
	}

	//判断活动是否是 兑吧活动
	private boolean isDuiba(Long operatingActivityId) {
		OperatingActivityDto op = commonService.findOperatingActivityDto(operatingActivityId);
		//判断是不是 自由单品抽奖
		if(op == null || (op.getActivityId() != null && op.getType() != OperatingActivityDto.TypeAppSingleLottery)){
			return true;
		}
		return false;
	}
	@Override
	public OrdersDto takePrize(TakePrizeVo takePrize, HttpServletRequest request,ConsumerExchangeRecordDto record) throws AccessActivityException {

		//当兑换类型为支付宝且又是活动工具，调用远程接口进行风控规则校验
        Result<Boolean> riskResult = verifyAlipayAntichet(takePrize, request, record);
        if (Boolean.FALSE.equals(riskResult.getSuccess())) {
			CatLogTool.logMetric(CatLogTool.METRIC_ANTICHEAT_ALIPAY_HIT);
			throw new AccessActivityException(riskResult.getDesc());
		}

		checkTakePrizeItemKey(takePrize.getItemKeyDto());
		//直充类奖品防刷
		if(ItemDto.TypeQB.equals(takePrize.getPrizeType()) ||
				ItemDto.TypeAlipay.equals(takePrize.getPrizeType()) ||
				ItemDto.TypePhonebill.equals(takePrize.getPrizeType())){
			checkExchangeLimitNew(takePrize,request);
		}

		// 校验支付宝领奖限制
		if (Objects.equals(takePrize.getPrizeType(), ItemDto.TypeAlipay)) {
			checkAlipayExchangeLimit(takePrize.getApp().getId(), StringUtils.trim(request.getParameter(CommonConstants.ACCOUNT)), StringUtils.trim(request.getParameter(CommonConstants.REALNAME)));
		}

		//更改领奖状态
		changerPirzeStatus(takePrize);
		OrdersDto order = null;
		try{
			order = createMainOrder(takePrize,request);
			//更新子订单状态
			if (order == null) {
				return order;
			}
			//更新子订单中 主订单号
			updateMainOrderId(takePrize,order,record);
		} catch (AccessActivityRuntimeException e){
			rollbackTakePrize(takePrize);
			log.warn("领奖失败", e);
			throw new AccessActivityRuntimeException(e.getMessage(), e);
		} catch (Exception e) {
            rollbackTakePrize(takePrize);
            log.warn("领奖失败", e);
            throw new AccessActivityRuntimeException(CommonConstants.MSG_SERVER_EXCEPTION_LJSB, e);
		}
		return order;
	}

    private void rollbackTakePrize(TakePrizeVo takePrize) {
        // 回滚  领奖状态
        if (ConsumerExchangeRecordDto.TypeHdtoolLottery == takePrize.getRecordType().intValue()) {
            remoteCreditsHdtoolOrdersService.rollbackTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
            updateMarketingHdtoolPrizeStatus(takePrize, MarketingHdtoolPrizeRecordDto.STATUS_FAILED);
        } else if(ConsumerExchangeRecordDto.TypeSuperSurprise == takePrize.getRecordType().intValue()){
			remoteActivityOrderService.exchangeStatusToWait(takePrize.getOrderNum());
			rollBackSuperSurpriseRecordStatus(takePrize.getOrderNum());
		}else if (ConsumerExchangeRecordDto.TypePluginLottery == takePrize.getRecordType().intValue() || ConsumerExchangeRecordDto.TypeSign == takePrize.getRecordType().intValue()
                || ConsumerExchangeRecordDto.TypeCreditGame == takePrize.getRecordType().intValue()) {
            remoteActivityOrderService.exchangeStatusToWait(takePrize.getOrderNum());
        } else if (ConsumerExchangeRecordDto.TypeGuess == takePrize.getRecordType().intValue()) {
            remoteGuessOrdersConsumerService.rollbackTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
        } else if (ConsumerExchangeRecordDto.TypeSingleLottery == takePrize.getRecordType().intValue()) {
            remoteSingleLotteryOrderService.rollbackTakePrize(takePrize.getOrderId());
        } else if (ConsumerExchangeRecordDto.TypeNgame == takePrize.getRecordType().intValue()) {
            remoteNgameOrdersConsumerService.rollbackTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
        } else if (ConsumerExchangeRecordDto.TypeQuestion == takePrize.getRecordType().intValue()) {
            remoteDuibaQuestionAnswerOrdersService.rollbackTakePrize(takePrize.getOrderId());
        } else if (ConsumerExchangeRecordDto.TypeQuizz == takePrize.getRecordType().intValue()) {
            remoteQuizzOrdersStatusChangeService.rollbackTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
        } else if (ConsumerExchangeRecordDto.TypeLuckyCode == takePrize.getRecordType().intValue()) {
            remoteLuckyCodeUserAwardRecordService.updateStateByIds(Collections.singletonList(takePrize.getOrderId()), LuckyCodeUserAwardStateEnum.FAIL_DRAW.getCode());
        }else if (ConsumerExchangeRecordDto.TypeHappyCodeNew == takePrize.getRecordType().intValue()){
			HappyCodeUserRecordDto recordDto = new HappyCodeUserRecordDto();
			recordDto.setId(takePrize.getOrderId());
			recordDto.setRewardStatus(HappyRewardStatusEnum.REWARD_STATUS_NOT);
			remoteHappyCodeUserRecordService.updateById(recordDto);
		} else if (ConsumerExchangeRecordDto.TypeDigGold == takePrize.getRecordType().intValue()) {
            DigGoldRecordDto digGoldRecordDto = new DigGoldRecordDto();
            digGoldRecordDto.setId(takePrize.getOrderId());
            digGoldRecordDto.setExchangeStatus(RecordExchangeStatusEnum.GET_PRIZE_FAIL.getType());
            remoteDigGoldService.updateRecord(digGoldRecordDto);
        } else if (ConsumerExchangeRecordDto.TypeHaggle == takePrize.getRecordType().intValue()) {
        	HaggleOpenRecordDto recordDto = new HaggleOpenRecordDto();
        	recordDto.setId(takePrize.getOrderId());
        	recordDto.setRecordStatus(RecordStatusEnum.RECEIVEFAIL.getType());
        	remoteHaggleService.updateOpenRecord(recordDto);
		} else if (ConsumerExchangeRecordDto.TypeCentscan == takePrize.getRecordType()) {
        	remoteCentscanPrizeRecordService.updateStatus(takePrize.getOrderId(), CentscanPrizeRecordDto.STATUS_FAILED);
		} else if (ConsumerExchangeRecordDto.TypeUnderseaGame == takePrize.getRecordType()) {
        	remoteUnderseaGamePrizeRecordService.updateStatus(takePrize.getOrderId(), UnderseaGamePrizeRecordDto.STATUS_FAILED);
		} else if (ConsumerExchangeRecordDto.TypeTaiLongTaskUnit == takePrize.getRecordType()) {
        	remoteTaiLongTaskUnitService.updateRecordStatus(takePrize.getOrderId(), TaskStatusEnum.RECEIVED, TaskStatusEnum.FAIL);
		}
    }

	// 判断app是否是陌陌
	@Override
	public boolean checkMoMo(Long appId){
		if(Environment.isOnline() || Environment.isPrepub()){
			return MOMO_APP_LIST_ONLINE.contains(appId);
		}else if(Environment.isDaily()){
			return MOMO_APP_LIST_DAILY.contains(appId);
		}
		return false;
	}

	// 数据格式校验
	private void checkPattern(String phone, String idCard){
		if(StringUtils.isBlank(phone)){
			throw new AccessActivityRuntimeException("手机号不能为空");
		}
		if(!PatternValidator.isMobileSimple(phone)) {
			throw new AccessActivityRuntimeException("请输入正确的手机号");
		}
		if(StringUtils.isBlank(idCard)){
			throw new AccessActivityRuntimeException("身份证号不能为空");
		}
		if(idCard.length() != 18 && idCard.length()!= 15){
			throw new AccessActivityRuntimeException("身份证号长度有误");
		}
		String numberPart = idCard.length() == 18 ? idCard.substring(0, idCard.length()-1) : idCard;
		if(!NumberUtils.isNumeric(numberPart)){
			throw new AccessActivityRuntimeException("身份证号格式有误");
		}
	}

	// 是否可修改校验
	private void checkModify(ConsumerDto consumerDto, String alipay, String name, String phone, String idCard){
		// 校验支付宝账号
		if(StringUtils.isNotBlank(consumerDto.getLastAlipay()) && !consumerDto.getLastAlipay().equals(alipay)){
			throw new AccessActivityRuntimeException("支付宝账号不能修改");
		}
		// 校验姓名
		if(StringUtils.isNotBlank(consumerDto.getLastRealname()) && !consumerDto.getLastRealname().equals(name)){
			throw new AccessActivityRuntimeException("姓名不能修改");
		}
		// 校验手机号
		if(StringUtils.isNotBlank(consumerDto.getLastPhone()) && !consumerDto.getLastPhone().equals(phone)){
			throw new AccessActivityRuntimeException("手机号不能修改");
		}
		// 校验身份证号
		DubboResult<String> dubboResult = remoteConsumerService.findExtra(consumerDto.getId(), ID_CARD);
		if(null == dubboResult || !dubboResult.isSuccess()){
			throw new AccessActivityRuntimeException("获取用户扩展信息异常，请重试");
		}
		if(StringUtils.isNotBlank(dubboResult.getResult()) && !dubboResult.getResult().equals(idCard)){
			throw new AccessActivityRuntimeException("身份证号不能修改");
		}
	}

	// 绑定
	private void saveUserInfo(ConsumerDto consumerDto, HttpServletRequest request){
		String alipay= request.getParameter(CommonConstants.ACCOUNT);
		String phone = request.getParameter("phone");
		String name = request.getParameter(CommonConstants.REALNAME);
		String idCard = request.getParameter(ID_CARD);
		if(StringUtils.isBlank(consumerDto.getLastPhone()) && remoteConsumerService.updateLastPhone(consumerDto.getId(), phone) <= 0){
			log.info("陌陌支付宝领奖，手机号绑定失败");
		}
		if(StringUtils.isBlank(consumerDto.getLastAlipay()) && remoteConsumerService.updateLastAlipay(consumerDto.getId(), alipay, name) <= 0){
			log.info("陌陌支付宝领奖，支付宝账号绑定失败");
		}
		DubboResult<String> dubboResult = remoteConsumerService.findExtra(consumerDto.getId(), ID_CARD);
		if(null == dubboResult || !dubboResult.isSuccess() || StringUtils.isBlank(dubboResult.getResult())){
			DubboResult<Integer> updateIdCard = remoteConsumerService.updateExtra(consumerDto.getId(), ID_CARD, idCard);
			if(null == updateIdCard || !updateIdCard.isSuccess()){
				log.info("陌陌支付宝领奖，身份证号绑定失败");
			}
		}
		AccessLogFilter.putExPair("suc",1);
		AccessLogFilter.putExPair("uid",consumerDto.getPartnerUserId());
		AccessLogFilter.putExPair("reqType",4);
		AccessLogFilter.putExPair("idcard",idCard);
		AccessLogFilter.putExPair("aliAccount",alipay);
		AccessLogFilter.putExPair("phone",phone);
		AccessLogFilter.putExPair("name",name);
		AccessLogFilter.putExPair("result",true);
	}



	private void checkAlipayExchangeLimit(Long appId, String account, String name){

		int code = alipayTakePrizeCheckService.check(account, name, appId);
		if (code==0) {
			return;
		}
		if (code==1) {
			throw new AccessActivityRuntimeException("您的账号领奖次数今日已达上限，明天再来哦");
		}
		if (code==2) {
			throw new AccessActivityRuntimeException("您的账户名领奖次数今日已达上限，明天再来哦");
		}
	}

	@Override
	public void updateMainOrderId(TakePrizeVo takePrize, OrdersDto order, ConsumerExchangeRecordDto record) {
		if(ConsumerExchangeRecordDto.TypeHdtoolLottery == takePrize.getRecordType().intValue()){
			remoteCreditsHdtoolOrdersService.updateMainOrderId(takePrize.getConsumerId(), takePrize.getOrderId(), order.getId(), order.getOrderNum());
		}else if(ConsumerExchangeRecordDto.TypePluginLottery == takePrize.getRecordType().intValue() || ConsumerExchangeRecordDto.TypeSign == takePrize.getRecordType().intValue()
				|| ConsumerExchangeRecordDto.TypeCreditGame == takePrize.getRecordType().intValue()
				|| ConsumerExchangeRecordDto.TypeSuperSurprise == takePrize.getRecordType().intValue()){
			remoteActivityOrderService.updateMainOrderNum(takePrize.getOrderNum(),order.getOrderNum());
		}else if (ConsumerExchangeRecordDto.TypeGuess == takePrize.getRecordType().intValue()) {
			remoteGuessOrdersConsumerService.updateMainOrderId(takePrize.getConsumerId(), takePrize.getOrderId(), order.getId(), order.getOrderNum());
		}else if (ConsumerExchangeRecordDto.TypeSingleLottery == takePrize.getRecordType().intValue()) {
			remoteSingleLotteryOrderService.updateMainOrderId(takePrize.getOrderId(), order.getId());
		}else if (ConsumerExchangeRecordDto.TypeNgame == takePrize.getRecordType().intValue()) {
			remoteNgameOrdersConsumerService.updateMainOrderId(takePrize.getConsumerId(), takePrize.getOrderId(), order.getId(), order.getOrderNum());
		}else if (ConsumerExchangeRecordDto.TypeQuestion == takePrize.getRecordType().intValue()) {
			remoteDuibaQuestionAnswerOrdersService.updateMainOrderId(takePrize.getOrderId(), order.getId(), order.getOrderNum());
		}else if (ConsumerExchangeRecordDto.TypeQuizz == takePrize.getRecordType().intValue()) {
			remoteQuizzOrderTextChangeService.updateMainOrderId(takePrize.getConsumerId(),takePrize.getOrderId(), order.getId(), order.getOrderNum());
		} else if (ConsumerExchangeRecordDto.TypeCentscan == takePrize.getRecordType()) {
			remoteCentscanPrizeRecordService.updateOrderId(takePrize.getOrderId(), order.getId());
		} else if (ConsumerExchangeRecordDto.TypeUnderseaGame == takePrize.getRecordType()) {
			remoteUnderseaGamePrizeRecordService.updateOrderId(takePrize.getOrderId(), order.getId());
		} else if (ConsumerExchangeRecordDto.TypeTaiLongTaskUnit == takePrize.getRecordType()) {
			TaskRecordDto taskRecordDto = new TaskRecordDto();
			taskRecordDto.setId(takePrize.getOrderId());
			taskRecordDto.setOrderNum(order.getOrderNum());
			remoteTaiLongTaskUnitService.saveOrUpdateTaskRecord(taskRecordDto);
		}
		// 更新小红点
		ConsumerExchangeRecordDto cer4update = new ConsumerExchangeRecordDto(record.getId());
		cer4update.setConsumerId(record.getConsumerId());
		cer4update.setSwitchs(record.getSwitchs());
		cer4update.setOrderId(order.getId());
		if (order.getType().equals(ItemDto.TypeCoupon)) {
			DubboResult<GoodsCouponDto> dubboResult1Con = remoteItemCouponGoodsService.findCoupon(takePrize.getItemKeyDto(), order.getCouponId());
			if (!dubboResult1Con.isSuccess()) {
				log.warn("find GoodsCouponDto failed, msg={}", dubboResult1Con.getMsg());
				throw new AccessActivityRuntimeException(dubboResult1Con.getMsg());
			}
			cer4update.setOverDue(dubboResult1Con.getResult().getOverDue());
		} else {
			//领奖成功后兑换记录有效期设置为100年
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.YEAR, 100);
			cer4update.setOverDue(cal.getTime());
		}
		// 汇丰定制：机会订单绑定领奖订单id
		changeOrderBindOrderId(cer4update,order.getAppId());
		remoteConsumerExchangeRecordService.update(cer4update);
	}

	/**
	 * 汇丰定制：机会订单绑定领奖订单id
	 */
	private void changeOrderBindOrderId(ConsumerExchangeRecordDto record,Long appId) {
		try {
			// 不是汇丰应用跳过
			if (!hsbcConfig.getAppIds().contains(appId)){
				return;
			}
			Long recordId = record.getId();
			Long orderId = record.getOrderId();
			if (recordId == null || orderId == null){
				return;
			}
			// 根据兑换记录找到机会订单id
			HsbcChanceOrderDto chanceOrder = remoteHsbcChanceOrderService.findByIdConsumerRecordId(record.getConsumerId(), recordId);
			if (chanceOrder == null){
				return;
			}
			// 状态校验，此时状态应该是未领奖
			if (!Objects.equals(HsbcChanceOrderUseStatusEnum.UN_RECEIVE.getCode(),chanceOrder.getUseStatus())){
				log.error("changeOrderBindOrderId 状态异常, chanceOrder.getId={}, recordId={}, useStatus={}", chanceOrder.getId(), recordId, chanceOrder.getUseStatus());
				return;
			}
			// 标记机会订单已领奖
			remoteHsbcChanceOrderService.markOrderReveivePrize(chanceOrder.getId(),orderId);
		}catch (Exception e){
			log.error("changeOrderBindOrderId error",e);
		}
	}

	private OrdersDto createMainOrder(TakePrizeVo takePrize, HttpServletRequest request) throws AccessActivityException, StatusException {
		OrdersDto order = null;
		if (ItemDto.TypeVirtual.equals(takePrize.getPrizeType())) {
			String username = null;
			if (wxFavorCouponService.isWxFavorCoupon(takePrize.getItemKeyDto())) {
				username = wxFavorCouponService.getOpenId(takePrize.getConsumerId());
			} else {
				if (request != null) {
					username = StringUtils.trimToNull(request.getParameter(CommonConstants.USERNAME));
				}
				if (username != null && (username.contains(PriceDegreeDto.BIZPARAMS_REX) || username.contains("&"))) {
					throw new AccessActivityRuntimeException("输入的用户名含有的特殊字符");
				}
				if (StringUtils.isNotBlank(username) && ItemKeyUtils.isNeedUserAccount(takePrize.getItemKeyDto())) {
					// 根据商品配置的账号格式进行处理
					Long appItemId = Optional.ofNullable(takePrize.getItemKeyDto()).map(ItemKeyDto::getAppItem).map(AppItemDto::getId).orElse(null);
					Long itemId = Optional.ofNullable(takePrize.getItemKeyDto()).map(ItemKeyDto::getItem).map(ItemDto::getId).orElse(null);
					username = checkAccountFormat(appItemId, itemId, username);
					if (StringUtils.isBlank(username)) {
						throw new AccessActivityRuntimeException("输入账号内容不可全为空格");
					}
					// 汇丰定制校验
					hsbcCustomCheck(takePrize, username);
				}
			}
			String bizParams = PriceDegreeDto.getBizParams(takePrize.getFacePrice(),username);
			order = virtualCreator.innerCreateOrder(takePrize,bizParams);
		} else if (ItemDto.TypeCoupon.equals(takePrize.getPrizeType())) {
			order = couponCreator.innerCouponCreateOrder(takePrize);
		}else if (ItemDto.TypeObject.equals(takePrize.getPrizeType())) {
			ReceiveAddressDto addressDto = getReceiveAddressDto(RequestLocal.getCid(),RequestLocal.getAppId(),takePrize.getAddressId());
			if (addressDto != null) {
				String addrName = addressDto.getAddrName();
				String addrPhone = addressDto.getAddrPhone();
				String addrProvince = addressDto.getAddrProvince();
				String addrCity = addressDto.getAddrCity();
				String addrArea = addressDto.getAddrArea();
				String addrStreet = addressDto.getAddrStreet();
				String addrDetail = addressDto.getAddrDetail();
				ObjectAddress address=ObjectCreator.generateAddress(addrName, addrPhone, addrProvince, addrCity, addrArea, addrStreet, addrDetail);
				order = objectCreator.innerObjectCreateOrder(address,takePrize);
			}
		}else if (ItemDto.TypePhonebill.equals(takePrize.getPrizeType())) {
			String phone = StringUtils.trimToNull(request.getParameter(CommonConstants.PHONE));
			String province = StringUtils.trimToNull(request.getParameter(CommonConstants.PROVINCE));
			String mobile = StringUtils.trimToNull(request.getParameter(CommonConstants.MOBILE));
			// 汇丰定制校验
			hsbcCustomCheck(takePrize, Optional.ofNullable(phone).orElse(mobile));
			order = phonebillCreator.innerPhonebillCreateOrder(takePrize,phone,province,mobile);
		} else if (ItemDto.TypeQB.equals(takePrize.getPrizeType())) {
			String qq = StringUtils.trimToNull(request.getParameter(CommonConstants.QQ));
			order = qBCreator.innerQbCreateOrder(takePrize,qq);
		} else if (ItemDto.TypeAlipay.equals(takePrize.getPrizeType())) {
			String account = StringUtils.trimToNull(request.getParameter(CommonConstants.ACCOUNT));
			String realname = StringEscapeUtils.unescapeHtml4(StringUtils.trimToNull(request.getParameter(CommonConstants.REALNAME)));
			// 汇丰定制校验
			hsbcCustomCheck(takePrize, account);
			order = alipayOfficialCreator.innerAlipayCreateOrder(takePrize,account,realname);
		}
		return order;
	}

	private void hsbcCustomCheck(TakePrizeVo takePrize, String username) {
		AppSimpleDto app = takePrize.getApp();
		if (Objects.isNull(app) || !hsbcConfig.getAppIds().contains(app.getId())) {
			return;
		}
		ConsumerDto consumerDto = takePrize.getConsumerDto();

		HsbcMatchPhoneParam hsbcMatchPhoneParam = new HsbcMatchPhoneParam();
		hsbcMatchPhoneParam.setPhone(username);
		hsbcMatchPhoneParam.setUid(consumerDto.getPartnerUserId());
		hsbcMatchPhoneParam.setAppId(app.getId());
		boolean match = remoteHsbcBankService.matchPhone(hsbcMatchPhoneParam);
		//todo 测试环境不校验
		if(SpringEnvironmentUtils.isTestEnv() || SpringEnvironmentUtils.isDevEnv()){
			return;
		}
		if (!match) {
			throw new AccessActivityRuntimeException("仅限使用app登录手机号进行充值");
		}
	}

	/**
	 * 是否是手机号类型的虚拟商品校验
	 * @param appItemId
	 * @param itemId
	 * @return
	 */
	private Boolean isPhoneAccountFormat(Long appItemId, Long itemId) {
		if (Objects.nonNull(appItemId)) {
			AppItemExtraDto appItemExtraDto = remoteGoodsAppItemExtraService.findByAppItemId(appItemId).getResult();
			Integer accountFormat = Optional.ofNullable(appItemExtraDto).map(AppItemExtraDto::getCustomPromptsJson).map(json -> json.getInteger(AppItemExtraDto.ACCOUNT_FORMAT)).orElse(-1);
			return VirtualAccountFormatEnum.MOBILE.getCode().equals(accountFormat);
		}

		List<KeyValueDto> itemAllApi = remoteItemNewExtraService.findItemAllApi(itemId);
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemAllApi)) {
			return false;
		}
		for (KeyValueDto keyValueDto : itemAllApi) {
			if (!ItemNewExtraEnum.ACCOUNT.getType().equals(keyValueDto.getPropName())) {
				continue;
			}
			JSONObject jsonObject = JSONObject.parseObject(keyValueDto.getPropValue());
			Integer accountFormat = Optional.ofNullable(jsonObject).map(json -> json.getInteger(AppItemExtraDto.ACCOUNT_FORMAT)).orElse(-1);
			return VirtualAccountFormatEnum.MOBILE.getCode().equals(accountFormat);
		}
		return false;
	}

	@Override
	public String checkAccountFormat(Long appItemId, Long itemId, String account) {
		if (StringUtils.isEmpty(account)) {
			return account;
		}
		if (Objects.nonNull(appItemId)) {
			AppItemExtraDto appItemExtraDto = remoteGoodsAppItemExtraService.findByAppItemId(appItemId).getResult();
			Integer accountFormat = Optional.ofNullable(appItemExtraDto).map(AppItemExtraDto::getCustomPromptsJson).map(json -> json.getInteger(AppItemExtraDto.ACCOUNT_FORMAT)).orElse(-1);
			if (VirtualAccountFormatEnum.BLANK.getCode().equals(accountFormat)) {
				account = account.replaceAll(" ", "");
			}
			return account;
		}
		if (Objects.isNull(itemId)) {
			return account;
		}
		List<KeyValueDto> itemAllApi = remoteItemNewExtraService.findItemAllApi(itemId);
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemAllApi)) {
			return account;
		}
		for (KeyValueDto keyValueDto : itemAllApi) {
			if (!ItemNewExtraEnum.ACCOUNT.getType().equals(keyValueDto.getPropName())) {
				continue;
			}
			JSONObject jsonObject = JSONObject.parseObject(keyValueDto.getPropValue());
			Integer accountFormat = Optional.ofNullable(jsonObject).map(json -> json.getInteger(AppItemExtraDto.ACCOUNT_FORMAT)).orElse(-1);
			if (VirtualAccountFormatEnum.BLANK.getCode().equals(accountFormat)) {
				account = account.replaceAll(" ", "");
			}
			break;
		}
		return account;
	}

	public ReceiveAddressDto getReceiveAddressDto( Long cid,Long appId, Long addressId) throws AccessActivityException{
		// 地址定制
		if (remoteCustomAddressService.isCustomAddressAppId(appId)) {
			try {
				ReceiveAddressDto receiveAddressDto = remoteCustomAddressService.buildDuibaReceiveAddressDto(appId, RequestLocal.getPartnerUserId(), addressId!=null ? addressId.toString() : "0");
				if (Objects.isNull(receiveAddressDto)) {
					throw new AccessActivityException("定制默认地址为空");
				}
				return receiveAddressDto;
			} catch (Exception e) {
				log.warn("获取定制默认地址异常", e);
				throw new AccessActivityException("实物领取地址异常");
			}
		}
		if(abcConfig.getAppIdSet().contains(appId)){
			ConsumerDto consumer = remoteConsumerService.find(cid);
			Cookie cookie =  CookieUtil.getCookieByName(RequestLocal.getRequest(),TRANSFER);
			if(cookie == null ){
				throw new AccessActivityException("农行 获取地址  用户信息异常");
			}
			String transfer = cookie.getValue();
			String[] datas = transfer.split(SPE_CHAT);
			String seqno = datas[0];
			String channel = datas[1];
			return abcBankService.getDefaultAddress(consumer,seqno,channel);
		}else{
			ReceiveAddressDto dto = remoteReceiveAddressService.getReceiveAddressCompatible(cid);
			if (dto == null || StringUtils.isBlank(dto.getAddrProvince()) || StringUtils.isBlank(dto.getAddrCity())) {
				throw new AccessActivityException("请填写实物领取地址");
			}
			return dto;
		}
	}

	private void updateSuperSurpriseRecordStatus(String orderNum){
		SuperSurpriseJoinRecordDto joinRecordDto = remoteSuperSurpriseService.findJoinRecordByOrderNum(orderNum);
		if(Objects.nonNull(joinRecordDto)){
			remoteSuperSurpriseService.updateExchangeStatus(joinRecordDto.getId(), RecordExchangeStatus.EXCHANGE_WAIT.getCode(),RecordExchangeStatus.EXCHANGE_SUCCESS.getCode());
		}else{
			RankListProvidePrizeRecordDto providePrizeRecordDto = remoteSuperSurpriseService.getRankListProvidePrizeRecordByOrderNum(orderNum);
			if(Objects.nonNull(providePrizeRecordDto)){
				remoteSuperSurpriseService.updateRankListProvidePrizeStatus(providePrizeRecordDto.getId(),RecordExchangeStatus.EXCHANGE_WAIT.getCode(),RecordExchangeStatus.EXCHANGE_SUCCESS.getCode());
			}
		}
	}

	private void rollBackSuperSurpriseRecordStatus(String orderNum){
		SuperSurpriseJoinRecordDto joinRecordDto = remoteSuperSurpriseService.findJoinRecordByOrderNum(orderNum);
		if(Objects.nonNull(joinRecordDto)){
			remoteSuperSurpriseService.updateExchangeStatus(joinRecordDto.getId(), RecordExchangeStatus.EXCHANGE_SUCCESS.getCode(),RecordExchangeStatus.EXCHANGE_WAIT.getCode());
		}else{
			RankListProvidePrizeRecordDto providePrizeRecordDto = remoteSuperSurpriseService.getRankListProvidePrizeRecordByOrderNum(orderNum);
			if(Objects.nonNull(providePrizeRecordDto)){
				remoteSuperSurpriseService.updateRankListProvidePrizeStatus(providePrizeRecordDto.getId(),RecordExchangeStatus.EXCHANGE_SUCCESS.getCode(),RecordExchangeStatus.EXCHANGE_WAIT.getCode());
			}
		}
	}

	private void changerPirzeStatus(TakePrizeVo takePrize) { //NOSONAR
		log.info("------------修改子订单状态--------------");
		log.info("orderId:{}，recordType:{}",takePrize.getOrderId(),takePrize.getRecordType());
		int ret = 0;
		if(ConsumerExchangeRecordDto.TypeHdtoolLottery == takePrize.getRecordType().intValue()){
			ret = remoteCreditsHdtoolOrdersService.doTakePrize(takePrize.getConsumerId(),takePrize.getOrderId());
			updateMarketingHdtoolPrizeStatus(takePrize, MarketingHdtoolPrizeRecordDto.STATUS_SUCCESS);
		}else if(ConsumerExchangeRecordDto.TypeSuperSurprise == takePrize.getRecordType().intValue()){
			DubboResult<Boolean> result = remoteActivityOrderService.exchangeStatusToSuccess(takePrize.getOrderNum());
			if (result.isSuccess() && result.getResult()) {
				ret =1;
			}
			updateSuperSurpriseRecordStatus(takePrize.getOrderNum());
		} else if(ConsumerExchangeRecordDto.TypePluginLottery == takePrize.getRecordType().intValue() || ConsumerExchangeRecordDto.TypeSign == takePrize.getRecordType().intValue()
				|| ConsumerExchangeRecordDto.TypeCreditGame == takePrize.getRecordType().intValue()){
			DubboResult<Boolean> result = remoteActivityOrderService.exchangeStatusToSuccess(takePrize.getOrderNum());
			if (result.isSuccess() && result.getResult()) {
				ret =1;
			}
		} else if (ConsumerExchangeRecordDto.TypeGuess == takePrize.getRecordType().intValue()) {
			ret = remoteGuessOrdersConsumerService.doTakePrize(takePrize.getConsumerId(),takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TypeSingleLottery == takePrize.getRecordType().intValue()) {
			ret = remoteSingleLotteryOrderService.doTakePrize(takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TypeNgame == takePrize.getRecordType().intValue()) {
			ret = remoteNgameOrdersConsumerService.doTakePrize(takePrize.getConsumerId(),takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TypeQuestion == takePrize.getRecordType().intValue()) {
			ret = remoteDuibaQuestionAnswerOrdersService.doTakePrize(takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TypeQuizz == takePrize.getRecordType().intValue()) {
			ret = remoteQuizzOrdersStatusChangeService.doTakePrize(takePrize.getConsumerId(),takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TYPE_HAPPY_CODE == takePrize.getRecordType()) {
			ret = remoteHappyCodeOrderService.doTakePrize(takePrize.getConsumerId(), takePrize.getOrderId());
		} else if (ConsumerExchangeRecordDto.TypeLuckyCode == takePrize.getRecordType()) {
            ret = remoteLuckyCodeUserAwardRecordService.updateStateByIds(Collections.singletonList(takePrize.getOrderId()), LuckyCodeUserAwardStateEnum.SUCCESS_DRAW.getCode());
		} else if (ConsumerExchangeRecordDto.TypeHappyCodeNew == takePrize.getRecordType()) {
			HappyCodeUserRecordDto recordDto = new HappyCodeUserRecordDto();
			recordDto.setId(takePrize.getOrderId());
			recordDto.setRewardStatus(HappyRewardStatusEnum.REWARD_STATUS_YES);
			ret = remoteHappyCodeUserRecordService.updateById(recordDto);
		} else if (ConsumerExchangeRecordDto.TypeDigGold == takePrize.getRecordType()) {
			DigGoldRecordDto recordDto = new DigGoldRecordDto();
			recordDto.setId(takePrize.getOrderId());
			recordDto.setExchangeStatus(RecordExchangeStatusEnum.GET_PRIZE_SUC.getType());
			if(remoteDigGoldService.updateRecord(recordDto)) {
				ret = 1;
			}
		} else if (ConsumerExchangeRecordDto.TypeUnderstandLevel == takePrize.getRecordType()) {
			UnderstandLevelAssistRecordDto recordDto = new UnderstandLevelAssistRecordDto();
			recordDto.setId(takePrize.getOrderId());
			recordDto.setExchangeStatus(RecordExchangeStatusEnum.GET_PRIZE_SUC.getType());
			remoteUnderstandLevelService.updateAssistRecordById(recordDto);
			ret = 1;
		} else if (ConsumerExchangeRecordDto.TypeHaggle == takePrize.getRecordType()) {
			HaggleOpenRecordDto recordDto = new HaggleOpenRecordDto();
			recordDto.setId(takePrize.getOrderId());
			recordDto.setRecordStatus(RecordStatusEnum.RECEIVED.getType());
			if (remoteHaggleService.updateOpenRecord(recordDto)) {
				ret = 1;
			}
		} else if (ConsumerExchangeRecordDto.TypeCentscan == takePrize.getRecordType()) {
			remoteCentscanPrizeRecordService.updateStatus(takePrize.getOrderId(), CentscanPrizeRecordDto.STATUS_SUCCESS);
			ret = 1;
		} else if (ConsumerExchangeRecordDto.TypeUnderseaGame == takePrize.getRecordType()) {
			remoteUnderseaGamePrizeRecordService.updateStatus(takePrize.getOrderId(), UnderseaGamePrizeRecordDto.STATUS_SUCCESS);
			ret = 1;
		} else if (ConsumerExchangeRecordDto.TypeTaiLongTaskUnit == takePrize.getRecordType()) {
			remoteTaiLongTaskUnitService.updateRecordStatus(takePrize.getOrderId(), TaskStatusEnum.RECEIVED, TaskStatusEnum.SUCCESS);
			ret = 1;
		}
		if (ret <= 0) {
			throw new AccessActivityRuntimeException(CommonConstants.MSG_SERVER_EXCEPTION_YJLJ);
		}
	}

	/**
	 * 更新营销活动中奖记录状态 <br>
	 * 用于使用通用活动工具实现的营销活动
	 * @param takePrize
	 * @param status
	 */
	private void updateMarketingHdtoolPrizeStatus(TakePrizeVo takePrize, int status) {
		OrdersVO order = takePrize.getSubOrder();
		if (order == null) {
			return;
		}
		try {
			if (isMarketingHdtool(order)) {
				remoteMarketingHdtoolPrizeRecordService.updateStatusByHdtoolOrderId(order.getId(), status);
			}
		} catch (Exception e) {
			log.warn("营销活动更新中奖流水状态异常: hdtoolOrderId=" + order.getId(), e);
		}
	}

	/**
	 * 是否是营销活动工具
	 * @param order
	 * @return
	 */
	private boolean isMarketingHdtool(OrdersVO order) {
		Integer type = order.getHdtoolType();
		return ActivityUniformityTypeEnum.RICH_MAN.getCode().equals(type);
	}

	private void checkExchangeLimitNew(TakePrizeVo takePrize,HttpServletRequest request) throws AccessActivityException {
		String account ;
		//参数验证
		if(takePrize.getPrizeType().equals(ItemDto.TypePhonebill)){
			account = StringUtils.trimToNull(request.getParameter(CommonConstants.PHONE));
			String province = StringUtils.trimToNull(request.getParameter(CommonConstants.PROVINCE));
			String mobile = StringUtils.trimToNull(request.getParameter(CommonConstants.MOBILE));
			if (account == null || province == null || mobile == null) {
				throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_CSQS);
			}
		}else if(takePrize.getPrizeType().equals(ItemDto.TypeQB)){
			account = StringUtils.trimToNull(request.getParameter(CommonConstants.QQ));
			if (account == null) {
				throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_CSQS);
			}
		}else{
			account = StringUtils.trimToNull(request.getParameter(CommonConstants.ACCOUNT));
			String realname = StringUtils.trimToNull(request.getParameter(CommonConstants.REALNAME));
			if (account == null || realname == null) {
				throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_CSQS);
			}
			if (account.contains("^") || account.contains("|") || realname.contains("^") || realname.contains("|")) {
				throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_BDBHTSZF);
			}
		}
		anticheatExchangeLimitService.checkExchangeLimitNew(takePrize,account);
	}

	@Override
	public void checkTakePrizeItemKey(ItemKeyDto itemKeyDto) throws AccessActivityException {
		//奖品状态判断
		if (itemKeyDto == null) {
			throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_YXJ);
		}
		if (itemKeyDto.getAppItem() != null && itemKeyDto.getAppItem().getDeleted()) {
			throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_YXJ);
		}
		if (itemKeyDto.getItem() != null && (!itemKeyDto.getItem().getEnable() || itemKeyDto.getItem().getDeleted())) {
			throw new AccessActivityException(CommonConstants.MSG_SERVER_EXCEPTION_YXJ);
		}
	}

	@Override
	public String doTakePrize(AppSimpleDto app, ConsumerDto consumer, ConsumerExchangeRecordDto record, OrdersVO order,HttpServletRequest request) throws AccessActivityException {
		JSONObject jsonObject = new JSONObject();
		ItemKeyDto itemKey = commonService.findItemKeyDto(order.getAppItemId(),order.getItemId(),app.getId());
		//虚拟商品领奖验证
		if(checkVirtual(order,app,jsonObject,itemKey)){
			return jsonObject.toString();
		}
		//校验兑吧商品
		if(verifyDuibaAddressLimit(consumer.getId(),jsonObject,itemKey.getItem())){
			return jsonObject.toString();
		}
		//央视频针对待领取定制
		if (yspObjectCheck(consumer, record, jsonObject, itemKey)){
			return jsonObject.toString();}
		//活动工具
		try{
			TakePrizeVo takePrize = makeTakePrizeInfo(app,record,order,itemKey,request);
			takePrize.setConsumerDto(consumer);
			String tradeDomain=domainService.getSystemDomain(app.getId()).getTradeDomain();
			//领奖
			OrdersDto orderDo = this.takePrize(takePrize, request,record);
			if (orderDo != null && null != orderDo.getId()) {
				jsonObject.put(CommonConstants.SUCCESS_KEY, true);
				jsonObject.put(CommonConstants.MESSAGE_KEY, "领奖成功");
				if(ItemDto.TypeObject.equals(orderDo.getType())||ItemDto.TypeVirtual.equals(orderDo.getType())){
					jsonObject.put("url", tradeDomain + "/crecord/recordDetailNew?id="+orderDo.getId()+"&after=1");
				} else {
					jsonObject.put("url", tradeDomain + "/crecord/recordDetail?orderId=" + orderDo.getId() + "&after=1");
				}
			}
			executorService.execute(() -> sendZhrsTaskPrizeMsg(consumer, itemKey, record, orderDo, order));
		}catch(AccessActivityRuntimeException e){
			log.info("领奖失败,errMsg={},consumerId={}",e.getMessage(),consumer.getId());
			jsonObject.put(CommonConstants.SUCCESS_KEY, false);
			jsonObject.put(CommonConstants.MESSAGE_KEY, e.getMessage());
			executorService.execute(() -> sendZhrsTaskPrizeMsg(consumer, itemKey, record, null, order));
			return jsonObject.toJSONString();
		}catch(Exception e){
			log.error("领奖失败",e);
			jsonObject.put(CommonConstants.SUCCESS_KEY, false);
			jsonObject.put(CommonConstants.MESSAGE_KEY, e.getMessage());
			executorService.execute(() -> sendZhrsTaskPrizeMsg(consumer, itemKey, record, null, order));
			return jsonObject.toJSONString();
		}
		return jsonObject.toJSONString();
	}

	private boolean yspObjectCheck(ConsumerDto consumer, ConsumerExchangeRecordDto record, JSONObject jsonObject, ItemKeyDto itemKey) {
		if (yspObjectCheckConfig.getCheckAppIds().contains(consumer.getAppId()) && ItemDto.TypeObject.equals(itemKey.getAppItem().getType())) {
			Boolean isPass = objectVerificationService.ifPass(consumer.getAppId(), consumer.getPartnerUserId(), String.valueOf(record.getId()), itemKey.getAppItem().getId());
			if (!isPass) {
				jsonObject.put(CommonConstants.SUCCESS_KEY, false);
				jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_SERVER_EXCEPTION_OBJECT_CHECK);
				return true;
			}
		}
		return false;
	}

	public void sendZhrsTaskPrizeMsg(ConsumerDto consumer, ItemKeyDto itemKey, ConsumerExchangeRecordDto record, OrdersDto order, OrdersVO ordersVO) {
		if (!zhrsConfig.getAppIds().contains(consumer.getAppId())) {
			return;
		}
		if (!Objects.equals(itemKey.getItemType(), ItemDto.TypeObject)) {
			return;
		}
		ZhrsTaskPrizeRequest request = new ZhrsTaskPrizeRequest();
		request.setCampCd(zhrsConfig.getCampCd());
		request.setCampaignCd(zhrsConfig.getCampaignCd());
		request.setLeadsCountry("CHN");
		request.setAgtCd(getProjectXAgtCd(consumer));
		setAddress(request,consumer);
		request.setLeadsTime(DateUtil.getSecondStr(new Date()));
		request.setRqstSn(order != null && null != order.getId() ? order.getOrderNum() : ordersVO.getOrderNum());
		String newSign = MD5Util.toMD5String(request.getRqstSn() + request.getCampCd() + request.getLeadsPhone() + request.getLeadsTime() + zhrsConfig.getSalt());
		request.setSign(newSign);
		request.setExt1(itemKey.getAppItem() != null ? itemKey.getAppItem().getTitle() : itemKey.getItem().getName());
		request.setExt2(DateUtil.getSecondStr(record.getGmtCreate()));
		request.setExt3(String.valueOf(order != null && null != order.getId()));
		log.info("中宏人寿-同步中奖信息request={}", JSON.toJSONString(request));
		HttpPost httpPost = new HttpPost(zhrsConfig.getZhrsUrl());
		httpPost.setEntity(new StringEntity(JSON.toJSONString(record), "UTF-8"));
		httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
		httpPost.setHeader("token", zhrsConfig.getToken());
		try(CloseableHttpResponse httpResponse = httpClient.execute(httpPost)) {
			String responseStr = EntityUtils.toString(httpResponse.getEntity());
			log.info("中宏人寿-同步中奖信息response={}", responseStr);
		} catch (Exception e) {
			log.warn("中宏人寿-同步中奖信息异常", e);
		}
	}

	/**
	 * 查询营销员编码
	 * https://docs.dui88.com/project/1482/interface/api/131958
	 * @return
	 */
	public String getProjectXAgtCd(ConsumerDto consumer) {
		StringBuilder urlStr = new StringBuilder(zhrsConfig.getProjectxUrl());
		String timestamp = String.valueOf(System.currentTimeMillis());
		String newSign = MD5Util.toMD5String(timestamp + consumer.getId() + "duiba#@zhonghongrenshou");
		urlStr.append("?").append("userId=").append(consumer.getId()).append("&timestamp=").append(timestamp).append("&sign=").append(newSign);
		HttpGet httpGet = new HttpGet(urlStr.toString());
		log.info("中宏人寿-查询营销员编码url={}", urlStr.toString());
		try(CloseableHttpResponse httpResponse = httpClient.execute(httpGet)) {
			String responseStr = EntityUtils.toString(httpResponse.getEntity());
			log.info("中宏人寿-查询营销员编码url={}", responseStr);
			if (StringUtils.isNotBlank(responseStr) && StringUtils.isNotBlank(JSON.parseObject(responseStr).getString("data"))) {
				String dataStr = JSON.parseObject(responseStr).getString("data");
				return JSON.parseObject(dataStr).getString("ucMapping");
			}
		} catch (Exception e) {
			log.warn("中宏人寿-请求星速台查询异常,cid={}", consumer.getId(), e);
		}
		return null;
	}

	public void setAddress(ZhrsTaskPrizeRequest request, ConsumerDto consumer) {
		try {
			ReceiveAddressDto addressDto = getReceiveAddressDto( consumer.getId(), consumer.getAppId(),null);
			request.setLeadsNm(addressDto.getAddrName());
			request.setLeadsPhone(addressDto.getAddrPhone());
			request.setLeadsProvince(addressDto.getAddrCode().substring(0,2) + "0000");
			request.setLeadsCity(addressDto.getAddrCode().substring(0,4) + "00");
			request.setLeadsCounty(addressDto.getAddrCode().substring(0,6));
			request.setMslDetailedAddress(addressDto.getAddrStreet() + addressDto.getAddrDetail());
		} catch (Exception e) {
			log.warn("中宏人寿-地址异常,cid={}", consumer.getId(), e);
		}
	}



	private TakePrizeVo makeTakePrizeInfo(AppSimpleDto app,ConsumerExchangeRecordDto record, OrdersVO order, ItemKeyDto itemKey, HttpServletRequest request) { //NOSONAR
		//查询商品信息
		TakePrizeVo takePrize = null;
		RequestParams params = new RequestParams();
		params.setIp(RequestLocal.getIp());
		params.setUserAgent(RequestLocal.getUserAgent());
		params.setConsumerId(order.getConsumerId());
		params.setCookies(com.duiba.activity.accessweb.tool.RequestParams.parse(request).getCookies());


		if(ConsumerExchangeRecordDto.TypeHdtoolLottery == record.getType()){
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeHdtoolLottery,params,OrdersDto.ChargeModeHdtool,OrdersDto.RelationTypeHdtool,OrdersDto.SubOrderTypeHdtool);
			takePrize.setOperatingActivityId(order.getOperatingActivityId());
			takePrize.setIsduiActivity(false);
			setIsduiActivityInfo(takePrize);
		}else if(ConsumerExchangeRecordDto.TypeSuperSurprise == record.getType()){
			//天降好礼
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeSuperSurprise,params,OrdersDto.ChargeModeSuperSurprise,OrdersDto.RelationTypeSuperSurprise,OrdersDto.SubOrderTypeSuperSurprise);
			takePrize.setOperatingActivityId(order.getActivityId());
			takePrize.setIsduiActivity(false);
			//插件订单号回写主订单
			setOrderNum4Plugin(takePrize);
		}else if (ConsumerExchangeRecordDto.TypePluginLottery == record.getType()){
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypePluginLottery,params,OrdersDto.ChargeModeActivity,OrdersDto.RelationTypePlugin,OrdersDto.SubOrderTypePlugin);
			//开发者后台集卡活动开奖走的是插件子订单
			takePrize.setIsduiActivity(!DEVELOPER_COST_ACTIVITY_LIST.contains(order.getActivityType()));
			//pk赛使用,如果是appactivityId 不为空，用这个当OperatingActivityId，否则用duibaactivityid
			if(Objects.equals(ActivityUniformityTypeEnum.PKH5.getCode().toString(),order.getActivityType())){
				takePrize.setOperatingActivityId(null != order.getAppActivityId() ? order.getAppActivityId() : order.getDuibaActivityId());
				takePrize.setIsPkH5(null != order.getAppActivityId());
			} else if (Objects.equals(ActivityUniformityTypeEnum.SIGN_CALENDAR_COMPONENT.getCode().toString(),order.getActivityType())) {
				takePrize.setOperatingActivityId(null != order.getAppActivityId() ? order.getAppActivityId() : order.getDuibaActivityId());
			} else{
				takePrize.setOperatingActivityId(order.getOperatingActivityId());
			}
			//插件订单号回写主订单
			setOrderNum4Plugin(takePrize);
		} else if(ConsumerExchangeRecordDto.TypeSingleLottery == record.getType()){
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeSingleLottery,params,OrdersDto.ChargeModeSingleLottery,OrdersDto.RelationTypeSingleLottery,OrdersDto.SubOrderTypeSinglelottery);
			takePrize.setOperatingActivityId(order.getOperatingActivityId());
            //判断是否是兑吧活动
            setIsDiuba(takePrize);
		}else if(ConsumerExchangeRecordDto.TypeGuess == record.getType()){
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeGuess,params,OrdersDto.ChargeModeGuess,OrdersDto.RelationTypeGuess,OrdersDto.SubOrderTypeGuess);
			takePrize.setOperatingActivityId(order.getDuibaActivityId());
			takePrize.setIsduiActivity(true);
		}else if (ConsumerExchangeRecordDto.TypeSign == record.getType()){
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeSign,params,OrdersDto.ChargeModeActivity,OrdersDto.RelationTypeSign,OrdersDto.SubOrderTypeSign);
			takePrize.setOperatingActivityId(order.getDuibaActivityId());
			takePrize.setIsduiActivity(false);
		} else if(ConsumerExchangeRecordDto.TypeNgame == record.getType()) {
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeNgame,params,OrdersDto.ChargeModeNgame,OrdersDto.RelationTypeNgame,OrdersDto.SubOrderTypeNgame);
			takePrize.setIsduiActivity(true);
		} else if (ConsumerExchangeRecordDto.TypeCreditGame == record.getType()) {
			takePrize = new TakePrizeVo(app,itemKey,order,ConsumerExchangeRecordDto.TypeCreditGame,params,OrdersDto.ChargeModeActivity,OrdersDto.RelationTypeCreditGame,"creditgame");
			takePrize.setOperatingActivityId(order.getDuibaActivityId());
			takePrize.setIsduiActivity(true);
		}else if (ConsumerExchangeRecordDto.TypeQuestion == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeQuestion,params,OrdersDto.ChargeModeQuestion,OrdersDto.RelationTypeQuestion,OrdersDto.SubOrderTypeQuestion);
			takePrize.setOperatingActivityId(order.getOperatingActivityId());
			takePrize.setIsduiActivity(false);
			setIsduiActivityInfo(takePrize);
		}else if (ConsumerExchangeRecordDto.TypeQuizz == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeQuizz,params,OrdersDto.ChargeModeQuizz,OrdersDto.RelationTypeQuizz,OrdersDto.SubOrderTypeQuizz);
			takePrize.setOperatingActivityId(order.getOperatingActivityId());
			takePrize.setIsduiActivity(true);
		} else if (ConsumerExchangeRecordDto.TYPE_HAPPY_CODE == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TYPE_HAPPY_CODE, params, OrdersDto.CHARGE_MODE_HAPPY_CODE, OrdersDto.RELATION_TYPE_HAPPY_CODE, OrdersDto.SubOrderTypeHappyCode);
			takePrize.setIsduiActivity(true);
			takePrize.setHappyCodePhaseId(order.getDuibaActivityId());
		} else if (ConsumerExchangeRecordDto.TypeLuckyCode == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeLuckyCode, params, OrdersDto.ChargeModeLuckyCode, OrdersDto.RelationTypeLuckyCode, OrdersDto.SubOrderTypeLuckyCode);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeHappyCodeNew == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeHappyCodeNew,params, OrdersDto.ChargeModeHappyCodeNew, OrdersDto.RELATION_TYPE_HAPPY_CODE_NEW,OrdersDto.SubOrderTypeHappyCodeNew);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeDigGold == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeDigGold, params, OrdersDto.ChargeModeDigGold, OrdersDto.RelationTypeDigGold, OrdersDto.SubOrderTypeDigGold);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeUnderstandLevel == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeUnderstandLevel, params, OrdersDto.ChargeModeUnderstandLevel, OrdersDto.RelationTypeUnderstandLevel, OrdersDto.SubOrderTypeUnderstandLevel);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeHaggle == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeHaggle, params, OrdersDto.ChargeModeHaggle, OrdersDto.RelationTypeHaggle, OrdersDto.SubOrderTypeHaggle);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeCentscan == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeCentscan, params, OrdersDto.ChargeModeCentscan, OrdersDto.RelationTypeCentscan, OrdersDto.SubOrderTypeCentscan);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeUnderseaGame == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeUnderseaGame, params, OrdersDto.ChargeModeUnderseaGame, OrdersDto.RelationTypeUnderseaGame, OrdersDto.SubOrderTypeUnderseaGame);
			takePrize.setIsduiActivity(false);
		} else if (ConsumerExchangeRecordDto.TypeTaiLongTaskUnit == record.getType()) {
			takePrize = new TakePrizeVo(app, itemKey, order, ConsumerExchangeRecordDto.TypeTaiLongTaskUnit, params, OrdersDto.ChargeModeTaiLongTaskUnit, OrdersDto.RelationTypeTaiLongTaskUnit, OrdersDto.SubOrderTypeTaiLongTaskUnit);
			takePrize.setIsduiActivity(false);
		}

		takePrize.setAddressId(Optional.ofNullable(request.getParameter("addressId")).map(Long::valueOf).orElse(null));
		return takePrize;
	}

    /**
     * 单品抽奖判断是否是兑吧商品
     * @param takePrize
     */
    private void setIsDiuba(TakePrizeVo takePrize){
        OperatingActivityDto operatingActivityDO = remoteOperatingActivityServiceNew.find(takePrize.getOperatingActivityId());
        if(operatingActivityDO != null && OperatingActivityDto.TypeDuibaSingleLottery == operatingActivityDO.getType()){
            takePrize.setIsduiActivity(true);
        }else{
            takePrize.setIsduiActivity(false);
        }
    }

	private void setOrderNum4Plugin(TakePrizeVo takePrize) {
		try{
			takePrize.setOrderId(Long.valueOf(takePrize.getOrderNum()));
		}catch (Exception e){
			log.info("plugin convert orderNum error,orderNum:{}",takePrize.getOrderNum(),e);
		}
	}

	private void setIsduiActivityInfo(TakePrizeVo takePrize) {
		//查询活动信息
		OperatingActivityDto operatingActivityDO = remoteOperatingActivityServiceNew.find(takePrize.getOperatingActivityId());
		if(operatingActivityDO.getActivityId() != null){

			DuibaHdtoolDto duibaHdtoolDto = remoteDuibaHdtoolServiceNew.find(operatingActivityDO.getActivityId());

			if (null!=duibaHdtoolDto&&Objects.equals(DuibaHdtoolDto.USE_DEV_BALANCE_ON,duibaHdtoolDto.getDevelopersDeduction())){
				takePrize.setUseDevBalance(true);
			}else {
				takePrize.setUseDevBalance(false);
			}

			takePrize.setIsduiActivity(true);
			takePrize.setActivityId(operatingActivityDO.getActivityId());
		}
	}

	private boolean checkVirtual(OrdersVO order, AppSimpleDto app,JSONObject jsonObject, ItemKeyDto itemKey) {
		if(order.getPrizeType().equals(ItemDto.TypeVirtual)&&Objects.isNull(itemKey.getItem())){
			if (StringUtils.isBlank(app.getVirtualExchangeUrl())) {
				log.info("虚拟商品兑换详情页,APP未配置虚拟商品领奖接口appId={}", app.getId());
				jsonObject.put(CommonConstants.SUCCESS_KEY, false);
				jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_SERVER_EXCEPTION_XYSHPJK);
				return true;
			}
			AppItemDto appItemDto = itemKey.getAppItem();
			//自有商品校验档位
			if (null != appItemDto) {
				if(!itemCheckService.checkPriceDegree(itemKey,order.getFacePrice())){
					log.info("虚拟商品兑换详情页,APP虚拟商品档位不可用appId={},appItemId:{}", app.getId(), appItemDto.getId());
					jsonObject.put(CommonConstants.SUCCESS_KEY, false);
					jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_SERVER_EXCEPTION_XYSPDW);
					return true;
				}
			}
		}
		return false;
	}

	private boolean verifyDuibaAddressLimit(Long consumerId, JSONObject jsonObject, ItemDto item) {
		if (item == null) {
			return false;
		}
		//说明不是实物
		if (!ItemDto.TypeObject.equals(item.getType())) {
			return false;
		}
		boolean isLimit = item.isOpTypeItem(ItemDto.OpTypeAddrLimit);
		//说明没有开启兑吧地域限制
		if (!isLimit) {
			return false;
		}
		ReceiveAddressDto  addressDto = remoteReceiveAddressService.getReceiveAddressCompatible(consumerId);
		if (addressDto != null && addressDto.getAddrCode() != null && addressDto.getAddrCode().length() >= 6) {
			Boolean flag = remoteAddrLimitService.verifyDuibaAddressLimit(item, addressDto.getAddrCode());
			if (!flag) {
				//如果地域限制校验没通过
				log.info("该区域无法领奖 consumerId={},itemId:{}", consumerId,item.getId());
				jsonObject.put(CommonConstants.SUCCESS_KEY, false);
				jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.CAN_NOT_TAKE_PRIZE);
				return true;
			}
		}
		return false;
	}

	//活动工具、支付宝提现校验，支付宝通用校验规则：返回true是为命中规则
	private Result<Boolean> verifyAlipayAntichet(TakePrizeVo takePrize, HttpServletRequest request, ConsumerExchangeRecordDto record) {
		if (Objects.equals(PrizeTypeEnum.ALIPAY.getType(), takePrize.getPrizeType())) {
			try {
				if(takePrize.getOperatingActivityId() == null){
					return ResultBuilder.success();
				}
				OperatingActivityDto operatingActivityDto = remoteOperatingActivityServiceNew.find(takePrize.getOperatingActivityId());
				SignActivityDto signActivityDto = null;
				SignOperatingDto signOperatingDto = null;
				if(Objects.equals(ActivityUniformityTypeEnum.SIGN_CALENDAR_COMPONENT.getCode().toString(),Optional.ofNullable(takePrize.getSubOrder()).map(OrdersVO::getActivityType).orElse("null"))){
					if(Optional.ofNullable(takePrize.getSubOrder()).map(OrdersVO::getAppActivityId).isPresent()){
						signOperatingDto = remoteSignOperatingService.find(takePrize.getOperatingActivityId());
					}else{
						signActivityDto = remoteSignActivityService.find(takePrize.getOperatingActivityId());
					}

				}
				if(operatingActivityDto == null && signActivityDto == null && signOperatingDto == null){
					return ResultBuilder.success();
				}

				Integer riskActType = null;

				String account = StringUtils.trimToNull(request.getParameter(CommonConstants.ACCOUNT));
				String realName = StringUtils.trimToNull(request.getParameter(CommonConstants.REALNAME));
				MobileUaInfoDTO uaInfo = InnerLogUtil.getUAInfo(request.getHeader(CommonConstants.USER_AGENT));
				RiskRuleEngineParam param = new RiskRuleEngineParam();
				if(signActivityDto != null){
					riskActType	= ActivityUniformityTypeEnum.SIGN_CALENDAR_COMPONENT.getCode();
					InnerLogUtil.withdrawInnerLog (riskActType, signActivityDto.getId(), signActivityDto.getId(),
                            account, realName,uaInfo);
					param.setActivityId(signActivityDto.getId());
					param.setOperatingId(signActivityDto.getId());
				}else if (signOperatingDto != null){
					riskActType	= ActivityUniformityTypeEnum.SIGN_CALENDAR_COMPONENT.getCode();
					InnerLogUtil.withdrawInnerLog (riskActType, signOperatingDto.getSignActivityId(), signOperatingDto.getId(),
							account, realName,uaInfo);
					param.setActivityId(signOperatingDto.getSignActivityId());
					param.setOperatingId(signOperatingDto.getId());
				}else{
					riskActType = getRiskActType(operatingActivityDto);
					InnerLogUtil.withdrawInnerLog (riskActType, operatingActivityDto.getActivityId(), operatingActivityDto.getId(),
							account, realName,uaInfo);
					param.setActivityId(operatingActivityDto.getActivityId());
					param.setOperatingId(operatingActivityDto.getId());
				}
				param.setActivityType(riskActType);
				cn.com.duiba.biz.tool.duiba.dto.RequestParams requestParams = cn.com.duiba.biz.tool.duiba.dto.RequestParams.parse(request);
				param.setAliPayAccount(StringUtils.trim(request.getParameter(CommonConstants.ACCOUNT)));
				param.setAliPayName(StringUtils.trim(request.getParameter(CommonConstants.REALNAME)));
				param.setAppId(takePrize.getApp().getId());
				param.setConsumerId(requestParams.getConsumerId());
				param.setIp(requestParams.getIp());
				param.setDate(new Date());
				param.setOs(requestParams.getOs());
				param.setScene(ActRiskSenceEnum.WITH_DRAW);
				param.setUa(requestParams.getUserAgent());
				param.setBizEnum(RiskRoutBizEnum.COMMON_ACTIVITY);
				param.setDeviceId(RiskConstant.DEVICE_PREFIX + requestParams.getConsumerId());

				DuibaStormEngineDto stormParam = new DuibaStormEngineDto();
				//怒风风控
				StormrageUtil.buildStormParam(stormParam, param,uaInfo);
//				executorService.submit(() -> remoteRiskRuleEngineService.execute(param));
				StormEngineResultDto stormEngineResultDto = remoteStormrageEngineService.execute(stormParam);
				CatLogTool.logMetric(CatLogTool.METRIC_ANTICHEAT_ALIPAY_API);
				if (stormEngineResultDto.getDecision() != null && Objects.equals(stormEngineResultDto.getDecision().getType(), RiskDecisionEnum.REJECT.getType())) {
					if(StringUtils.isNotBlank(stormEngineResultDto.getCopy()) && stormEngineResultDto.getCopy().contains("请使用常用邮箱账号提现")){
						return ResultBuilder.fail(stormEngineResultDto.getCopy());
					}else{
						return ResultBuilder.fail(CommonConstants.MSG_ANTICHEAT_ALIPAY_ERROR);
					}
				}
			} catch (Exception e) {
				log.warn("风控系统接口调用出现异常", e);
			}
		}
		return ResultBuilder.success();
	}


	//转换活动类型：由于 活动类型本身定义不规范，转换为规范好的类型值
	private Integer getRiskActType(OperatingActivityDto optDto){
		Integer activityType = optDto.getType();
		String subType = optDto.getSubType();
		if(activityType != null && activityType == cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto.TypeHdToolSmashg && subType != null){
			if(cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto.TypeHdToolQuestion_Action.equals(subType)){
				return ActivityUniformityTypeEnum.QuestionAnswer.getCode();
			}
			if(CommonConstants.HDTOOL_SUBTYPE_CLCARD.equals(subType)){
				return ActivityUniformityTypeEnum.CollectCard.getCode();
			}
		}
		if(cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto.hdToolTypeSet.contains(activityType)){
			return ActivityUniformityTypeEnum.HDTool.getCode();
		}
		return activityType;

	}

}
