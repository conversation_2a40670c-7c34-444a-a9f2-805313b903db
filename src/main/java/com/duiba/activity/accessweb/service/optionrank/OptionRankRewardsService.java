package com.duiba.activity.accessweb.service.optionrank;

import com.duiba.activity.accessweb.pjv.req.NgameMutiRewardsStrategyReq;
import com.duiba.activity.accessweb.pjv.rsp.NgameMutiRewardsStrategyRsp;

/**
 * Created by sty on 12/16/17.
 */
public interface OptionRankRewardsService {
    /**
     * 根据不同策略更改奖项的数量
     * @param type
     * @param ngameMutiRewardsStrategyReq
     * @return
     */
    NgameMutiRewardsStrategyRsp useMagic(Integer type, NgameMutiRewardsStrategyReq ngameMutiRewardsStrategyReq);

    /**
     * 根据不同策略更改指定分数值
     * @param type
     * @param ngameMutiRewardsStrategyReq
     * @param score
     * @return
     */
    NgameMutiRewardsStrategyRsp useMagic(Integer type, NgameMutiRewardsStrategyReq ngameMutiRewardsStrategyReq, Long score);
}
