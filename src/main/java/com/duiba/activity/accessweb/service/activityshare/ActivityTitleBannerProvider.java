package com.duiba.activity.accessweb.service.activityshare;

import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import com.duiba.activity.accessweb.tool.ConsumerItemService;
import org.apache.commons.lang.StringUtils;

/**
 * Created by zzy on 2017/9/21.
 */
public interface ActivityTitleBannerProvider {
    /**
     * 兑换记录类型
     */
    int getCrecordType();

    /**
     * 查询title和banner图
     *
     * @param relationId
     * @param consumerId
     * @param appId
     * @return
     */
    String[] getTitleAndBanner(Long relationId, Long consumerId, Long appId);

    default String getBannerImage(ItemKeyDto itemKey) {
        if (itemKey == null) {
            return "";
        }
        if (itemKey.isItemMode()) {
            return itemKey.getItem().getBannerImage();
        }
        if (itemKey.isSelfAppItemMode()) {
            return itemKey.getAppItem().getBannerImage();
        }
        if (itemKey.isDuibaAppItemMode()) {
            if (StringUtils.isNotBlank(itemKey.getAppItem().getBannerImage())) {
                return itemKey.getAppItem().getBannerImage();
            } else {
                return itemKey.getItem().getBannerImage();
            }
        }
        return "";
    }

    default String[] getDefaultResult() {
        return new String[]{"", ""};
    }

    default String getCandidateBanner(ItemKeyDto itemKeyDto, String bannerImage) {
        if (itemKeyDto == null || StringUtils.isNotBlank(bannerImage)) {
            return bannerImage;
        }
        return ConsumerItemService.getImage(itemKeyDto);
    }
}
