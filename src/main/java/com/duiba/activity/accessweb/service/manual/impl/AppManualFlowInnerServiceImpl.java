package com.duiba.activity.accessweb.service.manual.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.manual.ManualLotteryOrderDto;
import cn.com.duiba.activity.center.api.remoteservice.manual.RemoteManualOrderLotteryService;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.CreditsCallbackMessage;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.thirdparty.api.RemoteNotifyDeveloperService;
import cn.com.duiba.thirdparty.dto.NotifyQueueDto;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.core.event.ng.NGConsumerCreditsEvent;
import com.duiba.activity.accessweb.core.event.ng.NGDuibaEventsDispatcher;
import com.duiba.activity.accessweb.core.exception.DeveloperConsumeFailedException;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.manual.AppManualFlowInnerService;
import com.duiba.activity.accessweb.service.order.ObjectCreator;
import com.duiba.activity.accessweb.tool.AppIdConstant;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/** 
 * ClassName:AppManualFlowInnerServiceImpl.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年3月13日 下午4:35:29 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Service
public class AppManualFlowInnerServiceImpl implements AppManualFlowInnerService {

	private static Logger log = LoggerFactory.getLogger(AppManualFlowInnerServiceImpl.class);
	
	@Autowired
	private RemoteManualOrderLotteryService remoteManualOrderLotteryService;
	@Autowired
	private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
	@Autowired
	private RemoteNotifyDeveloperService remoteNotifyDeveloperService;
	@Autowired
	private RemoteConsumerService remoteConsumerService;
	@Autowired
	private CommonService commonService;
	@Autowired
	private ExecutorService executorService;
	@Autowired
	private ObjectCreator objectCreator;
	@Autowired
	private ActivityCacheService activityCacheService;


	@Override
	public void onCreditsFail(Map<String,String> extMap,Exception exception,ManualLotteryOrderDto order){
		try{
			//查询子订单信息
			ManualLotteryOrderDto t4u = new ManualLotteryOrderDto(order.getId());
			if ((exception instanceof DeveloperConsumeFailedException) && ((DeveloperConsumeFailedException) exception).getNormalFail()) {
				t4u.setError4admin("扣积分失败，开发者返回扣积分失败。" + exception.getMessage());
				t4u.setError4developer("扣积分失败，开发者返回扣积分失败。");
				t4u.setError4consumer("抽奖失败，请稍后再试。");
			} else {
				t4u.setError4admin("扣积分失败，开发者服务器异常。" + exception.getMessage());
				t4u.setError4developer("扣积分失败，" + exception.getMessage());
				t4u.setError4consumer("抽奖失败，请稍后再试。");
			}
			t4u.setStatus(ManualLotteryOrderDto.STATUS_FAIL);
			remoteManualOrderLotteryService.update(t4u);
			// 返还本地积分
			if (order.getCredits() > 0) {
				remoteConsumerService.increaseCredits(order.getConsumerId(), order.getCredits());
			}

			// 异步通知开发者扣积分结果
			String transfer = null;
			if (MapUtils.isNotEmpty(extMap)) {
				transfer = extMap.get("transfer");
			}
			insertManualOrderNotifyQueueIfNesscery(order.getId(),transfer);
		} catch (Exception e) {
			log.warn("manual fail, consumerId={}, subOrderId={}", order.getConsumerId(), order.getId(), e);
		} finally {
			Map<String, Object> params = new HashMap<>();
			params.put(NGConsumerCreditsEvent.PARAM_EXCEPTION, exception);
			params.put(NGConsumerCreditsEvent.PARAM_APPID, order.getAppId());
			params.put(NGConsumerCreditsEvent.PARAM_CONSUMERID, order.getConsumerId());
			params.put(NGConsumerCreditsEvent.PARAM_ORDERID, order.getId());
			params.put(NGConsumerCreditsEvent.PARAM_RELATION_TYPE, OrdersDto.RelationTypeManualLottery);
			NGConsumerCreditsEvent event = new NGConsumerCreditsEvent(NGConsumerCreditsEvent.TYPE_MANUAL, false, params);
			NGDuibaEventsDispatcher.getInstance().dispatchConsumeCreditsEvent(event);
		}
	}

	@Override
	public void onCreditsSuccess(Map<String,String> extMap,ManualLotteryOrderDto order){
		try{
			//查询子订单信息
			ManualLotteryOrderDto t4u = new ManualLotteryOrderDto(order.getId());
			t4u.setStatus(ManualLotteryOrderDto.STATUS_SUCCESS);
			t4u.setExchangeStatus(ManualLotteryOrderDto.EXCHANGE_STATUS_WAITOPEN);
			remoteManualOrderLotteryService.update(t4u);

			ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
			record.setConsumerId(order.getConsumerId());
			record.setType(ConsumerExchangeRecordDto.TypeManualLottery);
			record.setRelationId(order.getId());
			record.setAppId(order.getAppId());
			record.setOrigin(order.getOperatingActivityId() * 100);
			//通过id查询活动
			if(order.getOperatingActivityId() != null){
				OperatingActivityDto operatingDto = activityCacheService.getOperatingDto(order.getOperatingActivityId());
				//设置活动名称
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("trueActivityTitle",operatingDto.getTitle());
				record.setJson(
						jsonObject.toJSONString()
				);
			}

			DubboResult<ConsumerExchangeRecordDto> recordResult = remoteConsumerExchangeRecordService.insert(record);
			if(!recordResult.isSuccess()){
				log.error("onCreditsSuccess 兑换记录创建失败");
				return;
			}
			// 通知开发者操作结果
			String transfer = null;
			if (MapUtils.isNotEmpty(extMap)) {
				transfer = extMap.get("transfer");
			}
			insertManualOrderNotifyQueueIfNesscery(order.getId(),transfer);
		} catch (Exception e) {
			log.warn("manual fail, consumerId={}, subOrderId={}", order.getConsumerId(), order.getId(), e);
		}finally {
			Map<String, Object> params = new HashMap<>();
			params.put(NGConsumerCreditsEvent.PARAM_CREDITS, order.getCredits());
			params.put(NGConsumerCreditsEvent.PARAM_APPID, order.getAppId());
			NGDuibaEventsDispatcher.getInstance().dispatchConsumeCreditsEvent(new NGConsumerCreditsEvent(NGConsumerCreditsEvent.TYPE_MANUAL, true, params));
		}

	}

	private void insertManualOrderNotifyQueueIfNesscery(Long manualLotteryOrderId, String transfer) {
		ManualLotteryOrderDto manualLotteryOrder = remoteManualOrderLotteryService.find(manualLotteryOrderId);
		if (manualLotteryOrder==null||manualLotteryOrder.getCredits() < 0) {
			return;
		}

		if (manualLotteryOrder.getCredits()==0 && !AppIdConstant.FREE_SUB_CREIDS.contains(manualLotteryOrder.getAppId())) {
			return;
		}

		if (manualLotteryOrder.getStatus() == ManualLotteryOrderDto.STATUS_CREATE) {
			return;
		}
		ConsumerDto consumer=commonService.findConsumerDto(manualLotteryOrder.getConsumerId());
		NotifyQueueDto nq=new NotifyQueueDto();
		nq.setAppId(manualLotteryOrder.getAppId());
		nq.setConsumerId(manualLotteryOrder.getConsumerId());
		nq.setDeveloperBizId(manualLotteryOrder.getDeveloperBizId());
		nq.setDuibaOrderNum(ManualLotteryOrderDto.generateOrderNum(manualLotteryOrder.getId()));
		if (manualLotteryOrder.getError4developer() != null) {
			nq.setError4developer(manualLotteryOrder.getError4developer());
		}
		if(StringUtils.isNotBlank(transfer)) {
			nq.setTransfer(transfer);
		}
		nq.setNextTime(new Date());
		nq.setPartnerUserId(consumer.getPartnerUserId());
		nq.setRelationId(manualLotteryOrder.getId());
		nq.setRelationType(NotifyQueueDto.RT_MANUALORDER);
		nq.setResult(ManualLotteryOrderDto.STATUS_SUCCESS == manualLotteryOrder.getStatus());
		nq.setTimes(0);
		remoteNotifyDeveloperService.notifyDeveloper(nq);
	}

	@Override
	public void creditsCallback(CreditsCallbackMessage message) {
		if(null == message || StringUtils.isBlank(message.getRelationId())){
			return;
		}
		//查询子订单信息
		ManualLotteryOrderDto order = remoteManualOrderLotteryService.find(Long.parseLong(message.getRelationId()));
		if(order == null || order.getStatus()!= ManualLotteryOrderDto.STATUS_CREATE){
			return;
		}
		// 订单状态判断
		if (CreditsCallbackMessage.CALLBACK_TYPE_COMPLETED.equals(message.getCallbackType())) {
			completed(message,order);
		} else if (CreditsCallbackMessage.CALLBACK_TYPE_FAILED.equals(message.getCallbackType())) {
			Exception finallyE = new DeveloperConsumeFailedException("","向开发者请求扣积分，网络请求出错",false,message.getHttpUrl(),
					message.getMessage(),DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
			onCreditsFail(message.getParams(),finallyE,order);
		} else if (CreditsCallbackMessage.CALLBACK_TYPE_CANCELLED.equals(message.getCallbackType())) {
			DeveloperConsumeFailedException exception = new DeveloperConsumeFailedException("","请求被取消",false,
					message.getHttpUrl(),message.getMessage(),DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
			onCreditsFail(message.getParams(),exception,order);
		}
	}

	private void completed(final CreditsCallbackMessage message,final ManualLotteryOrderDto order) {
		Exception exception = null;
		try{
			successBlock(message);
		}catch(DeveloperConsumeFailedException e){
			exception = e;
		}catch (Exception e) {
			log.error("completed  error",e);
			exception = new DeveloperConsumeFailedException("","向开发者请求扣积分，网络请求出错",false,message.getHttpUrl(),message.getMessage(),
					DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
		}

		final Exception finallyE=exception; 
		executorService.submit(new Runnable() {
			@Override
			public void run() {
				if(finallyE == null){
					onCreditsSuccess(message.getParams(), order);
				}else{
					onCreditsFail(message.getParams(),finallyE,order);
				}
			}
		});
	}

	private void successBlock(CreditsCallbackMessage message)throws DeveloperConsumeFailedException{
		JSONObject o = null;
		try {
			o = JSON.parseObject(message.getMessage());
		} catch (Exception e) {
			log.info("json解析失败,bizId=" + message.getRelationId(),e);
			throw new DeveloperConsumeFailedException("","开发者服务器响应内容JSON解析失败",false,
					message.getHttpUrl(),message.getMessage(),DeveloperConsumeFailedException.FailTypeJson,DeveloperConsumeFailedException.ConsumerCreditsType);
		}
		if (o == null) {
			throw new DeveloperConsumeFailedException("","开发者服务器响应内容JSON解析失败",false,
					message.getHttpUrl(),message.getMessage(),DeveloperConsumeFailedException.FailTypeJson,DeveloperConsumeFailedException.ConsumerCreditsType);
		}

		JSONObject json = getOneDegreeJson(o);
		if ("ok".equalsIgnoreCase(json.getString("status"))) {
			final Long credits = json.getLong("credits");
			String orderNum = message.getRelationId();
			if (credits != null && credits >= 0) {
				//更新 本地积分
				remoteConsumerService.updateCredits(Long.parseLong(message.getConsumerId()), credits);
			}
			final String bizId = json.getString("bizId");
			if(StringUtils.isBlank(bizId)){
				throw new DeveloperConsumeFailedException("","开发者服务器响应开发订单号为空",false,
						message.getHttpUrl(),message.getMessage(),DeveloperConsumeFailedException.FailTypeJson,DeveloperConsumeFailedException.ConsumerCreditsType);
			}
			ManualLotteryOrderDto t4u = new ManualLotteryOrderDto(Long.parseLong(orderNum));
			t4u.setDeveloperBizId(bizId);
			t4u.setGmtModified(new Date());
			remoteManualOrderLotteryService.update(t4u);
		} else {
			throw new DeveloperConsumeFailedException("",json.getString("errorMessage"),true,message.getHttpUrl(),message.getMessage(),
					DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
		}
	}
	
	private JSONObject getOneDegreeJson(JSONObject o){
		JSONObject json = new JSONObject();
		for (String key : o.keySet()) {
			if(o.get(key) instanceof JSONObject){
				JSONObject json2 = (JSONObject) o.get(key);
				for (String key2 : json2.keySet()) {
					json.put(key2, json2.get(key2));
				}
			}else{
				json.put(key, o.get(key));
			}
		}
		return json;
	}

	@Override
	public void giveAwardList(Long activityId, JSONArray jsonArray) {
		try{
			for(int i =0;i<jsonArray.size();i++){
				ManualLotteryOrderDto manualOrder = remoteManualOrderLotteryService.find(jsonArray.getLong(i));
				int ret= remoteManualOrderLotteryService.updateAward(jsonArray.getLong(i),activityId,new Date());
				if(StringUtils.isBlank(manualOrder.getPhone()) || ret !=1){
					continue;
				}
				//查询兑换记录
				Map<String,Object> maprecord =new HashMap<>();
				maprecord.put("relationId", manualOrder.getId());
				maprecord.put(CommonConstants.CONSUMERID, manualOrder.getConsumerId());
				maprecord.put("type", ConsumerExchangeRecordDto.TypeManualLottery);
				ConsumerExchangeRecordDto record =remoteConsumerExchangeRecordService.selectOneByMapCondition(maprecord).getResult();
				//创建主订单流程
				OrdersDto order = objectCreator.innerManualLottery(manualOrder);
				if(order != null){
					ManualLotteryOrderDto t4u = new ManualLotteryOrderDto(manualOrder.getId());
					t4u.setOrderId(order.getId());
					t4u.setWinDate(new Date());
					remoteManualOrderLotteryService.update(t4u);
					ConsumerExchangeRecordDto c4update = new ConsumerExchangeRecordDto(record.getId());
					c4update.setOrderId(order.getId());
					c4update.setConsumerId(manualOrder.getConsumerId());
					remoteConsumerExchangeRecordService.update(c4update);
				}
			}
		}catch(Exception e){
			log.info("处理失败",e);
		}
	}
}
