package com.duiba.activity.accessweb.service.newgamecenter.impl;

import cn.com.duiba.activity.center.api.dto.newgamecenter.SeasonConfigDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameOptionsDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameMutiRankingBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameMutiRankingRecordDto;
import cn.com.duiba.activity.center.api.dto.ngame.NgameOrdersDto;
import cn.com.duiba.activity.center.api.rank.NGameRank;
import cn.com.duiba.activity.center.api.remoteservice.newgamecenter.RemoteSeasonConfigService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteDuibaNgameOptionsService;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteNgameMutiRankingRecordService;
import cn.com.duiba.activity.center.api.remoteservice.ngame_con.RemoteNgameOrdersConsumerService;
import cn.com.duiba.activity.common.center.api.dto.consumeraccount.ConsumerAccountsDto;
import cn.com.duiba.activity.common.center.api.dto.wallet.WalletAccountDto;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountActionTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountSubTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteConsumerAccountService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountAmountModifyRequest;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountModifyResponse;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.kvtable.service.api.enums.ActAccessWebHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbaseApiKvService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.enums.NewGameCenterRedPacketStatusEnum;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.req.NgameRealTimeRankReq;
import com.duiba.activity.accessweb.pjv.rsp.NgameRealTimeRankRsp;
import com.duiba.activity.accessweb.service.CreditsService;
import com.duiba.activity.accessweb.service.newgamecenter.NewGameCenterService;
import com.duiba.activity.accessweb.service.ngame.NgameRankService;
import com.duiba.activity.accessweb.service.ngame.NgameService;
import com.duiba.activity.accessweb.service.ngamerank.impl.NgameMutiRankHandler;
import com.duiba.activity.accessweb.service.wallet.WalletAccountService;
import com.duiba.activity.accessweb.vo.newgamecenter.NewGameCenterAccountInfoVO;
import com.duiba.activity.accessweb.vo.newgamecenter.NewGameCenterIndexInfoVO;
import com.duiba.activity.accessweb.vo.newgamecenter.NewGameCenterPrizeVO;
import com.duiba.activity.accessweb.vo.newgamecenter.PopupVO;
import com.duiba.activity.accessweb.vo.newgamecenter.PrizeUserVO;
import com.duiba.activity.accessweb.vo.newgamecenter.RankListVO;
import com.duiba.activity.accessweb.vo.newgamecenter.RedPacketConfigVO;
import com.duiba.activity.accessweb.vo.newgamecenter.SeasonConfigVO;
import com.duiba.activity.accessweb.vo.ngame.NGameMultiPrizeVO;
import com.duiba.activity.accessweb.vo.ngame.PrizeUser;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/09/04
 */
@Service
public class NewGameCenterServiceImpl implements NewGameCenterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NewGameCenterServiceImpl.class);
    private static final String SCORE = "score";
    private static final String RED_PACKET = "redPacket";
    private static final String RED_PACKET_AMOUNT = "redPacketAmount";
    private static final int TEN_THOUSAND = 10000;
    private static final String ZERO = "0";
    private static final String NULL = "null";
    @Autowired
    private WalletAccountService walletAccountService;
    @Autowired
    private NgameRankService ngameRankService;
    @Autowired
    private NgameService ngameService;
    @Autowired
    private CreditsService creditsService;
    @Autowired
    private RemoteSeasonConfigService remoteSeasonConfigService;
    @Autowired
    private RemoteNgameMutiRankingRecordService remoteNgameMutiRankingRecordService;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;
    @Autowired
    private RemoteHbaseApiKvService remoteHbaseApiKvService;
    @Autowired
    private RemoteConsumerAccountService remoteConsumerAccountService;
    @Autowired
    private NgameMutiRankHandler rankHandler;
    @Autowired
    private DomainService domainService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private RemoteNgameOrdersConsumerService remoteNgameOrdersConsumerService;
    @Autowired
    private RemoteDuibaNgameOptionsService remoteDuibaNgameOptionsService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private DeveloperCacheService developerCacheService;

    @Override
    public Result<NewGameCenterAccountInfoVO> getAccountInfo(ConsumerDto consumer) {
        //1.获取用户积分
        JSONObject data = creditsService.getCredits(consumer);
        if (data == null) {
            LOGGER.error("获取用户积分出错{}", consumer.getId());
            return ResultBuilder.fail("获取用户积分出错");
        }
        NewGameCenterAccountInfoVO accountInfoVO = new NewGameCenterAccountInfoVO();
        accountInfoVO.setCid(consumer.getId());
        accountInfoVO.setCredits(data.getString("credits"));
        accountInfoVO.setCreditsUnitName(data.getString("unitName"));
        //2.获取用户红包账户余额
        try {
            WalletAccountDto walletAccountDto = walletAccountService.getWallAccountByAppId(consumer.getAppId());
            if (walletAccountDto == null) {
                LOGGER.info("未开启钱包体系,appId: {}", consumer.getAppId());
                accountInfoVO.setWalletBalance("0");
            }
        } catch (BizException e) {
            LOGGER.error("获取钱包账户出错", e);
            return ResultBuilder.fail("获取钱包账户错误");
        }
        try {
            ConsumerAccountsDto consumerAccountsDto = walletAccountService.searchOrInsert(consumer);
            if (consumerAccountsDto == null || consumerAccountsDto.getBalanceAmount().equals(0L)) {
                accountInfoVO.setWalletBalance("0");
            } else {
                accountInfoVO.setWalletBalance(
                        new BigDecimal(consumerAccountsDto.getBalanceAmount()).divide(
                                new BigDecimal(100), 2, BigDecimal.ROUND_DOWN).toString());
            }
        } catch (BizException e) {
            LOGGER.error("获取钱包余额出错", e);
            return ResultBuilder.fail("获取钱包余额出错");
        }
        //3.获取战斗值
        SeasonConfigDto seasonConfigDto = getSeasonConfigByAppId(consumer.getAppId());
        if (seasonConfigDto == null) {
            LOGGER.info("{}没有游戏中心配置", consumer.getAppId());
            return ResultBuilder.fail("缺少游戏中心配置定向");
        }
        //设置排名
        NgameMutiRankingBaseConfigDto configDto = getRankListConfig(seasonConfigDto.getRankListId());
        NgameMutiRankingRecordDto recordDto = getRankListRecord(
                seasonConfigDto.getRankListId(), consumer.getAppId(), consumer.getId());

        setAccountInfoScore(recordDto, accountInfoVO);
        accountInfoVO.setRank("未上榜");
        if (configDto != null && configDto.getOpenPrize()) {
            //如果已经开奖则从数据库获取用户的排名
            if (recordDto != null) {
                accountInfoVO.setRank(convert2Rank(String.valueOf(recordDto.getRank())));
            }
        } else {
            accountInfoVO.setRank(getRank(seasonConfigDto.getRankListId(), consumer.getId(), consumer.getAppId()));
        }
        //4.获取昵称头像
        ConsumerExtraDto consumerExtraDto = getConsumerExtraDto(consumer.getId());
        if (consumerExtraDto != null) {
            accountInfoVO.setAvatar(consumerExtraDto.getAvatar());
            accountInfoVO.setNickName(consumerExtraDto.getNickname());
        }
        return ResultBuilder.success(accountInfoVO);
    }

    @Override
    public Result<NewGameCenterIndexInfoVO> getIndexInfo(ConsumerDto consumer, Long appId) {
        NewGameCenterIndexInfoVO gameCenterIndexInfoVO = new NewGameCenterIndexInfoVO();
        if (consumer == null || appId == null) {
            gameCenterIndexInfoVO.setLogin(Boolean.FALSE);
            return ResultBuilder.success(gameCenterIndexInfoVO);
        }
        SeasonConfigDto seasonConfigDto = getSeasonConfigByAppId(appId);
        if (seasonConfigDto == null) {
            LOGGER.info("{}没有游戏中心配置", consumer.getAppId());
            return ResultBuilder.fail("缺少游戏中心配置定向");
        }

        gameCenterIndexInfoVO.setGameCenterConfigId(seasonConfigDto.getGameCenterId());
        if (consumer.isNotLoginUser()) {
            gameCenterIndexInfoVO.setLogin(Boolean.FALSE);
            gameCenterIndexInfoVO.setLoginCode(developerCacheService.getCallLoginProgram(appId));
        } else {
            gameCenterIndexInfoVO.setLogin(Boolean.TRUE);
        }
        gameCenterIndexInfoVO.setSeasonConfig(convert2SeasonConfigVO(seasonConfigDto, consumer.getId(), consumer.getAppId()));
        gameCenterIndexInfoVO.setPrizeList(getPrizeList(seasonConfigDto.getRankListId()));

        NgameMutiRankingBaseConfigDto configDto = activityCacheService.findMultiRankConfig(seasonConfigDto.getRankListId());
        if (configDto != null) {
            gameCenterIndexInfoVO.setLimitScope(configDto.getLimitScope());
            gameCenterIndexInfoVO.setLimitTimes(configDto.getLimitTimes());
        }

        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("id", seasonConfigDto.getGameCenterId());
        AccessLogFilter.putExPair("seasonId", seasonConfigDto.getId());
        AccessLogFilter.putExPair("loginStatus", consumer.isNotLoginUser() ? 0 : 1);
        AccessLogFilter.putExPair("userCredits", consumer.getCredits());
        AccessLogFilter.putExPair("pageBizId", 155);

        return ResultBuilder.success(gameCenterIndexInfoVO);
    }

    @Override
    public Result<PopupVO> getPopup(ConsumerDto consumerDto) {
        SeasonConfigDto seasonConfigDto = getSeasonConfigByAppId(consumerDto.getAppId());
        if (seasonConfigDto == null) {
            LOGGER.info("appId:{}没有游戏中心配置", consumerDto.getAppId());
            return ResultBuilder.fail("缺少游戏中心配置");
        }
        PopupVO popupVO = new PopupVO();
        List<NewGameCenterPrizeVO> prizeVOList = new ArrayList<>();
        popupVO.setPrizeList(prizeVOList);
        popupVO.setShow(Boolean.FALSE);
        popupVO.setSeasonTitle(seasonConfigDto.getTitle());
        //1.获取当前赛季排行榜已经开奖的奖品
        NewGameCenterPrizeVO prizeVO=
                getUserWinPrize(seasonConfigDto.getRankListId(), consumerDto.getId(), consumerDto.getAppId());
        if (prizeVO != null && NgameOrdersDto.ExchangeStatusWait == prizeVO.getStatus()) {
            popupVO.setShow(Boolean.TRUE);
            prizeVO.setDcm(getDcm(prizeVO.getItemId()));
            prizeVO.setDpm(getDpm(consumerDto.getAppId()));
            prizeVOList.add(prizeVO);
        }
        //如果有上个赛季，获取上个赛季的排行榜奖品
        if (seasonConfigDto.getPrevSeasonId() != 0) {
            SeasonConfigDto prevSeason = remoteSeasonConfigService.findById(seasonConfigDto.getPrevSeasonId());
            if (prevSeason != null) {
                NewGameCenterPrizeVO gameCenterPrizeVO =
                        getUserWinPrize(prevSeason.getRankListId(), consumerDto.getId(), consumerDto.getAppId());
                if (gameCenterPrizeVO != null && NgameOrdersDto.ExchangeStatusWait == gameCenterPrizeVO.getStatus()) {
                    popupVO.setSeasonTitle(prevSeason.getTitle());
                    gameCenterPrizeVO.setDcm(getDcm(gameCenterPrizeVO.getItemId()));
                    gameCenterPrizeVO.setDpm(getDpm(consumerDto.getAppId()));
                    popupVO.setShow(Boolean.TRUE);
                    prizeVOList.add(gameCenterPrizeVO);
                }
            }
        }
        return ResultBuilder.success(popupVO);
    }

    @Override
    public Result doDraw(String redPacketKey, ConsumerDto consumerDto) {
        String redPacket = RED_PACKET + redPacketKey;
        SeasonConfigDto seasonConfigDto = getSeasonConfigByAppId(consumerDto.getAppId());
        if (seasonConfigDto == null) {
            LOGGER.info("缺少赛季配置{}", consumerDto.getAppId());
            return ResultBuilder.fail("缺少赛季配置");
        }
        if (seasonConfigDto.getEndTime().before(new Date())) {
            return ResultBuilder.fail("赛季已结束，无法领取");
        }
        String hbKey = getHbKey(seasonConfigDto.getRankListId(), consumerDto.getId());
        try {
            String configStr = remoteHbaseApiKvService.getStringByKey(hbKey);
            if (StringUtils.isBlank(configStr)) {
                return ResultBuilder.fail("没有权限领取");
            }
            JSONObject config = JSONObject.parseObject(configStr);
            Integer status = config.getInteger(redPacket);
            if (status == null) {
                LOGGER.info("key：{}, cid：{}", redPacket, consumerDto.getId());
                return ResultBuilder.fail("红包key错误");
            }
            if (NewGameCenterRedPacketStatusEnum.LOCKED.getCode().equals(status)) {
                return ResultBuilder.fail("红包未解锁");
            } else if (NewGameCenterRedPacketStatusEnum.OPENED.getCode().equals(status)) {
                return ResultBuilder.fail("已经领取");
            } else if (NewGameCenterRedPacketStatusEnum.CAN_OPEN.getCode().equals(status)) {
                return openRedPacket(redPacket, seasonConfigDto, consumerDto, hbKey, config);
            }
            LOGGER.info("红包状态不支持{}", status);
            return ResultBuilder.fail("服务器开小差了");
        } catch (Exception e) {
            LOGGER.error("获取红包状态出错", e);
            return ResultBuilder.fail("服务器开小差，请稍后再试");
        }
    }

    @Override
    public Result<RankListVO> getRankList(Long seasonConfigId, ConsumerDto consumerDto, Long appId) {
        SeasonConfigDto seasonConfigDto = findBySeasonId(seasonConfigId);
        if (seasonConfigDto == null) {
            return ResultBuilder.fail("非法赛季id");
        }
        NgameRealTimeRankReq realTimeRankReq = new NgameRealTimeRankReq();
        realTimeRankReq.setAppId(appId);
        realTimeRankReq.setCount(100);
        realTimeRankReq.setId(seasonConfigDto.getRankListId());
        realTimeRankReq.setConsumerId(consumerDto.getId());

        NgameRealTimeRankRsp realTimeRankRsp = rankHandler.getRankDataWithConsumerId(realTimeRankReq);

        RankListVO rankListVO = new RankListVO();
        rankListVO.setStartTime(seasonConfigDto.getStartTime().getTime());
        rankListVO.setEndTime(seasonConfigDto.getEndTime().getTime());
        long openPrizeTime = 0L;
        if (realTimeRankRsp.getOpenPrizeTime() != null) {
            openPrizeTime = realTimeRankRsp.getOpenPrizeTime().getTime();
        }
        rankListVO.setOpenPrizeTime(openPrizeTime);
        //排行榜已经开奖
        if (realTimeRankRsp.getOpenPrize()) {
            setRankListVOWithOpenPrize(rankListVO, seasonConfigDto, consumerDto, realTimeRankRsp);
        } else {
            rankListVO.setCloseStatus(Boolean.FALSE);
            List<NewGameCenterPrizeVO> prizeVOList = getPrizeList(seasonConfigDto.getRankListId());
            rankListVO.setPrizeList(prizeVOList);
            PrizeUser prizeUser = new PrizeUser();
            if (CollectionUtils.isNotEmpty(realTimeRankRsp.getUserList())) {
                rankListVO.setPrizeUserList(getPrizeUserVOListFromRedis(realTimeRankRsp));
                if (StringUtils.isNotBlank(realTimeRankRsp.getUser().getCid())) {
                    rankListVO.setPrizeUser(convert2PrizeUserVO(realTimeRankRsp.getUser()));
                } else {
                    prizeUser.setCid(String.valueOf(consumerDto.getId()));
                    prizeUser.setMaxScore("0");
                    prizeUser.setRank("0");
                    rankListVO.setPrizeUser(convert2PrizeUserVO(prizeUser));
                }
            } else {
                prizeUser.setCid(String.valueOf(consumerDto.getId()));
                prizeUser.setMaxScore("0");
                prizeUser.setRank("0");
                rankListVO.setPrizeUser(convert2PrizeUserVO(prizeUser));
            }
        }

        //设置当前用户是否中奖
        rankListVO.setWin(isWin(rankListVO.getPrizeList()));
        return ResultBuilder.success(rankListVO);
    }

    private Boolean isWin(List<NewGameCenterPrizeVO> prizeVOList) {
        if (CollectionUtils.isEmpty(prizeVOList)) {
            return Boolean.FALSE;
        }
        return prizeVOList.stream().anyMatch(prizeVO -> prizeVO.getStatus() != null);
    }

    private void setAccountInfoScore(NgameMutiRankingRecordDto recordDto, NewGameCenterAccountInfoVO accountInfoVO) {
        if (recordDto == null) {
            accountInfoVO.setScore("0");
        } else {
            accountInfoVO.setScore(recordDto.getTotalScore().toString());
        }
    }

    private String getDcm(Long itemId) {
        return "101." + itemId + ".0.0";
    }

    private String getDpm(Long appId) {
        return appId + "." + "4.6.0";
    }

    private NgameMutiRankingBaseConfigDto getRankListConfig(Long rankListId) {
        if (rankListId == null) {
            return null;
        }
        try {
            return activityCacheService.findMultiRankConfig(rankListId);
        } catch (Exception e) {
            LOGGER.error("获取排行榜配置错误", e);
            return null;
        }
    }
    private void setRankListVOWithOpenPrize(RankListVO rankListVO, SeasonConfigDto seasonConfigDto,
                                            ConsumerDto consumerDto, NgameRealTimeRankRsp realTimeRankRsp) {
        rankListVO.setCloseStatus(Boolean.TRUE);
        NewGameCenterPrizeVO gameCenterPrizeVO = getUserWinPrize(
                seasonConfigDto.getRankListId(), consumerDto.getId(), consumerDto.getAppId());
        //如果缓存中还有排行榜的数据则继续往缓存中获取列表
        if (CollectionUtils.isNotEmpty(realTimeRankRsp.getUserList())) {
            rankListVO.setPrizeUserList(getPrizeUserVOListFromRedis(realTimeRankRsp));
            PrizeUserVO prizeUserVO = convert2PrizeUserVO(realTimeRankRsp.getUser());
            if (prizeUserVO == null) {
                ConsumerExtraDto consumerExtraDto = getConsumerExtraDto(consumerDto.getId());
                prizeUserVO = new PrizeUserVO();
                prizeUserVO.setMaxScore("0");
                prizeUserVO.setRank(convert2Rank("null"));
                prizeUserVO.setCid(String.valueOf(consumerDto.getId()));
                if (consumerExtraDto != null) {
                    prizeUserVO.setNickName(consumerExtraDto.getNickname());
                    prizeUserVO.setAvatar(consumerExtraDto.getAvatar());
                }
                rankListVO.setPrizeUser(prizeUserVO);
            } else {
                rankListVO.setPrizeUser(prizeUserVO);
            }
        } else {
            //设置排行榜列表,已经开奖从数据库获取前100名排名
            rankListVO.setPrizeUserList(getPrizeUserVOListFromDB(seasonConfigDto.getRankListId(), consumerDto.getAppId()));
            //获取当前用户的排行榜记录
            NgameMutiRankingRecordDto rankingRecordDto =
                    getRankListRecord(seasonConfigDto.getRankListId(), consumerDto.getAppId(), consumerDto.getId());
            PrizeUserVO prizeUserVO;
            if (rankingRecordDto != null) {
                PrizeUser prizeUser = new PrizeUser();
                prizeUser.setRank(convert2Rank(String.valueOf(rankingRecordDto.getRank())));
                prizeUser.setMaxScore(convert2Score(rankingRecordDto.getTotalScore()));
                prizeUser.setCid(String.valueOf(consumerDto.getId()));
                prizeUserVO = convert2PrizeUserVO(prizeUser);
            } else {
                //用户没有榜单记录时
                PrizeUser prizeUser = new PrizeUser();
                prizeUser.setCid(String.valueOf(consumerDto.getId()));
                prizeUser.setMaxScore("0");
                prizeUser.setRank("0");
                prizeUserVO = convert2PrizeUserVO(prizeUser);
            }
            //设置当前用户的信息
            rankListVO.setPrizeUser(prizeUserVO);
        }
        //设置奖品列表
        rankListVO.setPrizeList(getPrizeListWithStatus(getPrizeList(seasonConfigDto.getRankListId()), gameCenterPrizeVO));
    }

    @Override
    public ConsumerExtraDto getConsumerExtraDto(Long consumerId) {
        try {
            DubboResult<ConsumerExtraDto> result = remoteConsumerExtraService.findByConsumerId(consumerId);
            if (result == null || !result.isSuccess()) {
                return null;
            }
            return result.getResult();
        } catch (Exception e) {
            LOGGER.error("获取用户信息出错", e);
            return null;
        }
    }
    //返回排行榜奖品列表，有当前用户是否中奖的状态
    private List<NewGameCenterPrizeVO> getPrizeListWithStatus(List<NewGameCenterPrizeVO> prizeVOList, NewGameCenterPrizeVO winPrize) {
        if (winPrize == null) {
            return prizeVOList;
        }
        return prizeVOList.stream().peek(prize -> {
            if (prize.getPrizeId().equals(winPrize.getPrizeId())) {
                prize.setStatus(winPrize.getStatus());
                prize.setLink(winPrize.getLink());
            }
        }).filter(prizeVO -> prizeVO.getStatus() != null).collect(Collectors.toList());
    }

    //1.设置用户的昵称和头像
    //2.积分转换
    private PrizeUserVO convert2PrizeUserVO(PrizeUser user) {
        if (user == null) {
            LOGGER.info("当前用户为空");
            return null;
        }
        if (user.getCid() != null) {
            ConsumerExtraDto consumerExtraDto = getConsumerExtraDto(Long.valueOf(user.getCid()));
            PrizeUserVO prizeUserVO = new PrizeUserVO();
            if (consumerExtraDto != null) {
                prizeUserVO.setAvatar(consumerExtraDto.getAvatar());
                prizeUserVO.setNickName(consumerExtraDto.getNickname());
            }
            prizeUserVO.setRank(convert2Rank(user.getRank()));
            prizeUserVO.setCid(user.getCid());
            //用户超过2w，score=null
            if (StringUtils.isBlank(user.getMaxScore()) || NULL.equals(user.getMaxScore())) {
                prizeUserVO.setMaxScore("0");
            } else {
                Long score = Long.valueOf(user.getMaxScore());
                prizeUserVO.setMaxScore(convert2Score(score));
                if (score > TEN_THOUSAND) {
                    prizeUserVO.setUnit("万");
                }
            }
            return prizeUserVO;
        }
        return null;
    }

    private String convert2Rank(String rank) {
        if (ZERO.equals(rank) || NULL.equals(rank) || StringUtils.isBlank(rank)) {
            return "未上榜";
        }
        return rank;
    }

    private List<PrizeUserVO> getPrizeUserVOListFromRedis(NgameRealTimeRankRsp realTimeRankRsp) {
        Map<Long, ConsumerExtraDto> consumerExtraDtoMap = getConsumerExtraMap(
                realTimeRankRsp.getUserList().stream().map(u -> Long.valueOf(u.getCid()))
                        .collect(Collectors.toList()));
        return realTimeRankRsp.getUserList().stream().map(prizeUser -> {
            PrizeUserVO prizeUserVO = BeanUtils.copy(prizeUser, PrizeUserVO.class);
            if (StringUtils.isNotBlank(prizeUserVO.getMaxScore()) &&
                    !NULL.equals(prizeUserVO.getMaxScore()) && Long.valueOf(prizeUserVO.getMaxScore()) > TEN_THOUSAND) {
                prizeUserVO.setMaxScore(convert2Score(Long.valueOf(prizeUserVO.getMaxScore())));
                prizeUserVO.setUnit("万");
            }
            if (consumerExtraDtoMap != null) {
                ConsumerExtraDto consumerExtraDto = consumerExtraDtoMap.get(Long.valueOf(prizeUser.getCid()));
                if (consumerExtraDto != null) {
                    prizeUserVO.setNickName(consumerExtraDto.getNickname());
                    prizeUserVO.setAvatar(consumerExtraDto.getAvatar());
                }
            }
            return prizeUserVO;
        }).collect(Collectors.toList());
    }

    private List<PrizeUserVO> getPrizeUserVOListFromDB(Long rankListId, Long appId) {
        try {
            List<NgameMutiRankingRecordDto> recordDtoList = remoteNgameMutiRankingRecordService.listByConfigIdAndAppId(rankListId, appId);
            recordDtoList.sort(Comparator.comparing(NgameMutiRankingRecordDto::getRank));
            if (CollectionUtils.isEmpty(recordDtoList)) {
                return Collections.emptyList();
            }
            Map<Long, ConsumerExtraDto> consumerExtraDtoMap = getConsumerExtraMap(
                    recordDtoList.stream().map(NgameMutiRankingRecordDto::getConsumerId).collect(Collectors.toList()));
            return recordDtoList.stream().map(record -> {
                PrizeUserVO prizeUserVO = new PrizeUserVO();

                prizeUserVO.setCid(String.valueOf(record.getConsumerId()));
                prizeUserVO.setRank(String.valueOf(record.getRank()));
                prizeUserVO.setMaxScore(convert2Score(record.getTotalScore()));
                if (record.getTotalScore() >= TEN_THOUSAND) {
                    prizeUserVO.setUnit("万");
                }
                ConsumerExtraDto consumerExtraDto = consumerExtraDtoMap.get(record.getConsumerId());
                if (consumerExtraDto != null) {
                    prizeUserVO.setNickName(consumerExtraDto.getNickname());
                    prizeUserVO.setAvatar(consumerExtraDto.getAvatar());
                }
                return prizeUserVO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("获取排行榜失败{}", rankListId, e);
            return Collections.emptyList();
        }
    }

    private Map<Long, ConsumerExtraDto> getConsumerExtraMap(List<Long> consumerIds) {
        try {
            DubboResult<List<ConsumerExtraDto>> result = remoteConsumerExtraService.findAllByConsumerIds(consumerIds);
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getResult())) {
                return new HashMap<>();
            }
            return result.getResult().stream().collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, e -> e));
        } catch (Exception e) {
            LOGGER.error("获取用户信息出错", e);
            return new HashMap<>();
        }
    }

    private SeasonConfigDto findBySeasonId(Long seasonConfigId) {
        try {
            return remoteSeasonConfigService.findById(seasonConfigId);
        } catch (Exception e) {
            LOGGER.error("获取赛季信息出错", e);
            return null;
        }
    }

    private Result openRedPacket(String redPacket, SeasonConfigDto seasonConfigDto,
                                 ConsumerDto consumerDto, String hbKey, JSONObject config) {
        Long money = getMoney(seasonConfigDto.getRedPacketConfig(), redPacket);
        if (money == 0) {
            return ResultBuilder.fail("非法红包配置");
        }
        AccountModifyResponse response = addMoney(consumerDto, redPacket, seasonConfigDto.getRankListId(), money);
        if (response == null) {
            LOGGER.info("领取失败");
            return ResultBuilder.fail("领取红包失败");
        }
        else if (!response.isSuccess()) {
            LOGGER.info("领取失败{}", response.getErrorMsg());
            if (response.getErrorCode() == AccountModifyResponse.ERROR_CODE_FOR_UN_BUDGET) {
                return ResultBuilder.fail("本月游戏奖金获取已达上限");
            }
            return ResultBuilder.fail("领取红包失败，请稍后再试");
        } else {
            config.put(redPacket, NewGameCenterRedPacketStatusEnum.OPENED.getCode());
            //更新hb
            remoteHbaseApiKvService.upsertKStrV(hbKey, config.toJSONString());
            return ResultBuilder.success();
        }
    }

    private Long getMoney(String config, String redPacketKey) {
        if (StringUtils.isBlank(config)) {
            return 0L;
        }
        try {
            JSONObject configObject = JSONObject.parseObject(config);
            JSONObject redPacket = configObject.getJSONObject(redPacketKey);
            if (redPacket == null) {
                return 0L;
            }
            return redPacket.getLong(RED_PACKET_AMOUNT);
        } catch (Exception e) {
            LOGGER.error("json解析出错:{}",config, e);
            return 0L;
        }
    }

    private AccountModifyResponse addMoney(ConsumerDto consumer, String redPacketKey, Long rankListId, Long money) {
        AccountAmountModifyRequest request = new AccountAmountModifyRequest();

        request.setAppId(consumer.getAppId());
        request.setConsumerId(consumer.getId());
        request.setBizId(redPacketKey + "_" + rankListId + "_" + consumer.getId());
        request.setBizType(AccountBizTypeEnum.GAME_CENTER);
        request.setAccActType(AccountActionTypeEnum.ACTION_IN);
        request.setAccountType(AccountTypeEnum.ALL_WALLET);
        request.setBizDescription("游戏中心红包");
        request.setPartnerUserId(consumer.getPartnerUserId());
        request.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
        request.setChangeMoney(money);
        request.setNeedLog(Boolean.TRUE);

        try {
            return remoteConsumerAccountService.accountModify(request);
        } catch (BizException e) {
            LOGGER.error("红包增加失败", e);
            return null;
        }
    }

    //通过排行榜id获取奖品列表
    private List<NewGameCenterPrizeVO> getPrizeList(Long rankListId) {
        Result<List<NGameMultiPrizeVO>> result = ngameService.getRankOptions(rankListId);
        if (!result.getSuccess()) {
            return Collections.emptyList();
        }
        return result.getData().stream().map(nGameMultiPrizeVO -> {
            NewGameCenterPrizeVO newGameCenterPrizeVO = new NewGameCenterPrizeVO();
            newGameCenterPrizeVO.setGrade(convert2Grade(nGameMultiPrizeVO.getScope()));
            newGameCenterPrizeVO.setPrizeName(nGameMultiPrizeVO.getName());
            newGameCenterPrizeVO.setPrizeImg(nGameMultiPrizeVO.getLogo());
            newGameCenterPrizeVO.setPrizeId(nGameMultiPrizeVO.getId());
            newGameCenterPrizeVO.setItemId(nGameMultiPrizeVO.getItemId());

            return newGameCenterPrizeVO;
        }).collect(Collectors.toList());
    }

    private List<String> getScope(String extJson) {
        if (StringUtils.isBlank(extJson)) {
            return Lists.newArrayList();
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(extJson);
            String ranking = jsonObject.getString(DuibaNgameOptionsDto.EXT_JSON_KEY_MULTI_RANKING);
            List<String> scopes = Splitter.on(",").omitEmptyStrings().splitToList(ranking);
            return scopes.stream()
                    .map(scope -> {
                        List<String> area = Splitter.on("-").omitEmptyStrings().splitToList(scope);
                        if (CollectionUtils.isNotEmpty(area)
                                && area.size() == 2
                                && Objects.equals(area.get(0), area.get(1))) {
                            return area.get(0);
                        }
                        return scope;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("获取区间出错", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 获取用户某个排行榜的得到的奖品
     * @param id 排行榜id
     * @param cid 用户id
     * @param appId appId
     * @return NewGameCenterPrizeVO
     */
    private NewGameCenterPrizeVO getUserWinPrize(Long id, Long cid, Long appId) {
        NgameMutiRankingBaseConfigDto config = activityCacheService.findMultiRankConfig(id);
        if (config == null) {
            return null;
        }
        NgameMutiRankingRecordDto record = getRankListRecord(id, appId, cid);
        if (record == null) {
            return null;
        }
        NgameOrdersDto order = remoteNgameOrdersConsumerService.find(cid, record.getOrderId());
        if (order == null) {
            return null;
        }
        Long prizeId = order.getPrizeId();
        DuibaNgameOptionsDto option = remoteDuibaNgameOptionsService.find(prizeId);

        if (option == null) {
            return null;
        }
        NewGameCenterPrizeVO prizeVO = new NewGameCenterPrizeVO();
        prizeVO.setPrizeId(option.getId());
        prizeVO.setPrizeImg(option.getLogo());
        prizeVO.setPrizeName(option.getPrizeName());
        prizeVO.setItemId(option.getItemId());
        List<String> scopes = getScope(option.getExtJson());
        //游戏中心排行榜只有一个区间
        prizeVO.setGrade(convert2Grade(scopes.get(0)));
        prizeVO.setStatus(order.getExchangeStatus());

        ConsumerExchangeRecordDto exRecord = getRecord(order.getId(), cid);
        if (exRecord == null) {
            return prizeVO;
        }
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(appId);
        if (exRecord.getOrderId() != null) {
            prizeVO.setLink(domainConfigDto.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + exRecord.getOrderId() + "&fromPage=record&dbnewopen");
        } else {
            prizeVO.setLink(domainConfigDto.getActivityDomain() + "/activity/takePrizeNew?recordId=" + exRecord.getId());
        }
        return prizeVO;
    }

    private ConsumerExchangeRecordDto getRecord(Long oid, Long cid) {
        if (oid == null || cid == null) {
            return null;
        }
        DubboResult<ConsumerExchangeRecordDto> result = remoteConsumerExchangeRecordService.findByRelationIdAndType(oid, ConsumerExchangeRecordDto.TypeNgame, cid);
        if (result == null || !result.isSuccess() || result.getResult() == null) {
            return null;
        }
        return result.getResult();
    }

    private Integer convert2Grade(String scope) {
        String[] scopeList = scope.split(",");
        if (scopeList.length == 0) {
            return 0;
        }
        int grade = 0;
        for (String s : scopeList) {
            String[] scores = s.split("-");
            int score1 = Integer.parseInt(scores[0]);
            if (scores.length > 1) {
                int score2 = Integer.parseInt(scores[1]);
                grade = getGrade(score1, score2);
            } else  {
                grade = getGrade(score1);
            }
        }
        return grade;
    }

    private int getGrade(int score) {
        if (score == 1) {
            return 1;
        } else if (score == 2) {
            return  2;
        } else if (score == 3) {
            return  3;
        } else {
            return 0;
        }
    }

    private int getGrade(int score1, int score2) {
        if (score1 == 1) {
            return 1;
        } else if (score1 <= 2 && score2 >= 2) {
            return 2;
        } else if (score1 <= 3 && score2 >= 3) {
            return 3;
        } else {
            return 0;
        }
    }

    private SeasonConfigVO convert2SeasonConfigVO(SeasonConfigDto seasonConfigDto, Long consumerId, Long appId) {
        SeasonConfigVO seasonConfigVO = new SeasonConfigVO();

        seasonConfigVO.setId(seasonConfigDto.getId());
        seasonConfigVO.setPrevSeasonId(seasonConfigDto.getPrevSeasonId());
        seasonConfigVO.setTitle(seasonConfigDto.getTitle());
        seasonConfigVO.setStartTime(seasonConfigDto.getStartTime().getTime());
        seasonConfigVO.setEndTime(seasonConfigDto.getEndTime().getTime());
        seasonConfigVO.setRedPacketConfig(convert2RedPacketConfigVO(
                seasonConfigDto.getRedPacketConfig(), seasonConfigDto.getRankListId(), consumerId, appId));

        return seasonConfigVO;
    }

    private List<RedPacketConfigVO> convert2RedPacketConfigVO(String config, Long rankListId, Long consumerId, Long appId) {
        if (StringUtils.isBlank(config)) {
            LOGGER.info("红包配置为空,排行榜id：{}", rankListId);
            return Collections.emptyList();
        }
        JSONObject jsonConfig;
        try {
            jsonConfig = JSONObject.parseObject(config);
            List<RedPacketConfigVO> redPacketConfigVOList = new ArrayList<>();
            JSONObject redPacketStatus = getRedPacketStatus(jsonConfig, rankListId, consumerId, appId);
            if (redPacketStatus == null) {
                return Collections.emptyList();
            }
            jsonConfig.keySet().forEach(key -> {
                RedPacketConfigVO redPacketConfigVO = new RedPacketConfigVO();
                redPacketConfigVO.setId(Long.valueOf(key.substring(key.length() - 1)));
                JSONObject jsonObject = jsonConfig.getJSONObject(key);

                redPacketConfigVO.setRedPacketAmount(convert2RedPacketAmount(jsonObject.getLong(RED_PACKET_AMOUNT)));
                Long score = jsonObject.getLong(SCORE);
                if (score == null) {
                    redPacketConfigVO.setScore("0");
                    redPacketConfigVO.setStatus(NewGameCenterRedPacketStatusEnum.LOCKED.getCode());
                    redPacketConfigVOList.add(redPacketConfigVO);
                    return;
                } else {
                    redPacketConfigVO.setScore(score.toString());
                }
                Integer status = redPacketStatus.getInteger(key);
                redPacketConfigVO.setStatus(status == null ? NewGameCenterRedPacketStatusEnum.LOCKED.getCode() : status);
                redPacketConfigVOList.add(redPacketConfigVO);
            });
            return redPacketConfigVOList;
        } catch (Exception e) {
            LOGGER.error("{}非法红包配置",config, e);
            return Collections.emptyList();
        }
    }

    //排行榜下用户红包的状态
    private String getHbKey(Long rankListId, Long consumerId) {
        return ActAccessWebHBaseKeyEnum.K022.toString() + rankListId + "_" + consumerId;
    }

    private JSONObject getRedPacketStatus(JSONObject redPacketConfig, Long rankListId, Long consumerId, Long appId) {
        if (rankListId == null || appId == null || redPacketConfig == null) {
            LOGGER.info("获取红包状态参数错误");
            return null;
        }
        NgameMutiRankingRecordDto rankingRecordDto = null;
        if (consumerId != null) {
            rankingRecordDto = getRankListRecord(rankListId, appId, consumerId);
        }
        //未参与过游戏中心游戏
        if (rankingRecordDto == null) {
            JSONObject redPacketStatus = new JSONObject();
            redPacketConfig.keySet().forEach(key -> redPacketStatus.put(key, NewGameCenterRedPacketStatusEnum.LOCKED.getCode()));
            return redPacketStatus;
        }
        String hbKey = getHbKey(rankListId, consumerId);
        String jsonStr = remoteHbaseApiKvService.getStringByKey(hbKey);
        if (StringUtils.isNotBlank(jsonStr)) {
            return handleHasHb(jsonStr, redPacketConfig, rankingRecordDto, hbKey);
        } else {
            return handleNoHb(redPacketConfig, hbKey, rankingRecordDto);
        }
    }

    private JSONObject handleNoHb(JSONObject redPacketConfig, String hbKey, NgameMutiRankingRecordDto rankingRecordDto) {
        JSONObject redPacketStatus = new JSONObject();
        redPacketConfig.keySet().forEach(key -> {
            Long score = redPacketConfig.getJSONObject(key).getLong(SCORE);
            if (score != null) {
                if (rankingRecordDto.getTotalScore() >= score) {
                    redPacketStatus.put(key, NewGameCenterRedPacketStatusEnum.CAN_OPEN.getCode());
                } else {
                    redPacketStatus.put(key, NewGameCenterRedPacketStatusEnum.LOCKED.getCode());
                }
            }
        });
        try {
            remoteHbaseApiKvService.upsertKStrV(hbKey, redPacketStatus.toJSONString());
        } catch (Exception e) {
            LOGGER.error("新建hb失败", e);
        }
        return redPacketStatus;
    }

    private JSONObject handleHasHb(String jsonStr, JSONObject redPacketConfig, NgameMutiRankingRecordDto rankingRecordDto, String hbKey) {
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        redPacketConfig.keySet().forEach(key -> {
            Long score = redPacketConfig.getJSONObject(key).getLong(SCORE);
            if (score != null) {
                //1.缺少key
                //2.值是锁定的
                if (!jsonObject.containsKey(key)) {
                    if (rankingRecordDto.getTotalScore() >= score) {
                        jsonObject.put(key, NewGameCenterRedPacketStatusEnum.CAN_OPEN.getCode());
                    } else {
                        jsonObject.put(key, NewGameCenterRedPacketStatusEnum.LOCKED.getCode());
                    }
                }
                if (NewGameCenterRedPacketStatusEnum.LOCKED.getCode().equals(jsonObject.getInteger(key)) &&
                        rankingRecordDto.getTotalScore() >= score) {
                    jsonObject.put(key, NewGameCenterRedPacketStatusEnum.CAN_OPEN.getCode());
                }
            }
        });
        try {
            remoteHbaseApiKvService.upsertKStrV(hbKey, jsonObject.toJSONString());
        } catch (Exception e) {
            LOGGER.error("更新hb失败", e);
        }
        return jsonObject;
    }

    private NgameMutiRankingRecordDto getRankListRecord(Long rankListId, Long appId, Long consumerId) {
        NgameMutiRankingRecordDto query = new NgameMutiRankingRecordDto();
        query.setAppId(appId);
        query.setConsumerId(consumerId);
        query.setConfigBaseInfoId(rankListId);
        try {
            return remoteNgameMutiRankingRecordService.findByCidAndAppIdAndConfigId(query);
        } catch (Exception e) {
            LOGGER.error("获取排行榜记录失败", e);
            return null;
        }
    }

    private String convert2Score(Long score) {
        if (score < 10000) {
            return String.valueOf(score);
        }
        return new BigDecimal(score).divide(new BigDecimal(10000), 1, BigDecimal.ROUND_DOWN).toString();
    }

    private String convert2RedPacketAmount(Long amount) {
        if (amount == null) {
            return "";
        }
        return new BigDecimal(amount).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN).toString();
    }

    private SeasonConfigDto getSeasonConfigByAppId(Long appId) {
        if (appId == null || appId == 0) {
            return null;
        }
        try {
            return remoteSeasonConfigService.findByAppId(appId);
        } catch (Exception e) {
            LOGGER.error("获取赛季信息出错", e);
            return null;
        }
    }

    //获取当前赛季排行榜的排名
    private String getRank(Long rankListId, Long consumerId, Long appId) {
        NGameRank gameRank = ngameRankService.getNewMultiNgameTotalRankConfig(rankListId, appId);
        Long rank = gameRank.getRank(consumerId.toString());
        return convert2Rank(String.valueOf(rank));
    }
}
