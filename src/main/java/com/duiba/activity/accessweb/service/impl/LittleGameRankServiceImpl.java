package com.duiba.activity.accessweb.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.service.LittleGameRankService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

/**
 * ClassName: LittleGameRankServiceImpl <br/>
 * date: 2016年12月5日 上午11:06:21 <br/>
 *
 * @since JDK 1.6
 */
@Component("littleGameRankService")
public class LittleGameRankServiceImpl implements LittleGameRankService {

    private static Logger log = LoggerFactory.getLogger(LittleGameRankServiceImpl.class);

    /**
     * 游戏成绩上限
     */
    private static final long MAX_SCORE = 137371844607L;
    /**
     * 游戏成绩下线
     */
    private static final long MIN_SCORE = -1L;
    /**
     * 游戏时间计数器上限
     */
    private static final long MAX_TIME_SEQ = 67108864L;
    /**
     * 计算战胜多少用户时，舍入分割分数线
     */
    private static final BigDecimal CUT_PERCENTAGE = BigDecimal.valueOf(0.999);

    /**
     * redis缓存时间
     */
    private static final int EXPIRE_DAYS = 10;
    /**
     * 排名限制数
     */
    private static final int RANK_LIMIT_NUM = 200;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean add(final long gameId, String cid, long score, boolean isDesc, final String gameType, String duiBaGameType) {
        //获取200名成绩
        long score200 = getLimitRankScoreNew(gameId, isDesc);
        //根据isDesc判断该成绩是否在两百名以内
        boolean needTime = isDesc ? score > score200 : score < score200;
        //获取含时间成绩
        double nScore = getTimeScore(score, getTimeSeqNew(gameId, isDesc, needTime));
        //写入redis
        Boolean ret = redisTemplate.opsForZSet().add(RedisKeyFactory.K027.toString() + gameId, cid, nScore);
        redisTemplate.expire(RedisKeyFactory.K027.toString() + gameId, EXPIRE_DAYS, TimeUnit.DAYS);
        return ret;
    }

    @Override
    public Long rank(final long gameId, String cid, boolean isDesc, final String gameType) {
        Long rawRank = isDesc ? redisTemplate.opsForZSet().reverseRank(RedisKeyFactory.K027.toString() + gameId, cid) : redisTemplate.opsForZSet().rank(RedisKeyFactory.K027.toString() + gameId, cid);
        return rawRank == null ? null : rawRank + 1;
    }

    @Override
    public BigDecimal percentage(final long gameId, Long rank, final String gameType) {
        try {
            if (rank == null) {
                return BigDecimal.ZERO;
            }
            //获取游戏总用户
            Long count = redisTemplate.opsForZSet().zCard(RedisKeyFactory.K027.toString() + gameId);
            if (count == null || count == 0L) {
                return BigDecimal.ZERO;
            }
            BigDecimal percentage = new BigDecimal(count - rank + 1).divide(new BigDecimal(count), 6, RoundingMode.DOWN);
            if (percentage.compareTo(CUT_PERCENTAGE) > 0) {
                return percentage.setScale(4, RoundingMode.DOWN).movePointRight(2);
            } else {
                return percentage.setScale(4, RoundingMode.HALF_UP).movePointRight(2);
            }
        } catch (Exception e) {
            log.error("percentage", e);
            return BigDecimal.ZERO;
        }

    }

    private static double getTimeScore(long sc, long time) {
        if (sc > MAX_SCORE || sc < MIN_SCORE) {
            sc = sc > MAX_SCORE ? MAX_SCORE : MIN_SCORE;
        }
        long n = sc << 26;
        long last = n + time;
        return Double.longBitsToDouble(last);
    }

    private static long getScore(double timeScore) {
        return Double.doubleToLongBits(timeScore) >> 26;
    }


    private long getTimeSeqNew(long gameId, boolean isDesc, boolean needTime) {
        if (!needTime) {
            return isDesc ? 0L : MAX_TIME_SEQ;
        }
        long timeSeq = redisTemplate.opsForValue().increment(RedisKeyFactory.K029.toString() + gameId, 1);
        redisTemplate.expire(RedisKeyFactory.K029.toString() + gameId, EXPIRE_DAYS, TimeUnit.DAYS);
        timeSeq = timeSeq > MAX_TIME_SEQ ? MAX_TIME_SEQ : timeSeq;
        if (isDesc) {
            return MAX_TIME_SEQ - timeSeq;
        }
        return timeSeq;
    }

    private long getLimitRankScoreNew(long gameId, boolean isDesc) {
        String score = redisTemplate.opsForValue().get(RedisKeyFactory.K028.toString() + gameId);
        score = StringUtils.trim(score);
        if (score == null) {
            //写入新的200名成绩
            Set<ZSetOperations.TypedTuple<String>> set;
            if (isDesc) {
                set = redisTemplate.opsForZSet().reverseRangeWithScores(RedisKeyFactory.K027.toString() + gameId, RANK_LIMIT_NUM, RANK_LIMIT_NUM);
                if (set.isEmpty()) {
                    score = String.valueOf(MIN_SCORE);
                }
            } else {
                set = redisTemplate.opsForZSet().rangeWithScores(RedisKeyFactory.K027.toString() + gameId, RANK_LIMIT_NUM, RANK_LIMIT_NUM);
                if (set.isEmpty()) {
                    score = String.valueOf(MAX_SCORE);
                }
            }
            if (score == null) {
                Iterator<ZSetOperations.TypedTuple<String>> it = set.iterator();
                score = String.valueOf(getScore(it.next().getScore()));
            }
            redisTemplate.opsForValue().set(RedisKeyFactory.K028.toString() + gameId, score, 30, TimeUnit.SECONDS);
        }

        try {
            return Long.valueOf(score);
        } catch (NumberFormatException e) {
            log.error("", e);
            return 1;
        }
    }


}
