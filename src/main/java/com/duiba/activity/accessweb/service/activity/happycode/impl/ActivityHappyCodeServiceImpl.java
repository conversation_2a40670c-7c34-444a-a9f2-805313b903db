package com.duiba.activity.accessweb.service.activity.happycode.impl;

import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeBasicDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOptionDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOrderDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodePhaseDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeWinRecordDto;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.api.params.HappyCodeWinPrizeMsgParam;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeBasicService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeOptionService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeOrderService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodePhaseService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeWinRecordService;
import cn.com.duiba.activity.center.api.remoteservice.plugin.RemoteActivityPluginBackendService;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.bo.page.PageQuery;
import cn.com.duiba.api.tools.IdentificationTool;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsCouponDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemBaseDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsBackendService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemCouponGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.kvtable.service.api.enums.ActAccessWebHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.enums.HbaseKeySpaceEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbaseApiKvService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.dto.RequestParams;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.prize.center.api.dto.happycode.HappyCodePrizeDto;
import cn.com.duiba.prize.center.api.remoteservice.happycode.RemoteHappyCodeService;
import cn.com.duiba.stock.service.api.remoteservice.RemoteStockBackendService;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibabiz.component.domain.DomainService;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.constant.AbcHappyCodeConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.core.event.plugin.DuibaPluginEventsDispatcher;
import com.duiba.activity.accessweb.core.event.plugin.happycode.HappyCodePluginContext;
import com.duiba.activity.accessweb.core.event.plugin.happycode.impl.HappyCodeCouponStockPluginImpl;
import com.duiba.activity.accessweb.core.event.plugin.happycode.impl.HappyCodeStockPluginImpl;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.exception.BusinessException;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeIndexRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeJoinRecordRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeMyCodeRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeMyPastCodeRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodePastResultRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodePluginJoinRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeWinOptionRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.HappyCodeWithPluginRsp;
import com.duiba.activity.accessweb.service.ActivityTakePrizeService;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.activity.happycode.ActivityHappyCodeRedisService;
import com.duiba.activity.accessweb.service.activity.happycode.ActivityHappyCodeService;
import com.duiba.activity.accessweb.service.activity.happycode.HappyCodeTakeCodeRequest;
import com.duiba.activity.accessweb.service.activity.happycode.HappyCodeTakeCodeResponse;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.duiba.activity.accessweb.vo.TakePrizeVo;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeCreditsVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeJoinRecordVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeMyCodeVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeMyPastCodeVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodePastResultVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodePhaseInfoVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodePluginJoinVO;
import com.google.common.base.Splitter;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimaps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by hww on 2017/12/12
 */
@Service
public class ActivityHappyCodeServiceImpl implements ActivityHappyCodeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityHappyCodeServiceImpl.class);

    /** 空串 */
    private static final String EMPTY_STRING = "";

    /** 只取时间的 月份日期 */
    private final SimpleDateFormat dateFormatForCreateCode = new SimpleDateFormat("MMdd");

    /** 只取时间的 月份日期 */
    private final SimpleDateFormat dateFormatForJoinRecord = new SimpleDateFormat("YYYY/MM/dd HH:mm");

    /** 只取时间的 年份后两位月份日期 */
    private final SimpleDateFormat dateFormatForPhaseNumber = new SimpleDateFormat("yyMMdd");

    /** 将开心码来源数据库值 转化成页面显示文本 */
    private static final ImmutableMap<Integer, String> HAPPY_CODE_ORIGIN_DESC = ImmutableMap.of(0, "签到得码", 1, "插件得码");

    /** 将开心码期次数据库值 转化成页面显示文本 */
    private static final ImmutableMap<Integer, String> HAPPY_CODE_PHASE_STATUS = ImmutableMap.of(0, "未开启", 1, "进行中", 2, "待开奖", 3, "已开奖");

    /** 开心码开奖支持的奖品类型 */
    private final ImmutableList HAPPY_CODE_SUPPORTED_PRIZE = ImmutableList.of(ItemDto.TypeObject, ItemDto.TypeVirtual, ItemDto.TypeCoupon, ItemDto.TypeAlipay, ItemDto.TypePhonebill, ItemDto.TypeQB);

    /** 默认分享次数（每天）*/
    private static final int DEFAULT_SHARE_LIMIT_COUNT = 2;


    /** 往期信息缓存 */
    private static final Cache<Long, HappyCodePastResultRsp> HAPPY_CODE_PAST_RESULT_RSP = CacheBuilder.newBuilder().expireAfterWrite(8, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 参与记录缓存 */
    private static final Cache<Long, HappyCodeJoinRecordRsp> HAPPY_CODE_JOIN_RECORD_RSP = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();
    /** 我的开心码缓存 */
    private static final Cache<String, HappyCodeMyCodeRsp> HAPPY_CODE_MY_CODE_RSP = CacheBuilder.newBuilder().expireAfterWrite(20, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 登录用户首页缓存 */
    private static final Cache<String, HappyCodeIndexRsp> HAPPY_CODE_INDEX_RSP = CacheBuilder.newBuilder().expireAfterWrite(9, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 登录用户首页(带插件)缓存 */
    private static final Cache<String, HappyCodeWithPluginRsp> HAPPY_CODE_INDEX_WITH_PLUGIN_RSP = CacheBuilder.newBuilder().expireAfterWrite(3, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 未登录用户首页(带插件)缓存 */
    private static final Cache<String, HappyCodeWithPluginRsp> HAPPY_CODE_NOT_LOGIN_INDEX_WITH_PLUGIN_RSP = CacheBuilder.newBuilder().expireAfterWrite(3, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 商品库存缓存 */
    private static final Cache<Long, ItemDto> HAPPY_CODE_ITEM_CACHE = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 期次信息缓存 */
    private static final Cache<Long, HappyCodePhaseDto> HAPPY_CODE_PHASE_CACHE = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.SECONDS).maximumSize(1000).build();
    /** 用户未登录首页缓存 */
    private static final Cache<Long, HappyCodeIndexRsp> HAPPY_CODE_NOT_LOGIN_INDEX_RSP = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).maximumSize(1000).build();
    /** 开心码规则缓存 */
    private static final Cache<Long, String> HAPPY_CODE_RULE_RSP = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).maximumSize(1000).build();
    /** 开心码活动集合页缓存 */
    private static final Cache<String, HappyCodePluginJoinRsp> HAPPY_CODE_PLUGIN_JOIN_LIST_RSP_CACHE = CacheBuilder.newBuilder().expireAfterWrite(9, TimeUnit.SECONDS).maximumSize(1000).build();

    /** 开心码我的往期码缓存 */
    private static final Cache<String, HappyCodeMyPastCodeRsp> HAPPY_CODE_MY_PAST_CODE_RSP_CACHE = CacheBuilder.newBuilder().expireAfterWrite(20, TimeUnit.SECONDS).maximumSize(1000).build();

    /** 开心码我的往期码缓存 */
    private static final Cache<Long, HappyCodePhaseInfoVO> HAPPY_CODE_CURRENT_PHASE_OPTION_CACHE = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();


    @Autowired
    private RemoteHappyCodeBasicService remoteHappyCodeBasicService;
    @Autowired
    private RemoteHappyCodePhaseService remoteHappyCodePhaseService;
    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;
    @Autowired
    private RemoteDuibaItemGoodsBackendService remoteDuibaItemGoodsBackendService;
    @Autowired
    private RemoteHappyCodeOrderService remoteHappyCodeOrderService;
    @Autowired
    private RemoteHappyCodeOptionService remoteHappyCodeOptionService;
    @Autowired
    private RemoteHappyCodeWinRecordService remoteHappyCodeWinRecordService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;
    @Autowired
    private RemoteActivityPluginBackendService remoteActivityPluginBackendService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private ActivityHappyCodeRedisService activityHappyCodeRedisService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ActivityTakePrizeService activityTakePrizeService;
    @Resource(name="redisTemplate")
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private RemoteItemCouponGoodsService remoteItemCouponGoodsService;
    @Resource(name="redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
    @Autowired
    private RemoteStockBackendService remoteStockBackendService;
    @Autowired
    private RemoteHbaseApiKvService remoteHbaseApiKvService;
    @Autowired
    private RemoteHappyCodeService remoteHappyCodeService;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private GoodsCacheService goodsCacheService;
    @Autowired
    private DomainService domainService;

    @Autowired
    private AbcHappyCodeConstants abcHappyCodeConstants;


    @Override
    public HappyCodeTakeCodeResponse takeOneHappyCode(HappyCodeTakeCodeRequest request) {
        Long basicId = request.getBasicId();
        try {
            //校验发码请求参数
            checkRequestParams(request);
        } catch (BusinessException e) {
            LOGGER.warn("发码失败，请求参数异常，赛事id={}，msg={}", basicId, e.getMsg());
            return HappyCodeTakeCodeResponse.fail();
        }
        //校验赛事状态
        HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(basicId);
        if (checkBasicConfig(request, basic)) {
            return HappyCodeTakeCodeResponse.fail();
        }
        //校验期次状态
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(basic.getPhaseId());
        if (phase == null || Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_WAIT_FOR_START)) {
            //期次不存在，或者期次未开启
            return HappyCodeTakeCodeResponse.fail();
        }
        HappyCodeOrderDto order = createHappyCodeOrder(request);
        if (order == null) {
            return HappyCodeTakeCodeResponse.fail();
        } else if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_TIME)) {
            return takeOneHappyCodeForTimeLottery(order, phase);
        } else if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            return takeOneHappyCodeForNumberLottery(order, phase, basic);
        } else {
            return HappyCodeTakeCodeResponse.fail();
        }
    }

    /**
     * 到时开奖模式开奖
     * @param order
     * @param phase
     * @return
     */
    private HappyCodeTakeCodeResponse takeOneHappyCodeForTimeLottery(HappyCodeOrderDto order, HappyCodePhaseDto phase) {
        Date now = new Date();
        Date startDate = phase.getStartDate();
        //期次开始时间校验
        if (startDate == null || now.before(startDate)) {
            return HappyCodeTakeCodeResponse.fail();
        }
        //期次结束时间校验
        Date endDate = phase.getEndDate();
        if (endDate == null || now.after(endDate)) {
            return HappyCodeTakeCodeResponse.fail();
        }
        return takeOneHappyCodeForOnce(order, phase);
    }

    /**
     * 到量开奖模式发码
     * 如果当期库存不足，且赛事配置为自动续期，会尝试发下一期
     * @param order
     * @param phase
     * @param basic
     * @return
     */
    private HappyCodeTakeCodeResponse takeOneHappyCodeForNumberLottery(HappyCodeOrderDto order, HappyCodePhaseDto phase, HappyCodeBasicDto basic) {
        if (Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_WAIT_FOR_START)) {
            return HappyCodeTakeCodeResponse.fail();
        }
        if (Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_OPENING)) {
            try {
                return getHappyCode(order, phase, basic);
            } catch (AccessActivityRuntimeException a) {
                LOGGER.warn("发码失败", a);
                //降级处理
                checkAndUpdatePhaseStatus(order.getAppId(), phase.getId(), remoteDuibaItemGoodsService.find(phase.getCodeItemId()).getResult());
            }
        }
        if (shouldDegraded(basic)) {
            return lowerProcessor(order, basic.getNextPhaseId());
        }
        return HappyCodeTakeCodeResponse.fail();
    }

    /**
     * 校验是否需要降级处理 需要满足条件
     * 1.赛事开启状态
     * 2.赛事开启自动续期功能
     *
     * 满足上诉条件后 校验下一期是否开启
     * 若未开启 则开启
     *
     * @param basic
     * @return
     */
    private boolean shouldDegraded(HappyCodeBasicDto basic) {
        if (!Objects.equals(basic.getAutoContinue(), HappyCodeBasicDto.AUTO_CONTINUE_TRUE) || !Objects.equals(basic.getBasicStatus(), HappyCodeBasicDto.STATUS_OPENING)) {
            //不是自动续期模式 或者 赛事未开启 返回失败
            return false;
        }
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(basic.getNextPhaseId());
        if (phase == null) {
            return false;
        }
        //未开启状态
        if (Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_WAIT_FOR_START)) {
            //此处实现一个逻辑，不同天生成期次号时，后缀从01开始重新计数
            String oldPhaseNumber = phase.getPhaseNumber().substring(0, 6);
            String oldPhaseNumberPrefix = oldPhaseNumber.substring(0, 6);
            String phaseNumberPrefix = dateFormatForPhaseNumber.format(new Date());
            if (!Objects.equals(oldPhaseNumberPrefix, phaseNumberPrefix)) {
                remoteHappyCodePhaseService.updatePhaseNumber(phase.getId(), phaseNumberPrefix + "01");
            }
            //开启当前期次
            remoteHappyCodePhaseService.updateStatusToOpening(basic.getNextPhaseId());
        }
        return true;
    }

    /**
     * 降级处理，发下一期的开心码
     * @param order
     * @param nextPhaseId
     * @return
     */
    private HappyCodeTakeCodeResponse lowerProcessor(HappyCodeOrderDto order, Long nextPhaseId) {
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(nextPhaseId);
        if (phase == null) {
            return HappyCodeTakeCodeResponse.fail();
        }
        return takeOneHappyCodeForOnce(order, phase);

    }

    /**
     * 走一次发码逻辑
     * @param order
     * @param phase
     * @return
     */
    private HappyCodeTakeCodeResponse takeOneHappyCodeForOnce(HappyCodeOrderDto order, HappyCodePhaseDto phase) {
        if (!Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_OPENING)) {
            return HappyCodeTakeCodeResponse.fail();
        }
        try {
            return getHappyCode(order, phase, null);
        } catch (AccessActivityRuntimeException a) {
            LOGGER.warn("发码失败", a);
        }
        return HappyCodeTakeCodeResponse.fail();
    }

    /**
     * 发码逻辑
     * @param order 开心码子订单
     * @param phase 期次配置信息
     * @return
     * @throws AccessActivityRuntimeException
     */
    private HappyCodeTakeCodeResponse getHappyCode(HappyCodeOrderDto order, HappyCodePhaseDto phase, HappyCodeBasicDto basic) throws AccessActivityRuntimeException{
        //1.校验开心码商品库存
        //是否需要切换当前期次为待开奖状态
        boolean needCheck = false;
        DubboResult<ItemDto> result = remoteDuibaItemGoodsService.find(phase.getCodeItemId());
        if (!result.isSuccess() || result.getResult() == null) {
            LOGGER.warn("开心码商品不存在赛事id={}，商品id={}", phase.getBasicId(), phase.getCodeItemId());
            return HappyCodeTakeCodeResponse.fail();
        }
        ItemDto item = result.getResult();
        if (item.getRemaining() == null || item.getRemaining() <= 0) {
            //库存不足抛出异常
            throw new AccessActivityRuntimeException();
        }
        if (item.getRemaining() <= 1 && Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            //如果是到量模式 并且当前库存小于等于1
            needCheck = true;
        }
        //2.开心码商品扣库存
        HappyCodePluginContext context = new HappyCodePluginContext();
        context.setAttribute(HappyCodePluginContext.ITEM_KEY, item);
        DuibaPluginEventsDispatcher.get().triggerBeforeHappyCodeStockComplete(order, context);
        //校验是否扣库存成功
        if (!Objects.equals(context.getAttribute(HappyCodeStockPluginImpl.HAPPY_CODE_STOCK), true)) {
            throw new AccessActivityRuntimeException("扣库存失败");
        }
        order.setPhaseId(phase.getId());
        order.setPhaseNumber(phase.getPhaseNumber());
        try {
            //3.生成开心码
            String happyCode = getHappyCode(phase.getCodeType(), phase.getPayload(), phase.getId());
            if (happyCode == null) {
                LOGGER.warn("生成开心码失败，orderId={}", order.getId());
                return HappyCodeTakeCodeResponse.fail();
            }
            order.setHappyCode(happyCode);
            //4.更新子订单
            int skip = remoteHappyCodeOrderService.updateHappyCodeAndPhase(order);
            if (skip != 1) {
                throw new BusinessException("更新开心码子订单失败");
            }
            if (needCheck && basic != null) {
                //需要校验一次当前期次是否需要切换成待开奖状态
                checkAndUpdatePhaseStatus(order.getAppId(), order.getPhaseId(), item);
                shouldDegraded(basic);
            }
            return HappyCodeTakeCodeResponse.success(happyCode, String.valueOf(order.getId()));
        } catch (Exception e) {
            LOGGER.warn("发码失败", e);
            DuibaPluginEventsDispatcher.get().beforeHappyCodeStockCompleteException(order, context);
        }
        return HappyCodeTakeCodeResponse.fail();
    }

    /**
     * 校验期次状态是否需要改成待开奖
     * @param appId
     * @param phaseId
     * @param item
     */
    private void checkAndUpdatePhaseStatus(Long appId, Long phaseId, ItemDto item) {
        //查询商品确实已经发完，更新期次状态为待开奖
        Long stockId = item.getStockId();
        DubboResult<Long> result;
        if (stockId != null) {
            //商品库存已经改造到库存中心，由于库存中心有缓存，通过goods-center接口查询的商品库存会不准确，此处直接查询没有缓存的库存
            result = remoteStockBackendService.find(item.getStockId());
        } else {
            result =remoteItemKeyService.findStock(new ItemKeyDto(null, item, appId));
        }
        if (result == null || !result.isSuccess() || result.getResult() == null) {
            LOGGER.warn("查询库存信息失败, itemId={}, stockId={}", item.getId(), stockId);
            return;
        }
        Long stock = result.getResult();
        if (stock != null && stock <= 0) {
            remoteHappyCodePhaseService.updateStatusToWaitOpenPrize(phaseId);
        }
    }

    /**
     * 校验赛事配置
     * @param request
     * @param basic
     * @return
     */
    private boolean checkBasicConfig(HappyCodeTakeCodeRequest request, HappyCodeBasicDto basic) {
        if (basic == null) {
            return true;
        }
        if (Objects.equals(basic.getBasicStatus(), HappyCodeBasicDto.STATUS_READY)) {
            LOGGER.warn("发码失败，赛事未开启，赛事id={}，用户id={}", basic.getId(), request.getConsumerId());
            return true;
        }
        //校验发码定向
        if (!checkAppId(basic.getAppIds(), request.getAppId())) {
            LOGGER.warn("发码失败，appId未定向，赛事id={}，用户id={}", basic.getId(), request.getConsumerId());
            return true;
        }
        return false;
    }

    /**
     * 根据定向设置，校验是否可以发码
     * @param appIds
     * @param appId
     * @return
     */
    private boolean checkAppId(String appIds, Long appId) {
        return StringUtils.isBlank(appIds) || Splitter.on(",").omitEmptyStrings().splitToList(appIds).contains(String.valueOf(appId));
    }

    /**
     * 校验发码请求参数
     * @param request
     */
    private void checkRequestParams(HappyCodeTakeCodeRequest request) {
        if (request.getAppId() == null) {
            throw new BusinessException("appId不能为空");
        }
        if (request.getBasicId() == null) {
            throw new BusinessException("赛事Id不能为空");
        }
        if (request.getConsumerId() == null) {
            throw new BusinessException("兑吧用户Id不能为空");
        }
        if (StringUtils.isBlank(request.getPartnerUserId())) {
            throw new BusinessException("开发者用户Id不能为空");
        }
        if (request.getOrigin() == null) {
            throw new BusinessException("请求来源不能为空");
        }
    }

    /**
     * 生成开心码
     * @param codeType
     * @param payLoad
     * @param phaseId
     * @return
     */
    private String getHappyCode(Integer codeType, Integer payLoad, Long phaseId) {
        if (!Objects.equals(codeType, HappyCodePhaseDto.CODE_TYPE_A) && !Objects.equals(codeType, HappyCodePhaseDto.CODE_TYPE_B) || phaseId == null || payLoad == null) {
            return null;
        }
        String datePrefix = dateFormatForCreateCode.format(new Date());
        if (Objects.equals(codeType, HappyCodePhaseDto.CODE_TYPE_A)) {
            return activityHappyCodeRedisService.generateHappyCodeByTypeA(payLoad, phaseId, datePrefix);
        } else if (Objects.equals(codeType, HappyCodePhaseDto.CODE_TYPE_B)) {
            return activityHappyCodeRedisService.generateHappyCodeByTypeB(phaseId, datePrefix);
        }
        return null;
    }

    /**
     * 创建开心码子订单
     * @param request
     * @return
     */
    private HappyCodeOrderDto createHappyCodeOrder(HappyCodeTakeCodeRequest request) {
        HappyCodeOrderDto order = new HappyCodeOrderDto();
        Date now = new Date();
        order.setAppId(request.getAppId());
        order.setConsumerId(request.getConsumerId());
        order.setDeleted(0);
        order.setExchangeStatus(HappyCodeOrderDto.EXCHANGE_STATUS_NONE);
        order.setGmtCreate(now);
        order.setGmtModified(now);
        order.setOrderStatus(HappyCodeOrderDto.STATUS_READY);
        order.setOrigin(request.getOrigin());
        order.setPartnerUserId(request.getPartnerUserId());
        Long id = remoteHappyCodeOrderService.insert(order);
        if (id == null) {
            return null;
        }
        order.setId(id);
        return order;
    }

    /**
     * 开心码开奖
     *
     * @param msg
     */
    @Override
    public void openPrize(String msg) {
        HappyCodeWinPrizeMsgParam param = JSONObject.parseObject(msg, HappyCodeWinPrizeMsgParam.class);
        Long orderId = param.getOrderId();
        Long optionId = param.getOptionId();
        boolean cheat = param.isCheat();
        HappyCodeOrderDto order = remoteHappyCodeOrderService.findOrderById(orderId);
        if (order == null) {
            return;
        }
        HappyCodeOptionDto option = remoteHappyCodeOptionService.findById(optionId);
        if (option == null) {
            return;
        }
        ItemDto item = remoteDuibaItemGoodsService.find(option.getItemId()).getResult();
        if (item == null || !HAPPY_CODE_SUPPORTED_PRIZE.contains(item.getType())) {
            return;
        }
        HappyCodePluginContext context = new HappyCodePluginContext();
        try {
            context.setAttribute(HappyCodePluginContext.ITEM_KEY, item);
            order.setOptionId(optionId);
            order.setOptionType(option.getPrizeType());
            order.setFacePrice(option.getFacePrice());
            order.setOptionName(option.getPrizeName());
            order.setItemId(option.getItemId());
            order.setExchangeStatus(HappyCodeOrderDto.EXCHANGE_STATUS_WAIT);
            DuibaPluginEventsDispatcher.get().triggerBeforeHappyCodeStockComplete(order, context);
            if (!checkConsumeStock(context, order.getOptionType())) {
                throw new BusinessException("扣库存失败");
            }
            int skip = remoteHappyCodeOrderService.updatePrizeInfo(order);
            if (skip != 1) {
                throw new BusinessException("更新开心码子订单中奖信息失败");
            }
            HappyCodeWinRecordDto winRecord = new HappyCodeWinRecordDto();
            Date now = new Date();
            winRecord.setAppId(order.getAppId());
            winRecord.setBasicId(param.getBasicId());
            winRecord.setCheat(cheat ? 1 : 0);
            winRecord.setConsumerId(order.getConsumerId());
            winRecord.setDeleted(0);
            winRecord.setGmtCreate(now);
            winRecord.setGmtModified(now);
            winRecord.setOptionId(option.getId());
            winRecord.setOptionName(option.getPrizeName());
            winRecord.setOptionLogo(option.getLogo());
            winRecord.setOptionType(option.getPrizeType());
            winRecord.setPartnerUserId(order.getPartnerUserId());
            winRecord.setOrderId(order.getId());
            winRecord.setPhaseId(order.getPhaseId());
            winRecord.setWinCode(order.getHappyCode());
            remoteHappyCodeWinRecordService.insertWinRecord(winRecord);
            ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
            record.setConsumerId(order.getConsumerId());
            record.setType(ConsumerExchangeRecordDto.TYPE_HAPPY_CODE);
            record.setRelationId(order.getId());
            if (param.getBasicId() != null) {
                record.setOrigin(param.getBasicId() * 100 + ConsumerExchangeRecordDto.ORIGIN_HAPPY_CODE);
            }
            record.setOverDue(getOverDue(item, order, context));
            // 此处不传appId,如果需要通知开发者则需要传appId
            //record.setAppId(order.getAppId());
            DubboResult<ConsumerExchangeRecordDto> recordResult = remoteConsumerExchangeRecordService.insert(record);
            if (Objects.equals(ItemDto.TypeVirtual, option.getPrizeType()) && !item.isOpTypeItem(ItemBaseDto.OpTypeNeedUserName)) {
                AppSimpleDto app = commonService.findAppDto(order.getAppId());
                TakePrizeVo takePrize = new TakePrizeVo(
                        app,
                        new ItemKeyDto(null, item, app.getId()),
                        BeanUtils.copy(order, OrdersVO.class),
                        ConsumerExchangeRecordDto.TYPE_HAPPY_CODE,
                        new RequestParams(),
                        OrdersDto.CHARGE_MODE_HAPPY_CODE,
                        OrdersDto.RELATION_TYPE_HAPPY_CODE,OrdersDto.SubOrderTypeHappyCode);
                takePrize.setIsduiActivity(true);
                takePrize.setPrizeType(option.getPrizeType());
                String facePrice = remoteDuibaItemGoodsService.getJsonValue(option.getItemId(), "itemJson").getResult();
                takePrize.setFacePrice(facePrice);
                activityTakePrizeService.takePrize(takePrize, null, recordResult.getResult());
                //添加提示次数 保留7天
                String redisKey = getExchangeNoticeForVirtualRedisKey(order.getConsumerId(), winRecord.getBasicId());
                redisAtomicClient.incrBy(redisKey, 1, 7, TimeUnit.DAYS);
            }
        } catch (BusinessException b) {
            LOGGER.warn("开心码发奖失败, msg={}, rderId={}, optionId={}", b.getMsg(), orderId, optionId);
            DuibaPluginEventsDispatcher.get().beforeHappyCodeStockCompleteException(order, context);
            return;
        } catch (Exception e) {
            LOGGER.warn("系统异常,order={}", order.getId(), e);
            DuibaPluginEventsDispatcher.get().beforeHappyCodeStockCompleteException(order, context);
            return;
        }
        //发奖后续处理,失效中奖订单
        delWinnerOrder(param.getPhaseId(), order.getConsumerId());
    }

    /**
     * 开心码扑克牌模式开奖
     *
     * @param param
     */
    @Override
    public Pair<String, String> openPrizePoker(HappyCodeWinPrizeMsgParam param) throws BizException {//NOSONAR
        Long orderId = param.getOrderId();
        Long optionId = param.getOptionId();
        HappyCodeOrderDto order = remoteHappyCodeOrderService.findOrderById(orderId);
        if (order == null) {
            throw new BizException("无效的开心码订单id");
        }
        if(!Objects.equals(HappyCodeOrderDto.EXCHANGE_STATUS_WAIT_TAKE_PRIZE,order.getExchangeStatus())){
            throw new BizException("当前开心码未中奖或已领奖，无法开奖");
        }

        HappyCodeOptionDto option = remoteHappyCodeOptionService.findById(optionId);
        if (option == null) {
            throw new BizException("奖项不存在");
        }
        ItemDto item = remoteDuibaItemGoodsService.find(option.getItemId()).getResult();
        if (item == null || !HAPPY_CODE_SUPPORTED_PRIZE.contains(item.getType())) {
            throw new BizException("商品id不存在或不支持领奖！");
        }
        HappyCodePluginContext context = new HappyCodePluginContext();
        try {
            context.setAttribute(HappyCodePluginContext.ITEM_KEY, item);
            order.setOptionId(optionId);
            order.setOptionType(option.getPrizeType());
            order.setOptionName(option.getPrizeName());
            order.setItemId(option.getItemId());
            int skip = remoteHappyCodeOrderService.updatePrizeInfo(order);
            if (skip != 1) {
                throw new BusinessException("更新开心码子订单中奖信息失败");
            }
            DuibaPluginEventsDispatcher.get().triggerBeforeHappyCodeStockComplete(order, context);
            if (!checkConsumeStock(context, order.getOptionType())) {
                throw new BizException("扣库存失败");
            }
            ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
            record.setConsumerId(order.getConsumerId());
            record.setType(ConsumerExchangeRecordDto.TYPE_HAPPY_CODE);
            record.setRelationId(order.getId());
            if (param.getBasicId() != null) {
                record.setOrigin(param.getBasicId() * 100 + ConsumerExchangeRecordDto.ORIGIN_HAPPY_CODE);
            }
            record.setOverDue(newGetOverDue(item, order, context, param, option));
            // 此处不传appId,如果需要通知开发者则需要传appId
            DubboResult<ConsumerExchangeRecordDto> recordResult = remoteConsumerExchangeRecordService.insert(record);
            OrdersDto mainOrder = null;
            // 不需要手动填写账号的虚拟商品自动领奖
            if (Objects.equals(ItemDto.TypeVirtual, option.getPrizeType())  && !item.isOpTypeItem(ItemBaseDto.OpTypeNeedUserName)) {
                AppSimpleDto app = commonService.findAppDto(order.getAppId());
                TakePrizeVo takePrize = new TakePrizeVo(
                        app,
                        new ItemKeyDto(null, item, app.getId()),
                        BeanUtils.copy(order, OrdersVO.class),
                        ConsumerExchangeRecordDto.TYPE_HAPPY_CODE,
                        new RequestParams(),
                        OrdersDto.CHARGE_MODE_HAPPY_CODE,
                        OrdersDto.RELATION_TYPE_HAPPY_CODE, OrdersDto.SubOrderTypeHappyCode);
                takePrize.setIsduiActivity(true);
                takePrize.setPrizeType(option.getPrizeType());
                String facePrice = remoteDuibaItemGoodsService.getJsonValue(option.getItemId(), "itemJson").getResult();
                takePrize.setFacePrice(facePrice);
                mainOrder = activityTakePrizeService.takePrize(takePrize, null, recordResult.getResult());
                //添加提示次数 保留7天
                String redisKey = getExchangeNoticeForVirtualRedisKey(order.getConsumerId(), param.getBasicId());
                redisAtomicClient.incrBy(redisKey, 1, 7, TimeUnit.DAYS);
            } else {
                //实物商品/优惠券-用户手动领奖-将领奖状态设置为待领奖
                order.setExchangeStatus(HappyCodeOrderDto.EXCHANGE_STATUS_WAIT);
                remoteHappyCodeOrderService.updatePrizeInfo(order);
            }
            //提前生成winRecord
            HappyCodeWinRecordDto winRecord = new HappyCodeWinRecordDto();
            winRecord.setAppId(order.getAppId());
            winRecord.setBasicId(param.getBasicId());
            winRecord.setCheat(0);
            winRecord.setConsumerId(order.getConsumerId());
            winRecord.setDeleted(0);
            winRecord.setOptionId(option.getId());
            winRecord.setOptionName(option.getPrizeName());
            winRecord.setOptionLogo(option.getLogo());
            winRecord.setOptionType(option.getPrizeType());
            winRecord.setPrizeLevel(option.getPrizeLevel());
            winRecord.setPartnerUserId(order.getPartnerUserId());
            winRecord.setOrderId(order.getId());
            winRecord.setPhaseId(order.getPhaseId());
            winRecord.setWinCode(order.getHappyCode());
            remoteHappyCodeWinRecordService.insertWinRecord(winRecord);

            DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
            String itemLink;

            if (null != mainOrder) {
                itemLink = domainConfigDto.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + mainOrder.getId() + "&fromPage=record&dbnewopen";
            } else {
                itemLink = domainConfigDto.getActivityDomain() + "/activity/takePrizeNew?recordId=" + recordResult.getResult().getId();
            }
            return Pair.with(null == mainOrder ? null : mainOrder.getOrderNum(), itemLink);
        } catch (BizException b) {
            LOGGER.warn("开心码扑克牌模式发奖失败, msg={}, orderId={}, optionId={}", b.getMessage(), orderId, optionId);
            DuibaPluginEventsDispatcher.get().beforeHappyCodeStockCompleteException(order, context);
            return Pair.with(null, null);
        } catch (Exception e) {
            LOGGER.warn("系统异常,order={}", order.getId(), e);
            DuibaPluginEventsDispatcher.get().beforeHappyCodeStockCompleteException(order, context);
            return Pair.with(null, null);
        }
    }

    /**
     * 校验是否成功扣除库存
     * @return
     */
    private boolean checkConsumeStock(HappyCodePluginContext context, String optionType) {
        if (!Objects.equals(context.getAttribute(HappyCodeStockPluginImpl.HAPPY_CODE_STOCK), true)) {
            //扣除库存失败
            return false;
        }
        if (!Objects.equals(optionType, ItemDto.TypeCoupon)) {
            //扣除库存成功  非优惠券商品
            return true;
        }
        if (!Objects.equals(context.getAttribute(HappyCodeCouponStockPluginImpl.HAPPY_CODE_COUPON_CONSUME), true)) {
            //扣除优惠券库存失败
            return false;
        }
        //优惠券id是否为空
        return context.getAttribute(HappyCodeCouponStockPluginImpl.HAPPY_CODE_COUPON_ID) != null;
    }

    /**
     * 扩展 获取兑换记录过期时间，增加定制逻辑
     * @param item
     * @param order
     * @param context
     * @param param
     * @param option
     * @return
     */
    private Date newGetOverDue(ItemDto item, HappyCodeOrderDto order, HappyCodePluginContext context
            , HappyCodeWinPrizeMsgParam param, HappyCodeOptionDto option) {
        // 浙江农行开心码活动实物领奖过期时间定制
        if(param.getBasicId() != null
                && Objects.equals(param.getBasicId(), abcHappyCodeConstants.getBasicId())
                && Objects.equals(ItemDto.TypeObject, option.getPrizeType())
                && abcHappyCodeConstants.getOverDueMillis() != null
                && abcHappyCodeConstants.getOverDueMillis() > System.currentTimeMillis()) {
            return new Date(abcHappyCodeConstants.getOverDueMillis());
        }
        return getOverDue(item, order, context);
    }

    /**
     * 获取兑换记录的过期时间 默认设置为7天
     * 优惠券根据本身的过期时间
     * @param item
     * @param order
     * @param context
     * @return
     */
    private Date getOverDue(ItemDto item, HappyCodeOrderDto order, HappyCodePluginContext context) {
        if (ItemDto.TypeCoupon.equals(order.getOptionType())) {
            Long couponId = (Long) context.getAttribute(HappyCodeCouponStockPluginImpl.HAPPY_CODE_COUPON_ID);
            if (couponId == null) {
                return DateUtils.daysAddOrSub(new Date(), 7);
            }
            saveCouponId(order.getId(), couponId);
            try {
                GoodsCouponDto goodsCouponDto = remoteItemCouponGoodsService.findCoupon(new ItemKeyDto(null, item, order.getAppId()), couponId).getResult();
                if (goodsCouponDto != null) {
                    return goodsCouponDto.getOverDue();
                }
            } catch (Exception e) {
                LOGGER.warn("查询商品券有效日期失败,兑换记录有效期设置为7天 couponId={}", couponId);
            }
        }
        return DateUtils.daysAddOrSub(new Date(), 7);
    }

    /** 将优惠券id存入redis中 */
    private void saveCouponId(Long orderId, Long couponId) {
        String redisKey = getCouponIdRedisKey(orderId);
        advancedCacheClient.set(redisKey, couponId, 7, TimeUnit.DAYS);
    }

    /**
     * 根据期次id,用户id，处理失效订单逻辑
     * @param phaseId
     * @param consumerId
     */
    private void delWinnerOrder(Long phaseId, Long consumerId) {
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (phase == null) {
            return;
        }
        if (!Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_TIME) || !Objects.equals(phase.getDelWinnerOrder(), 1)) {
            //非到时开奖或者不需要失效订单
            return;
        }
        Date startDate;
        Date endDate;
        if (Objects.equals(HappyCodePhaseDto.PRIZE_ORDER_AREA_A, phase.getPrizeOrderArea())) {
            //当期下所有开奖当天凌晨0点之前所发的码
            startDate = null;
            endDate = DateUtils.getDayDate(new Date());
        } else if (Objects.equals(HappyCodePhaseDto.PRIZE_ORDER_AREA_B, phase.getPrizeOrderArea())) {
            //当期下所有当天凌晨0点-开奖时刻之间所发的码
            startDate = DateUtils.getDayDate(new Date());
            endDate = new Date();
        } else if (Objects.equals(HappyCodePhaseDto.PRIZE_ORDER_AREA_C, phase.getPrizeOrderArea())) {
            //当期下所有开奖时刻之前所发的码
            startDate = null;
            endDate = new Date();
        } else {
            return;
        }
        remoteHappyCodeOrderService.delWinnerOrder(phaseId, consumerId, startDate, endDate);
    }

    /**
     * 根据赛事id查询赛事主页情况
     *
     * @param id
     * @param consumerId
     * @return
     */
    @Override
    public HappyCodeIndexRsp getIndex(Long id, Long consumerId,Long pluginId,Boolean needShare,Boolean needCache) {
        String key = id + "_" + consumerId;
        HappyCodeIndexRsp happyCodeIndexRsp;
        if (needCache) {
            happyCodeIndexRsp = HAPPY_CODE_INDEX_RSP.getIfPresent(key);
            if (null != happyCodeIndexRsp) {
                return happyCodeIndexRsp;
            }
        }
        HappyCodeBasicDto basicConfig = remoteHappyCodeBasicService.findById(id);
        if (Objects.equals(basicConfig.getDeleted(), 1)) {
            //赛事已删除，返回空响应
            happyCodeIndexRsp = new HappyCodeIndexRsp();
            happyCodeIndexRsp.setLogin(true);
            happyCodeIndexRsp.setExchangeNotice(false);
            happyCodeIndexRsp.setPastInfo(Lists.newArrayList());
            happyCodeIndexRsp.setPhase(null);
            HAPPY_CODE_INDEX_RSP.put(key, happyCodeIndexRsp);
            return happyCodeIndexRsp;
        }
        Long phaseId = basicConfig.getPhaseId();
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (null == phase) {
            throw new BusinessException("期次不存在");
        }
        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        DubboResult<ItemDto> itemResult = remoteDuibaItemGoodsService.find(phase.getCodeItemId());
        if (!itemResult.isSuccess() ||itemResult.getResult() == null) {
            throw new BusinessException("开心码商品不存在");
        }
        List<HappyCodePastResultVO> happyCodePastResultVOS = getHappyCodePastResultVOS(id, phase, consumerId);

        boolean exchangeNotice = isExchangeNotice(consumerId, option, happyCodePastResultVOS, id);

        //根据pastResult结果筛选用户未领奖奖品
        HappyCodeWinOptionRsp winOption = new HappyCodeWinOptionRsp();
        setConsumerWinOption(consumerId, happyCodePastResultVOS, exchangeNotice, winOption);

        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(phaseId, consumerId);
        //过滤失效的开心码  删除状态表示失效
        orders.removeIf(o -> Objects.equals(o.getDeleted(), 1));
        ItemDto item = itemResult.getResult();
        //设置期次基本信息
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = getHappyCodeLoginPhaseInfoVO(phase, option, orders, item);

        //获取插件相关信息
        HappyCodeCreditsVO creditsVO = new HappyCodeCreditsVO();
        creditsVO = getCreditsVO(pluginId, happyCodePhaseInfoVO, creditsVO);

        //计算中奖概率
        if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            //到量模式  用户有效投注码数量/本期发放总数
            happyCodePhaseInfoVO.setWinRate(getWinRate(orders.size(), phase.getCodeCount()));
        } else {
            //到时模式  用户有效投注码数量/本期已发放数
            happyCodePhaseInfoVO.setWinRate(getWinRate(orders.size(), item.getSales()));
        }
        if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_TIME)) {
            happyCodePhaseInfoVO.setNextPrizeDate(phase.getNextPrizeDate());
            happyCodePhaseInfoVO.setPhaseNumber(String.valueOf(DateUtils.getDayNumber(phase.getNextPrizeDate())));
        } else {
            happyCodePhaseInfoVO.setPhaseNumber(phase.getPhaseNumber());
        }
        HappyCodePhaseDto nextPhase = remoteHappyCodePhaseService.findByPhaseId(basicConfig.getNextPhaseId());
        HappyCodeOptionDto nextOption = remoteHappyCodeOptionService.findOneByPhaseId(basicConfig.getNextPhaseId());
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        HappyCodePhaseInfoVO nextPhaseInfoVO = getHappyCodePhaseInfoVO(nextPhase, nextOption);

        happyCodeIndexRsp = new HappyCodeIndexRsp();
        happyCodeIndexRsp.setLogin(true);
        happyCodeIndexRsp.setExchangeNotice(exchangeNotice);
        happyCodeIndexRsp.setPastInfo(happyCodePastResultVOS);
        happyCodeIndexRsp.setNextPhase(nextPhaseInfoVO);
        happyCodeIndexRsp.setCredits(creditsVO);
        happyCodeIndexRsp.setWinOption(winOption);
        //返回相关分享参数
        if (needShare) {
            getHappyCodeShareInfo(consumerId, key, happyCodeIndexRsp, basicConfig, phaseId);
        }

        //期次不是未开启，才返回当前期次信息
        if (!Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_WAIT_FOR_START)) {
            happyCodeIndexRsp.setPhase(happyCodePhaseInfoVO);
        }
        HAPPY_CODE_INDEX_RSP.put(key, happyCodeIndexRsp);
        return happyCodeIndexRsp;
    }

    private void getHappyCodeShareInfo(Long consumerId, String key, HappyCodeIndexRsp happyCodeIndexRsp, HappyCodeBasicDto basicConfig, Long phaseId) {
        happyCodeIndexRsp.setShareCode(IdentificationTool.getEncryptCid(consumerId) + "_"
                + Long.toHexString(phaseId) + "_"
                + Long.toHexString(basicConfig.getId()) + "_"
                + Long.toHexString(RequestLocal.getAppId()));
        //获取剩余可助力次数
        Long usedShareCount = stringRedisTemplate.opsForValue().increment(getHappyCodeTodayFreeGiveKey(consumerId, phaseId), 0L);
        if (null != basicConfig.getShareLimitCount()) {
            happyCodeIndexRsp.setRestShareCount(Integer.max(0, basicConfig.getShareLimitCount() - usedShareCount.intValue()));
        } else {
            happyCodeIndexRsp.setRestShareCount(Integer.max(0, DEFAULT_SHARE_LIMIT_COUNT - usedShareCount.intValue()));
        }
        //获取助力得到的抽奖码 - 获得后删除并更新本地缓存
        String winCodeRedisKey = getMyHelpHappyCodeReidsKey(consumerId, phaseId);
        happyCodeIndexRsp.setLastWinCode(stringRedisTemplate.opsForValue().get(winCodeRedisKey));
        stringRedisTemplate.delete(winCodeRedisKey);
        HAPPY_CODE_INDEX_RSP.invalidate(key);
    }

    private HappyCodeCreditsVO getCreditsVO(Long pluginId, HappyCodePhaseInfoVO happyCodePhaseInfoVO, HappyCodeCreditsVO creditsVO) {
        if(Objects.nonNull(pluginId)){
            ActivityPluginDto plugin = remoteActivityPluginBackendService.findById(pluginId).getResult();
            if (null != plugin.getCreditsPrice()) {
                creditsVO = getHappyCodeShowCredit(RequestLocal.getConsumerAppDO(), plugin.getCreditsPrice());
            }
           //确认没用到可以去掉
            happyCodePhaseInfoVO.setCreditsPrice(plugin.getCreditsPrice());
        }
        return creditsVO;
    }

    private void setConsumerWinOption(Long consumerId, List<HappyCodePastResultVO> happyCodePastResultVOS, boolean exchangeNotice, HappyCodeWinOptionRsp winOption) {
        if(exchangeNotice){
            List<HappyCodePastResultVO> consumerWinOptionList = happyCodePastResultVOS.stream()
                    .filter(p->p.getConsumerId().equals(consumerId))
                    .filter(p->HappyCodePastResultVO.WIN_STATUS_WAIT == p.getWinStatus())
                    .sorted((p1, p2) -> (int)(p2.getPhaseId() - p1.getPhaseId()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(consumerWinOptionList)){
                winOption.setImgUrl(consumerWinOptionList.get(0).getImgUrl());
                winOption.setOptionName(consumerWinOptionList.get(0).getOptionName());
                winOption.setOrderId(consumerWinOptionList.get(0).getOrderId());
            }
        }
    }

    /**
     * 根据赛事id查询赛事主页情况
     *
     * @param id
     * @return
     */
    @Override
    public HappyCodePhaseInfoVO getPhaseOption(Long id) {
        HappyCodePhaseInfoVO optionCache = HAPPY_CODE_CURRENT_PHASE_OPTION_CACHE.getIfPresent(id);
        if (null != optionCache) {
            return optionCache;
        }
        HappyCodePhaseInfoVO phaseInfoVO = new HappyCodePhaseInfoVO();
        HappyCodeBasicDto basicConfig = remoteHappyCodeBasicService.findById(id);
        if (Objects.equals(basicConfig.getDeleted(), 1)) {
            return phaseInfoVO;
        }
        Long phaseId = basicConfig.getPhaseId();
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (null == phase) {
            throw new BusinessException("期次不存在");
        }
        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        //设置期次基本信息
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = getHappyCodePhaseInfoVO(phase, option);
        HAPPY_CODE_CURRENT_PHASE_OPTION_CACHE.put(id, happyCodePhaseInfoVO);

        return happyCodePhaseInfoVO;
    }

    @Override
    public Long findFirstWaitOrderBasicId(Long consumerId, List<Long> basicIds) {
        try{
            List<HappyCodeOrderDto> waitOrderList = remoteHappyCodeOrderService.findWaitOrderByBasicIdsAndConsumerId(consumerId, basicIds);
            if (CollectionUtils.isNotEmpty(waitOrderList)){
                Long phaseId = waitOrderList.get(0).getPhaseId();
                //获取期次相关信息（带缓存）
                HappyCodePhaseDto phase = getHappyCodePhaseCache(phaseId);
                return phase.getBasicId();
            }
        } catch (Exception e){
            LOGGER.warn("查询待领奖订单失败，consumerId:{}",consumerId, e);
        }
        return null;
    }


    /**
     * 赛事首页信息含插件 - 不包括往期信息
     * @param id
     * @param consumerId
     * @param pluginId
     * @return
     */
    @Override
    public HappyCodeWithPluginRsp getIndexWithPlugin(Long id, Long consumerId,Long pluginId) {
        String key = id + "_" + consumerId + "_" + pluginId;
        HappyCodeWithPluginRsp happyCodeWithPluginRsp = HAPPY_CODE_INDEX_WITH_PLUGIN_RSP.getIfPresent(key);
        if (null != happyCodeWithPluginRsp) {
            //更新开心码库存
            updateItemCountInfo(happyCodeWithPluginRsp);
            return happyCodeWithPluginRsp;
        }
        happyCodeWithPluginRsp = new HappyCodeWithPluginRsp();
        HappyCodeBasicDto basicConfig = remoteHappyCodeBasicService.findById(id);
        if (null == basicConfig) {
            throw new BusinessException("赛事不存在");
        }
        if (Objects.equals(basicConfig.getDeleted(), 1)) {
            //赛事已删除，返回空响应
            happyCodeWithPluginRsp.setLogin(true);
            happyCodeWithPluginRsp.setExchangeNotice(false);
            HAPPY_CODE_INDEX_WITH_PLUGIN_RSP.put(key, happyCodeWithPluginRsp);
            return happyCodeWithPluginRsp;
        }
        Long phaseId = basicConfig.getPhaseId();
        //获取期次相关信息（带缓存）
        HappyCodePhaseDto phase = getHappyCodePhaseCache(phaseId);

        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        //获取库存相关信息（带缓存）
        ItemDto item = getItemDtoCache(phase.getCodeItemId());

        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(phaseId, consumerId);
        //过滤失效的开心码  删除状态表示失效
        orders.removeIf(o -> Objects.equals(o.getDeleted(), 1));

        //设置期次基本信息
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = getHappyCodeLoginPhaseInfoVO(phase, option, orders, item);

        if(Objects.nonNull(pluginId)){
            ActivityPluginDto plugin = remoteActivityPluginBackendService.findById(pluginId).getResult();
            happyCodePhaseInfoVO.setCreditsPrice(plugin.getCreditsPrice());
        }

        //更新用户获奖状态及获奖码
        updateWinStatusAndWinCode(id, consumerId, phaseId, option, happyCodePhaseInfoVO);
        //是否有未领奖品
        happyCodeWithPluginRsp.setExchangeNotice(isExchangeNotice(consumerId, id));
        //用户中奖概率信息
        updateUserGiftStatus(phase, orders, item, happyCodePhaseInfoVO);

        happyCodeWithPluginRsp.setLogin(true);
        //期次不是未开启，才返回当前期次信息
        if (!Objects.equals(phase.getPhaseStatus(), HappyCodePhaseDto.STATUS_WAIT_FOR_START)) {
            happyCodeWithPluginRsp.setPhase(happyCodePhaseInfoVO);
        }
        HAPPY_CODE_INDEX_WITH_PLUGIN_RSP.put(key, happyCodeWithPluginRsp);
        return happyCodeWithPluginRsp;
    }

    private void updateItemCountInfo(HappyCodeWithPluginRsp happyCodeWithPluginRsp) {
        HappyCodePhaseDto phase = getHappyCodePhaseCache(happyCodeWithPluginRsp.getPhase().getPhaseId());
        ItemDto item = getItemDtoCache(phase.getCodeItemId());
        if(null != happyCodeWithPluginRsp.getPhase()) {
            happyCodeWithPluginRsp.getPhase().setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(phase.getPhaseStatus()));
            happyCodeWithPluginRsp.getPhase().setRestCount(item.getRemaining());
            happyCodeWithPluginRsp.getPhase().setSendedCount(item.getSales());
        }
    }

    private HappyCodePhaseDto getHappyCodePhaseCache(Long phaseId) {
        HappyCodePhaseDto phase = HAPPY_CODE_PHASE_CACHE.getIfPresent(phaseId);
        if (phase == null) {
            phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
            if (null == phase) {
                throw new BusinessException("期次不存在");
            }
            HAPPY_CODE_PHASE_CACHE.put(phaseId, phase);
        }
        return phase;
    }

    private ItemDto getItemDtoCache(Long codeItemId) {
        ItemDto item = HAPPY_CODE_ITEM_CACHE.getIfPresent(codeItemId);
        if (item == null) {
            try{
                item = remoteDuibaItemGoodsBackendService.findWithoutCache(codeItemId);
                if (item == null) {
                    throw new BusinessException("开心码商品不存在");
                }
            }catch (BizException e){
                throw new BusinessException("开心码商品不存在");
            }
            HAPPY_CODE_ITEM_CACHE.put(codeItemId, item);
        }
        return item;
    }

    /**
     * 更新用户获奖状态及获奖码
     * @param id
     * @param consumerId
     * @param phaseId
     * @param option
     * @param happyCodePhaseInfoVO
     */
    private void updateWinStatusAndWinCode(Long id, Long consumerId, Long phaseId, HappyCodeOptionDto option, HappyCodePhaseInfoVO happyCodePhaseInfoVO) {
        HappyCodeWinRecordDto winRecordDto = remoteHappyCodeWinRecordService.findByBasicIdAndPhaseId(id,phaseId);
        if(winRecordDto != null) {
            happyCodePhaseInfoVO.setWinCode(winRecordDto.getWinCode());
            if (Objects.equals(winRecordDto.getConsumerId(), consumerId)) {
                if (Objects.equals(option.getPrizeType(), ItemDto.TypeVirtual)) {
                    //虚拟商品直接领奖
                    happyCodePhaseInfoVO.setWinStatus(HappyCodePhaseInfoVO.WIN_STATUS_DONE);
                } else {
                    HappyCodeOrderDto winOrder = remoteHappyCodeOrderService.findOrderById(winRecordDto.getOrderId());
                    if (winOrder != null) {
                        happyCodePhaseInfoVO.setWinStatus(winOrder.getExchangeStatus());
                    }
                }
            }
        }
    }

    /**
     * 用户中奖概率信息
     *
     * @param phase
     * @param orders
     * @param item
     * @param happyCodePhaseInfoVO
     */
    private void updateUserGiftStatus(HappyCodePhaseDto phase, List<HappyCodeOrderDto> orders, ItemDto item, HappyCodePhaseInfoVO happyCodePhaseInfoVO) {

        //计算中奖概率
        if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            //到量模式  用户有效投注码数量/本期发放总数
            happyCodePhaseInfoVO.setWinRate(getWinRate(orders.size(), phase.getCodeCount()));
        } else {
            //到时模式  用户有效投注码数量/本期已发放数
            happyCodePhaseInfoVO.setWinRate(getWinRate(orders.size(), item.getSales()));
        }
        if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_TIME)) {
            happyCodePhaseInfoVO.setNextPrizeDate(phase.getNextPrizeDate());
            happyCodePhaseInfoVO.setPhaseNumber(String.valueOf(DateUtils.getDayNumber(phase.getNextPrizeDate())));
        } else {
            happyCodePhaseInfoVO.setPhaseNumber(phase.getPhaseNumber());
        }

    }

    /**
     * 是否领奖通知
     * @param consumerId
     * @param id
     * @return
     */
    private boolean isExchangeNotice(Long consumerId, Long id) {
        List<HappyCodeWinRecordDto> totalWinRecordDto = remoteHappyCodeWinRecordService.findByBasicId(id);

        List<Long> orderIds = totalWinRecordDto.stream()
                .filter(p -> Objects.equals(consumerId, p.getConsumerId()))
                .map(p ->  p.getOrderId())
                .collect(Collectors.toList());

        boolean exchangeNotice = false;
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<HappyCodeOrderDto> winOrders = remoteHappyCodeOrderService.findOrderByIds(consumerId, orderIds);
            for (HappyCodeOrderDto o : winOrders) {
                if (Objects.equals(o.getExchangeStatus(), HappyCodeOrderDto.EXCHANGE_STATUS_WAIT)) {
                    exchangeNotice = true;
                }
            }
        }
        return exchangeNotice;
    }


    /**
     * 赛事首页信息含插件 - 不包括往期信息 - 用户未登陆
     *
     * @param id
     * @return
     */
    @Override
    public HappyCodeWithPluginRsp getIndexWithPluginForNotLoginUser(Long id, Long pluginId) {
        String key = id + "_" + pluginId;
        HappyCodeWithPluginRsp happyCodeNotLoginWithPluginRsp = HAPPY_CODE_NOT_LOGIN_INDEX_WITH_PLUGIN_RSP.getIfPresent(key);
        if (happyCodeNotLoginWithPluginRsp != null) {
            //更新开心码库存
            updateItemCountInfo(happyCodeNotLoginWithPluginRsp);
            return happyCodeNotLoginWithPluginRsp;
        }
        HappyCodeBasicDto basicConfig = remoteHappyCodeBasicService.findById(id);
        if (null == basicConfig) {
            throw new BusinessException("赛事不存在");
        }
        Long phaseId = basicConfig.getPhaseId();
        //获取期次信息-带缓存
        HappyCodePhaseDto phase = getHappyCodePhaseCache(phaseId);

        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        //获取库存信息-带缓存
        ItemDto item = getItemDtoCache(phase.getCodeItemId());

        //设置期次信息
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = getHappyCodePhaseInfoVO(phase, option, item);

        //获取本期中奖码
        HappyCodeWinRecordDto winRecordDto = remoteHappyCodeWinRecordService.findByBasicIdAndPhaseId(id,phaseId);
        if (winRecordDto != null) {
            happyCodePhaseInfoVO.setWinCode(winRecordDto.getWinCode());
        }

        //设置插件信息
        if (Objects.nonNull(pluginId)) {
            ActivityPluginDto plugin = remoteActivityPluginBackendService.findById(pluginId).getResult();
            if (null == plugin) {
                throw new BusinessException("插件不存在");
            }
            happyCodePhaseInfoVO.setCreditsPrice(plugin.getCreditsPrice());
        }

        happyCodeNotLoginWithPluginRsp = new HappyCodeWithPluginRsp();
        happyCodeNotLoginWithPluginRsp.setLogin(false);
        happyCodeNotLoginWithPluginRsp.setExchangeNotice(false);
        happyCodeNotLoginWithPluginRsp.setPhase(happyCodePhaseInfoVO);
        HAPPY_CODE_NOT_LOGIN_INDEX_WITH_PLUGIN_RSP.put(key, happyCodeNotLoginWithPluginRsp);
        return happyCodeNotLoginWithPluginRsp;
    }

    private HappyCodePhaseInfoVO getHappyCodePhaseInfoVO(HappyCodePhaseDto phase, HappyCodeOptionDto option, ItemDto item) {
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = new HappyCodePhaseInfoVO();
        happyCodePhaseInfoVO.setImgUrl(option.getLogo());
        happyCodePhaseInfoVO.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(phase.getPhaseStatus()));
        happyCodePhaseInfoVO.setCodeItemName(phase.getCodeItemName());
        happyCodePhaseInfoVO.setOptionName(option.getPrizeName());
        happyCodePhaseInfoVO.setOptionId(option.getId());
        happyCodePhaseInfoVO.setPrizeCount(option.getRemaining());
        happyCodePhaseInfoVO.setRestCount(item.getRemaining());
        happyCodePhaseInfoVO.setSendedCount(item.getSales());
        happyCodePhaseInfoVO.setWinRate(getWinRate(0, phase.getCodeCount()));
        happyCodePhaseInfoVO.setTotalCount(phase.getCodeCount());
        happyCodePhaseInfoVO.setPhaseNumber(phase.getPhaseNumber());
        happyCodePhaseInfoVO.setPhaseId(phase.getId());
        happyCodePhaseInfoVO.setStartDate(phase.getStartDate());
        happyCodePhaseInfoVO.setEndDate(phase.getEndDate());
        happyCodePhaseInfoVO.setNextPrizeDate(phase.getNextPrizeDate());
        happyCodePhaseInfoVO.setItemId(item.getId());
        happyCodePhaseInfoVO.setPrizeType(option.getPrizeType());
        return happyCodePhaseInfoVO;
    }

    private HappyCodePhaseInfoVO getHappyCodeLoginPhaseInfoVO(HappyCodePhaseDto phase, HappyCodeOptionDto option, List<HappyCodeOrderDto> orders, ItemDto item) {
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = new HappyCodePhaseInfoVO();
        happyCodePhaseInfoVO.setMyCodes(getMyCode(orders, 3));
        happyCodePhaseInfoVO.setImgUrl(option.getLogo());
        happyCodePhaseInfoVO.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(phase.getPhaseStatus()));
        happyCodePhaseInfoVO.setCodeItemName(phase.getCodeItemName());
        happyCodePhaseInfoVO.setOptionId(option.getId());
        happyCodePhaseInfoVO.setOptionName(option.getPrizeName());
        happyCodePhaseInfoVO.setPrizeCount(option.getRemaining());
        happyCodePhaseInfoVO.setRestCount(item.getRemaining());
        happyCodePhaseInfoVO.setSendedCount(item.getSales());
        happyCodePhaseInfoVO.setDescription(option.getDescription());
        happyCodePhaseInfoVO.setTotalCount(phase.getCodeCount());
        happyCodePhaseInfoVO.setPhaseId(phase.getId());
        happyCodePhaseInfoVO.setStartDate(phase.getStartDate());
        happyCodePhaseInfoVO.setEndDate(phase.getEndDate());
        happyCodePhaseInfoVO.setItemId(option.getItemId());
        happyCodePhaseInfoVO.setPrizeType(option.getPrizeType());
        //如果当期已开奖，存入开奖码
        if (HappyCodePhaseDto.STATUS_END == phase.getPhaseStatus()){
            HappyCodeWinRecordDto happyCodeWinRecordDto = remoteHappyCodeWinRecordService.findByBasicIdAndPhaseId(phase.getBasicId(),phase.getId());
            if (null != happyCodeWinRecordDto){
                happyCodePhaseInfoVO.setWinCode(happyCodeWinRecordDto.getWinCode());
            }
        }
        return happyCodePhaseInfoVO;
    }

    private HappyCodePhaseInfoVO getHappyCodePhaseInfoVO(HappyCodePhaseDto phase, HappyCodeOptionDto option) {
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = new HappyCodePhaseInfoVO();
        happyCodePhaseInfoVO.setImgUrl(option.getLogo());
        happyCodePhaseInfoVO.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(phase.getPhaseStatus()));
        happyCodePhaseInfoVO.setCodeItemName(phase.getCodeItemName());
        happyCodePhaseInfoVO.setOptionId(option.getId());
        happyCodePhaseInfoVO.setOptionName(option.getPrizeName());
        happyCodePhaseInfoVO.setPrizeCount(option.getRemaining());
        happyCodePhaseInfoVO.setDescription(option.getDescription());
        happyCodePhaseInfoVO.setTotalCount(phase.getCodeCount());
        happyCodePhaseInfoVO.setPhaseId(phase.getId());
        happyCodePhaseInfoVO.setStartDate(phase.getStartDate());
        happyCodePhaseInfoVO.setEndDate(phase.getEndDate());
        happyCodePhaseInfoVO.setNextPrizeDate(phase.getNextPrizeDate());
        happyCodePhaseInfoVO.setItemId(option.getItemId());
        happyCodePhaseInfoVO.setPrizeType(option.getPrizeType());

        return happyCodePhaseInfoVO;
    }

    /**
     * 获取往期信息，用于首页展示
     * 到量模式会剔除正在进行的一期
     * @param id
     * @return
     */
    private List<HappyCodePastResultVO> getHappyCodePastResultVOS(Long id, HappyCodePhaseDto phase, Long consuemrId) {
        List<HappyCodePastResultVO> pastResult = getPastResult(id,consuemrId).getPastResult();
        if (CollectionUtils.isEmpty(pastResult)) {
            return Lists.newArrayList();
        }
        List<HappyCodePastResultVO> temp = BeanUtils.copyList(pastResult, HappyCodePastResultVO.class);
        List<Long> phaseIds = Lists.transform(temp, HappyCodePastResultVO::getPhaseId);
        if (Objects.equals(phase.getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER) && phaseIds.contains(phase.getId())) {
            temp.remove(0);
        }
        return temp;
    }

    /**
     * 返回标识,是否需要通知用户有未领取的奖品
     * @param consumerId
     * @param option
     * @param happyCodePastResultVOS
     * @return
     */
    private boolean isExchangeNotice(Long consumerId, HappyCodeOptionDto option, List<HappyCodePastResultVO> happyCodePastResultVOS, Long basicId) {
        boolean exchangeNotice;
        if (Objects.equals(option.getPrizeType(), ItemDto.TypeVirtual)) {
            exchangeNotice = isExchangeNoticeForVirtual(consumerId, basicId);
            happyCodePastResultVOS.forEach(p -> {
                if (Objects.equals(consumerId, p.getConsumerId())) {
                    //虚拟商品自动领奖,所以设置为已领奖状态
                    p.setWinStatus(HappyCodeOrderDto.EXCHANGE_STATUS_DONE);
                }
            });
        } else {
            Map<Long, HappyCodePastResultVO> pastResult = Maps.newHashMap();
            List<Long> orderIds = happyCodePastResultVOS.stream()
                    .filter(p -> Objects.equals(consumerId, p.getConsumerId()))
                    .map(p -> {
                        Long oid = p.getOrderId();
                        pastResult.put(oid, p);
                        return oid;
                    })
                    .collect(Collectors.toList());
            exchangeNotice = isExchangeNotice(consumerId, pastResult, orderIds);
        }
        return exchangeNotice;
    }

    /**
     * 判断虚拟商品是否需要提示用户领奖信息
     * @param consumerId
     * @return
     */
    private boolean isExchangeNoticeForVirtual(Long consumerId, Long basicId) {
        String redisKey = getExchangeNoticeForVirtualRedisKey(consumerId, basicId);
        Long notice = redisAtomicClient.getLong(redisKey);
        if (notice == null || notice <= 0) {
            return false;
        }
        redisAtomicClient.incrBy(redisKey, -1, 7, TimeUnit.DAYS);
        return true;
    }

    /**
     * 生成用于提醒虚拟商品中奖纪录的redisKey
     * @param consumerId
     * @param basicId
     * @return
     */
    private String getExchangeNoticeForVirtualRedisKey(Long consumerId, Long basicId) {
        return RedisKeyFactory.K069.toString() + basicId + "_" + consumerId;
    }


    /**
     * 生成用于提醒虚拟商品中奖纪录的redisKey
     * @param orderId
     * @return
     */
    private String getCouponIdRedisKey(Long orderId) {
        return RedisKeyFactory.K085.toString() + orderId;
    }

    /**
     * 是否需要提醒有未领取奖品
     * @param consumerId
     * @param pastResult
     * @param orderIds
     * @return
     */
    private boolean isExchangeNotice(Long consumerId, Map<Long, HappyCodePastResultVO> pastResult, List<Long> orderIds) {
        boolean exchangeNotice = false;
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByIds(consumerId, orderIds);
            for (HappyCodeOrderDto o : orders) {
                if (Objects.equals(o.getExchangeStatus(), HappyCodeOrderDto.EXCHANGE_STATUS_WAIT)) {
                    exchangeNotice = true;
                }
                HappyCodePastResultVO happyCodePastResultVO = pastResult.get(o.getId());
                if (null != happyCodePastResultVO) {
                    happyCodePastResultVO.setWinStatus(o.getExchangeStatus());
                }
            }
        }
        return exchangeNotice;
    }

    @Override
    public HappyCodeIndexRsp getIndexForNotLoginUser(Long id, Long pluginId) {
        HappyCodeIndexRsp happyCodeIndexRsp = HAPPY_CODE_NOT_LOGIN_INDEX_RSP.getIfPresent(id);
        if (happyCodeIndexRsp != null) {
            return happyCodeIndexRsp;
        }
        HappyCodeBasicDto basicConfig = remoteHappyCodeBasicService.findById(id);
        if (null == basicConfig) {
            throw new BusinessException("赛事不存在");
        }
        Long phaseId = basicConfig.getPhaseId();
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (null == phase) {
            throw new BusinessException("期次不存在");
        }
        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        DubboResult<ItemDto> itemResult = remoteDuibaItemGoodsService.find(phase.getCodeItemId());
        if (!itemResult.isSuccess() ||itemResult.getResult() == null) {
            throw new BusinessException("开心码商品不存在");
        }
        ItemDto item = itemResult.getResult();
        List<HappyCodePastResultVO> happyCodePastResultVOS = getHappyCodePastResultVOS(id, phase, null);
        //设置期次信息
        HappyCodePhaseInfoVO happyCodePhaseInfoVO = getHappyCodePhaseInfoVO(phase, option, item);

        HappyCodeCreditsVO creditsVO = new HappyCodeCreditsVO();
        if(Objects.nonNull(pluginId)){
            ActivityPluginDto plugin = remoteActivityPluginBackendService.findById(pluginId).getResult();
            if (null != plugin.getCreditsPrice()) {
                creditsVO = getHappyCodeShowCredit(RequestLocal.getConsumerAppDO(), plugin.getCreditsPrice());
            }
        }
        HappyCodePhaseDto nextPhase = remoteHappyCodePhaseService.findByPhaseId(basicConfig.getNextPhaseId());
        HappyCodeOptionDto nextOption = remoteHappyCodeOptionService.findOneByPhaseId(basicConfig.getNextPhaseId());
        if (null == option) {
            throw new BusinessException("奖项不存在");
        }
        HappyCodePhaseInfoVO nextPhaseInfoVO = getHappyCodePhaseInfoVO(nextPhase, nextOption);

        happyCodeIndexRsp = new HappyCodeIndexRsp();
        happyCodeIndexRsp.setLogin(false);
        happyCodeIndexRsp.setExchangeNotice(false);
        happyCodeIndexRsp.setPastInfo(happyCodePastResultVOS);
        happyCodeIndexRsp.setPhase(happyCodePhaseInfoVO);
        happyCodeIndexRsp.setCredits(creditsVO);
        happyCodeIndexRsp.setNextPhase(nextPhaseInfoVO);
        HAPPY_CODE_NOT_LOGIN_INDEX_RSP.put(id, happyCodeIndexRsp);
        return happyCodeIndexRsp;
    }

    /**
     * 计算当前中奖概率 保留六位小数
     * @param size
     * @param codeCount
     * @return
     */
    private String getWinRate(int size, int codeCount) {
        if (codeCount == 0) {
            //正常业务不存在codeCount=0的场景，程序保护性校验
            return "0.000000";
        }
        BigDecimal randomNumber = BigDecimal.valueOf(((double)size) / codeCount).setScale(6, BigDecimal.ROUND_HALF_UP);
        return randomNumber.toString();
    }

    /**
     * 获取为的开心码，用于首页展示
     * @param orders
     * @param count
     * @return
     */
    private List<String> getMyCode(List<HappyCodeOrderDto> orders, Integer count) {
        if (CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }
        Integer size = orders.size();
        if (size <= count) {
            return Lists.transform(orders, HappyCodeOrderDto::getHappyCode);
        }
        List<String> codes = Lists.newArrayList();
        for (int i = size - 1; i >= size - count; i--) {
            codes.add(orders.get(i).getHappyCode());
        }
        return codes;
    }

    /**
     * 根据赛事id获取我的各期开心码获取情况
     *
     * @param id
     * @param consumerId
     * @return
     */
    @Override
    public HappyCodeMyCodeRsp getMyCode(Long id, Long consumerId, Boolean needCache) {
        String key = id + "_" + consumerId;
        HappyCodeMyCodeRsp happyCodeMyCodeRsp;
        if(needCache){
            happyCodeMyCodeRsp = HAPPY_CODE_MY_CODE_RSP.getIfPresent(key);
            if (null != happyCodeMyCodeRsp) {
                return happyCodeMyCodeRsp;
            }
        }
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(id);
        if (phase == null) {
            throw new BusinessException("期次不存在");
        }
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(id, consumerId);

        List<HappyCodeMyCodeVO> disabledCodes = Lists.newArrayList();
        List<HappyCodeMyCodeVO> enabledCodes = Lists.newArrayList();

        orders.forEach(o -> {
            HappyCodeMyCodeVO myCode = new HappyCodeMyCodeVO();
            myCode.setCode(o.getHappyCode());
            myCode.setDate(DateUtils.getMinuteStr(o.getGmtCreate()));
            myCode.setOrigin(HAPPY_CODE_ORIGIN_DESC.get(o.getOrigin()));
            if (Objects.equals(o.getDeleted(), 1)) {
                //失效抽奖码
                disabledCodes.add(myCode);
            } else {
                //有效抽奖码
                enabledCodes.add(myCode);
            }
        });
        happyCodeMyCodeRsp = new HappyCodeMyCodeRsp();
        happyCodeMyCodeRsp.setPhaseId(id);
        happyCodeMyCodeRsp.setPhaseNumber(phase.getPhaseNumber());
        happyCodeMyCodeRsp.setCodes(enabledCodes);
        happyCodeMyCodeRsp.setDisabledCodes(disabledCodes);
        HAPPY_CODE_MY_CODE_RSP.put(key, happyCodeMyCodeRsp);
        return happyCodeMyCodeRsp;
    }

    /**
     * 根据赛事id获取往期获奖情况
     *
     * @param id
     * @return
     */
    /**
     *
     * @param id
     * @param consumerId(到时模式待开奖状态需要)
     * @return
     */
    @Override
    public HappyCodePastResultRsp getPastResult(Long id, Long consumerId) {
        HappyCodePastResultRsp pastResultRsp = HAPPY_CODE_PAST_RESULT_RSP.getIfPresent(id);
        if (pastResultRsp != null) {
            return pastResultRsp;
        }
        HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(id);
        if (basic == null || basic.getDeleted() == 1) {
            pastResultRsp = new HappyCodePastResultRsp();
            HAPPY_CODE_PAST_RESULT_RSP.put(id, pastResultRsp);
            return pastResultRsp;
        }
        List<HappyCodePhaseDto> phases = remoteHappyCodePhaseService.findByBasicIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(phases)) {
            pastResultRsp = new HappyCodePastResultRsp();
            HAPPY_CODE_PAST_RESULT_RSP.put(id, pastResultRsp);
            return pastResultRsp;
        }
        List<HappyCodePastResultVO> pastResults;
        List<HappyCodeWinRecordDto> winRecord = remoteHappyCodeWinRecordService.findByBasicId(basic.getId());

        if (CollectionUtils.isEmpty(winRecord) && Objects.equals(phases.get(0).getLotteryType(), HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            pastResultRsp = new HappyCodePastResultRsp();
            HAPPY_CODE_PAST_RESULT_RSP.put(id, pastResultRsp);
            return pastResultRsp;
        }
        Map<Long,List<HappyCodeWinRecordDto>> winRecordListMap = winRecord.stream()
                .collect(Collectors.groupingBy(HappyCodeWinRecordDto::getPhaseId));

        //获取用户头像及昵称
        List<Long> consumerIds = Lists.transform(winRecord, HappyCodeWinRecordDto::getConsumerId);
        List<ConsumerExtraDto> consumerExtraList = getConsumerExtraList(consumerIds);
        Map<Long, ConsumerExtraDto> consumerExtraMap = Maps.uniqueIndex(consumerExtraList, ConsumerExtraDto::getConsumerId);

        //开奖方式
        Map<Long, HappyCodePhaseDto> phaseDtoMap = Maps.uniqueIndex(phases, HappyCodePhaseDto::getId);
        Integer lotteryType = phases.get(0).getLotteryType();
        if (Objects.equals(lotteryType, HappyCodePhaseDto.LOTTERY_TYPE_TIME)) {
            //到时模式只有进行中状态
            phases = phases.stream()
                    .filter(p -> Objects.equals(p.getPhaseStatus(), HappyCodePhaseDto.STATUS_OPENING))
                    .sorted((p1, p2) -> p1.getGmtCreate().before(p2.getGmtCreate()) ? 1 : -1)
                    .collect(Collectors.toList());
            pastResults = new ArrayList<>();
            //到时模式
            getTimePastPhaseWinRecord(phases, pastResults, winRecordListMap, consumerExtraMap, phaseDtoMap, basic, consumerId);

        } else if (Objects.equals(lotteryType, HappyCodePhaseDto.LOTTERY_TYPE_NUMBER)) {
            Map<Long, HappyCodeWinRecordDto> winRecordMap = Maps.uniqueIndex(winRecord, HappyCodeWinRecordDto::getPhaseId);
            pastResults = phases.stream()
                    .filter(p -> Objects.equals(p.getPhaseStatus(), HappyCodePhaseDto.STATUS_END))
                    .filter(p -> null != winRecordMap.get(p.getId()))
                    .sorted((p1,p2) -> p2.getPayload() - p1.getPayload())
                    .map(p -> {
                        HappyCodeWinRecordDto winRecordDto = winRecordMap.get(p.getId());
                        HappyCodePastResultVO pastResult = new HappyCodePastResultVO();

                        pastResult.setImgUrl(winRecordDto.getOptionLogo());
                        pastResult.setOptionName(winRecordDto.getOptionName());
                        pastResult.setConsumerId(winRecordDto.getConsumerId());
                        getConsumerExtraInfo(consumerExtraMap,winRecordDto.getConsumerId(),pastResult);
                        pastResult.setWinCode(winRecordDto.getWinCode());
                        pastResult.setPhaseNumber(p.getPhaseNumber());
                        pastResult.setOrderId(winRecordDto.getOrderId());
                        pastResult.setPhaseId(p.getId());
                        pastResult.setPrizeCount(1);
                        pastResult.setDevelopUserId(winRecordDto.getPartnerUserId());
                        pastResult.setStartDate(p.getStartDate());
                        pastResult.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(p.getPhaseStatus()));
                        pastResult.setCodeItemName(p.getCodeItemName());
                        pastResult.setItemId(p.getCodeItemId());
                        pastResult.setPrizeType(winRecordDto.getOptionType());
                        return pastResult;
                    })
                    .collect(Collectors.toList());
        } else {
            pastResults = Lists.newArrayList();
        }
        pastResultRsp = new HappyCodePastResultRsp();
        pastResultRsp.setPastResult(pastResults);
        HAPPY_CODE_PAST_RESULT_RSP.put(id, pastResultRsp);
        return pastResultRsp;
    }

    /**
     * 构建到时开奖模式pastResult
     * @param phases
     * @param pastResults
     * @param winRecordListMap
     * @param consumerExtraMap
     * @param phaseDtoMap
     * @param basic
     * @param consumerId
     */
    private void getTimePastPhaseWinRecord(List<HappyCodePhaseDto> phases, List<HappyCodePastResultVO> pastResults, Map<Long, List<HappyCodeWinRecordDto>> winRecordListMap,
                                           Map<Long, ConsumerExtraDto> consumerExtraMap,
                                           Map<Long, HappyCodePhaseDto> phaseDtoMap,
                                           HappyCodeBasicDto basic,
                                           Long consumerId) {
        phases.forEach(phase -> {
            List<HappyCodeWinRecordDto> thisPhaseWinRecord = winRecordListMap.get(phase.getId());
            if (CollectionUtils.isEmpty(thisPhaseWinRecord)) {
                if (!phase.getId().equals(basic.getPhaseId()) && !phase.getId().equals(basic.getNextPhaseId())) {
                    HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phase.getId());
                    if (null == option) {
                        throw new BusinessException("奖项不存在");
                    }
                    HappyCodePastResultVO pastResult = new HappyCodePastResultVO();
                    pastResult.setImgUrl(option.getLogo());
                    pastResult.setOptionName(option.getPrizeName());
                    pastResult.setPhaseNumber(String.valueOf(DateUtils.getDayNumber(phase.getNextPrizeDate())));
                    pastResult.setPhaseId(phase.getId());
                    pastResult.setPrizeCount(1);
                    pastResult.setStartDate(phase.getStartDate());
                    //没有存在中奖记录-待开奖
                    pastResult.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(2));

                    //登陆用户 - 待开奖添加以下字段
                    setReadyGetPrizeConsumerInfo(consumerId, phase, pastResult);
                    pastResult.setItemId(phase.getCodeItemId());
                    pastResult.setPrizeType(option.getPrizeType());
                    pastResult.setCodeItemName(phase.getCodeItemName());
                    pastResult.setNextPrizeDate(phase.getNextPrizeDate());
                    pastResults.add(pastResult);
                }
            } else {
                thisPhaseWinRecord.forEach(p -> {
                    HappyCodePastResultVO pastResult = new HappyCodePastResultVO();
                    pastResult.setImgUrl(p.getOptionLogo());
                    pastResult.setOptionName(p.getOptionName());
                    pastResult.setConsumerId(p.getConsumerId());
                    getConsumerExtraInfo(consumerExtraMap, p.getConsumerId(), pastResult);
                    pastResult.setWinCode(p.getWinCode());
                    pastResult.setPhaseNumber(String.valueOf(DateUtils.getDayNumber(p.getGmtCreate())));
                    pastResult.setOrderId(p.getOrderId());
                    pastResult.setPhaseId(p.getPhaseId());
                    pastResult.setPrizeCount(1);
                    pastResult.setDevelopUserId(p.getPartnerUserId());
                    pastResult.setStartDate(phaseDtoMap.get(p.getPhaseId()).getStartDate());
                    //存在中奖记录-已开奖
                    pastResult.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(3));
                    pastResult.setCodeItemName(phaseDtoMap.get(p.getPhaseId()).getCodeItemName());
                    pastResult.setNextPrizeDate(phaseDtoMap.get(p.getPhaseId()).getNextPrizeDate());
                    pastResults.add(pastResult);
                });
            }
        });
    }

    /**
     * 往期信息 - 待开奖状态下添加字段
     * @param consumerId
     * @param phase
     * @param pastResult
     */
    private void setReadyGetPrizeConsumerInfo(Long consumerId, HappyCodePhaseDto phase, HappyCodePastResultVO pastResult) {
        if (null == consumerId) {
            return;
        }
        ItemDto item = goodsCacheService.findById(phase.getCodeItemId());
        if (item == null) {
            throw new BusinessException("开心码商品不存在");
        }
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(phase.getId(), consumerId);
        //过滤失效的开心码  删除状态表示失效
        orders.removeIf(o -> Objects.equals(o.getDeleted(), 1));
        //设置期次基本信息
        pastResult.setSendedCount(item.getSales());
        pastResult.setMyCodes(getMyCode(orders, 2));
        //到时模式  用户有效投注码数量/本期已发放数
        pastResult.setWinRate(getWinRate(orders.size(), item.getSales()));
    }

    @Override
    public HappyCodeJoinRecordRsp getJoinRecord(Long id) {
        HappyCodeJoinRecordRsp happyCodeJoinRecordRsp = HAPPY_CODE_JOIN_RECORD_RSP.getIfPresent(id);
        if (null != happyCodeJoinRecordRsp) {
            return happyCodeJoinRecordRsp;
        }
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(id);
        if (phase == null) {
            throw new BusinessException("期次不存在");
        }
        Integer count = 20;
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findNewestOrderByPhaseIdAndCount(id, count);
        List<Long> consumerIds = Lists.transform(orders, HappyCodeOrderDto::getConsumerId);
        List<ConsumerExtraDto> consumerExtraList = getConsumerExtraList(consumerIds);
        Map<Long, ConsumerExtraDto> consumerExtraMap = Maps.uniqueIndex(consumerExtraList, ConsumerExtraDto::getConsumerId);
        List<HappyCodeJoinRecordVO> joinRecords = orders.stream()
                .map(o -> {
                    Long consumerId = o.getConsumerId();
                    ConsumerExtraDto consumerExtra = consumerExtraMap.get(consumerId);
                    HappyCodeJoinRecordVO joinRecord = new HappyCodeJoinRecordVO();
                    joinRecord.setDate(dateFormatForJoinRecord.format(o.getGmtCreate()));
                    joinRecord.setDevelopUserId(o.getPartnerUserId());
                    joinRecord.setDuiBaUserId(String.valueOf(consumerId));
                    if (null != consumerExtra) {
                        joinRecord.setHeadImg(consumerExtra.getAvatar());
                        joinRecord.setNickname(consumerExtra.getNickname());
                    }
                    return joinRecord;
                })
                .collect(Collectors.toList());

        happyCodeJoinRecordRsp = new HappyCodeJoinRecordRsp();
        happyCodeJoinRecordRsp.setJoinRecords(joinRecords);
        HAPPY_CODE_JOIN_RECORD_RSP.put(id, happyCodeJoinRecordRsp);
        return happyCodeJoinRecordRsp;
    }

    @Override
    public HappyCodePluginJoinRsp findHappyCodeListByIdsAndPlugin(List<Long> happyCodeId, List<Long> pluginId, Boolean needCache) {
        String key = happyCodeId.toString() + "_" + pluginId.toString();
        if (needCache) {
            HappyCodePluginJoinRsp pluginJoinRsp = HAPPY_CODE_PLUGIN_JOIN_LIST_RSP_CACHE.getIfPresent(key);
            if (pluginJoinRsp != null) {
                return pluginJoinRsp;
            }
        }
        Map<Long, Long> happyCodePluginMap = new HashMap<>();

        for (int i = 0; i < happyCodeId.size(); i++) {
            happyCodePluginMap.put(happyCodeId.get(i), pluginId.get(i));
        }

        List<HappyCodeBasicDto> basicList = remoteHappyCodeBasicService.findByIds(happyCodeId);
        List<ActivityPluginDto> plugins = remoteActivityPluginBackendService.findPluginsByIds(pluginId).getResult();

        List<Long> phaseIds = basicList.stream()
                .filter(p -> Objects.nonNull(p.getPhaseId()))
                .map(p -> p.getPhaseId())
                .collect(Collectors.toList());

        List<HappyCodePhaseDto> phaseList = remoteHappyCodePhaseService.findByPhaseIds(phaseIds);

        if (CollectionUtils.isEmpty(phaseList)) {
            throw new BusinessException("期次不存在");
        }

        List<Long> itemids = phaseList.stream()
                .filter(p -> Objects.nonNull(p.getCodeItemId()))
                .map(p -> p.getCodeItemId())
                .collect(Collectors.toList());

        List<HappyCodeOptionDto> optionList = remoteHappyCodeOptionService.findByPhaseIds(phaseIds);
        DubboResult<List<ItemDto>> itemResult = remoteDuibaItemGoodsService.findByIds(itemids);
        if (!itemResult.isSuccess() || itemResult.getResult() == null) {
            throw new BusinessException("开心码商品不存在");
        }

        Map<Long, ItemDto> itemMap = Maps.uniqueIndex(itemResult.getResult(), ItemDto::getId);
        Map<Long, HappyCodePhaseDto> phaseBasicIdMap = Maps.uniqueIndex(phaseList, HappyCodePhaseDto::getBasicId);
        Map<Long, HappyCodeOptionDto> phaseOptionMap = Maps.uniqueIndex(optionList, HappyCodeOptionDto::getPhaseId);
        Map<Long, ActivityPluginDto> pluginMap = Maps.uniqueIndex(plugins, ActivityPluginDto::getId);

        List<HappyCodePluginJoinVO> list = new ArrayList<>();

        for (HappyCodeBasicDto basic : basicList) {

            HappyCodePluginJoinVO pluginJoinVO = new HappyCodePluginJoinVO();
            HappyCodePhaseDto phaseDto = phaseBasicIdMap.get(basic.getId());
            ActivityPluginDto pluginDto = pluginMap.get(happyCodePluginMap.get(basic.getId()));
            ItemDto item = itemMap.get(phaseDto.getCodeItemId());
            pluginJoinVO.setBasicId(basic.getId());
            HappyCodeOptionDto option = phaseOptionMap.get(phaseDto.getId());

            pluginJoinVO.setImgUrl(option.getLogo());
            pluginJoinVO.setPhaseId(phaseDto.getId());
            pluginJoinVO.setTitle(phaseDto.getTitle());
            pluginJoinVO.setSubTitle(phaseDto.getSubTitle());
            pluginJoinVO.setPhaseStatus(HAPPY_CODE_PHASE_STATUS.get(phaseDto.getPhaseStatus()));
            pluginJoinVO.setCodeItemName(phaseDto.getCodeItemName());
            pluginJoinVO.setOptionId(option.getId());
            pluginJoinVO.setOptionName(option.getPrizeName());
            pluginJoinVO.setPrizeCount(option.getRemaining());
            pluginJoinVO.setRestCount(item.getRemaining());
            pluginJoinVO.setSendedCount(item.getSales());
            pluginJoinVO.setTotalCount(phaseDto.getCodeCount());
            pluginJoinVO.setEndDate(phaseDto.getEndDate());
            pluginJoinVO.setHasPastPhase(phaseDto.getPayload() > 1);

            //设置插件相关参数
            setPluginRelatedInfo(pluginJoinVO, pluginDto);

            list.add(pluginJoinVO);
        }

        //已开奖开心码下沉 放到list的最后
        Map<String, List<HappyCodePluginJoinVO>> phaseStatusListMap = list.stream()
                .collect(Collectors.groupingBy(p -> p.getPhaseStatus()));

        List statusEndList = phaseStatusListMap.get(HAPPY_CODE_PHASE_STATUS.get(HappyCodePhaseDto.STATUS_END));
        if(CollectionUtils.isNotEmpty(statusEndList)) {
            list.removeAll(statusEndList);
            list.addAll(statusEndList);
        }

        HappyCodePluginJoinRsp happyCodePluginJoinRsp = new HappyCodePluginJoinRsp();
        happyCodePluginJoinRsp.setPluginJoinVOs(list);

        HAPPY_CODE_PLUGIN_JOIN_LIST_RSP_CACHE.put(key,happyCodePluginJoinRsp);

        return happyCodePluginJoinRsp;
    }

    private void setPluginRelatedInfo(HappyCodePluginJoinVO pluginJoinVO, ActivityPluginDto pluginDto) {
        if (Objects.nonNull(pluginDto)) {
            pluginJoinVO.setPluginId(pluginDto.getId());
            //插件未配置扣积分
            if(null != pluginDto.getCreditsPrice()){
                pluginJoinVO.setCredits(getHappyCodeShowCredit(RequestLocal.getConsumerAppDO(),pluginDto.getCreditsPrice()));

            }
            //确认没用到可以去掉
            pluginJoinVO.setCreditsPrice(pluginDto.getCreditsPrice());
            pluginJoinVO.setBrickId(pluginDto.getBrickId());

        }
    }

    /**
     * 我的往期开心码
     * 只支持查询到量模式
     * @param id
     * @param consumerId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public HappyCodeMyPastCodeRsp getMyPastCode(Long id, Long consumerId, Integer pageNo, Integer pageSize) throws ExecutionException {
        String key = id + "_" + consumerId + "_" + pageNo + "_" + pageSize;
        return HAPPY_CODE_MY_PAST_CODE_RSP_CACHE.get(key, () -> getMyPastCodeNoCache(id, consumerId, pageNo, pageSize));
    }

    private HappyCodeMyPastCodeRsp getMyPastCodeNoCache(Long id, Long consumerId, Integer pageNo, Integer pageSize) {
        HappyCodePastResultRsp pastResult = getPastResult(id,null);
        List<HappyCodePastResultVO> phases = pastResult.getPastResult();
        if (CollectionUtils.isEmpty(phases)) {
            return HappyCodeMyPastCodeRsp.EMPTY;
        }
        List<Long> phaseIds = Lists.transform(phases, HappyCodePastResultVO::getPhaseId);
        if (CollectionUtils.isEmpty(phaseIds)) {
            return HappyCodeMyPastCodeRsp.EMPTY;
        }
        ImmutableMap<Long, HappyCodePastResultVO> phaseMap = Maps.uniqueIndex(phases, HappyCodePastResultVO::getPhaseId);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNo(pageNo == null ? 0 : pageNo);
        pageQuery.setPageSize(pageSize == null ? 20 : pageSize);
        Page<HappyCodeOrderDto> page = remoteHappyCodeOrderService.findOrderPageByPhaseIdsAndConsumerIdApi(phaseIds, consumerId, pageQuery);
        if (page == null || CollectionUtils.isEmpty(page.getList())) {
            return HappyCodeMyPastCodeRsp.EMPTY;
        }
        List<HappyCodeOrderDto> orders = page.getList();
        ImmutableListMultimap<Long, HappyCodeOrderDto> ordersMap = Multimaps.index(orders, HappyCodeOrderDto::getPhaseId);
        List<HappyCodeMyPastCodeVO> codeList = ordersMap.keySet().stream()
                .sorted((key1, key2) -> (int)(key2 - key1))
                .map(key -> {
                    HappyCodePastResultVO phaseResult = phaseMap.get(key);
                    List<HappyCodeOrderDto> orderUnit = ordersMap.get(key);
                    HappyCodeMyPastCodeVO myPastCode = new HappyCodeMyPastCodeVO();
                    myPastCode.setOptionName(phaseResult.getOptionName());
                    myPastCode.setStatus("未中奖");
                    List<HappyCodeMyCodeVO> codes = orderUnit.stream()
                            .sorted((o1, o2) -> o1.getGmtCreate().after(o2.getGmtCreate()) ? -1 : 1)
                            .map(o -> {
                                HappyCodeMyCodeVO myCode = new HappyCodeMyCodeVO();
                                myCode.setCode(o.getHappyCode());
                                myCode.setDate(DateUtils.getMinuteStr(o.getGmtCreate()));
                                myCode.setOrigin(HAPPY_CODE_ORIGIN_DESC.get(o.getOrigin()));
                                if (Objects.equals(phaseResult.getWinCode(), o.getHappyCode())) {
                                    myCode.setWin(true);
                                    myPastCode.setStatus("中奖啦");
                                }
                                return myCode;
                            })
                            .collect(Collectors.toList());
                    myPastCode.setCodes(codes);
                    return myPastCode;
                })
                .collect(Collectors.toList());
        HappyCodeMyPastCodeRsp result = new HappyCodeMyPastCodeRsp();
        result.setPageSize(page.getPageSize());
        result.setTotalCount(page.getTotalCount());
        result.setPageNo(page.getPageNo());
        result.setTotalPages(page.getTotalPages());
        result.setList(codeList);
        return result;
    }

    /**
     * 获取用户信息拓展表
     * @param consumerIds
     * @return
     */
    private List<ConsumerExtraDto> getConsumerExtraList(List<Long> consumerIds) {
        if(CollectionUtils.isEmpty(consumerIds)){
            return Lists.newArrayList();
        }
        DubboResult<List<ConsumerExtraDto>> result = remoteConsumerExtraService.findAllByConsumerIds(consumerIds);
        if (result == null || !result.isSuccess() || result.getResult() == null) {
            return Lists.newArrayList();
        }
        return result.getResult();
    }

    @Override
    public String getRule(Long id) throws ExecutionException {
        if (id == null) {
            return EMPTY_STRING;
        }
        return HAPPY_CODE_RULE_RSP.get(id, () -> {
            HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(id);
            if (basic == null || basic.getDeleted() == 1) {
                HAPPY_CODE_RULE_RSP.put(id, EMPTY_STRING);
                return EMPTY_STRING;
            }
            HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(basic.getPhaseId());
            if (phase == null || phase.getRule() == null) {
                return EMPTY_STRING;
            }
            return phase.getRule();
        });
    }

    /**
     * 根据开心码订单id获取couponId
     *
     * @param orderId
     * @return
     */
    @Override
    public Long findCouponId(Long orderId) {
        String redisKey = getCouponIdRedisKey(orderId);
        return advancedCacheClient.get(redisKey);
    }

    /**
     * IdentificationTool.getEncryptCid(consumerId) + "_"
     + Long.toHexString(phaseId) + "_"
     + Long.toHexString(basicConfig.getId()) + "_"
     + Long.toHexString(RequestLocal.getAppId());
     * @param shareCode
     * @param consumer
     * @return
     * @throws BizException
     */
    @Override
    public Long addTimesForHappyCode(String shareCode, ConsumerDto consumer) throws BizException {
        //分享码格式check
        String[] shareCodeInfo = shareCode.split("_");
        if (shareCodeInfo.length != 4) {
            throw new BizException("分享码格式错误！");
        }
        Long shareConsumerId = IdentificationTool.getDecryptCid(shareCodeInfo[0]);
        Long phaseId = Long.valueOf(shareCodeInfo[1], 16);
        Long basicId = Long.valueOf(shareCodeInfo[2], 16);
        Long appId = Long.valueOf(shareCodeInfo[3], 16);

        //用户check
        if (shareConsumerId.equals(consumer.getId())) {
            throw new BizException("用户不可为自己助力！");
        }

        ConsumerDto shareConsumer = commonService.findConsumerDto(shareConsumerId);
        if (null == shareConsumer) {
            throw new BizException("分享用户不存在！");
        }

        HappyCodeBasicDto basic = remoteHappyCodeBasicService.findById(basicId);
        if(null == basic){
            throw new BizException("当前赛事不存在！");
        }

        if(!Objects.equals(phaseId,basic.getPhaseId())){
            throw new BizException("分享期次已结束，无法助力！");
        }

        String hbaseKey = getHappyCodeShareKey(HbaseKeySpaceEnum.K01, phaseId, consumer.getId());
        //查询用户是否有助力记录
        Long result = remoteHbaseApiKvService.getLongByKey(hbaseKey);
        if (null != result) {
            LOGGER.info("当前用户当前期次已为用户助力，不可重复助力！, consumerId {} vkey:{}", consumer.getId(), hbaseKey);
            throw new BizException("当前用户当前期次已为分享用户助力，不可重复助力！");
        }

        String thisConsumerPhaseFreeCountKey = getHappyCodeTodayFreeGiveKey(shareConsumerId, phaseId);
        Integer count = stringRedisTemplate.opsForValue().increment(thisConsumerPhaseFreeCountKey, 0L).intValue();

        Integer shareLimitCount = null == basic.getShareLimitCount() ? DEFAULT_SHARE_LIMIT_COUNT : basic.getShareLimitCount();
        if (count < shareLimitCount) {
            //增加助力记录
            HappyCodePrizeDto prize = remoteHappyCodeService.takeOneHappyCode(basicId, shareConsumerId, appId, shareConsumer.getPartnerUserId(), HappyCodeOrderDto.ORDER_ORIGIN_SHARE, 0L);

            //设置助力成功获奖得开心码弹窗
            String happyCodeRedisKey = getMyHelpHappyCodeReidsKey(shareConsumerId, phaseId);
            String codeList = stringRedisTemplate.opsForValue().get(happyCodeRedisKey);
            if (null != prize && StringUtils.isNotBlank(prize.getHappyCode())) {
                if (StringUtils.isBlank(codeList)) {
                    stringRedisTemplate.opsForValue().set(happyCodeRedisKey, prize.getHappyCode());
                } else {
                    stringRedisTemplate.opsForValue().append(happyCodeRedisKey, "," + prize.getHappyCode());
                }
                stringRedisTemplate.expire(happyCodeRedisKey, 15, TimeUnit.DAYS);
            }
            stringRedisTemplate.opsForValue().increment(thisConsumerPhaseFreeCountKey, 1L);
            stringRedisTemplate.expire(thisConsumerPhaseFreeCountKey, 1, TimeUnit.DAYS);
            remoteHbaseApiKvService.upsertKLongV(hbaseKey, 1L);
        }
        return 0L;

    }


    /**
     * Hbase用户是否助力key
     * @param hbaseKeySpaceEnum
     * @param helpConsumerId
     * @param phaseId
     * @return
     */
    private String getHappyCodeShareKey(HbaseKeySpaceEnum hbaseKeySpaceEnum, Long helpConsumerId, Long phaseId) {
        return hbaseKeySpaceEnum + "_" + ActAccessWebHBaseKeyEnum.K006.toString() + phaseId + "_" + helpConsumerId;
    }

    private String getHappyCodeTodayFreeGiveKey(Long consumerId, Long phaseId) {
        String today = DateUtils.getDayStr(new Date());
        return RedisKeyFactory.K092.toString() + today + "_" + consumerId + "_" + phaseId;
    }

    private String getMyHelpHappyCodeReidsKey(Long shareConsumerId, Long phaseId) {
        return RedisKeyFactory.K091.toString() + shareConsumerId + "_" + phaseId;
    }

    /**
     * 设置用户头像及昵称
     * @param consumerExtraMap
     * @param consumerId
     * @param pastResult
     * @return
     */
    private void getConsumerExtraInfo(Map<Long, ConsumerExtraDto> consumerExtraMap, Long consumerId,
                                      HappyCodePastResultVO pastResult) {
        ConsumerExtraDto consumerExtra = consumerExtraMap.get(consumerId);
        if (null != consumerExtra ) {
            pastResult.setHeadImg(consumerExtraMap.get(consumerId).getAvatar());
            pastResult.setNickname(consumerExtraMap.get(consumerId).getNickname());
        }
    }

    private HappyCodeCreditsVO getHappyCodeShowCredit(AppSimpleDto app, Integer credits) {
        //人民币模式开启-直接显示元
        //人民币模式关闭-读取app配置积分-乘以积分汇率
        HappyCodeCreditsVO creditsVO = new HappyCodeCreditsVO();
        Integer creditsInt ;
        if (!app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
            creditsInt =  (int)Math.ceil(credits *  app.getCreditsRate() / 100.0);
            creditsVO.setUnitName(app.getUnitName());
        } else {
            creditsInt = new BigDecimal(app.getCreditsRate())
                    .divide(new BigDecimal(100))
                    .intValue();
            creditsVO.setUnitName("元");
        }

        creditsVO.setCreditsValue(Integer.max(creditsInt, 1));

        return creditsVO;
    }
}
