package com.duiba.activity.accessweb.service.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.recommend.RecommendDto;
import cn.com.duiba.activity.center.api.params.RecommendParams;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.recommend.RemoteRecommendService;
import cn.com.duiba.developer.center.api.domain.enums.DuibaTaskStatusEnum;
import cn.com.duiba.developer.center.api.domain.enums.ItemActivityExtEnum;
import cn.com.duiba.developer.center.api.domain.enums.ShowcaseRelationTypeEnum;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.UrlUtils;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.service.ActivityRecommendService;
import cn.com.duibabiz.component.domain.DomainService;
import com.duiba.activity.accessweb.tool.ActivityBusinessTool;
import com.duiba.activity.accessweb.vo.RecommendSkinVO;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhengjy on 2017/2/14.
 */
@Service
public class ActivityRecommendServiceImpl implements ActivityRecommendService {
    private static Logger log = LoggerFactory.getLogger(ActivityRecommendServiceImpl.class);
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private RemoteRecommendService remoteRecommendService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private DeveloperCacheService developerCacheService;
    //推荐位皮肤缓存5分钟
    private Cache<Long, RecommendSkinVO> recommendSkinCache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(5, TimeUnit.MINUTES).build();

    @Override
    public Map<Long, OperatingActivityDto> getOperatingActivityMap(List<RecommendDto> recommendDtos) {
        if(CollectionUtils.isEmpty(recommendDtos)){
            return Maps.newHashMap();
        }
        List<Long> operatingActivityIds = Lists.newArrayList();
        for(RecommendDto rd :recommendDtos){
            operatingActivityIds.add(rd.getOperatingActivityId());
        }
        List<OperatingActivityDto> oads = remoteOperatingActivityServiceNew.findAllByIds(operatingActivityIds);
        Map<Long,OperatingActivityDto> oaMap = new HashMap<>(oads.size());
        for(OperatingActivityDto oad : oads){
            oaMap.put(oad.getId(),oad);
        }
        return  oaMap;
    }

    @Override
    public void setRecommendParams(RecommendParams rp, OperatingActivityDto oa, HttpServletRequest request) {
        //set当前用户参加过的活动id
        rp.setActivityIds(ActivityBusinessTool.getConsumerJoinActivityIdCookie(request));
    }


    @Override
    public JSONObject getRecommendSkin(JSONObject json, final OperatingActivityDto oa, HttpServletRequest request) {
        json.put(CommonConstants.SUCCESS_KEY, false);
        //自有活动
        if(oa == null || oa.getActivityId() == null){
            json.put("message","活动找不到或自有活动！");
            return new JSONObject();
        }
        //只有这几种活动有推荐皮肤  活动工具、兑吧答题、测试题
        if(!(OperatingActivityDto.hdToolTypeSet.contains(oa.getType())||
                OperatingActivityDto.TypeDuibaQuestionAnswer == oa.getType()||
                OperatingActivityDto.TypeActivityAccessQuestionAnswer == oa.getType()||
                OperatingActivityDto.TypeDuibaQuizz == oa.getType())){
            json.put("message","活动类型不支持！");
            return json;
        }


        RecommendSkinVO  rsd =recommendSkinCache.getIfPresent(oa.getId());
        if(rsd == null){
            try {
                rsd =recommendSkinCache.get(oa.getId(), () -> {
                    //获取兑吧活动的扩展字段，推荐位皮肤就在扩展字段中
                    Map<String,String> ret = developerCacheService.getItemActExt(oa.getActivityId(), ShowcaseRelationTypeEnum.getByType(oa.getType()).getSourceType());
                    String recommendSkinIdStr = ret.get(ItemActivityExtEnum.RECOMMEND_SKIN_ID.getCode());
                    //推荐位皮肤id不为空 且  是开启状态
                    boolean flag  = StringUtils.isNotBlank(recommendSkinIdStr)
                            && StringUtils.equals(ret.get(ItemActivityExtEnum.RECOMMEND_SKIN_STATUS.getCode()), DuibaTaskStatusEnum.OPEN.getCode());
                    if(!flag){
                        return  new RecommendSkinVO();
                    }
                    //查询推荐位皮肤
                    RecommendSkinVO rsv = BeanUtils.copy(remoteRecommendService.findRecommendSkin(Long.parseLong(recommendSkinIdStr)).getResult(),RecommendSkinVO.class);
                    //推荐位提示
                    rsv.setRecommendPrompt(ret.get(ItemActivityExtEnum.RECOMMEND_PROMPT.getCode()));
                    return rsv;
                });
            } catch (ExecutionException e) {
                log.warn("getRecommendSkin error",e);
            }
        }

        if (rsd!=null) {
            // 推荐按钮dpm
            JSONObject embedJson = new JSONObject();
            embedJson.put("dpm", oa.getAppId()+".3.7.1");
            embedJson.put("dcm", "219."+rsd.getId()+".0.0");
            embedJson.put("domain", domainService.getSystemDomain(oa.getAppId()).getEmbedDomain());
            rsd.setEmbedJson(JSONObject.toJSONString(embedJson));
        }

        boolean openFlag = (rsd != null && StringUtils.equals(rsd.getShowStatus(), DuibaTaskStatusEnum.OPEN.getCode()));
        json.put("recommendSkin",openFlag ? rsd: null);
        //当前活动用户参与数
        json.put("activityJoinNum",ActivityBusinessTool.getCookieJoinTimes(oa.getId(),request));
        json.put(CommonConstants.SUCCESS_KEY,openFlag);
        return json;
    }

    @Override
    public void setEmb(List<RecommendDto> recommendDtos, Long appId,
                       Long recommendSkinId,String requestType,HttpServletRequest request,Long oaId) {
        if(CollectionUtils.isEmpty(recommendDtos) || appId == null){
            return;
        }
        String embedDomain=domainService.getSystemDomain(appId).getEmbedDomain();
        int i=0;
        //因为一般推荐位只会有2条数据，这里循环调用下可以接受
        for(RecommendDto rd :recommendDtos){
            i++;
            String dpm;
            String dcm;
            if(RecommendParams.REQUEST_TYPE_SIGN_ACTIVITY.equals(requestType)){
                dpm = appId + ".40.0." + i;
                dcm = "217." + request.getParameter("signActivityId") + "." + rd.getOperatingActivityId() + ".0";
            }else {
                //dpm=appid.10.1.1，如果有推荐位皮肤，则将第三位设置推荐位皮肤id，如果没有则设置成0，i=推荐位的位置
                dpm = appId + ".10." + (recommendSkinId == null ? 0 : recommendSkinId) + "." + i;
                dcm = "202." + rd.getOperatingActivityId() + ".1."+ Objects.toString(oaId, "0");
            }
            //推荐位埋点
            if("item".equalsIgnoreCase(requestType) || "activity".equalsIgnoreCase(requestType) || "signActivity".equalsIgnoreCase(requestType)) {
                Map<String, String> params = Maps.newHashMap();
                params.put("dpm", dpm);
                params.put("dcm", dcm);
                rd.setUrl(UrlUtils.appendParams(rd.getUrl(), params));
                rd.setEmdJson("");//活动本身的埋点

                //推荐位埋点，统一曝光用新埋点
                JSONObject json = new JSONObject();
                json.put("domain", embedDomain);
                json.put("dpm", dpm);
                json.put("dcm", dcm);
                rd.setEmdDpmJson(json.toJSONString());
            }else {
                Map<String, String> params = Maps.newHashMap();
                rd.setUrl(UrlUtils.appendParams(rd.getUrl(), params));
                rd.setEmdJson("");//活动本身的埋点

                //推荐位埋点，统一曝光用新埋点
                JSONObject json = new JSONObject();
                json.put("domain", embedDomain);
                rd.setEmdDpmJson(json.toJSONString());
            }

        }
    }
}
