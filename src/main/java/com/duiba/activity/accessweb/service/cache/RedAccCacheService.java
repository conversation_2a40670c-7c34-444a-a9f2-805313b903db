package com.duiba.activity.accessweb.service.cache;

import cn.com.duiba.activity.common.center.api.dto.wallet.RedAccPeriodDto;
import cn.com.duiba.activity.common.center.api.dto.wallet.RedAccPeriodRelDto;
import cn.com.duiba.activity.common.center.api.remoteservice.wallet.RemoteRedAccPeriodRelService;
import cn.com.duiba.activity.common.center.api.remoteservice.wallet.RemoteRedAccPeriodService;
import cn.com.duiba.developer.center.api.domain.dto.visualeditor.VisualEditorSimpleAppSkinDto;
import cn.com.duiba.developer.center.api.domain.enums.visualeditor.OpenStatusEnum;
import cn.com.duiba.developer.center.api.remoteservice.visualeditor.RemoteVisualEditorAppSkinService;
import com.google.common.base.Objects;
import com.google.common.base.Optional;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by xiaoxuda on 2019/1/17.
 */
@Service
public class RedAccCacheService {
    @Autowired
    private RemoteRedAccPeriodService remoteRedAccPeriodService;
    @Autowired
    private RemoteVisualEditorAppSkinService remoteVisualEditorAppSkinService;
    @Autowired
    private RemoteRedAccPeriodRelService remoteRedAccPeriodRelService;

    /**
     * 当前开启周期查询
     */
    private Cache<Long, Optional<RedAccPeriodDto>> openPeriodCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();

    /**
     * 周期功能开启状态
     */
    private Cache<Long, Optional<Boolean>> periodOpenCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();
    /**
     * 周期关联活动
     */
    private Cache<Long, Optional<List<RedAccPeriodRelDto>>> periodRelCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();

    /**
     * 是否开启组件化开关
     */
    private Cache<Long, Boolean> visualOpenCache = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();

    /**
     * 实际可用的最大奖金额度，rel中的winner_limit最小值*period中的withdraw_threshold
     */
    private Cache<Long, Long> maxBonusCahce = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).build();


    public RedAccPeriodDto getOpenPeriod(Long appId){
        try {
            return openPeriodCache.get(appId,
                    () -> Optional.fromNullable(remoteRedAccPeriodService.findOpenPeriodByAppId(appId))).orNull();
        } catch (ExecutionException e){
            return null;
        }
    }

    public boolean getPeriodOpenStatus(Long appId){
        try {
            return Boolean.TRUE.equals(periodOpenCache.get(appId,
                    () -> {
                        VisualEditorSimpleAppSkinDto dto = remoteVisualEditorAppSkinService.selectIndexPageWithCache(appId);
                        if (dto == null || !Objects.equal(OpenStatusEnum.OPEN.getCode(),dto.getOpenStatus())) {
                            return Optional.fromNullable(false);
                        }
                        return Optional.fromNullable(remoteRedAccPeriodService.redPeriodHasOpen(appId));
                    }).orNull());
        } catch (ExecutionException e){
            return false;
        }
    }

    public List<RedAccPeriodRelDto> getPeriodRelByPeriodId(Long periodId){
        try {
            return periodRelCache.get(periodId, ()-> Optional.fromNullable(remoteRedAccPeriodRelService.selectByPeriodId(periodId))).orNull();
        } catch (ExecutionException e) {
            return Collections.emptyList();
        }
    }

    public boolean getVisualOpenStatus(Long appId){
        try {
            return visualOpenCache.get(appId, ()-> {
                VisualEditorSimpleAppSkinDto dto = remoteVisualEditorAppSkinService.selectIndexPageWithCache(appId);
                return dto != null && Objects.equal(OpenStatusEnum.OPEN.getCode(),dto.getOpenStatus());
            });
        } catch (ExecutionException e) {
            return false;
        }
    }

    public Long getMaxBonusLimit(Long appId){
        try{
            return maxBonusCahce.get(appId, ()->{
                RedAccPeriodDto period = getOpenPeriod(appId);
                if(period == null){
                    return 0L;
                }
                List<RedAccPeriodRelDto> rels = remoteRedAccPeriodRelService.selectByPeriodId(period.getId());
                if(CollectionUtils.isEmpty(rels)){
                    return 0L;
                }
                Long minWinnerLimit = rels.get(0).getWinnerLimit();
                for (RedAccPeriodRelDto rel : rels) {
                    minWinnerLimit = minWinnerLimit > rel.getWinnerLimit() ? rel.getWinnerLimit() : minWinnerLimit;
                }
                return period.getWithdrawThreshold() * minWinnerLimit;
            });
        }catch(ExecutionException e){
            return 0L;
        }
    }
}
