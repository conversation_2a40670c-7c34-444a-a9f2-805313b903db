package com.duiba.activity.accessweb.service.activity.happycode;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.boot.exception.BizException;
import com.duiba.activity.accessweb.pjv.rsp.happycode.poker.HappyCodePokerIndexRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.poker.HappyCodePokerLastPhaseRsp;
import com.duiba.activity.accessweb.pjv.rsp.happycode.poker.HappyCodePokerPastPhaseRsp;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeJoinRecordVO;
import com.duiba.activity.accessweb.vo.happycode.poker.HappyCodeAbcPopDataVo;
import com.duiba.activity.accessweb.vo.happycode.poker.HappyCodePokerPrizeRecordVo;

import java.util.List;

public interface HappyCodePokerService {
    HappyCodePokerIndexRsp index(Long basicId, Long consumerId) throws BizException;

    HappyCodeAbcPopDataVo abcPopData(Long basicId, Long consumerId) throws BizException;

    void abcPop(Long phaseId, Long consumerId) throws BizException;

    HappyCodePokerLastPhaseRsp lastPhaseIndex(Long basicId, Long consumerId, Boolean needCache) throws BizException;

    List<HappyCodeJoinRecordVO> winRecord(Long basicId, Long phaseId) throws BizException;

    Page<HappyCodePokerPastPhaseRsp> myPastPhase(Long basicId, Long consumerId, Integer pageNo, Integer pageSize) throws BizException;

    Page<HappyCodePokerPrizeRecordVo> prizeRecord(Long basicId, Integer pageNo, Integer pageSize) throws BizException;
}
