package com.duiba.activity.accessweb.service.bargain.impl;

import cn.com.duiba.activity.center.api.dto.bargain.BargainActivityInfoDto;
import cn.com.duiba.activity.center.api.dto.bargain.BargainItemInfoDto;
import cn.com.duiba.activity.center.api.dto.bargain.BargainOrderDto;
import cn.com.duiba.activity.center.api.dto.bargain.BargainRecordDto;
import cn.com.duiba.activity.center.api.dto.bargain.BargainRuleInfoDto;
import cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto;
import cn.com.duiba.activity.center.api.dto.fakeuser.FakeUserDto;
import cn.com.duiba.activity.center.api.enums.BargainOrderStatusEnum;
import cn.com.duiba.activity.center.api.enums.DeletedEnum;
import cn.com.duiba.activity.center.api.params.bargain.BargainItemInfoParam;
import cn.com.duiba.activity.center.api.params.bargain.BargainOrderParam;
import cn.com.duiba.activity.center.api.remoteservice.bargain.RemoteBargainActivityInfoService;
import cn.com.duiba.activity.center.api.remoteservice.bargain.RemoteBargainItemInfoService;
import cn.com.duiba.activity.center.api.remoteservice.bargain.RemoteBargainOrderService;
import cn.com.duiba.activity.center.api.remoteservice.bargain.RemoteBargainRecordService;
import cn.com.duiba.activity.center.api.remoteservice.bargain.RemoteBargainRuleInfoService;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteDuibaActivityAppSpecifyNewService;
import cn.com.duiba.activity.center.api.remoteservice.fakeuser.RemoteFakeUserService;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByItemRequest;
import cn.com.duiba.plugin.center.api.response.PlaceOrderResponse;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.duiba.activity.accessweb.cache.ConsumerCacheService;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.constant.BargainActivityConstant;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.enums.HelpBargainStatusEnum;
import com.duiba.activity.accessweb.mq.producer.BargainRecordInsertMqProducer;
import com.duiba.activity.accessweb.service.bargain.BargainActivityService;
import com.duiba.activity.accessweb.tool.adapter.BargainRuleAdapter;
import com.duiba.activity.accessweb.tool.adapter.BargainRuleAdapterManager;
import com.duiba.activity.accessweb.tool.adapter.domain.BargainRuleExt;
import com.duiba.activity.accessweb.vo.bargain.ActivityRuleVO;
import com.duiba.activity.accessweb.vo.bargain.BargainOrderListVO;
import com.duiba.activity.accessweb.vo.bargain.BargainOrderVO;
import com.duiba.activity.accessweb.vo.bargain.BargainPriceVO;
import com.duiba.activity.accessweb.vo.bargain.BargainUserVO;
import com.duiba.activity.accessweb.vo.bargain.WelfareGoodsDetailVO;
import com.duiba.activity.accessweb.vo.bargain.WelfareGoodsListVO;
import com.duiba.activity.accessweb.vo.bargain.WelfareGoodsVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @auther: linjianhui
 * @date: 2018/12/10 11:10
 * @Description: 砍价活动逻辑处理
 */
@Service
public class BargainActivityServiceImpl implements BargainActivityService {

    private Logger logger = LoggerFactory.getLogger(BargainActivityServiceImpl.class);

    /**
     * 砍价记录mq tag
     */
    private static final String BARGAIN_ACTIVITY_TAG = "BARGAIN_ACTIVITY_TAG";

    @Autowired
    private RemoteBargainOrderService remoteBargainOrderService;

    @Autowired
    private RemoteBargainRecordService remoteBargainRecordService;

    @Autowired
    private RemoteBargainActivityInfoService remoteBargainActivityInfoService;

    @Autowired
    private RemoteBargainRuleInfoService remoteBargainRuleInfoService;

    @Autowired
    private RemoteBargainItemInfoService remoteBargainItemInfoService;

    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;

    @Autowired
    private GoodsCacheService goodsCacheService;

    @Autowired
    private RemoteDuibaActivityAppSpecifyNewService remoteDuibaActivityAppSpecifyNewService;

    @Autowired
    private BargainActivityConstant bargainActivityConstant;

    @Autowired
    private BargainRuleAdapterManager bargainRuleAdapterManager;

    @Autowired
    private BargainRecordInsertMqProducer bargainRecordInsertMqProducer;

    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;

    @Autowired
    private RemoteFakeUserService remoteFakeUserService;

    @Resource
    private ConsumerCacheService consumerCacheService;

    @Autowired
    private DomainService domainService;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public BargainActivityInfoDto getBargainActivityInfo(Long activityId) {
        return remoteBargainActivityInfoService.findById(activityId);
    }

    @Override
    public WelfareGoodsListVO getWelfareGoodsList(BargainItemInfoParam param) throws BizException {
        WelfareGoodsListVO voList = new WelfareGoodsListVO();
        Page<BargainItemInfoDto> pageData = remoteBargainItemInfoService.pageByParam(param);
        if (CollectionUtils.isEmpty(pageData.getList()) || pageData.getTotalCount() == 0) {
            voList.setTotalCount(0);
            return voList;
        }
        List<Long> itemIds = pageData.getList().stream().map(BargainItemInfoDto::getItemId).collect(toList());
        Map<Long, ItemDto> itemDtoMap = getItemDtoMap(itemIds);
        List<WelfareGoodsVO> goods = Lists.newArrayList();
        pageData.getList().forEach(w -> {
            WelfareGoodsVO vo = BeanUtils.copy(w, WelfareGoodsVO.class);
            vo.setName(itemDtoMap.get(w.getItemId()).getName());
            vo.setMarketPrice(itemDtoMap.get(w.getItemId()).getMarketPrice());
            //成功砍价人数公式=基数+真实成功人数*倍数
            vo.setSuccessNum(w.getCheatBase() + w.getCheatMultiple() * w.getCompletedCount());
            goods.add(vo);
        });
        voList.setGoods(goods);
        voList.setTotalCount(pageData.getTotalCount());
        return voList;
    }

    /**
     * @Description: 根据兑换项id列表获取兑换项信息
     * @Author: ljh
     * @Date: 2018/12/17
     */
    private Map<Long, ItemDto> getItemDtoMap(List<Long> itemIds) {
        //通过兑换项id获取兑换项信息
        DubboResult<List<ItemDto>> result = remoteDuibaItemGoodsService.findByIds(itemIds);
        if (result == null || CollectionUtils.isEmpty(result.getResult()) || !result.isSuccess()) {
            return Maps.newHashMap();
        }
        return result.getResult().stream().collect(Collectors.toMap(ItemDto::getId, i -> i));
    }

    @Override
    public boolean orientation(Long appId, Long activityId) {
        DuibaActivityAppSpecifyNewDto appSpecifyNewDto = remoteDuibaActivityAppSpecifyNewService.findAppSpecifyByActivityIdAndAppIdAndActivityType
                (activityId, appId, DuibaActivityAppSpecifyNewDto.BARGAIN_ACTIVITY).getResult();
        return appSpecifyNewDto != null && Objects.equals(appSpecifyNewDto.getAppId(), appId);
    }

    @Override
    public WelfareGoodsDetailVO getWelfareGoodsInfo(Long goodsId) {
        //查询商品信息
        BargainItemInfoDto itemInfoDto = remoteBargainItemInfoService.findById(goodsId);
        if (itemInfoDto == null || Objects.equals(itemInfoDto.getDeleted(), DeletedEnum.DELETED.value())) {
            return null;
        }
        WelfareGoodsDetailVO vo = BeanUtils.copy(itemInfoDto, WelfareGoodsDetailVO.class);
        try {
            ItemDto itemDto = goodsCacheService.findById(itemInfoDto.getItemId());
            if (itemDto != null) {
                vo.setName(itemDto.getName());
                vo.setMarketPrice(itemDto.getMarketPrice());
            }
        } catch (Exception e) {
            logger.error("查询兑换项失败 itemId={}", itemInfoDto.getItemId(), e);
            return vo;
        }
        //查询用户该商品是否下过单，查出最新订单
        Long cid = RequestLocal.getCid();
        BargainOrderDto orderDto = remoteBargainOrderService.selectByParams(cid, goodsId, itemInfoDto.getBargainActivityId());
        if (orderDto != null) {
            vo.setOrderId(orderDto.getId());
        }
        vo.setActivityId(itemInfoDto.getBargainActivityId());
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        String url = domainService.getSystemDomain(app.getId()).getGoodsDomain() + "/mobile/detail?itemId="
                + itemInfoDto.getItemId() + "&type=preview&dbnewopen&from=login&spm=1.1.1.1";
        vo.setPreviewUrl(url);
        vo.setItemId(itemInfoDto.getItemId());
        return vo;
    }

    @Override
    public BargainPriceVO doBargain(Long goodsId, Long activityId,ConsumerDto consumer) throws BizException {
        Long cid = consumer.getId();
        //校验商品是否有库存
        BargainItemInfoDto itemInfoDto = remoteBargainItemInfoService.findById(goodsId);
        if (itemInfoDto == null || itemInfoDto.getRemaining() <= 0 || Objects.equals(itemInfoDto.getDeleted(), DeletedEnum.DELETED.value())) {
            throw new BizException("商品已砍光");
        }
        //goodsId应该归属于activityId
        if (!Objects.equals(itemInfoDto.getBargainActivityId(), activityId)) {
            throw new BizException("商品归属有误");
        }
        //校验兑换项是否启用
        ItemDto itemDto = goodsCacheService.findById(itemInfoDto.getItemId());
        if (itemDto == null || !itemDto.getEnable() || itemDto.getMarketPrice() == null) {
            throw new BizException("兑换项未启用");
        }

        //是否有砍价进行中或成功订单
        Boolean result = remoteBargainOrderService.checkInitOrSuccessOrder(cid, goodsId, itemInfoDto.getBargainActivityId());
        if (result) {
            throw new BizException("您已发起砍价");
        }
        BargainRuleAdapter adapter = bargainRuleAdapterManager.getBargainRuleAdapterByType(bargainActivityConstant.getActivityEnum(itemInfoDto.getBargainActivityId()));
        BargainRuleInfoDto bargainRuleInfoDto = remoteBargainRuleInfoService.findById(itemInfoDto.getBargainRuleId());
        BargainRuleExt bargainRuleExt = BeanUtils.copy(bargainRuleInfoDto, BargainRuleExt.class);
        //获取砍价金额
        Integer random = adapter.doBargainRule(cid, bargainRuleExt);
        if (random == null || random < 0) {
            throw new BizException("超出每日砍价限制");
        }

        //用户对应商品是否有超时订单
        BargainOrderDto timeoutOrder = remoteBargainOrderService.selectTimeoutOrder(cid, goodsId, itemInfoDto.getBargainActivityId());
        //超时订单设置为结束
        if (timeoutOrder != null) {
            BargainOrderDto dto = new BargainOrderDto();
            dto.setId(timeoutOrder.getId());
            dto.setBargainStatus(BargainOrderStatusEnum.END);
            remoteBargainOrderService.updateBargainPriceOrStatus(dto);
            //原订单库存已回滚，重新扣除订单
            if (BargainOrderStatusEnum.TIME_OUT.getCode().equals(timeoutOrder.getBargainStatus().getCode())) {
                remoteBargainItemInfoService.updateRemaining(goodsId, -1);
            }
        } else {
            //之前没有超时订单，属于新发起订单，扣除库存
            remoteBargainItemInfoService.updateRemaining(goodsId, -1);
        }
        //如果大于市场价，则取市场价
        Integer bargainAmount = random >= itemDto.getMarketPrice() ? itemDto.getMarketPrice() : random;

        //发起砍价
        BargainOrderDto orderDto = new BargainOrderDto();
        orderDto.setConsumerId(cid);
        orderDto.setAppId(consumer.getAppId());
        orderDto.setBargainActivityId(itemInfoDto.getBargainActivityId());
        orderDto.setBargainItemId(itemInfoDto.getId());
        orderDto.setItemName(itemDto.getName());
        orderDto.setImageUrl(itemInfoDto.getImageUrl());
        orderDto.setMarketPrice(itemDto.getMarketPrice());
        orderDto.setTotalBargainPrice(bargainAmount);
        orderDto.setBargainStatus(BargainOrderStatusEnum.INIT);
        orderDto.setBargainEndTime(new Date(System.currentTimeMillis() + itemInfoDto.getBargainDuration().longValue() * 1000));
        Long orderId = remoteBargainOrderService.insertBargainOrder(orderDto);
        AccessLogFilter.putExPair("consumetype", 0);
        AccessLogFilter.putExPair("Participationmode", 1);
        bargainLog(2, orderId, itemInfoDto);
        //插入砍价记录,判断是否发奖
        orderDto.setId(orderId);
        insertBargainRecord(cid, bargainAmount, orderDto, itemInfoDto);
        BargainPriceVO vo = new BargainPriceVO();
        vo.setId(orderId);
        vo.setBargainPrice(bargainAmount);
        return vo;
    }

    private void bargainLog(Integer subType, Long orderId, BargainItemInfoDto itemInfoDto) {
        //页面类型	500 搜狐砍价
        AccessLogFilter.putExPair("type", 500);
        AccessLogFilter.putExPair("sub_type", subType);
        AccessLogFilter.putExPair("orderNum", orderId);
        AccessLogFilter.putExPair("pageBizId", 230);
        AccessLogFilter.putExPair("BargainitemID", Objects.toString(itemInfoDto.getId(), ""));
        AccessLogFilter.putExPair("pageId", 3);
    }

    @Override
    public Boolean helpBargainStatus(Long orderId) throws BizException {
        //缓存看是否帮砍过
        Long cid = RequestLocal.getCid();
        String key = RedisKeyFactory.K352.toString() + cid + "_" + orderId;
        String record = stringRedisTemplate.opsForValue().get(key);

        if (record != null) {
            return true;
        }
        return Objects.equals(orderId, getRecordFromDB(cid, orderId));
    }

    @Override
    public BargainOrderDto getBasisOrderInfo(Long orderId) {
        return remoteBargainOrderService.findById(orderId);
    }

    private Long getRecordFromDB(Long cid, Long orderId) {
        //缓存过期，查库验证
        BargainRecordDto recordDto = remoteBargainRecordService.selectByCidAndOrderId(cid, orderId);
        if (recordDto != null) {
            return recordDto.getBargainOrderId();
        } else {
            return null;
        }
    }

    @Override
    public Integer helpBargain(BargainOrderDto bargainOrderDto, Map<String, String> dcustomMap) throws BizException {
        //帮砍用户id
        Long cid = RequestLocal.getCid();
        //商品已经过期
        if (bargainOrderDto.getBargainEndTime().getTime() <= System.currentTimeMillis()) {
            return HelpBargainStatusEnum.ORDER_TIME_OUT.getCode();
        }
        //好友已经砍价成功
        if (Objects.equals(bargainOrderDto.getBargainStatus().getCode(), BargainOrderStatusEnum.SUCCESS.getCode()) ||
                Objects.equals(bargainOrderDto.getBargainStatus().getCode(), BargainOrderStatusEnum.RECEIVED.getCode())) {
            return HelpBargainStatusEnum.ORDER_SUCCESS.getCode();
        }
        try {
            //缓存设置帮砍记录
            String key = RedisKeyFactory.K352.toString() + RequestLocal.getCid() + "_" + bargainOrderDto.getId();
            stringRedisTemplate.opsForValue().set(key, bargainOrderDto.getId().toString(), 60, TimeUnit.SECONDS);
            BargainItemInfoDto itemInfoDto = remoteBargainItemInfoService.findById(bargainOrderDto.getBargainItemId());
            BargainRuleInfoDto bargainRuleInfoDto = remoteBargainRuleInfoService.findById(itemInfoDto.getBargainRuleId());
            BargainRuleExt bargainRuleExt = BeanUtils.copy(bargainRuleInfoDto, BargainRuleExt.class);
            bargainRuleExt.setDcustomMap(dcustomMap);
            bargainRuleExt.setOrderCreateTime(bargainOrderDto.getGmtCreate());
            // 根据新老用户 返回砍价金额
            BargainRuleAdapter adapter = bargainRuleAdapterManager.getBargainRuleAdapterByType(bargainActivityConstant.getActivityEnum(itemInfoDto.getBargainActivityId()));
            Integer random = adapter.checkNewAndOldUsers(cid, bargainRuleExt);
            //砍价总金额是否超出市场价
            Integer bargainAmount = random + bargainOrderDto.getTotalBargainPrice() >= bargainOrderDto.getMarketPrice() ?
                    bargainOrderDto.getMarketPrice() - bargainOrderDto.getTotalBargainPrice() : random;
            bargainLog(2, bargainOrderDto.getId(), itemInfoDto);

            AccessLogFilter.putExPair("Participationmode", 2);

            insertBargainRecord(cid, bargainAmount, bargainOrderDto, itemInfoDto);
            return bargainAmount;
        } catch (Exception e) {
            logger.error("帮砍失败，helpConsumerId={},orderId={}", cid, bargainOrderDto.getAppId(), e);
            throw new BizException("帮砍失败");
        }
    }

    /**
     * @Description: 插入砍价记录  判断是否开奖
     * @Author: ljh
     * @Date: 2018/12/18
     */
    private void insertBargainRecord(Long cid, Integer bargainAmount, BargainOrderDto bargainOrderDto, BargainItemInfoDto itemInfoDto) {
        try {
            //添加记录
            BargainRecordDto record = new BargainRecordDto();
            record.setBargainOrderId(bargainOrderDto.getId());
            record.setConsumerId(bargainOrderDto.getConsumerId());
            record.setHelpConsumerId(cid);
            record.setHelpBargainPrice(bargainAmount);
            bargainRecordInsertMqProducer.sendMsg(record, BARGAIN_ACTIVITY_TAG);

            BargainOrderDto updateOrder = new BargainOrderDto();
            updateOrder.setId(bargainOrderDto.getId());
            if (!Objects.equals(cid, bargainOrderDto.getConsumerId())) {
                //帮忙砍价，增加总金额
                updateOrder.setTotalBargainPrice(bargainAmount);
            } else {
                //发起砍价，订单起始砍价金额为0
                bargainOrderDto.setTotalBargainPrice(0);
            }
            //判断是否开奖
            if ((bargainOrderDto.getTotalBargainPrice() + bargainAmount) >= bargainOrderDto.getMarketPrice()) {
                ConsumerDto consumerDto = consumerCacheService.findConsumerById(bargainOrderDto.getConsumerId());
                ItemDto item = goodsCacheService.findById(itemInfoDto.getItemId());
                if (consumerDto != null && item != null) {
                    PlaceOrderByItemRequest request = new PlaceOrderByItemRequest();
                    request.setAppId(bargainOrderDto.getAppId());
                    request.setConsumerId(bargainOrderDto.getConsumerId());
                    request.setPartnerUserId(consumerDto.getPartnerUserId());
                    request.setItemId(itemInfoDto.getItemId());
                    request.setItemType(item.getType());
                    PlaceOrderResponse re = remoteActivityOrderService.placeOrder(request);
                    if (!re.isSuccess()) {
                        logger.info("bargain 发奖失败 cid={] message={} ordernum={}", bargainOrderDto.getConsumerId(), re.getErrorMsg(), re.getOrderNum());
                    }
                }
                updateOrder.setBargainStatus(BargainOrderStatusEnum.SUCCESS);
                remoteBargainOrderService.updateBargainPriceOrStatus(updateOrder);
                remoteBargainItemInfoService.updateCompletedCountById(itemInfoDto.getId(), 1);
            } else if (!Objects.equals(cid, bargainOrderDto.getConsumerId())) {
                remoteBargainOrderService.updateBargainPriceOrStatus(updateOrder);
            }
        } catch (BizException e) {
            logger.error("插入砍价记录失败 cid={},orderId={}", cid, bargainOrderDto.getId(), e);
        }
    }

    @Override
    public BargainOrderListVO getBargainOrderList(BargainOrderParam param) {
        //订单状态改为枚举
        BargainOrderListVO voList = new BargainOrderListVO();
        List<BargainOrderVO> orders = Lists.newArrayList();
        Integer count = 0;
        Page<BargainOrderDto> pageData = remoteBargainOrderService.pageByParams(param);
        if (pageData == null || CollectionUtils.isEmpty(pageData.getList())) {
            voList.setTotalCount(count);
            return voList;
        }
        List<BargainOrderDto> orderDtoList = pageData.getList();
        List<Long> itemIds = orderDtoList.stream().map(BargainOrderDto::getBargainItemId).collect(toList());
        List<BargainItemInfoDto> itemInfoDtoList = remoteBargainItemInfoService.findByIds(itemIds);
        Map<Long, BargainItemInfoDto> itemInfoDtoMap = itemInfoDtoList.stream().collect(Collectors.toMap(BargainItemInfoDto::getId, i -> i));
        orderDtoList.forEach(o -> {
            BargainOrderVO orderVO = fillBargainOrderVO(o);
            if (itemInfoDtoMap.get(o.getBargainItemId()) != null) {
                orderVO.setItemId(itemInfoDtoMap.get(o.getBargainItemId()).getItemId());
            }
            orders.add(orderVO);
        });
        voList.setOrders(orders);
        count = pageData.getTotalCount();
        voList.setTotalCount(count);
        return voList;
    }

    private BargainOrderVO fillBargainOrderVO(BargainOrderDto orderDto) {
        BargainOrderVO orderVO = BeanUtils.copy(orderDto, BargainOrderVO.class);
        if (Objects.equals(orderDto.getBargainStatus(), BargainOrderStatusEnum.INIT)
                && orderDto.getBargainEndTime().getTime() <= System.currentTimeMillis()) {
            orderVO.setRemainingTime(0L);
            orderVO.setBargainStatus(BargainOrderStatusEnum.TIME_OUT.getCode());
        } else if (Objects.equals(orderDto.getBargainStatus(), BargainOrderStatusEnum.INIT)) {
            orderVO.setRemainingTime(orderDto.getBargainEndTime().getTime() - System.currentTimeMillis());
            orderVO.setBargainStatus(orderDto.getBargainStatus().getCode());
        } else if (!Objects.equals(orderDto.getBargainStatus(), BargainOrderStatusEnum.INIT)) {
            orderVO.setRemainingTime(0L);
            orderVO.setBargainStatus(orderDto.getBargainStatus().getCode());
        }
        orderVO.setName(orderDto.getItemName());
        return orderVO;
    }

    @Override
    public BargainOrderVO getOrderInfo(Long orderId) {
        BargainOrderVO orderVO = null;
        try {
            BargainOrderDto orderDto = remoteBargainOrderService.findById(orderId);
            if (orderDto == null) {
                return null;
            }
            orderVO = fillBargainOrderVO(orderDto);
            orderVO.setActivityId(orderDto.getBargainActivityId());
            orderVO.setGoodsId(orderDto.getBargainItemId());
            //获取商品预览链接
            BargainItemInfoDto bargainItemInfoDto = remoteBargainItemInfoService.findById(orderDto.getBargainItemId());
            String url = domainService.getSystemDomain(orderDto.getAppId()).getGoodsDomain() + "/mobile/detail?itemId="
                    + bargainItemInfoDto.getItemId() + "&type=preview&dbnewopen&from=login&spm=1.1.1.1";
            orderVO.setPreviewUrl(url);
            orderVO.setItemId(bargainItemInfoDto.getItemId());
        } catch (Exception e) {
            logger.error("获取订单信息失败", e);
        }
        return orderVO;
    }

    /**
     * 根据传入的appItemId和itemId获取itemKey,并验证itemKey的有效性
     * 写法参考:goods-access-web GoodsItemDataService.getItemKeyByAppItemIdAndItemId
     *
     * @param itemId
     * @return ItemKeyDto
     */
    @Override
    public ItemDto getByItemId(Long itemId) {
        // 商品查询商品查询
        ItemDto itemDto = goodsCacheService.findById(itemId);
        ItemDto result = new ItemDto();
        result.setOperationsType(0);
        if (itemDto != null) {
            //安全起见,只给前端需要的数据
            result.setDescription(itemDto.getDescription());
            result.setName(itemDto.getName());
            result.setSubtitle(itemDto.getSubtitle());
            result.setOperationsType(itemDto.getOperationsType());
        }
        return result;
    }

    @Override
    public Boolean receiveItem(Long orderId) {
        return remoteBargainOrderService.receiveItem(orderId);
    }

    @Override
    public List<BargainUserVO> getUserList(Integer pageSize) {
        List<FakeUserDto> fakeUserList = remoteFakeUserService.getRandomUsersWithCount(pageSize);
        return BeanUtils.copyList(fakeUserList, BargainUserVO.class);
    }

    @Override
    public List<BargainUserVO> getUserList2(Integer pageSize, Long bargainActivityId) {
        BargainOrderParam param = new BargainOrderParam();
        param.setBargainActivityId(bargainActivityId);
        param.setBargainStatus(Arrays.asList(BargainOrderStatusEnum.SUCCESS.getCode(), BargainOrderStatusEnum.RECEIVED.getCode()));
        //这里取出来是真实数据
        Page<BargainOrderDto> page = remoteBargainOrderService.pageByParams(param);
        List<BargainOrderDto> bargainOrderDtos = page.getList();

        List<BargainUserVO> result = null;
        if (CollectionUtils.isNotEmpty(bargainOrderDtos)) {
            pageSize = pageSize - bargainOrderDtos.size();
            result = getNickName(bargainOrderDtos);
        } else {
            result = new ArrayList<>();
        }

        List<FakeUserDto> fakeUserList = remoteFakeUserService.getRandomUsersWithCount(pageSize);
        for (FakeUserDto each : fakeUserList) {
            BargainUserVO vo = new BargainUserVO();
            vo.setUserName(each.getUserName());
            vo.setAvatar(each.getAvatar());
            //时间随机,取之前的任意时刻
            //为了让数据看起来更真实,用头像和昵称做随机种子
            long rand = (StringUtils.defaultString(vo.getAvatar()) + StringUtils.defaultString(vo.getUserName())).hashCode() % (24 * 60 * 60 * 1000);
            vo.setCreateTime(new Date(System.currentTimeMillis() - rand));
            result.add(vo);
        }

        return result;
    }

    /**
     * 这里是真实的数据
     *
     * @param bargainOrderDtos
     * @return
     */
    private List<BargainUserVO> getNickName(List<BargainOrderDto> bargainOrderDtos) {
        List<BargainUserVO> result = new ArrayList<>();

        List<Long> consumerIds = bargainOrderDtos.stream().map(BargainOrderDto::getConsumerId).collect(toList());
        List<ConsumerExtraDto> extraDtos = consumerCacheService.getConsumerExtraListCache(consumerIds);
        Map<Long, ConsumerExtraDto> mapExtraDto =
                extraDtos.stream().collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, Function.identity(), (a, b) -> a));
        bargainOrderDtos.forEach(each -> {
            BargainUserVO vo = new BargainUserVO();
            vo.setCreateTime(each.getGmtModified());
            ConsumerExtraDto extraDto = mapExtraDto.get(each.getConsumerId());
            if (extraDto != null) {
                vo.setUserName(extraDto.getNickname());
                vo.setAvatar(extraDto.getAvatar());
            }
            result.add(vo);
        });
        return result;
    }

    @Override
    public ActivityRuleVO getActivityRule(Long activityId) {
        BargainActivityInfoDto info = remoteBargainActivityInfoService.findById(activityId);
        return BeanUtils.copy(info, ActivityRuleVO.class);
    }

    @Override
    public BargainUserVO information(Long cid) {
        ConsumerExtraDto dto = consumerCacheService.getConsumerExtraByConsumerId(cid);
        BargainUserVO result = new BargainUserVO();
        if (dto != null) {
            result.setUserName(dto.getNickname());
            result.setAvatar(dto.getAvatar());
        }
        return result;
    }
}
