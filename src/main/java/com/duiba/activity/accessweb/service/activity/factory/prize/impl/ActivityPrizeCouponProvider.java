package com.duiba.activity.accessweb.service.activity.factory.prize.impl;

import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.HtdoolConstants;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.GoodsService;
import com.duiba.activity.accessweb.service.activity.factory.prize.ActivityPrizeProvider;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/** 
 * ClassName:ActivityPrizeCouponProvider.java <br/>
 * 优惠券 弹层信息柱状
 * <AUTHOR> 
 * @date 创建时间：2017年7月31日 下午4:13:21 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Component
public class ActivityPrizeCouponProvider implements ActivityPrizeProvider {

	@Autowired
	private ActivityPrizeCommonService activityPrizeCommonService;
	@Autowired
	private CommonService commonService;
	@Autowired
	private GoodsService goodsService;

	@Override
	public String getPrizeType() {
		return ItemDto.TypeCoupon;
	}

	@Override
	public void prizeData(OrdersVO orderDO, JSONObject jsonObject, String floatingId,int lotteryType){
		//查询兑换记录
		ConsumerExchangeRecordDto record = activityPrizeCommonService.recordInfo(orderDO,lotteryType);
		if(record == null || record.getOrderId() == null){
			//  兑换记录没有生成  继续
			jsonObject.put(CommonConstants.RESULT, HtdoolConstants.LOTTERY_CODE_PROCESS);
			jsonObject.put(CommonConstants.MESSAGE_KEY, "处理中。。。");
			return ;
		}
		//图片地址
		String imgurl = activityPrizeCommonService.getOptionImgUrl(orderDO,lotteryType);
		
		//设置弹层 上图片及标题信息
		String link = "/activity/takePrizeNew?recordId=" + record.getId() + "&dbnewopen";
		Map<String, Object> lottery = Maps.newHashMap();
		lottery.put(CommonConstants.IMGURL, imgurl);
		lottery.put(CommonConstants.LINK, link);
		lottery.put(CommonConstants.TYPE, orderDO.getPrizeType());
		lottery.put("id", orderDO.getPrizeId());
		lottery.put(CommonConstants.TITLE, orderDO.getPrizeName());
		lottery.put(CommonConstants.ISDOWNLOADURL, true);
		lottery.put(CommonConstants.LINKTO, ItemDto.LinkToBussiness);
		//查询商品信息
		ItemKeyDto itemKey = commonService.findItemKeyDto(orderDO.getAppItemId(), orderDO.getItemId(), orderDO.getAppId());
		if(itemKey != null){
			ItemDto itemDto = itemKey.getItem();
			lottery.put(CommonConstants.ITEMID, itemDto == null ? null : itemDto.getId());
		}
		goodsService.activityCouponDcm(lottery,itemKey, orderDO,record.getOrderId(),floatingId);
		jsonObject.put(CommonConstants.RESULT, HtdoolConstants.LOTTERY_CODE_SUCCESS);
		jsonObject.put("lottery", lottery);
	}
}
