package com.duiba.activity.accessweb.service.handler.sharecode;

import cn.com.duiba.wolf.utils.BeanUtils;
import com.duiba.activity.accessweb.service.activity.sharecode.CheckParameter;
import com.duiba.activity.accessweb.service.activity.sharecode.PresentParameter;
import com.duiba.activity.accessweb.service.activity.sharecode.PresentPrizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/04/24
 */
@Service
public class DefaultCheckoutHandler implements CheckoutHandler {
    @Autowired
    private PresentPrizeService presentPrizeService;

    @PostConstruct
    public void init() {
        CheckoutManager.register(this);
    }
    @Override
    public List<Long> getTypes() {
        return Arrays.asList(19643L);
    }

    @Override
    public void check(CheckParameter checkParameter) {
        //默认通过校验开始发奖
        presentPrizeService.present(BeanUtils.copy(checkParameter, PresentParameter.class));
    }
}
