package com.duiba.activity.accessweb.service.happygroup.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupBaseConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupConfigDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupInfoDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupItemDto;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupMQMessage;
import cn.com.duiba.activity.center.api.dto.happygroup.HappyGroupRecordDto;
import cn.com.duiba.activity.center.api.enums.HappyGroupRecordStatusEnum;
import cn.com.duiba.activity.center.api.enums.JoinGroupStatusEnum;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import cn.com.duiba.activity.center.api.params.HappyGroupRecordPageParam;
import cn.com.duiba.activity.center.api.remoteservice.happygroup.RemoteHappyGroupInfoAppService;
import cn.com.duiba.activity.center.api.remoteservice.happygroup.RemoteHappyGroupItemAppService;
import cn.com.duiba.activity.center.api.remoteservice.happygroup.RemoteHappyGroupRecordAppService;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.ItemTypeEnums;
import cn.com.duiba.api.enums.LimitScopeEnum;
import cn.com.duiba.api.enums.mq.RocketMqTagEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbConsisHashKvService;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByCreditsRequest;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByItemRequest;
import cn.com.duiba.plugin.center.api.request.PlaceOrderRequest;
import cn.com.duiba.plugin.center.api.response.PlaceOrderResponse;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.ConsumerCacheService;
import com.duiba.activity.accessweb.cache.GoodsCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.happygroup.HappyGroupService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.tool.ConsumerCheckUtil;
import com.duiba.activity.accessweb.vo.happygroup.BaseInfoVO;
import com.duiba.activity.accessweb.vo.happygroup.GroupDetailVO;
import com.duiba.activity.accessweb.vo.happygroup.ItemGroupVO;
import com.duiba.activity.accessweb.vo.happygroup.ItemVO;
import com.duiba.activity.accessweb.vo.happygroup.JoinResultVO;
import com.duiba.activity.accessweb.vo.happygroup.MainPageVO;
import com.duiba.activity.accessweb.vo.happygroup.MemberInfoVO;
import com.duiba.activity.accessweb.vo.happygroup.PrizeInfoVO;
import com.duiba.activity.accessweb.vo.happygroup.RecordInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: zhengjianhao
 * @date: 2019/2/26 15:29
 * @description: 良品铺子拼拼乐拼团活动
 */
@Service
public class HappyGroupServiceImpl extends HappyGroupCacheUtil implements HappyGroupService {

	private static final Logger LOGGER = LoggerFactory.getLogger(HappyGroupServiceImpl.class);

	@Autowired
	private ActivityCacheService activityCacheService;
	@Autowired
	private ConsumerCacheService consumerCacheService;
	@Autowired
	private GoodsCacheService goodsCacheService;
	@Autowired
	private RemoteHappyGroupRecordAppService remoteHappyGroupRecordAppService;
	@Autowired
	private RemoteHappyGroupInfoAppService remoteHappyGroupInfoAppService;
	@Autowired
	private RemoteHappyGroupItemAppService remoteHappyGroupItemAppService;
	@Autowired
	private ExecutorService executorService;
	@Resource(name = "stringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;
	@Resource(name = "redisTemplate")
	private RedisAtomicClient redisAtomicClient;
	@Autowired
	private RemoteHbConsisHashKvService remoteHbConsisHashKvService;
	@Autowired
	private RemoteActivityOrderService remoteActivityOrderService;
	@Autowired
	private RocketMqMessageTopic rocketMqMessageTopic;
	@Autowired
	private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;
	@Autowired
	private RiskService riskService;

	// 团详情中展示的团员头像数最大值
	private static Integer SHOW_GROUP_MEMBER_NUMBER = 4;
	// 天
	private static int DAY_DIVISOR = 24 * 60 * 60;
	// 小时
	private static int HOUR_DIVISOR = 60 * 60;
	// 分
	private static int MINUTE_DIVISOR = 60;

	/**
	 * 校验入库活动、app、活动配置
	 */
	private HappyGroupConfigDto checkActivityAndConfig(Long operatingActivityId, Long appId) throws BizException{
		HappyGroupConfigDto happyGroupConfigDto = checkActivity(operatingActivityId, appId);
		if(new Date().before(happyGroupConfigDto.getStartTime())){
			throw new BizException(ErrorCode.E0100016.getDesc()).withCode(ErrorCode.E0100016.getErrorCode());
		}
		if(new Date().after(happyGroupConfigDto.getEndTime())){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		return happyGroupConfigDto;
	}

	/**
	 * 校验入库活动、app
	 */
	private HappyGroupConfigDto checkActivity(Long operatingActivityId, Long appId) throws BizException{
		if(null == operatingActivityId){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(operatingActivityId);
		if(null == operatingActivityDto || null == operatingActivityDto.getActivityId()){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(!Objects.equals(appId, operatingActivityDto.getAppId())){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(operatingActivityDto.getDeleted() || operatingActivityDto.getStatus() != OperatingActivityDto.StatusIntOpen){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		HappyGroupConfigDto happyGroupConfigDto = getConfigById(operatingActivityDto.getActivityId());
		if(null == happyGroupConfigDto){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		return happyGroupConfigDto;
	}

	/**
	 * 校验入库活动、app
	 */
	private HappyGroupConfigDto checkActivityForDetail(Long operatingActivityId) throws BizException{
		if(null == operatingActivityId){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(operatingActivityId);
		if(null == operatingActivityDto || null == operatingActivityDto.getActivityId()){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(operatingActivityDto.getDeleted() || operatingActivityDto.getStatus() != OperatingActivityDto.StatusIntOpen){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		HappyGroupConfigDto happyGroupConfigDto = getConfigById(operatingActivityDto.getActivityId());
		if(null == happyGroupConfigDto){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		return happyGroupConfigDto;
	}

	/**
	 * 校验拼团商品
	 */
	private HappyGroupItemDto checkGroupItem(Long groupItemId, Long groupConfigId) throws BizException{
		HappyGroupItemDto groupItem = getGroupItemById(groupItemId);
		if(null == groupItem){
			throw new BizException("拼团商品不存在");
		}
		if(null != groupConfigId && !Objects.equals(groupItem.getActivityConfigId(), groupConfigId)){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		return groupItem;
	}

	@Override
	public BaseInfoVO getBaseInfo(Long operatingActivityId) throws BizException {
		if(null == operatingActivityId){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(operatingActivityId);
		if(null == operatingActivityDto || null == operatingActivityDto.getActivityId()){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(!Objects.equals(RequestLocal.getAppId(), operatingActivityDto.getAppId())){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(operatingActivityDto.getDeleted() || operatingActivityDto.getStatus() != OperatingActivityDto.StatusIntOpen){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		HappyGroupBaseConfigDto happyGroupBaseConfigDto = getBaseConfigById(operatingActivityDto.getActivityId());
		if(null == happyGroupBaseConfigDto){
			throw new BizException(ErrorCode.E0100014.getDesc()).withCode(ErrorCode.E0100014.getErrorCode());
		}
		return BeanUtils.copy(happyGroupBaseConfigDto, BaseInfoVO.class);
	}

	@Override
    public MainPageVO getMainInfo(Long operatingActivityId, Long itemId) throws BizException {
		// 获取拼团配置
		HappyGroupConfigDto groupConfig = checkActivity(operatingActivityId, RequestLocal.getAppId());
		// 获取拼团商品列表
		List<HappyGroupItemDto> groupItems = getGroupItemsByConfigId(groupConfig.getId());
		if(CollectionUtils.isEmpty(groupItems)){
			throw new BizException("拼团商品为空");
		}
		MainPageVO mainPageVO = new MainPageVO();
		mainPageVO.setActivityEndTime(groupConfig.getEndTime().getTime());
		// 记录统一访问日志
		ConsumerDto consumerDto = RequestLocal.getConsumerDO();
		accessLog(operatingActivityId, groupConfig.getId(), 211, consumerDto == null ? null : consumerDto.getPartnerUserId(),
				consumerDto == null ? 0L : consumerDto.getCredits());
		Long consumerId = consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId()) ? null : consumerDto.getId();
	    // 设置奖品列表
		mainPageVO.setWinPrizeList(getWinPrizeList(groupItems.stream().collect(Collectors.toMap(HappyGroupItemDto::getId, x->x)), consumerId));
		// 设置要展示的拼团商品信息
		List<Long> groupItemIds = groupItems.stream().map(HappyGroupItemDto::getId).collect(Collectors.toList());
		Long showItemId = null != itemId && groupItemIds.contains(itemId) ? itemId : groupItemIds.get(0);
		HappyGroupItemDto showGroupItem = groupItems.stream().filter(x->x.getId().equals(showItemId)).findAny().orElse(null);
		if(null == showGroupItem){
			throw new BizException("拼团商品数据异常");
		}
		// 设置开团剩余次数
		mainPageVO.setRemainOpenGroupTime(getRemainOpenGroupTime(groupConfig, showGroupItem, consumerId));

		mainPageVO.setShowItemId(showItemId);
	    mainPageVO.setOwnerPrizeName(showGroupItem.getOwnerPrizeName());
	    mainPageVO.setOwnerPrizeType(showGroupItem.getPrizeType());
	    mainPageVO.setGroupOpenRemainTime(showGroupItem.getExpiredTimeSecond());
	    // 设置商品列表
	    List<ItemVO> groupItemVOS = new ArrayList<>();
	    groupItems.forEach(x->groupItemVOS.add(convertGroupItemToVO(x)));
		mainPageVO.setGroupItemList(groupItemVOS);
		// 如果活动已结束，则不再查询团信息
		if(new Date().after(groupConfig.getEndTime())){
		    mainPageVO.setActivityEnd(true);
			return mainPageVO;
	    }
	    // 校验团状态
		checkItemGroup(mainPageVO, groupItemVOS, showGroupItem, groupItemIds, consumerId);
	    return mainPageVO;
	}

	/**
	 * 获取总的开团剩余次数
	 */
	private int getRemainOpenGroupTime(HappyGroupConfigDto groupConfig, HappyGroupItemDto groupItem, Long consumerId) throws BizException{
		int remainActOpenTime = getRemainOpenGroupTimeByAct(groupConfig, consumerId);
		if(remainActOpenTime <= 0){
			return 0;
		}
		Integer remainItemOpenTime = getRemainOpenTimeTodayByItem(groupItem, consumerId);
		if(null == remainItemOpenTime){
			return remainActOpenTime;
		}else if(remainItemOpenTime <= 0) {
			return 0;
		}
		return Math.min(remainActOpenTime, remainItemOpenTime);
	}

	/**
	 * 获取在拼团活动总配置下的开团剩余次数
	 */
	private int getRemainOpenGroupTimeByAct(HappyGroupConfigDto groupConfig, Long consumerId) throws BizException{
		if(null == consumerId){
			return 0;
		}
		Date recordCreate = null;
		if (LimitScopeEnum.EVERY_DAY.getId().equals(groupConfig.getOpenGroupLimitType())) {
			recordCreate = DateUtils.getDayDate(new Date());
		}
		Integer hasOpenTime = remoteHappyGroupRecordAppService.getCountByConfigAndCIdAndType(groupConfig.getId(), consumerId, YesOrNoEnum.YES.getCode(), recordCreate);
		if(null == hasOpenTime || hasOpenTime == 0){
			return groupConfig.getOpenGroupLimit();
		}else if(hasOpenTime.compareTo(groupConfig.getOpenGroupLimit()) >= 0){
			return 0;
		}else{
			return groupConfig.getOpenGroupLimit() - hasOpenTime;
		}
	}

	/**
	 * 获取在拼团商品配置下的每日开团剩余次数
	 */
	private Integer getRemainOpenTimeTodayByItem(HappyGroupItemDto groupItem, Long consumerId) throws BizException{
		if(null == groupItem.getSingleUserLimit()){
			return null;
		}
		Integer hasOpenTime = remoteHappyGroupRecordAppService.getCountByItemAndCIdAndType(groupItem.getId(), consumerId, YesOrNoEnum.YES.getCode(), DateUtils.getDayDate(new Date()));
		if(null == hasOpenTime || hasOpenTime == 0){
			return groupItem.getSingleUserLimit();
		}else if(hasOpenTime.compareTo(groupItem.getSingleUserLimit()) >= 0){
			return 0;
		}else{
			return groupItem.getSingleUserLimit() - hasOpenTime;
		}
	}

	/**
	 * 转换拼团商品对象
	 */
	private ItemVO convertGroupItemToVO(HappyGroupItemDto groupItem){
		ItemVO groupItemVO = new ItemVO();
		groupItemVO.setGroupItemId(groupItem.getId());
		groupItemVO.setItemName(groupItem.getItemName());
		groupItemVO.setNeedMemberCount(groupItem.getGroupNumber());
		groupItemVO.setStatus(YesOrNoEnum.NO.getCode());
		return groupItemVO;
	}

	/**
	 * 获取中奖商品列表
	 */
	private List<PrizeInfoVO> getWinPrizeList(Map<Long, HappyGroupItemDto> groupItemMap, Long consumerId){
		if(null == consumerId){
			return Collections.emptyList();
		}
		List<HappyGroupRecordDto> groupRecords = remoteHappyGroupRecordAppService.getExchangeWaitingList(consumerId);
		if(CollectionUtils.isEmpty(groupRecords)){
			return Collections.emptyList();
		}
		List<PrizeInfoVO> prizeInfoVOS = new ArrayList<>();
		// 设置奖品信息
		setPrizeInfo(groupRecords, groupItemMap, prizeInfoVOS);
		// 设置奖品图片
		setPrizeImage(prizeInfoVOS);
		return prizeInfoVOS;
	}

	/**
	 * 设置奖品信息
	 */
	private void setPrizeInfo(List<HappyGroupRecordDto> groupRecords, Map<Long, HappyGroupItemDto> groupItemMap, List<PrizeInfoVO> prizeInfoVOS){
		groupRecords.forEach(groupRecord ->{
			HappyGroupItemDto happyGroupItemDto = groupItemMap.get(groupRecord.getGroupItemId());
			if(null != happyGroupItemDto){
				PrizeInfoVO prizeInfoVO = new PrizeInfoVO();
				prizeInfoVO.setPrizeId(YesOrNoEnum.YES.getCode().equals(groupRecord.getOwnerType())
						? happyGroupItemDto.getOwnerPrizeId() : happyGroupItemDto.getMemberPrizeId());
				prizeInfoVO.setGroupInfoId(groupRecord.getGroupInfoId());
				prizeInfoVO.setPrizeName(YesOrNoEnum.YES.getCode().equals(groupRecord.getOwnerType())
						? happyGroupItemDto.getOwnerPrizeName() : happyGroupItemDto.getMemberPrizeName());
				prizeInfoVO.setNeedMemberCount(happyGroupItemDto.getGroupNumber());
				prizeInfoVO.setPrizeType(happyGroupItemDto.getPrizeType());
				prizeInfoVOS.add(prizeInfoVO);

			}
		});
	}

	/**
	 * 设置奖品图片
	 */
	private void setPrizeImage(List<PrizeInfoVO> prizeInfoVOS){
		if(CollectionUtils.isEmpty(prizeInfoVOS)){
			return;
		}
		List<Long> prizeIds = prizeInfoVOS.stream().filter(x->null != x.getPrizeId()).map(PrizeInfoVO::getPrizeId).distinct().collect(Collectors.toList());
		if(CollectionUtils.isEmpty(prizeIds)){
			return;
		}
		List<AppItemDto> appItemDtoList = goodsCacheService.getAppItemsByIds(prizeIds);
		if(CollectionUtils.isNotEmpty(appItemDtoList)){
			// 非自有商品，需要获取对应的兑吧商品
			Map<Long, ItemDto> itemDtoMap = getItemMap(appItemDtoList);
			Map<Long, String> itemImageMap = new HashMap<>();
			appItemDtoList.forEach(appItemDto -> itemImageMap.put(appItemDto.getId(), getPrizeImage(appItemDto, itemDtoMap.get(appItemDto.getItemId()))));
			prizeInfoVOS.forEach(prizeInfoVO -> prizeInfoVO.setPrizeImage(itemImageMap.get(prizeInfoVO.getPrizeId())));
		}
	}

	/**
	 * 获取兑吧商品
	 */
	private Map<Long, ItemDto> getItemMap(List<AppItemDto> appItemDtoList){
		List<Long> itemIdList = appItemDtoList.stream().filter(x->!x.getIsOwner() && null != x.getItemId()).map(AppItemDto::getItemId).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(itemIdList)){
			return new HashMap<>();
		}
		List<ItemDto> itemDtoList = remoteDuibaItemGoodsService.findByIds(itemIdList).getResult();
		if(CollectionUtils.isEmpty(itemDtoList)){
			return new HashMap<>();
		}
		return itemDtoList.stream().collect(Collectors.toMap(ItemDto::getId, x->x));
	}

	/**
	 * 获取奖品图片
	 */
	private String getPrizeImage(AppItemDto appItemDto, ItemDto itemDto){
		String smallImage = getSmallImage(appItemDto, itemDto);
		return StringUtils.isNotBlank(smallImage) ? smallImage : getLogoImage(appItemDto, itemDto);
	}

	/**
	 * 获取奖品缩略图
	 */
	private String getSmallImage(AppItemDto appItemDto, ItemDto itemDto){
		if(null == appItemDto){
			return null;
		}
		return StringUtils.isBlank(appItemDto.getSmallImage()) && null != itemDto ? itemDto.getSmallImage() : appItemDto.getSmallImage();
	}

	/**
	 * 获取奖品logo图
	 */
	private String getLogoImage(AppItemDto appItemDto, ItemDto itemDto){
		if(null == appItemDto){
			return null;
		}
		return StringUtils.isBlank(appItemDto.getLogo()) && null != itemDto ? itemDto.getLogo() : appItemDto.getLogo();
	}

	/**
	 * 校验团状态
	 */
	private void checkItemGroup(MainPageVO mainPageVO, List<ItemVO> groupItemVOS, HappyGroupItemDto showItem, List<Long> groupItemIds, Long consumerId) throws BizException{
		if(null == consumerId){
			return;
		}
		List<HappyGroupRecordDto> groupRecords = remoteHappyGroupRecordAppService.getUnderWayGroupByItems(groupItemIds, consumerId);
		if(CollectionUtils.isEmpty(groupRecords)){
			return;
		}
		List<Long> groupIds = groupRecords.stream().map(HappyGroupRecordDto::getGroupInfoId).collect(Collectors.toList());
		List<HappyGroupInfoDto> allGroups = remoteHappyGroupInfoAppService.getByIdList(groupIds);
		// 拼团商品设置团信息
		setItemGroupInfo(mainPageVO, groupItemVOS, allGroups);
		// 设置团详情
		HappyGroupInfoDto showGroup = allGroups.stream().filter(x->showItem.getId().equals(x.getGroupItemId())).findAny().orElse(null);
		// 如果要展示的拼团商品当前未开团，直接返回
		if(null == showGroup){
			return;
		}
		// 如果要展示的拼团商品当前进行中的团，已过结束时间，将团进行拼团失败处理
		if(new Date().after(showGroup.getEndTime())){
			executorService.execute(() -> remoteHappyGroupInfoAppService.modifyToFailure(showGroup.getId(), showItem.getId()));
			return;
		}
		// 设置是否团长
		HappyGroupRecordDto record = groupRecords.stream().filter(x->x.getGroupInfoId().equals(showGroup.getId())).findAny().orElse(null);
		mainPageVO.setOwnerType(null != record && record.getOwnerType().equals(YesOrNoEnum.YES.getCode()));

		Integer groupMemberNumber = getGroupMemberNumber(showGroup.getId());
		// 如果要展示的拼团商品当前进行中的团，人数已满，即拼团成功，直接返回
		if(showGroup.getGroupNumber().equals(groupMemberNumber)){
			return;
		}
		// 如果要展示的拼团商品当前进行中的团，确实是处于进行中状态，返回团详情
		mainPageVO.setGroupDetail(setGroupInfo(showGroup, groupMemberNumber));
	}

	/**
	 * 拼团商品设置团信息
	 */
	private void setItemGroupInfo(MainPageVO mainPageVO, List<ItemVO> groupItemVOS, List<HappyGroupInfoDto> allGroups){
		List<HappyGroupInfoDto> remainGroups = allGroups.stream()
		                                                .filter(x-> JoinGroupStatusEnum.UNDER_WAY.getCode().equals(x.getGroupStatus())
				                                                && new Date().before(x.getEndTime()))
		                                                .collect(Collectors.toList());
		if(CollectionUtils.isNotEmpty(remainGroups)){
			Map<Long, HappyGroupInfoDto> groupMap = remainGroups.stream().collect(Collectors.toMap(HappyGroupInfoDto::getGroupItemId, x->x));
			// 如果拼团商品有进行中的团，将拼团商品状态设置为已开团
			groupItemVOS.forEach(groupItemVO -> {
				HappyGroupInfoDto group = groupMap.get(groupItemVO.getGroupItemId());
				if(null != group){
					groupItemVO.setStatus(YesOrNoEnum.YES.getCode());
					groupItemVO.setGroupId(group.getId());
					groupItemVO.setGroupEndTime(group.getEndTime().getTime());
				}
			});
			// 设置拼团商品列表
			mainPageVO.setGroupItemList(groupItemVOS);
		}
	}

	/**
	 * 获取团信息，设置拼团商品状态，设置要展示的拼团商品的团信息
	 */
	private GroupDetailVO setGroupInfo(HappyGroupInfoDto groupInfo, Integer groupMemberNumber){
		GroupDetailVO groupDetailVO = new GroupDetailVO();
		Integer membersNumber = Math.max(groupMemberNumber - 1, 0);
		List<Long> memberIds = getMembersByGroupId(groupInfo.getId(), Math.min(membersNumber, SHOW_GROUP_MEMBER_NUMBER));
		Long ownerId = getOwnerByGroupId(groupInfo.getId());
		List<Long> allMemberIds = new ArrayList<>();
		allMemberIds.add(ownerId);
		allMemberIds.addAll(memberIds);
		List<ConsumerExtraDto> consumerExtraDtos = consumerCacheService.getConsumerExtraListCache(allMemberIds);

		ConsumerExtraDto ownerInfo = consumerExtraDtos.stream().filter(x->ownerId.equals(x.getConsumerId())).findAny().orElse(null);
		if(null != ownerInfo){
			groupDetailVO.setOwnerAvatar(ownerInfo.getAvatar());
			groupDetailVO.setOwnerName(ownerInfo.getNickname());
		}
		List<ConsumerExtraDto> memberInfos = consumerExtraDtos.stream().filter(x->!ownerId.equals(x.getConsumerId())).collect(Collectors.toList());
		groupDetailVO.setMemberInfoList(getMemberInfos(memberIds, memberInfos.stream().collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, x->x))));
		groupDetailVO.setGroupEndTime(groupInfo.getEndTime().getTime());
		groupDetailVO.setJoinMemberCount(membersNumber);
		groupDetailVO.setRemainMemberCount(groupInfo.getGroupNumber() - groupMemberNumber);
		return groupDetailVO;
	}

	private List<MemberInfoVO> getMemberInfos(List<Long> memberIds, Map<Long, ConsumerExtraDto> memberInfoMap){
		if(CollectionUtils.isEmpty(memberIds)){
			return Collections.emptyList();
		}
		List<MemberInfoVO> memberInfoVOList = new ArrayList<>();
		for (Long memberId : memberIds) {
			MemberInfoVO memberInfoVO = new MemberInfoVO();
			ConsumerExtraDto consumerExtraDto = memberInfoMap.get(memberId);
			if(null != consumerExtraDto){
				memberInfoVO.setAvatar(consumerExtraDto.getAvatar());
				memberInfoVO.setNickname(consumerExtraDto.getNickname());
			}
			memberInfoVOList.add(memberInfoVO);
		}
		return memberInfoVOList;
	}

	@Override
	public Long openGroup(Long operatingActivityId, Long groupItemId) throws BizException {
		Long appId = RequestLocal.getAppId();
		// 获取拼团配置
		HappyGroupConfigDto groupConfig = checkActivityAndConfig(operatingActivityId, appId);
		// 记录统一访问日志
		ConsumerDto consumerDto = RequestLocal.getConsumerDO();
		accessLog(operatingActivityId, groupConfig.getId(), 240, consumerDto == null ? null : consumerDto.getPartnerUserId(),
				consumerDto == null ? 0L : consumerDto.getCredits());
		if(null == consumerDto || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())){
			throw new BizException(CommonConstants.MSG_NOT_LOGIN_TIPS).withCode(ResultCode.*********.getCode());
		}
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(operatingActivityId, groupItemId, ActivityUniformityTypeEnum.HappyGroup.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}
		// 判断用户是否有该商品进行中的团
		checkUnderWayGroup(groupItemId, consumerDto.getId());

		// 参团
		return openGroupCheck(groupConfig, groupItemId, appId, consumerDto.getId());
	}

	/**
	 * 校验用户是否有该商品的进行中的团
	 */
	private void checkUnderWayGroup(Long groupItemId, Long consumerId) throws BizException{
		if(remoteHappyGroupRecordAppService.checkUnderWayGroupByItem(groupItemId, consumerId)){
			throw new BizException(ErrorCode.E0200111.getDesc()).withCode(ErrorCode.E0200111.getErrorCode());
		}
	}

	/**
	 * 开团
	 */
	private Long openGroupCheck(HappyGroupConfigDto groupConfig, Long groupItemId, Long appId, Long consumerId) throws BizException{
		String key = RedisKeyFactory.K192.toString() + groupConfig.getId() + "_" + consumerId;
		try (RedisLock lock = redisAtomicClient.getLock(key, 2)){
			if(Objects.isNull(lock)){
				throw new BizException(ResultCode.C100804.getDescription()).withCode(ResultCode.C100804.getCode());
			}
			int remainActOpenTime = getRemainOpenGroupTimeByAct(groupConfig, consumerId);
			if(remainActOpenTime <= 0){
				if (LimitScopeEnum.EVERY_DAY.getId().equals(groupConfig.getOpenGroupLimitType())){
					throw new BizException(ErrorCode.E0200112.getDesc()).withCode(ErrorCode.E0200112.getErrorCode());
				}else {
					throw new BizException(ErrorCode.E0200113.getDesc()).withCode(ErrorCode.E0200113.getErrorCode());
				}
			}
			// 获取拼团商品
			HappyGroupItemDto groupItem = checkGroupItem(groupItemId, groupConfig.getId());
			// 判断用户在该商品下的单个用户每日开团限制次数
			Integer remainItemOpenTime = getRemainOpenTimeTodayByItem(groupItem, consumerId);
			if(null != remainItemOpenTime && remainItemOpenTime <= 0){
				throw new BizException(ErrorCode.E0200123.getDesc()).withCode(ErrorCode.E0200123.getErrorCode());
			}
			// 判断商品成团数限制
			if(LimitScopeEnum.EVERY_DAY.getId().equals(groupItem.getOpenGroupLimitType())){
				if(!increaseItemGroupToday(groupItem)){
					throw new BizException(ErrorCode.E0200114.getDesc()).withCode(ErrorCode.E0200114.getErrorCode());
				}
			} else {
				if(!increaseItemGroupEver(groupItem)){
					throw new BizException(ErrorCode.E0200115.getDesc()).withCode(ErrorCode.E0200115.getErrorCode());
				}
			}
			return createGroup(groupConfig, groupItem, appId, consumerId);
		}catch (BizException e){
			LOGGER.info("拼拼乐活动开团失败", e);
			throw new BizException(e.getMessage()).withCode(e.getCode());
		}catch (Exception e){
			LOGGER.warn("拼拼乐活动开团异常", e);
			throw new BizException("开团异常");
		}
	}

	/**
	 * 增加商品每日成团数：true，增加成功；false，增加失败
	 */
	private boolean increaseItemGroupToday(HappyGroupItemDto groupItem){
		String key = redisKeyForItemGroupToday(groupItem.getId());
		String oldCountStr = stringRedisTemplate.opsForValue().get(key);
		Integer count;
		if(StringUtils.isBlank(oldCountStr)){
			Long newCount = stringRedisTemplate.opsForValue().increment(key, 1);
			stringRedisTemplate.expire(key, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
			count = null == newCount ? 0 : newCount.intValue();
		}else{
			Integer oldCount = Integer.valueOf(oldCountStr);
			if(oldCount.compareTo(groupItem.getOpenGroupLimit()) >= 0){
				return false;
			}
			Long newCount = stringRedisTemplate.opsForValue().increment(key, 1);
			stringRedisTemplate.expire(key, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
			count = null == newCount ? 0 : newCount.intValue();
		}
		if(count.compareTo(groupItem.getOpenGroupLimit()) > 0){
			stringRedisTemplate.opsForValue().increment(key, -1);
			stringRedisTemplate.expire(key, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
			return false;
		}
		return true;
	}

	/**
	 * 增加商品永久成团数：true，增加成功；false，增加失败
	 */
	private boolean increaseItemGroupEver(HappyGroupItemDto groupItem){
		String key = hbaseKeyForItemGroupEver(groupItem.getId());
		Long countLong = remoteHbConsisHashKvService.getLongByKey(key);
		Integer count = null == countLong ? 0 : countLong.intValue();
		if(count.compareTo(groupItem.getOpenGroupLimit()) >= 0){
			return false;
		}
		Long lastCount = remoteHbConsisHashKvService.increaseByKey(key, 1);
		count = null == lastCount ? 0 : lastCount.intValue();
		if(count.compareTo(groupItem.getOpenGroupLimit()) > 0){
			remoteHbConsisHashKvService.increaseByKey(key, -1);
			return false;
		}
		return true;
	}

	/**
	 * 开团-新增团记录和用户参团记录，团参与人数+1
	 */
	private Long createGroup(HappyGroupConfigDto groupConfig, HappyGroupItemDto groupItem, Long appId, Long consumerId){
		// 新增团记录
		HappyGroupInfoDto groupInfo = new HappyGroupInfoDto();
		groupInfo.setAppId(appId);
		groupInfo.setActivityConfigId(groupConfig.getId());
		groupInfo.setGroupItemId(groupItem.getId());
		groupInfo.setGroupNumber(groupItem.getGroupNumber());
		groupInfo.setEndTime(setGroupEndTime(new Date(), groupItem.getExpiredTimeSecond()));
		Long groupId = remoteHappyGroupInfoAppService.add(groupInfo);
		groupInfo.setId(groupId);
		// 新增参团记录
		addJoinGroupRecord(groupInfo, consumerId, YesOrNoEnum.YES.getCode());
		// 团参与人数+1
		stringRedisTemplate.opsForValue().set(redisKeyForGroupNumber(groupId), "1", groupItem.getExpiredTimeSecond() + 300, TimeUnit.SECONDS);
		return groupId;
	}

	/**
	 * 参团-新增用户参团记录
	 */
	private void addJoinGroupRecord(HappyGroupInfoDto groupInfo, Long consumerId, Integer ownerType){
		// 新增参团记录
		HappyGroupRecordDto record = new HappyGroupRecordDto();
		record.setAppId(groupInfo.getAppId());
		record.setActivityConfigId(groupInfo.getActivityConfigId());
		record.setGroupItemId(groupInfo.getGroupItemId());
		record.setGroupInfoId(groupInfo.getId());
		record.setConsumerId(consumerId);
		record.setOwnerType(ownerType);
		record.setEndTime(groupInfo.getEndTime());
		remoteHappyGroupRecordAppService.add(record);
	}

	/**
	 * 计算团结束时间
	 */
	private Date setGroupEndTime(Date date, Long expiredTime){
		if(null == expiredTime){
			return date;
		}
		Long day = expiredTime / DAY_DIVISOR;
		Long remain = expiredTime % DAY_DIVISOR;
		Long hour = remain / HOUR_DIVISOR;
		remain = remain % HOUR_DIVISOR;
		Long minute = remain / MINUTE_DIVISOR;
		Long second = remain % MINUTE_DIVISOR;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, day.intValue());
		calendar.add(Calendar.HOUR_OF_DAY, hour.intValue());
		calendar.add(Calendar.MINUTE, minute.intValue());
		calendar.add(Calendar.SECOND, second.intValue());
		return calendar.getTime();
	}

	@Override
	public ItemGroupVO getGroupDetail(Long operatingActivityId, Long groupId) throws BizException {
		// 获取拼团活动配置
		HappyGroupConfigDto groupConfig = checkActivityForDetail(operatingActivityId);
		// 获取团信息
		HappyGroupInfoDto groupInfo = checkGroupInfo(groupId, groupConfig.getId());
		// 获取拼团商品
		HappyGroupItemDto groupItem = checkGroupItem(groupInfo.getGroupItemId(), groupConfig.getId());

		ItemGroupVO itemGroupVO = new ItemGroupVO();
		itemGroupVO.setGroupId(groupId);
		itemGroupVO.setItemName(groupItem.getItemName());
		itemGroupVO.setNeedMemberCount(groupItem.getGroupNumber());
		itemGroupVO.setOwnerPrizeName(groupItem.getOwnerPrizeName());
		Date now = new Date();
		if(now.after(groupConfig.getEndTime())){
			itemGroupVO.setActivityEnd(true);
		}
		Integer groupMemberNumber = getGroupMemberNumber(groupId);
		// 如果团人数已满，即拼团成功，返回团已结束
		if(!JoinGroupStatusEnum.UNDER_WAY.getCode().equals(groupInfo.getGroupStatus()) || groupInfo.getGroupNumber().equals(groupMemberNumber)){
			itemGroupVO.setGroupEnd(true);
		}else if(now.after(groupInfo.getEndTime())){
			executorService.execute(() -> remoteHappyGroupInfoAppService.modifyToFailure(groupInfo.getId(), groupInfo.getGroupItemId()));
			itemGroupVO.setGroupEnd(true);
		}
		// 设置团详情
		itemGroupVO.setGroupDetail(setGroupInfo(groupInfo, groupMemberNumber));
		return itemGroupVO;
	}

	/**
	 * 获取团信息并校验
	 */
	private HappyGroupInfoDto checkGroupInfo(Long groupId, Long configId) throws BizException{
		HappyGroupInfoDto groupInfo = remoteHappyGroupInfoAppService.getById(groupId);
		if(null == groupInfo || !Objects.equals(groupInfo.getActivityConfigId(), configId)){
			throw new BizException("团不存在");
		}
		return groupInfo;
	}

	private Integer getGroupMemberNumber(Long groupId){
		String groupMemberNumberStr = stringRedisTemplate.opsForValue().get(redisKeyForGroupNumber(groupId));
		return StringUtils.isBlank(groupMemberNumberStr) ? getGroupMemberCountReal(groupId) : Integer.valueOf(groupMemberNumberStr);
	}

	private Integer getGroupMemberCountReal(Long groupId){
		Integer count = remoteHappyGroupRecordAppService.getCountByGroupId(groupId);
		return count == null ? 0 : count;
	}

	@Override
	public JoinResultVO joinGroup(Long operatingActivityId, Long groupId) throws BizException {
		// 获取拼团活动配置
		HappyGroupConfigDto groupConfig = checkActivityAndConfig(operatingActivityId, RequestLocal.getAppId());
		// 记录统一访问日志
		ConsumerDto consumerDto = RequestLocal.getConsumerDO();
		accessLog(operatingActivityId, groupConfig.getId(), 240, consumerDto == null ? null : consumerDto.getPartnerUserId(),
				consumerDto == null ? 0L : consumerDto.getCredits());
		if(null == consumerDto || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())){
			throw new BizException(CommonConstants.MSG_NOT_LOGIN_TIPS).withCode(ResultCode.*********.getCode());
		}
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(operatingActivityId, groupConfig.getId(), ActivityUniformityTypeEnum.HappyGroup.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}
		// 获取团信息
		HappyGroupInfoDto groupInfo = checkGroupInfo(groupId, groupConfig.getId());
		// 判断用户是否已参加该团
		HappyGroupRecordDto record = remoteHappyGroupRecordAppService.getByGroupIdAndConsumerId(groupId, consumerDto.getId());
		if(null == record){
			// 如果团已不是进行中，不能参团；如果团已过结束时间，不能参团
			checkGroupEnd(groupInfo);
			// 判断用户是否有该商品进行中的团
			checkUnderWayGroup(groupInfo.getGroupItemId(), consumerDto.getId());
			return joinGroupCheck(groupConfig, groupInfo, consumerDto.getId());
		}else if(HappyGroupRecordStatusEnum.EXCHANGE_WAITING.getCode().equals(record.getRecordStatus())){
			// 已参加该团，可领奖
			HappyGroupItemDto groupItem = checkGroupItem(groupInfo.getGroupItemId(), groupConfig.getId());
			return canExchangeInfo(groupItem, YesOrNoEnum.YES.getCode().equals(record.getOwnerType()));
		}else {
			// 已参与该团，不能再参与
			throw new BizException(ErrorCode.E0200122.getDesc()).withCode(ErrorCode.E0200122.getErrorCode());
		}
	}

	/**
	 * 团状态判断：非进行中；进行中，但是结束时间已到
	 */
	private void checkGroupEnd(HappyGroupInfoDto groupInfo) throws BizException{
		if(!JoinGroupStatusEnum.UNDER_WAY.getCode().equals(groupInfo.getGroupStatus())){
			throw new BizException(ErrorCode.E0200116.getDesc()).withCode(ErrorCode.E0200116.getErrorCode());
		}
		if(new Date().after(groupInfo.getEndTime())){
			executorService.execute(() -> remoteHappyGroupInfoAppService.modifyToFailure(groupInfo.getId(), groupInfo.getGroupItemId()));
			throw new BizException(ErrorCode.E0200116.getDesc()).withCode(ErrorCode.E0200116.getErrorCode());
		}
	}

	/**
	 * 参团成功可领奖
	 */
	private JoinResultVO canExchangeInfo(HappyGroupItemDto groupItem, Boolean isOwner){
		JoinResultVO joinResultVO = new JoinResultVO();
		joinResultVO.setWinStatus(YesOrNoEnum.YES.getCode());
		joinResultVO.setNeedMemberCount(groupItem.getGroupNumber());
		joinResultVO.setPrizeName(isOwner ? groupItem.getOwnerPrizeName() : groupItem.getMemberPrizeName());
		joinResultVO.setPrizeType(groupItem.getPrizeType());
		if(!ItemTypeEnums.TypeCredits.getName().equals(groupItem.getPrizeType())){
			AppItemDto appItemDto = goodsCacheService.findAppItemById(groupItem.getMemberPrizeId());
			if (null != appItemDto) {
				if(appItemDto.getIsOwner()){
					joinResultVO.setPrizeImage(getPrizeImage(appItemDto, null));
				}else {
					ItemDto itemDto = remoteDuibaItemGoodsService.find(appItemDto.getItemId()).getResult();
					joinResultVO.setPrizeImage(getPrizeImage(appItemDto, itemDto));
				}
			}
		}
		return joinResultVO;
	}

	/**
	 * 参团
	 */
	private JoinResultVO joinGroupCheck(HappyGroupConfigDto groupConfig, HappyGroupInfoDto groupInfo, Long consumerId) throws BizException{
		String key = RedisKeyFactory.K193.toString() + groupConfig.getId() + "_" + consumerId;
		try (RedisLock lock = redisAtomicClient.getLock(key, 3)){
			if(Objects.isNull(lock)){
				throw new BizException(ResultCode.C100804.getDescription()).withCode(ResultCode.C100804.getCode());
			}
			// 判断参团次数
			getRemainJoinGroupTime(groupConfig, consumerId);
			// 获取拼团商品
			HappyGroupItemDto groupItem = checkGroupItem(groupInfo.getGroupItemId(), groupConfig.getId());
			// 判断团的参与人数，若没满，则+1
			Integer groupMemberNumber = checkGroupJoinNumber(groupInfo.getId(), groupInfo.getGroupNumber(), groupItem.getExpiredTimeSecond());
			// 新增参团记录
			addJoinGroupRecord(groupInfo, consumerId, YesOrNoEnum.NO.getCode());

			if(groupMemberNumber.equals(groupInfo.getGroupNumber())){
				// 如果人数刚好满了，拼团成功
				remoteHappyGroupInfoAppService.modifyToSuccess(groupInfo.getId());
				return canExchangeInfo(groupItem, false);
			}else{
				JoinResultVO joinResultVO = new JoinResultVO();
				joinResultVO.setWinStatus(YesOrNoEnum.NO.getCode());
				joinResultVO.setRemainMemberCount(groupInfo.getGroupNumber() - groupMemberNumber);
				joinResultVO.setItemName(groupItem.getItemName());
				return joinResultVO;
			}
		}catch (BizException e){
			LOGGER.info("拼拼乐活动参团失败", e);
			throw new BizException(e.getMessage()).withCode(e.getCode());
		}catch (Exception e){
			LOGGER.warn("拼拼乐活动参团异常", e);
			throw new BizException("参团异常");
		}
	}

	/**
	 * 获取参团剩余次数
	 */
	private void getRemainJoinGroupTime(HappyGroupConfigDto groupConfig, Long consumerId) throws BizException{
		Date recordCreate = null;
		if (LimitScopeEnum.EVERY_DAY.getId().equals(groupConfig.getJoinGroupLimitType())) {
			recordCreate = DateUtils.getDayDate(new Date());
		}
		Integer hasJoinTime = remoteHappyGroupRecordAppService.getCountByConfigAndCIdAndType(groupConfig.getId(), consumerId, YesOrNoEnum.NO.getCode(), recordCreate);
		if(null != hasJoinTime && hasJoinTime.compareTo(groupConfig.getJoinGroupLimit()) >= 0){
			if (LimitScopeEnum.EVERY_DAY.getId().equals(groupConfig.getJoinGroupLimitType())) {
				throw new BizException(ErrorCode.E0200117.getDesc()).withCode(ErrorCode.E0200117.getErrorCode());
			}else {
				throw new BizException(ErrorCode.E0200118.getDesc()).withCode(ErrorCode.E0200118.getErrorCode());
			}
		}
	}

	/**
	 * 判断参团人数并增加
	 */
	private Integer checkGroupJoinNumber(Long groupId, Integer groupNumber, Long expiredTime) throws BizException{
		String groupNumberKey = redisKeyForGroupNumber(groupId);
		String groupMemberNumberStr = stringRedisTemplate.opsForValue().get(groupNumberKey);
		if(StringUtils.isBlank(groupMemberNumberStr)){
			// redis数据为空，异常场景
			return checkGroupJoinNumberReal(groupId, groupNumber, groupNumberKey, expiredTime);
		}
		Integer groupMemberNumber = Integer.valueOf(groupMemberNumberStr);
		if(groupMemberNumber.compareTo(groupNumber) >= 0){
			throw new BizException(ErrorCode.E0200119.getDesc()).withCode(ErrorCode.E0200119.getErrorCode());
		}
		Long lastNumber = stringRedisTemplate.opsForValue().increment(groupNumberKey, 1);
		stringRedisTemplate.expire(groupNumberKey, expiredTime + 300, TimeUnit.SECONDS);
		groupMemberNumber = null == lastNumber ? 0 : lastNumber.intValue();
		if(groupMemberNumber.compareTo(groupNumber) > 0){
			stringRedisTemplate.opsForValue().increment(groupNumberKey, -1);
			stringRedisTemplate.expire(groupNumberKey, expiredTime + 300, TimeUnit.SECONDS);
			throw new BizException(ErrorCode.E0200119.getDesc()).withCode(ErrorCode.E0200119.getErrorCode());
		}
		return groupMemberNumber;
	}

	/**
	 * 判断参团人数并增加，redis数据为空，异常
	 * 这种情况基本不会出现：1、redis值已过期，那么团已结束；2、redis挂了
	 */
	private Integer checkGroupJoinNumberReal(Long groupId, Integer groupNumber, String groupNumberKey, Long expiredTime) throws BizException{
		String key = RedisKeyFactory.K191.toString() + groupId;
		try (RedisLock lock = redisAtomicClient.getLock(key, 2)){
			if(Objects.isNull(lock)){
				throw new BizException(ResultCode.C100804.getDescription()).withCode(ResultCode.C100804.getCode());
			}
			Integer groupMemberNumber = getGroupMemberCountReal(groupId);
			if(groupMemberNumber.compareTo(groupNumber) >= 0){
				throw new BizException(ErrorCode.E0200119.getDesc()).withCode(ErrorCode.E0200119.getErrorCode());
			}
			groupMemberNumber = groupMemberNumber + 1;
			stringRedisTemplate.opsForValue().set(groupNumberKey, String.valueOf(groupMemberNumber), expiredTime + 300, TimeUnit.SECONDS);
			return groupMemberNumber;
		}catch (BizException e){
			LOGGER.info("拼拼乐活动参团失败", e);
			throw new BizException(e.getMessage()).withCode(e.getCode());
		}catch (Exception e){
			LOGGER.warn("拼拼乐活动参团异常", e);
			throw new BizException("参团异常");
		}
	}

	@Override
	public String openPrize(Long appId, ConsumerDto consumerDto, Long groupId, HttpServletRequest request) throws BizException {
		if(null == consumerDto || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())){
			throw new BizException(CommonConstants.MSG_NOT_LOGIN_TIPS).withCode(ResultCode.*********.getCode());
		}
		HappyGroupRecordDto record = remoteHappyGroupRecordAppService.getByGroupIdAndConsumerId(groupId, consumerDto.getId());
		// 校验拼团记录状态
		checkRecordPrizeStatus(record);
		// 获取拼团商品
		HappyGroupItemDto groupItem = checkGroupItem(record.getGroupItemId(), null);
		// 组装发奖入参
		PlaceOrderRequest placeOrderRequest = assemblePlaceOrderRequest(appId, consumerDto, request, record, groupItem);
		// 调发奖接口进行发奖
		PlaceOrderResponse placeOrderResponse = remoteActivityOrderService.placeOrder(placeOrderRequest);
		if(!placeOrderResponse.isSuccess()){
			LOGGER.warn("良品铺子拼拼乐活动-开奖失败，传参：{}，开奖返回信息：{}", JSON.toJSON(placeOrderRequest), JSON.toJSON(placeOrderResponse));
			throw new BizException("开奖失败");
		}
		String orderNum = placeOrderResponse.getOrderNum();
		// 将参团记录状态更新为已领奖/领奖中，同时将返回的子订单号存入参团记录中
		Integer recordStatus = ItemTypeEnums.TypeCredits.getName().equals(groupItem.getPrizeType())
				? HappyGroupRecordStatusEnum.EXCHANGE_PROCESSING.getCode() : HappyGroupRecordStatusEnum.EXCHANGE_SUCCESS.getCode();
		remoteHappyGroupRecordAppService.modifyStatusById(record.getId(), recordStatus, orderNum);
		return orderNum;
	}

	/**
	 * 拼团记录状态校验
	 */
	private void checkRecordPrizeStatus(HappyGroupRecordDto record) throws BizException{
		if(null == record){
			throw new BizException("没有参团记录");
		}
		if(HappyGroupRecordStatusEnum.UNDER_WAY.getCode().equals(record.getRecordStatus())
				|| HappyGroupRecordStatusEnum.FAILURE.getCode().equals(record.getRecordStatus())){
			throw new BizException("无权领奖");
		}
		if(HappyGroupRecordStatusEnum.EXCHANGE_PROCESSING.getCode().equals(record.getRecordStatus())){
			throw new BizException(ErrorCode.E0200120.getDesc()).withCode(ErrorCode.E0200120.getErrorCode());
		}
		if(HappyGroupRecordStatusEnum.EXCHANGE_SUCCESS.getCode().equals(record.getRecordStatus())){
			throw new BizException(ErrorCode.E0200121.getDesc()).withCode(ErrorCode.E0200121.getErrorCode());
		}
	}

	/**
	 * 组装发奖入参
	 */
	private PlaceOrderRequest assemblePlaceOrderRequest(Long appId, ConsumerDto consumerDto, HttpServletRequest request, HappyGroupRecordDto record, HappyGroupItemDto groupItem) throws BizException{
		if(ItemTypeEnums.TypeCredits.getName().equals(groupItem.getPrizeType())){
			// 加积分类型的奖品
			PlaceOrderByCreditsRequest placeOrderByCreditsRequest = new PlaceOrderByCreditsRequest();
			placeOrderByCreditsRequest.setAppId(appId);
			placeOrderByCreditsRequest.setConsumerId(consumerDto.getId());
			placeOrderByCreditsRequest.setPartnerUserId(consumerDto.getPartnerUserId());
			placeOrderByCreditsRequest.setActivityId(record.getActivityConfigId());
			placeOrderByCreditsRequest.setActivityType(ActivityUniformityTypeEnum.HappyGroup.toString());
			placeOrderByCreditsRequest.setPrizeName("加积分");
			placeOrderByCreditsRequest.setCredits(YesOrNoEnum.YES.getCode().equals(record.getOwnerType())
					? Long.valueOf(groupItem.getOwnerPrizeName()) : Long.valueOf(groupItem.getMemberPrizeName()));
			placeOrderByCreditsRequest.setRequestParams(RequestParams.parse(request));
			placeOrderByCreditsRequest.setCallbackTopic(rocketMqMessageTopic.getHappyGroupCreditsCallBack());
			placeOrderByCreditsRequest.setCallbackTag(RocketMqTagEnum.PRI_ADD_ACTIVITY_CREDITS.getTag());
			HappyGroupMQMessage happyGroupMQMessage = new HappyGroupMQMessage();
			happyGroupMQMessage.setRecordId(record.getId());
			placeOrderByCreditsRequest.setTransfer(JSON.toJSONString(happyGroupMQMessage));
			return placeOrderByCreditsRequest;
		}
		// 其他类型奖品
		PlaceOrderByItemRequest placeOrderByItemRequest = new PlaceOrderByItemRequest();
		placeOrderByItemRequest.setAppId(appId);
		placeOrderByItemRequest.setConsumerId(consumerDto.getId());
		placeOrderByItemRequest.setPartnerUserId(consumerDto.getPartnerUserId());
		placeOrderByItemRequest.setItemType(groupItem.getPrizeType());
		placeOrderByItemRequest.setPrizeName(YesOrNoEnum.YES.getCode().equals(record.getOwnerType())
				? groupItem.getOwnerPrizeName() : groupItem.getMemberPrizeName());

		Long appItemId = YesOrNoEnum.YES.getCode().equals(record.getOwnerType()) ? groupItem.getOwnerPrizeId() : groupItem.getMemberPrizeId();
		AppItemDto appItemDto = goodsCacheService.findAppItemById(appItemId);
		if(null == appItemDto){
			LOGGER.info("奖品{}不存在", appItemId);
			throw new BizException("奖品不存在");
		}
		placeOrderByItemRequest.setAppItemId(appItemId);
		placeOrderByItemRequest.setItemId(appItemDto.getIsOwner() ? null : appItemDto.getItemId());
		if(ItemDto.TypeVirtual.equals(appItemDto.getType())){
			placeOrderByItemRequest.setFacePrice(getDegree(appItemDto));
		}
		placeOrderByItemRequest.setRequestParams(RequestParams.parse(request));
		return placeOrderByItemRequest;
	}

	/**
	 * 虚拟商品获取档位
	 */
	private String getDegree(AppItemDto appItemDto) throws BizException{
		PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
		if(!pd.isSingleDegree() || pd.getCustomDegreeMap() == null || pd.getCustomDegreeMap().isEmpty()){
			LOGGER.info("奖品{}为多档位的虚拟商品，不支持", appItemDto.getId());
			throw new BizException("虚拟商品发奖异常");
		}
		Set<String> keySet = pd.getCustomDegreeMap().keySet();
		Iterator<String> keySetIter = keySet.iterator();
		String degree = null;
		if (keySetIter.hasNext()) {
			degree = keySetIter.next();
		}
		return degree;
	}

	@Override
	public List<RecordInfoVO> getRecordList(Long operatingActivityId, Integer groupStatus, Integer pageNo,
			Integer pageSize) throws BizException {
		ConsumerDto consumerDto = RequestLocal.getConsumerDO();
		if(null == consumerDto || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())){
			throw new BizException(CommonConstants.MSG_NOT_LOGIN_TIPS).withCode(ResultCode.*********.getCode());
		}
		if(null == operatingActivityId){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(operatingActivityId);
		if(null == operatingActivityDto || null == operatingActivityDto.getActivityId()){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		if(!Objects.equals(RequestLocal.getAppId(), operatingActivityDto.getAppId())){
			throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getErrorCode());
		}
		// 获取参团记录
		HappyGroupRecordPageParam happyGroupRecordPageParam = new HappyGroupRecordPageParam();
		happyGroupRecordPageParam.setGroupConfigId(operatingActivityDto.getActivityId());
		happyGroupRecordPageParam.setConsumerId(consumerDto.getId());
		happyGroupRecordPageParam.setPageNum(pageNo);
		happyGroupRecordPageParam.setPageSize(pageSize);
		happyGroupRecordPageParam.setRecordStatus(groupStatus);
		List<HappyGroupRecordDto> recordList = remoteHappyGroupRecordAppService.getPage(happyGroupRecordPageParam);
		if(CollectionUtils.isEmpty(recordList)){
			return Collections.emptyList();
		}
		// 获取拼团商品列表
		List<HappyGroupItemDto> groupItemList = remoteHappyGroupItemAppService.getByIds(
				recordList.stream().map(HappyGroupRecordDto::getGroupItemId).collect(Collectors.toList()));
		Map<Long, HappyGroupItemDto> groupItemMap = groupItemList.stream().collect(Collectors.toMap(HappyGroupItemDto::getId, x->x));
		List<RecordInfoVO> recordInfoVOList = recordList.stream().map(x->assembleRecordInfo(groupStatus, x, groupItemMap.get(x.getGroupItemId())))
		                 .collect(Collectors.toList());
		// 如果是进行中的，设置成团所需剩余人数
		if(JoinGroupStatusEnum.UNDER_WAY.getCode().equals(groupStatus)){
			setRemainCount(recordInfoVOList);
		}
		return recordInfoVOList;
	}

	/**
	 * 组装拼团记录信息
	 */
	private RecordInfoVO assembleRecordInfo(Integer groupStatus, HappyGroupRecordDto record, HappyGroupItemDto groupItem){
		RecordInfoVO recordInfoVO = new RecordInfoVO();
		recordInfoVO.setGroupId(record.getGroupInfoId());
		recordInfoVO.setOwner(YesOrNoEnum.YES.getCode().equals(record.getOwnerType()));
		if(null != groupItem){
			recordInfoVO.setItemId(groupItem.getId());
			recordInfoVO.setNeedMemberCount(groupItem.getGroupNumber());
			recordInfoVO.setPrizeType(groupItem.getPrizeType());
			recordInfoVO.setItemName(YesOrNoEnum.YES.getCode().equals(record.getOwnerType())
					? groupItem.getOwnerPrizeName() : groupItem.getMemberPrizeName());
		}
		if(JoinGroupStatusEnum.UNDER_WAY.getCode().equals(groupStatus)){
			recordInfoVO.setGroupEndTime(record.getEndTime().getTime());
		}else if(JoinGroupStatusEnum.SUCCESS.getCode().equals(groupStatus)){
			recordInfoVO.setExchangeStatus(record.getRecordStatus());
		}
		return recordInfoVO;
	}

	/**
	 * 设置成团所需剩余人数
	 */
	private void setRemainCount(List<RecordInfoVO> recordInfoVOList){
		if(CollectionUtils.isEmpty(recordInfoVOList)){
			return;
		}
		List<String> keys = recordInfoVOList.stream().map(x->redisKeyForGroupNumber(x.getGroupId())).collect(Collectors.toList());
		List<String> groupNumbers = stringRedisTemplate.opsForValue().multiGet(keys);
		for(int i = 0; i < recordInfoVOList.size(); i ++){
			RecordInfoVO recordInfoVO = recordInfoVOList.get(i);
			String groupNumberStr = groupNumbers.get(i);
			Integer groupNumber = StringUtils.isBlank(groupNumberStr) ? getGroupMemberCountReal(recordInfoVO.getGroupId()) : Integer.valueOf(groupNumberStr);
			recordInfoVO.setRemainMemberCount(Math.max(recordInfoVO.getNeedMemberCount() - groupNumber, 0));
		}
	}

	@Override
	public String addWinner(Long activityId, List<String> winnerList) throws BizException {
		if(null == activityId){
			throw new BizException("入库活动id没传");
		}
		if(CollectionUtils.isEmpty(winnerList)){
			throw new BizException("轮播数据没传");
		}
		String key = hbaseKeyForWinner(activityId);
		String valueOld = remoteHbConsisHashKvService.getStringByKey(key);
		JSONArray jsonArray = StringUtils.isBlank(valueOld) ? new JSONArray() : JSON.parseArray(valueOld);
		jsonArray.addAll(winnerList);
		String valueNew = jsonArray.toString();
		remoteHbConsisHashKvService.upsertKStrV(key, valueNew);
		return remoteHbConsisHashKvService.getStringByKey(key);
	}

	@Override
	public String getWinner(Long activityId) {
		return remoteHbConsisHashKvService.getStringByKey(hbaseKeyForWinner(activityId));
	}

	@Override
	public String coverWinner(Long activityId, List<String> winnerList) throws BizException {
		if(null == activityId){
			throw new BizException("入库活动id没传");
		}
		String key = hbaseKeyForWinner(activityId);
		String value = JSON.toJSONString(winnerList);
		remoteHbConsisHashKvService.upsertKStrV(key, value);
		return remoteHbConsisHashKvService.getStringByKey(key);
	}

	/**
	 * 记录统一访问日志
	 */
	private void accessLog(Long operatingActivityId, Long groupConfigId, int pageBizId, String partnerUserId, Long credits){
		AccessLogFilter.putExPair("suc", 1);
		AccessLogFilter.putExPair("activityid", operatingActivityId);
		AccessLogFilter.putExPair("id", groupConfigId);
		AccessLogFilter.putExPair("activitytype", ActivityUniformityTypeEnum.HappyGroup.getCode());
		AccessLogFilter.putExPair("pageBizId", pageBizId);
		AccessLogFilter.putExPair("loginStatus", ConsumerCheckUtil.isNotLoginUser(partnerUserId) ? 2 : 1);
		AccessLogFilter.putExPair("userCredits", credits);
	}
}
