package com.duiba.activity.accessweb.service.marketbet.impl;

import cn.com.duiba.activity.center.api.dto.betv2.BetV2RecordDto;
import cn.com.duiba.activity.center.api.enums.bet.BetExchangeStatusEnum;
import cn.com.duiba.activity.center.api.remoteservice.betv2.RemoteBetV2RecordService;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.prize.PrizeRelTypeEnum;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.prize.center.api.dto.prize.ActivityCommonOptionDto;
import cn.com.duiba.prize.center.api.remoteservice.prize.RemoteActivityOptionBackendService;
import cn.com.duiba.prize.center.api.remoteservice.prize.RemoteActivityPrizeProviderService;
import cn.com.duiba.prize.center.api.request.provideprize.ProvidePrizeItemRequest;
import cn.com.duiba.prize.center.api.response.provideprize.ProvidePrizeResponse;
import com.alibaba.fastjson.JSON;
import com.duiba.activity.accessweb.service.marketbet.MarketBetService;
import com.duiba.activity.accessweb.vo.marketbet.MarketBetTakePrizeStatusVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/10/31
 */
@Service
public class MarketBetServiceImpl implements MarketBetService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketBetServiceImpl.class);

    @Autowired
    private RemoteActivityPrizeProviderService remoteActivityPrizeProviderService;
    @Autowired
    private RemoteActivityOptionBackendService remoteActivityOptionBackendService;
    @Autowired
    private RemoteBetV2RecordService remoteBetV2RecordService;


    @Override
    public MarketBetTakePrizeStatusVo doTakePrize(RequestParams requestParams, ConsumerDto consumer, Long optId, Long actId, Long recordId) throws BizException {
        BetV2RecordDto detailDto = remoteBetV2RecordService.findById(recordId);
        if (detailDto == null || !Objects.equals(BetExchangeStatusEnum.EXCHANGE_STATUS_WATTING.getCode(), detailDto.getExchangeStatus())) {
            throw new BizException("用户不符合领奖要求");
        }
        if (!consumer.getId().equals(detailDto.getConsumerId())) {
            throw new BizException("非法的领奖用户");
        }
        List<ActivityCommonOptionDto> options = remoteActivityOptionBackendService.findAllCommonPrize(detailDto.getBetId(), PrizeRelTypeEnum.PK_H5.getType());
        LOGGER.warn("options:{}", JSON.toJSONString(options));
        LOGGER.warn("detailDto:{}", JSON.toJSONString(detailDto));
        List<ActivityCommonOptionDto> thisUserWinOption = options.stream()
                .filter(o -> Objects.equals(o.getId(), detailDto.getCommPrizeId()))
                .collect(Collectors.toList());
        LOGGER.warn("thisUserWinOption:{}", JSON.toJSONString(thisUserWinOption));
        if (CollectionUtils.isEmpty(thisUserWinOption)) {
            LOGGER.error("PK赛独立活动-发奖失败-奖项不存在，detailId:{},betId:{}", recordId, detailDto.getBetId());
            remoteBetV2RecordService.updateExchangeStatusById(recordId, BetExchangeStatusEnum.EXCHANGE_BONUS_RUN_OUT.getCode());
            throw new BizException("奖项不存在");
        }
        ProvidePrizeItemRequest request = new ProvidePrizeItemRequest();
        request.setConsumerId(consumer.getId());
        request.setPartnerUserId(consumer.getPartnerUserId());
        request.setActivityId(actId);
        request.setOperatingActivityId(optId);
        request.setActivityType(ActivityUniformityTypeEnum.PKH5.getCode().toString());
        request.setAppItemId(thisUserWinOption.get(0).getAppItemId());
        request.setAppId(consumer.getAppId());
        request.setActivityName("PK赛活动奖励-" + thisUserWinOption.get(0).getPrizeName());
        request.setRequestParams(requestParams);
        if (null != thisUserWinOption.get(0).getFacePrice()) {
            request.setFacePrice(thisUserWinOption.get(0).getFacePrice());
        }
        ProvidePrizeResponse response = remoteActivityPrizeProviderService.providePrize(request);
        String prizeDetailUrl;
        if (response.isSuccess()) {
            //修改获奖记录的状态值
            if (StringUtils.isNotBlank(response.getMainOrderNum())) {
                prizeDetailUrl = "/crecord/recordDetailNew?orderId=" + response.getMainOrderNum();
            } else {
                prizeDetailUrl = "/activity/takePrizeNew?recordId=" + response.getRecordId();
            }
            remoteBetV2RecordService.updateExchangeStatusById(recordId, BetExchangeStatusEnum.EXCHANGE_STATUS_SUCCESS.getCode());
        } else {
            remoteBetV2RecordService.updateExchangeStatusById(recordId, BetExchangeStatusEnum.EXCHANGE_BONUS_RUN_OUT.getCode());
            LOGGER.warn("PK赛独立活动-发奖失败，detailId:{},cause:{}", recordId, response.getErrorMsg());
            throw new BizException(response.getErrorMsg());
        }
        MarketBetTakePrizeStatusVo vo = new MarketBetTakePrizeStatusVo();
        if (StringUtils.isNotBlank(prizeDetailUrl)) {
            vo.setPrizeDetailUrl(prizeDetailUrl);
        }
        vo.setOptionName(thisUserWinOption.get(0).getDescription());
        vo.setOptionPic(thisUserWinOption.get(0).getLogo());
        return vo;
    }
}
