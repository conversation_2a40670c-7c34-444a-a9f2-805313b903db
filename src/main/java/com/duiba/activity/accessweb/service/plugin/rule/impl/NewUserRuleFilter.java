package com.duiba.activity.accessweb.service.plugin.rule.impl;

import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import com.duiba.activity.accessweb.service.plugin.rule.AbstractPluginRuleFilter;
import org.springframework.stereotype.Service;

/**
 * 新人礼包弹层
 * Created by zzy on 2017/9/11.
 */
@Service
public class NewUserRuleFilter extends AbstractPluginRuleFilter {
    @Override
    public int getRuleType() {
        return ActivityPluginDto.SIGN_TYPE_NEW_MEMBER;
    }

    @Override
    protected boolean doFilterByRule(ConsumerDto consumerDto, String redisKey, boolean byTriggerTime, int remainTime) {
        //新人礼包 查询用户信息
        //活动有效期内首次登陆
        String value = getPluginRecord(consumerDto.getId(), redisKey);
        if (value != null) {
            return false;
        } else {
            if (isConsumerNewInNational(consumerDto)) {
                setPluginRecord(consumerDto.getId(), redisKey, "1");
                return true;
            } else {
                return false;
            }
        }
    }
}
