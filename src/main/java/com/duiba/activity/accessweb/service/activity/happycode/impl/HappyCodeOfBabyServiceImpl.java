package com.duiba.activity.accessweb.service.activity.happycode.impl;

import cn.com.duiba.activity.center.api.dto.happy_code.*;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.api.remoteservice.happycode.*;
import cn.com.duiba.activity.center.api.remoteservice.plugin.RemoteActivityPluginBackendService;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsBackendService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.DateUtils;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.enums.HappyCodeBabyOrderStatusEnum;
import com.duiba.activity.accessweb.enums.HappyCodeBabyPhaseStatusEnum;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.activity.happycode.HappyCodeOfBabyService;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeBabyPhaseOrderVO;
import com.duiba.activity.accessweb.vo.happycode.HappyCodeBabyVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class HappyCodeOfBabyServiceImpl implements HappyCodeOfBabyService {

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, List<HappyCodeBabyVO>> redisListService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, HappyCodeBabyVO> redisDetailService;

    //过期未领取天数
    private static final int EXPIREDAY = 7;

    @Autowired
    private RemoteHappyCodeBasicService remoteHappyCodeBasicService;

    @Autowired
    private RemoteActivityPluginBackendService remoteActivityPluginBackendService;

    @Autowired
    private RemoteHappyCodePhaseService remoteHappyCodePhaseService;

    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;

    @Autowired
    private RemoteHappyCodeOptionService remoteHappyCodeOptionService;

    @Autowired
    private RemoteDuibaItemGoodsBackendService remoteDuibaItemGoodsBackendService;

    @Autowired
    private RemoteHappyCodeOrderService remoteHappyCodeOrderService;

    @Autowired
    private RemoteHappyCodeWinRecordService remoteHappyCodeWinRecordService;

    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    @Override
    public Result<List<HappyCodeBabyVO>> listMatchs(List<Long> matchIds, List<Long> pluginIds,AppSimpleDto app ) {
        String indexKey = buildIndexKey(matchIds, pluginIds);
        List<HappyCodeBabyVO> happyCodeBabyVOs = redisListService.opsForValue().get(indexKey);
        if (happyCodeBabyVOs != null) {
            return ResultBuilder.success(happyCodeBabyVOs);
        }
        List<HappyCodePhaseDto> phaseList = remoteHappyCodePhaseService.listFirstPhaseByBasicIds(matchIds);
        if (CollectionUtils.isEmpty(phaseList)) {
            return ResultBuilder.fail(ResultCode.C100054);
        }
        List<HappyCodeBasicDto> basicList = remoteHappyCodeBasicService.findByIds(matchIds);
        List<ActivityPluginDto> pluginList = remoteActivityPluginBackendService.findPluginsByIds(pluginIds).getResult();

        List<Long> phaseIds = Lists.transform(phaseList, HappyCodePhaseDto::getId);
        List<Long> itemids = Lists.transform(phaseList, HappyCodePhaseDto::getCodeItemId);
        List<HappyCodeOptionDto> optionList = remoteHappyCodeOptionService.findByPhaseIds(phaseIds);
        DubboResult<List<ItemDto>> itemResult = remoteDuibaItemGoodsService.findByIds(itemids);
        if (!itemResult.isSuccess() || itemResult.getResult() == null) {
            return ResultBuilder.fail(ResultCode.C100055);
        }
        Map<Long, Long> happyCodePluginMap = new HashMap<>();
        for (int i = 0, len = matchIds.size(); i < len; i++) {
            happyCodePluginMap.put(matchIds.get(i), pluginIds.get(i));
        }
        happyCodeBabyVOs = parseMatchs(basicList, itemResult.getResult(), phaseList, optionList, pluginList, happyCodePluginMap,app);
        redisListService.opsForValue().set(indexKey, happyCodeBabyVOs, 60, TimeUnit.SECONDS);
        return ResultBuilder.success(happyCodeBabyVOs);
    }

    private List<HappyCodeBabyVO> parseMatchs(List<HappyCodeBasicDto> basicList, List<ItemDto> itemList,
                                              List<HappyCodePhaseDto> phaseList, List<HappyCodeOptionDto> optionList,
                                              List<ActivityPluginDto> pluginList, Map<Long, Long> happyCodePluginMap,AppSimpleDto app) {
        Map<Long, ItemDto> itemMap = Maps.uniqueIndex(itemList, ItemDto::getId);
        Map<Long, HappyCodePhaseDto> phaseBasicIdMap = Maps.uniqueIndex(phaseList, HappyCodePhaseDto::getBasicId);
        Map<Long, HappyCodeOptionDto> phaseOptionMap = Maps.uniqueIndex(optionList, HappyCodeOptionDto::getPhaseId);
        Map<Long, ActivityPluginDto> pluginMap = Maps.uniqueIndex(pluginList, ActivityPluginDto::getId);
        List<HappyCodeBabyVO> list = Lists.newArrayList();
        HappyCodeBabyVO happyCodeBabyVO;
        for (HappyCodeBasicDto basic : basicList) {
            happyCodeBabyVO = new HappyCodeBabyVO();
            HappyCodePhaseDto phaseDto = phaseBasicIdMap.get(basic.getId());
            ActivityPluginDto pluginDto = pluginMap.get(happyCodePluginMap.get(basic.getId()));
            ItemDto itemDto = itemMap.get(phaseDto.getCodeItemId());
            HappyCodeOptionDto option = phaseOptionMap.get(phaseDto.getId());
            happyCodeBabyVO.setBasicId(basic.getId());
            happyCodeBabyVO.setPhaseStatusEnum(HappyCodeBabyPhaseStatusEnum.getByCode(phaseDto.getPhaseStatus()));
            happyCodeBabyVO.setPluginId(pluginDto.getId());
            happyCodeBabyVO.setPluginCredit(calculateShowCredit(app,pluginDto.getCreditsPrice()));
            happyCodeBabyVO.setOptionImg(option.getLogo());
            happyCodeBabyVO.setOptionName(option.getPrizeName());
            happyCodeBabyVO.setOptionRemain(option.getRemaining());
            happyCodeBabyVO.setItemRemainNumber(itemDto.getRemaining());
            happyCodeBabyVO.setItemJoinNumber(itemDto.getSales());
            happyCodeBabyVO.setPhaseCodeNumber(phaseDto.getCodeCount());
            happyCodeBabyVO.setPhaseId(phaseDto.getId());
            list.add(happyCodeBabyVO);
        }
        return list;
    }

    @Override
    public Result<HappyCodeBabyVO> viewPhase(Long phaseId, Long pluginId, Boolean needCahce,AppSimpleDto app ) throws Exception {
        HappyCodeBabyVO happyCodeBabyVO;
        String detailKey = buildDetailKey(phaseId);
        if (needCahce) {
            happyCodeBabyVO = redisDetailService.opsForValue().get(detailKey);
            if (happyCodeBabyVO != null) {
                return ResultBuilder.success(happyCodeBabyVO);
            }
        }
        //获取期次信息
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (null == phase) {
            return ResultBuilder.fail(ResultCode.C100058);
        }
        //获取奖项信息
        HappyCodeOptionDto option = remoteHappyCodeOptionService.findOneByPhaseId(phaseId);
        if (null == option) {
            return ResultBuilder.fail(ResultCode.C100057);
        }
        //获取奖品价格
        ItemDto prize = remoteDuibaItemGoodsBackendService.findWithoutCache(option.getItemId());
        ItemDto item = remoteDuibaItemGoodsBackendService.findWithoutCache(phase.getCodeItemId());
        //获取插件信息
        ActivityPluginDto plugin = remoteActivityPluginBackendService.findById(pluginId).getResult();
        if (null == plugin) {
            return ResultBuilder.fail(ResultCode.C100058);
        }
        //设置赛事信息
        happyCodeBabyVO = new HappyCodeBabyVO();
        happyCodeBabyVO.setBasicId(phase.getBasicId());
        happyCodeBabyVO.setPluginId(pluginId);
        happyCodeBabyVO.setPluginCredit(calculateShowCredit(app,plugin.getCreditsPrice()));
        happyCodeBabyVO.setOptionImg(option.getLogo());
        happyCodeBabyVO.setOptionName(option.getPrizeName());
        happyCodeBabyVO.setOptionRemain(option.getRemaining());
        happyCodeBabyVO.setOptionDesc(option.getDescription());
        happyCodeBabyVO.setOptionItemId(option.getItemId());
        happyCodeBabyVO.setOptionItemName(option.getItemName());
        happyCodeBabyVO.setItemRemainNumber(item.getRemaining());
        happyCodeBabyVO.setItemFacePrice(prize.getFacePrice());
        happyCodeBabyVO.setItemJoinNumber(item.getSales());
        happyCodeBabyVO.setPhaseCodeNumber(phase.getCodeCount());
        happyCodeBabyVO.setPhaseId(phaseId);
        happyCodeBabyVO.setPhaseRule(phase.getRule());
        happyCodeBabyVO.setPhaseNo(phase.getPhaseNumber());
        happyCodeBabyVO.setPhaseStatusEnum(HappyCodeBabyPhaseStatusEnum.getByCode(phase.getPhaseStatus()));
        redisDetailService.opsForValue().set(detailKey, happyCodeBabyVO, 60, TimeUnit.SECONDS);
        return ResultBuilder.success(happyCodeBabyVO);
    }

    @Override
    public Result<List<HappyCodeBabyVO>> prizeRecord(List<Long> matchIds) {
        String prizeRecordKey = buildPrizeRecordKey(matchIds);
        List<HappyCodeBabyVO> happyCodeBabyVOS = redisListService.opsForValue().get(prizeRecordKey);
        if (CollectionUtils.isNotEmpty(happyCodeBabyVOS)) {
            return ResultBuilder.success(happyCodeBabyVOS);
        }
        //根据matchIds获取赛事第一期的期次id
        List<HappyCodePhaseDto> phases = remoteHappyCodePhaseService.listFirstPhaseByBasicIds(matchIds);
        phases = phases.stream()
                .filter(p -> p.getPhaseStatus().equals(HappyCodeBabyPhaseStatusEnum.HAVE_PRIZE.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phases)) {
            return ResultBuilder.fail(ResultCode.C100058);
        }
        List<Long> phaseIds = Lists.transform(phases, HappyCodePhaseDto::getId);
        List<HappyCodeWinRecordDto> winners = remoteHappyCodeWinRecordService.listByPhaseIds(phaseIds);
        List<HappyCodeOptionDto> options = remoteHappyCodeOptionService.findByPhaseIds(phaseIds);

        happyCodeBabyVOS = getWinnerRecord(phases, winners, options);
        redisListService.opsForValue().set(prizeRecordKey, happyCodeBabyVOS, 60, TimeUnit.SECONDS);
        return ResultBuilder.success(happyCodeBabyVOS);
    }

    private List<HappyCodeBabyVO> getWinnerRecord(List<HappyCodePhaseDto> phases, List<HappyCodeWinRecordDto> winners, List<HappyCodeOptionDto> options) {
        List<HappyCodeBabyVO> happyCodeBabyVOS = Lists.newArrayList();
        Map<Long, HappyCodePhaseDto> phaseDtoMap = Maps.uniqueIndex(phases, HappyCodePhaseDto::getId);
        Map<Long, HappyCodeOptionDto> optionDtoMap = Maps.uniqueIndex(options, HappyCodeOptionDto::getPhaseId);

        List<Long> consumerIds = winners.stream()
                .map(o -> o.getConsumerId())
                .collect(Collectors.toList());
        List<ConsumerExtraDto> consumerExtraDOList = remoteConsumerExtraService.findAllByConsumerIds(consumerIds).getResult();
        Map<Long, ConsumerExtraDto> consumerExtraMap = Maps.uniqueIndex(consumerExtraDOList, ConsumerExtraDto::getConsumerId);

        HappyCodeBabyVO happyCodeBabyVO;
        for (HappyCodeWinRecordDto winner : winners) {
            happyCodeBabyVO = new HappyCodeBabyVO();
            happyCodeBabyVO.setPhaseId(winner.getPhaseId());
            happyCodeBabyVO.setPhaseNo(phaseDtoMap.get(winner.getPhaseId()).getPhaseNumber());
            happyCodeBabyVO.setOptionName(optionDtoMap.get(winner.getPhaseId()).getPrizeName());
            happyCodeBabyVO.setPhaseStatusEnum(HappyCodeBabyPhaseStatusEnum.getByCode(phaseDtoMap.get(winner.getPhaseId()).getPhaseStatus()));
            HappyCodeBabyPhaseOrderVO order = new HappyCodeBabyPhaseOrderVO();
            order.setHappyCode(winner.getWinCode());
            order.setConsummerId(winner.getConsumerId());

            ConsumerExtraDto consumer = consumerExtraMap.get(winner.getConsumerId());
            if(null != consumer){
                order.setAvatar(consumer.getAvatar());
                order.setNickname(consumer.getNickname());
            }
            order.setTime(winner.getGmtCreate());
            order.setPartnerUserId(winner.getPartnerUserId());
            happyCodeBabyVO.setOrders(Lists.newArrayList(order));
            happyCodeBabyVOS.add(happyCodeBabyVO);
        }
        return happyCodeBabyVOS;
    }

    @Override
    public Result<HappyCodeBabyVO> listMyCode(Long phaseId, Long consumerId, Boolean needCache) {
        String myCodeKey = buildMyCodeKey(phaseId, consumerId);
        HappyCodeBabyVO happyCodeBabyVO;
        if (needCache) {
            happyCodeBabyVO = redisDetailService.opsForValue().get(myCodeKey);
            if (happyCodeBabyVO != null) {
                return ResultBuilder.success(happyCodeBabyVO);
            }
        }
        //获取期次信息
        HappyCodePhaseDto phase = remoteHappyCodePhaseService.findByPhaseId(phaseId);
        if (null == phase) {
            return ResultBuilder.fail(ResultCode.C100058);
        }
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrderByPhaseIdAndConsumerId(phaseId, consumerId);
        happyCodeBabyVO = getOrderVo(orders, phase);
        redisDetailService.opsForValue().set(myCodeKey, happyCodeBabyVO, 60, TimeUnit.SECONDS);
        return ResultBuilder.success(happyCodeBabyVO);
    }

    private HappyCodeBabyVO getOrderVo(List<HappyCodeOrderDto> orders, HappyCodePhaseDto phase) {
        HappyCodeBabyVO happyCodeBabyVO = new HappyCodeBabyVO();
        happyCodeBabyVO.setPhaseNo(phase.getPhaseNumber());
        happyCodeBabyVO.setPhaseId(phase.getId());
        List<HappyCodeBabyPhaseOrderVO> orderVOS = Lists.newArrayList();
        if (orders == null || orders.size() <= 0) {
            happyCodeBabyVO.setOrders(orderVOS);
            return happyCodeBabyVO;
        }
        HappyCodeBabyPhaseOrderVO happyCodeBabyPhaseOrderVO;
        for (HappyCodeOrderDto order : orders) {
            happyCodeBabyPhaseOrderVO = new HappyCodeBabyPhaseOrderVO();
            happyCodeBabyPhaseOrderVO.setHappyCode(order.getHappyCode());
            happyCodeBabyPhaseOrderVO.setStatsEnum(HappyCodeBabyOrderStatusEnum.getByCode(order.getExchangeStatus()));
            orderVOS.add(happyCodeBabyPhaseOrderVO);
        }
        happyCodeBabyVO.setOrders(orderVOS);
        return happyCodeBabyVO;
    }

    @Override
    public Result<List<HappyCodeBabyVO>> lastMatchOfMyCode(List<Long> matchIds, Long consumerId) {
        String myLastCodeKey = buildMyLastCodeKey(matchIds, consumerId);
        List<HappyCodeBabyVO> list = redisListService.opsForValue().get(myLastCodeKey);
        if (CollectionUtils.isNotEmpty(list)) {
            return ResultBuilder.success(list);
        }
        //获取期次信息
        List<HappyCodePhaseDto> phases = remoteHappyCodePhaseService.listFirstPhaseByBasicIds(matchIds);
        if (CollectionUtils.isEmpty(phases)) {
            return ResultBuilder.fail(ResultCode.C100058);
        }
        List<Long> phaseIds = Lists.transform(phases, HappyCodePhaseDto::getId);
        List<HappyCodeOrderDto> orders = remoteHappyCodeOrderService.findOrders(phaseIds, consumerId);
        if (CollectionUtils.isEmpty(orders)) {
            return ResultBuilder.success(Lists.newArrayList());
        }
        List<HappyCodeOptionDto> options = remoteHappyCodeOptionService.findByPhaseIds(phaseIds);
        Map<Long, HappyCodeOptionDto> optionDtoMap = Maps.uniqueIndex(options, HappyCodeOptionDto::getPhaseId);
        Map<Long, HappyCodeBabyVO> orderMap = getPhaseMap(orders);
        list = phases.stream()
                .filter(p -> orderMap.get(p.getId()) != null)
                .map(p -> {
                    Long pid = p.getId();
                    HappyCodeBabyVO happyCodeBabyVO = new HappyCodeBabyVO();
                    happyCodeBabyVO.setPhaseCodeNumber(p.getCodeCount());
                    happyCodeBabyVO.setOptionName(optionDtoMap.get(pid).getPrizeName());
                    happyCodeBabyVO.setPhaseId(pid);
                    happyCodeBabyVO.setPhaseNo(p.getPhaseNumber());
                    happyCodeBabyVO.setOrderStatus(orderMap.get(pid).getOrderStatus());
                    happyCodeBabyVO.setOrders(orderMap.get(pid).getOrders());
                    happyCodeBabyVO.setPhaseTitle(p.getTitle());
                    return happyCodeBabyVO;
                })
                .collect(Collectors.toList());
        redisListService.opsForValue().set(myLastCodeKey, list, 60, TimeUnit.SECONDS);
        return ResultBuilder.success(list);
    }

    private Map<Long, HappyCodeBabyVO> getPhaseMap(List<HappyCodeOrderDto> orders) {
        Map<Long, HappyCodeBabyVO> map = Maps.newHashMap();
        HappyCodeBabyVO happyCodeBabyVO;
        HappyCodeBabyPhaseOrderVO happyCodeBabyPhaseOrderVO;
        Date date;
        for (HappyCodeOrderDto order : orders) {
            happyCodeBabyPhaseOrderVO = new HappyCodeBabyPhaseOrderVO();
            happyCodeBabyPhaseOrderVO.setHappyCode(order.getHappyCode());
            happyCodeBabyPhaseOrderVO.setStatsEnum(HappyCodeBabyOrderStatusEnum.getByCode(order.getExchangeStatus()));
            happyCodeBabyPhaseOrderVO.setTime(order.getGmtCreate());
            if (order.getExchangeStatus() == HappyCodeOrderDto.EXCHANGE_STATUS_WAIT) {
                date = new Date();
                if (DateUtils.daysBetween(order.getGmtModified(), date) > EXPIREDAY) {
                    happyCodeBabyPhaseOrderVO.setStatsEnum(HappyCodeBabyOrderStatusEnum.HAVE_EXPIRE);
                }
            }
            if (map.get(order.getPhaseId()) == null) {
                happyCodeBabyVO = new HappyCodeBabyVO();
                happyCodeBabyVO.setOrderStatus(happyCodeBabyPhaseOrderVO.getStatus());
                happyCodeBabyVO.setOrders(Lists.newArrayList(happyCodeBabyPhaseOrderVO));
                map.put(order.getPhaseId(), happyCodeBabyVO);
            } else {
                happyCodeBabyVO = map.get(order.getPhaseId());
                if (happyCodeBabyVO.getOrderStatus() < happyCodeBabyPhaseOrderVO.getStatus()) {
                    happyCodeBabyVO.setOrderStatus(happyCodeBabyPhaseOrderVO.getStatus());
                }
                happyCodeBabyVO.getOrders().add(happyCodeBabyPhaseOrderVO);
            }
        }
        return map;
    }

    private String buildIndexKey(List<Long> matchIds, List<Long> pluginIds) {
        //首页集合
        return RedisKeyFactory.K124.toString() + matchIds.toString() + "_" + pluginIds.toString();
    }

    private String buildDetailKey(Long phaseId) {
        //赛事详情
        return RedisKeyFactory.K125.toString() + phaseId;
    }

    private String buildMyCodeKey(Long phaseId, Long consumerId) {
        //我的本期开心码
        return RedisKeyFactory.K126.toString() + phaseId + "_" + consumerId;
    }

    private String buildPrizeRecordKey(List<Long> matchIds) {
        //往期中奖纪录
        return RedisKeyFactory.K127.toString() + matchIds.toString();
    }

    private String buildMyLastCodeKey(List<Long> matchIds, Long consumerId) {
        //我的往期开心码
        return RedisKeyFactory.K128.toString() + matchIds.toString() + "_" + consumerId;
    }

    private Integer calculateShowCredit(AppSimpleDto app, Integer credits) {
        //人民币模式开启-直接显示元
        //人民币模式关闭-读取app配置积分-乘以积分汇率
        Integer creditsInt ;
        if (!app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
            creditsInt = new BigDecimal(app.getCreditsRate())
                    .divide(new BigDecimal(100))
                    .multiply(new BigDecimal(credits))
                    .intValue();
        } else {
            creditsInt = new BigDecimal(app.getCreditsRate())
                    .divide(new BigDecimal(100))
                    .intValue();
        }

        return Integer.max(creditsInt, 1);
    }

}
