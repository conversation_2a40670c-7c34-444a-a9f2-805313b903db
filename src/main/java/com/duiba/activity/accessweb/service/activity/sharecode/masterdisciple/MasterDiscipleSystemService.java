package com.duiba.activity.accessweb.service.activity.sharecode.masterdisciple;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMainInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMasterInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMyRedPacketsVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.DiscipleInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.DrawsResultVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 18/10/30
 * @description 师徒体系相关接口
 */
public interface MasterDiscipleSystemService {

	/**
	 * 百姓网活动主页面数据
	 */
	BXWMainInfoVO getMainPageInfo(HttpServletRequest request, Long appId, ConsumerDto consumerDto, Long duibaActivityId) throws BizException;

	/**
	 * 百姓网获取分享者信息
	 */
	BXWMasterInfoVO getMasterInfo(HttpServletRequest request, String shareCode, Long appId, ConsumerDto consumerDto, Long activityId, Long duibaActivityId) throws BizException;

	/**
	 * 百姓网我的红包
	 */
	BXWMyRedPacketsVO getMyRedPacket(Long activityId, Integer pageNo, Integer pageSize, Long appId, ConsumerDto consumerDto) throws BizException;

	/**
	 * 提现金额校验
	 */
	void checkDrawAmount(String drawAmount, Long appId, ConsumerDto consumerDto) throws BizException;

	/**
	 * 提现
	 */
	Long withDraw(HttpServletRequest request, AppSimpleDto appSimpleDto, ConsumerDto consumerDto, Long duibaActivityId, String userName, String alipay, String drawAmount) throws BizException;

	/**
	 * 轮询提现结果
	 */
	DrawsResultVO withdrawStatus(Long drawRecordId, Long appId, Long consumerId) throws BizException;

	/**
	 * 提现记录
	 */
	List<DrawsResultVO> drawRecords(Integer pageNo, Integer pageSize, Long consumerId) throws BizException;

	/**
	 * 我的徒弟
	 */
	List<DiscipleInfoVO> getMyDisciples(Long consumerId, Long operatingActivityId, Integer pageNo, Integer pageSize) throws BizException;

	/**
	 * 我的徒孙
	 */
	List<DiscipleInfoVO> getDisciplesOfMyDisciple(Long consumerId, Long operatingActivityId, Integer pageNo, Integer pageSize) throws BizException;
}
