package com.duiba.activity.accessweb.service.handler.insurance;

import java.util.Map;
import java.util.concurrent.Semaphore;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import com.duiba.activity.accessweb.service.handler.insurance.param.UserInfoParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.com.duiba.activity.center.api.dto.activity.InsuranceResultDto;
import cn.com.duiba.activity.center.api.dto.activity.InsuranceUserInfoDto;

import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.service.handler.insurance.param.HeiNiuRequestParam;
import com.duiba.activity.accessweb.service.handler.insurance.param.HttpRequest;
import com.duiba.activity.accessweb.tool.DomainConstantUtil;
import com.duiba.activity.accessweb.tool.URLConnectionBuilder;

/**
 * 黑牛保险
 *
 * <p>HeiNiuInsuranceHandler</p>
 * <AUTHOR>
 * @since 2017年1月20日 上午10:57:42
 */
@Component
public class HeiNiuInsuranceHandler implements InsuranceHandler {
    private static Logger log = LoggerFactory.getLogger(HeiNiuInsuranceHandler.class);
	private Semaphore semaphore = new Semaphore(50);

	@Override
	public InsuranceResultDto handle(InsuranceUserInfoDto userInfo, Map<String, String> extParams) {
		boolean success = semaphore.tryAcquire();
	    if (!success) {
    		throw new AccessActivityRuntimeException("系统繁忙!");
    	}
	    
	  //保存投保结果
	    InsuranceResultDto resultDto = new InsuranceResultDto();
	    resultDto.setUserId(userInfo.getId());
	    resultDto.setConsumerId(RequestLocal.getCid());
	    resultDto.setChannelName(UserInfoParam.TYPE_HEINIU);
	    resultDto.setHtmlId(Long.valueOf(extParams.get("id")));
	    try {
	    	HeiNiuRequestParam requestParam = new HeiNiuRequestParam(userInfo, extParams);
			String url = DomainConstantUtil.getHeiniuInsureUrl();
			
			URLConnectionBuilder urlConnectionBuilder = new URLConnectionBuilder(url);
//			urlConnectionBuilder.header("Accept", "application/json");
//			urlConnectionBuilder.header("Content-Type", "application/json");
			urlConnectionBuilder.header("Connection", "close");
			urlConnectionBuilder.timeout(5 * 1000);
			
			HttpRequest httpRequest = new HttpRequest();
			httpRequest.setRequestParam(requestParam);
			httpRequest.setUrlConnectionBuilder(urlConnectionBuilder);
			
	    	//请求投保接口
	    	JSONObject result = httpRequest.post();
	    	JSONObject data = result.getJSONObject("data");
	    	
	    	resultDto.setStatus(result.getString("status"));
	    	resultDto.setMessage(result.getString("msg"));
	    	resultDto.setInsuredOrder(data.getString("policy_no"));
	    	resultDto.setInsuredUrl(data.getString("url"));
	    	resultDto.setResultContent(result.toJSONString());
	    }catch(Exception e){
	        log.warn("error:" + e.getMessage());
	        resultDto.setStatus(ErrorCode.E9999999.getErrorCode());
            resultDto.setMessage(e.getMessage());
	    }finally {
	        semaphore.release();
	    }
	    return resultDto;
	}

}
