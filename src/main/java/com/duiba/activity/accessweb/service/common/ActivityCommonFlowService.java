package com.duiba.activity.accessweb.service.common;

import java.util.List;
import java.util.Map;

import org.javatuples.Triplet;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.exception.StatusException;

import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.questionanswer.QuestionOptionsVO;


/** 
 * ClassName:ActivityCommonFlowService.java <br/>
 * 
 * 该类抽离  下单时 公共方法  减少重复代码
 * <AUTHOR> 
 * @date 创建时间：2017年4月10日 上午11:52:39 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
public interface ActivityCommonFlowService {

	/**
	 * 1.扣除本地积分： <br/>
	 * @param consumerId
	 * @param credits
	 * @return
	 */
	void decrementCredits(Long consumerId, Long credits);
	
	
	/**
	 * 2.活动 去除事件方法  triggerAfter××××××： <br/>
	 * @param orderId
	 * @param userAgent
	 * @param transfer
	 * @param type
	 * @param needCredits
	 * @return
	 */
	void afterOrderCreate(Long orderId,String userAgent,String transfer,String type,Long needCredits);
	
	/**
	 * 3.异步扣积分 <br/>
	 * @param consumer
	 * @param appSimpleDto 
	 * @param operatingActivity 
	 * @param needCredits 
	 * @param parms 
	 * @param topic 
	 * @param orderId 
	 * @param params 
	 * @return
	 */
	void asyncConsumeCredits(ConsumerDto consumer, AppSimpleDto appSimpleDto, OperatingActivityDto operatingActivity,
			Long needCredits, RequestParams parms, String topic, Long orderId, Map<String, String> params);
	
	/**
	 * 4.中奖概率计算 计算过程说明： <br/>
	 * 将每一个奖项的概率相加，如果小于100，则认为缺失部分概率为谢谢参与。<br/>
	 * 如果大于100，则超出100部分永远无法中奖(即使前面的商品已经抽完，后面的奖项也无法被抽中) 此处设计可能有缺陷！！！<br/>
	 * 
	 * @param list  所有奖品信息
	 * @param opId 
	 * @return 根据概率抽中的奖项    （根据概率出奖，谢谢参与，福袋）
	 * @throws StatusException
	 */
	public Triplet<QuestionOptionsVO, QuestionOptionsVO,QuestionOptionsVO> randomPrize(List<QuestionOptionsVO> list, Long opId);


	/**
	 * 5.奖项配置与属性的验证<br/>
	 * 
	 * 1.奖项是否已经被删除<br/>
	 * 2.奖项是否还有库存 <br/>
	 * 3.奖项保底人数是否满足<br/>
	 * 4.奖项用户中奖限制<br/>
	 * 
	 * @param activity
	 * @param winOp
	 * @param subOrderId
	 * @param consumerId
	 * @param logType
	 * 
	 * @return
	 * @throws Exception
	 */
	boolean validOptionCheck(OperatingActivityDto activity,
			QuestionOptionsVO winOp, Long subOrderId, Long consumerId,
			String actTypeHdtool);


	/**
	 * 6.处理中奖奖项<br/>
	 * 
	 * 开启事务 锁 option奖项记录 <br/>
	 * 1:减奖项剩余个数<br/>
	 * 2:减活动定向个数 <br/>
	 * 3:减兑换项库存<br/>
	 * 4:更新中奖信息到用户抽奖订单（状态：成功，领奖状态：待领奖）<br/>
	 * 5:记录用户兑换记录（待领奖）<br/>
	 * 6:提交事务
	 * @param orderDO 
	 * @param activity
	 * @param winOp
	 * @param extmap
	 * @param activityPluginType 
	 */
	void doWinPrize(QuizzOrdersDto orderDO, OperatingActivityDto activity, QuestionOptionsVO winOp,Map<String, String> extmap, String activityPluginType);


	/**
	 * 7. 参与失败通知开发者<br/>
	 * 
	 * 开启事务 锁 option奖项记录 <br/>
	 * @param orderId 
	 * @param credits 
	 * @param status 
	 * @param consumerId 
	 * @param extmap 
	 * @param developerBizId 
	 * @param error4developer 
	 */
	void notifyOrderConsumeFail(Long orderId, Long credits, Integer status, Long consumerId, Map<String, String> extmap, String developerBizId, String error4developer);

	/**
	 * 8. 参与成功通知开发者<br/>
	 * 
	 * 开启事务 锁 option奖项记录 <br/>
	 * @param orderId 
	 * @param status 
	 * @param consumerId 
	 * @param params 
	 * @param developerBizId 
	 * @param error4developer 
	 */
	void notifyOrderConsumeSuccess(Long orderId, boolean status,
			Long consumerId, Map<String, String> params, String developerBizId,
			String error4developer);
}
