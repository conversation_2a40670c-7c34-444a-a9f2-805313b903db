package com.duiba.activity.accessweb.service.shuqi.impl;

import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamInfoDto;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamMemberInfoDto;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamMemberRecordDto;
import cn.com.duiba.activity.center.api.dto.shuqipk.PkTeamRecordDto;
import cn.com.duiba.activity.center.api.enums.ShuQiPKRecordTypeEnum;
import cn.com.duiba.activity.center.api.enums.ShuQiPKSyncMarkEnum;
import cn.com.duiba.activity.center.api.enums.YesOrNoEnum;
import cn.com.duiba.activity.center.api.mqmessage.ShuQiTeamMatchMessage;
import cn.com.duiba.activity.center.api.mqmessage.ShuQiUpdateReadTimeMessage;
import cn.com.duiba.activity.center.api.remoteservice.shuqi.RemotePkTeamInfoService;
import cn.com.duiba.activity.center.api.remoteservice.shuqi.RemotePkTeamMemberInfoService;
import cn.com.duiba.activity.center.api.remoteservice.shuqi.RemotePkTeamMemberRecordService;
import cn.com.duiba.activity.center.api.remoteservice.shuqi.RemotePkTeamRecordService;
import cn.com.duiba.activity.common.center.api.enums.ActivityComCenterErrorEnum;
import cn.com.duiba.activity.common.center.api.enums.ShareCodeActivityTypeEnum;
import cn.com.duiba.activity.common.center.api.params.UserInviteParam;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteNewConsumerShareCodeService;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteUserShareCodeService;
import cn.com.duiba.activity.common.center.api.rsp.sharecode.InviteResponseDto;
import cn.com.duiba.activity.common.center.api.utils.sharecode.ShareCodeValidator;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.api.enums.LimitScopeEnum;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.idmaker.service.api.dto.sensword.SensitiveWordDto;
import cn.com.duiba.idmaker.service.api.remoteservice.sensword.RemoteSensitiveWordService;
import cn.com.duiba.plugin.center.api.dto.ActivityPluginDto;
import cn.com.duiba.plugin.center.api.remoteservice.order.RemoteActivityOrderService;
import cn.com.duiba.plugin.center.api.request.PlaceOrderByActivityStandbyRequest;
import cn.com.duiba.plugin.center.api.response.PlaceOrderResponse;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.UUIDUtils;
import com.alibaba.fastjson.JSON;
import com.duiba.activity.accessweb.cache.ConsumerCacheService;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.enums.HelpShareErrorCodeEnum;
import com.duiba.activity.accessweb.enums.ShuQiPrizeTypeEnum;
import com.duiba.activity.accessweb.enums.ShuQiTeamBattlePkStatusEnum;
import com.duiba.activity.accessweb.enums.ShuQiTeamBattlePrizeStatusEnum;
import com.duiba.activity.accessweb.enums.ShuQiTeamBattleResultEnum;
import com.duiba.activity.accessweb.enums.ShuQiTeamRecordStatusEnum;
import com.duiba.activity.accessweb.enums.ShuQiUserTeamEnum;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.shuqi.ShuQiTeamBattleCacheUtil;
import com.duiba.activity.accessweb.service.shuqi.ShuQiTeamBattleService;
import com.duiba.activity.accessweb.service.shuqi.bo.TeamMemberPrizeBO;
import com.duiba.activity.accessweb.service.shuqi.conf.ShuQiTeamBattleConstant;
import com.duiba.activity.accessweb.vo.shuqi.TeamBattleMainPageVO;
import com.duiba.activity.accessweb.vo.shuqi.TeamBattlePKMemberVO;
import com.duiba.activity.accessweb.vo.shuqi.TeamBattlePKRecordVO;
import com.duiba.activity.accessweb.vo.shuqi.TeamBattleRankInfoVO;
import com.duiba.activity.accessweb.vo.shuqi.TeamBattleRankListVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/28 11:02
 */
@Service
public class ShuQiTeamBattleServiceImpl extends ShuQiTeamBattleCacheUtil implements ShuQiTeamBattleService {

	private static Logger logger = LoggerFactory.getLogger(ShuQiTeamBattleServiceImpl.class);

	/**
	 * 战队最大人数
	 */
	private static final Integer MAX_TEAM_MEMBER = 5;

	/**
	 * 可匹配战队最小人数
	 */
	private static final Integer MIN_TEAM_MEMBER = 3;

	@Resource(name = "redisTemplate")
	private RedisAtomicClient redisAtomicClient;
	@Autowired
	private RemotePkTeamInfoService remotePkTeamInfoService;

	@Autowired
	private ConsumerCacheService consumerCacheService;
	@Resource(name = "stringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;
	@Autowired
	private RemotePkTeamMemberInfoService remotePkTeamMemberInfoService;
	@Autowired
	private RemotePkTeamRecordService remotePkTeamRecordService;
	@Autowired
	private RemotePkTeamMemberRecordService remotePkTeamMemberRecordService;
	@Autowired
	private RemoteUserShareCodeService remoteUserShareCodeService;
	@Autowired
	private RemoteNewConsumerShareCodeService remoteNewConsumerShareCodeService;
	@Autowired
	private ExecutorService executorService;
	@Autowired
	private ShuQiTeamBattleConstant shuQiTeamBattleConstant;
	@Resource(name = "redisTemplate")
	private RedisTemplate<String,TeamMemberPrizeBO> redisTemplate;
	@Autowired
	private RemoteActivityOrderService remoteActivityOrderService;
	@Resource
	private RemoteSensitiveWordService remoteSensitiveWordService;
	@Resource
	private DefaultMQProducer rocketMQProducer;
	@Resource
	private RocketMqMessageTopic rocketMqMessageTopic;

	@Override public TeamBattleMainPageVO getMainPageSimple(Long duibaActivityId, Long appId, Long consumerId) {
		TeamBattleMainPageVO teamBattleMainPageVO = new TeamBattleMainPageVO();
		teamBattleMainPageVO.setTimeNow(Calendar.getInstance().getTimeInMillis());
		teamBattleMainPageVO.setTimeEnd(pkEndTimeMillis());
		teamBattleMainPageVO.setPkStatus(ShuQiTeamBattlePkStatusEnum.NOT_START.getStatus());
		// 设置排行榜状态，是否有排行榜
		teamBattleMainPageVO.setHasRankList(setRankStatus(duibaActivityId, appId));
		// 获取用户所属战队
		PkTeamMemberInfoDto pkTeamMemberInfoDto = getTeamMemberByConsumerId(consumerId);
		if(null == pkTeamMemberInfoDto){
			// 战队不存在
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.NONE);
			return teamBattleMainPageVO;
		}
		teamBattleMainPageVO.setTeamId(pkTeamMemberInfoDto.getTeamId());
		teamBattleMainPageVO.setMyPastReadValue(pkTeamMemberInfoDto.getTotalReadValue());
		// 活动已结束判断
		if(afterOpenBigPrizeTimeTwoDay()){
			teamBattleMainPageVO.setMyTeamStatus(ShuQiUserTeamEnum.ACTIVITY_END.getType());
		}else {
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.SYSTEM_BUSY);
		}
		return teamBattleMainPageVO;
	}

	@Override
	public TeamBattleMainPageVO getMainPage(Long duibaActivityId, AppSimpleDto appSimpleDto, ConsumerDto consumerDto, HttpServletRequest request) throws BizException {
		TeamBattleMainPageVO teamBattleMainPageVO = new TeamBattleMainPageVO();
		teamBattleMainPageVO.setTimeNow(Calendar.getInstance().getTimeInMillis());
		teamBattleMainPageVO.setTimeEnd(pkEndTimeMillis());
		teamBattleMainPageVO.setPkStatus(ShuQiTeamBattlePkStatusEnum.NOT_START.getStatus());
		// 设置排行榜状态，是否有排行榜
		teamBattleMainPageVO.setHasRankList(setRankStatus(duibaActivityId, appSimpleDto.getId()));
		// 获取用户所属战队
		PkTeamMemberInfoDto pkTeamMemberInfoDto = getTeamMemberByConsumerId(consumerDto.getId());
		if(null == pkTeamMemberInfoDto){
			// 战队不存在
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.NONE);
			return teamBattleMainPageVO;
		}
		// 获取战队信息
		PkTeamInfoDto pkTeamInfoDto = getTeamById(pkTeamMemberInfoDto.getTeamId());
		if(null == pkTeamInfoDto){
			logger.warn("用户{}有所属战队:{}，但是战队信息没有了", consumerDto.getId(), JSON.toJSON(pkTeamMemberInfoDto));
			throw new BizException("数据异常，所属战队不在了");
		}
		teamBattleMainPageVO.setTeamId(pkTeamMemberInfoDto.getTeamId());
		teamBattleMainPageVO.setTeamName(pkTeamInfoDto.getTeamName());
		teamBattleMainPageVO.setTeamPastReadValue(pkTeamInfoDto.getTotalReadValue());
		teamBattleMainPageVO.setMyPastReadValue(pkTeamMemberInfoDto.getTotalReadValue());
		// 活动已结束判断
		if(afterOpenBigPrizeTimeTwoDay()){
			teamBattleMainPageVO.setMyTeamStatus(ShuQiUserTeamEnum.ACTIVITY_END.getType());
			return teamBattleMainPageVO;
		}
		// 判断战队人数
		if(null == pkTeamInfoDto.getMemberNum() || ObjectUtils.compare(MIN_TEAM_MEMBER, pkTeamInfoDto.getMemberNum()) > 0){
			// 战队人数不足3人，战队处于不能匹配状态
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.UNDER_MATCHING);
			teamBattleMainPageVO.setMyTeamMembers(setUnderMatchTeamAvatarList(consumerDto.getId(), consumerDto.getPartnerUserId(), pkTeamInfoDto));
			return teamBattleMainPageVO;
		}
		// 历史阅读值同步，异步操作
		executorService.execute(()-> syncReadTimePast(pkTeamInfoDto.getId()));
		// 获取当前pk状态
		PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.getByTeamIdAndDate(pkTeamInfoDto.getId(), recordDateMillis(new Date()));
		if(null == pkTeamRecordDto){
			// 战队匹配，异步操作
			executorService.execute(()-> battleMatch(duibaActivityId, appSimpleDto.getId(), pkTeamInfoDto.getId()));
		}else{
			// 同步阅读值，异步操作
			executorService.execute(()-> syncReadTimeAndPK(pkTeamInfoDto.getId(), pkTeamRecordDto));
		}
		// 获取需要结算的pk记录
		PkTeamRecordDto prizePkRecord = checkIfPkEndToday() ? pkTeamRecordDto :
				remotePkTeamRecordService.getByTeamIdAndDate(pkTeamInfoDto.getId(), recordDateMillis(DateUtils.daysAddOrSub(new Date(), -1)));
		// 发奖判断：如果当前是23点之前，发昨天的奖；23点之后，发今天的奖（今日未开启pk的除外，仍旧发昨天的奖）
		Pair<Boolean, String> pairPrize = checkOpenPrize(prizePkRecord, appSimpleDto, consumerDto, request);
		teamBattleMainPageVO.setOrderNum(pairPrize.getValue1());
		teamBattleMainPageVO.setOpenPrizeStatus(pairPrize.getValue0() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
		// 组装战队成员信息
		Pair<List<TeamBattlePKMemberVO>, List<TeamBattlePKMemberVO>> pairMemberInfoVO = assembleTeamMemberInfo(null != pkTeamRecordDto, pkTeamInfoDto.getId(), pkTeamRecordDto);
		teamBattleMainPageVO.setMyTeamMembers(pairMemberInfoVO.getValue0());
		teamBattleMainPageVO.setEnemyTeamMembers(pairMemberInfoVO.getValue1());

		if(null == pkTeamRecordDto){
			// 战队处于匹配中，未开启pk
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.MATCHING);
		}else{
			// 战队已匹配，pk中
			teamBattleMainPageVO.setMyTeamStatus(afterOpenBigPrizeTime(), ShuQiUserTeamEnum.DURING_BATTLE);
			teamBattleMainPageVO.setMyTeamCurReadValue(pkTeamRecordDto.getReadValue());
			teamBattleMainPageVO.setEnemyTeamName(pkTeamRecordDto.getEnemyName());
			teamBattleMainPageVO.setEnemyTeamCurReadValue(pkTeamRecordDto.getEnemyReadValue());
		}
		// 设置pk状态
		if(null != prizePkRecord){
			if(YesOrNoEnum.YES.getCode().equals(prizePkRecord.getSyncStatus())){
				teamBattleMainPageVO.setPkStatus(ShuQiTeamBattlePkStatusEnum.FINISH.getStatus());
				teamBattleMainPageVO.setBattleResult(ShuQiTeamBattleResultEnum.getBattleResult(prizePkRecord.getReadValue(), prizePkRecord.getEnemyReadValue()));
			}else if(checkIfPkEndToday()){
				teamBattleMainPageVO.setPkStatus(ShuQiTeamBattlePkStatusEnum.DURING.getStatus());
			}
		}
		return teamBattleMainPageVO;
	}

	/**
	* 设置排行榜状态，是否有排行榜
	*/
	private boolean setRankStatus(Long duibaActivityId, Long appId){
		if(new Date().before(DateUtils.getSecondDate(shuQiTeamBattleConstant.getRankStartTime()))){
			return false;
		}else{
			// 获取排行榜前10名战队id和阅读值
			String rankKey = redisKeyForRank(duibaActivityId, appId);
			Set<ZSetOperations.TypedTuple<String>> teamIdValueSet = stringRedisTemplate.opsForZSet().reverseRangeWithScores(rankKey, 0, 9);
			return CollectionUtils.isNotEmpty(teamIdValueSet);
		}
	}

	/**
	 * 战队人数不足3人，组装头像列表
	 */
	private List<TeamBattlePKMemberVO> setUnderMatchTeamAvatarList(Long consumerId, String partnerUserId, PkTeamInfoDto pkTeamInfoDto){
		List<TeamBattlePKMemberVO> myTeamMembers = new ArrayList<>();
		if(ObjectUtils.equals(1, pkTeamInfoDto.getMemberNum())){
			// 战队只有一个人，只需返回自己的信息
			TeamBattlePKMemberVO teamBattlePKMemberVO = new TeamBattlePKMemberVO();
			teamBattlePKMemberVO.setConsumerId(consumerId);
			teamBattlePKMemberVO.setPartnerUserId(partnerUserId);
			ConsumerExtraDto consumerExtraDto = consumerCacheService.getConsumerExtraByConsumerId(consumerId);
			if(null != consumerExtraDto){
				teamBattlePKMemberVO.setAvatar(consumerExtraDto.getAvatar());
				teamBattlePKMemberVO.setNickName(consumerExtraDto.getNickname());
			}
			myTeamMembers.add(teamBattlePKMemberVO);
			return myTeamMembers;
		}
		// 组装战队成员头像列表
		return assembleTeamMemberAvatars(pkTeamInfoDto.getId());
	}

	/**
	 * 历史阅读值同步，异步操作
	 */
	private void syncReadTimePast(Long teamId){
		String key = redisKeyForSyncMarkPast(teamId, DateUtils.getDayStr(new Date()));
		String field = String.valueOf((teamId % 500));
		String syncTotalMark = hashOpsForSync().get(key, field);
		if(null != syncTotalMark){
			// 当天已同步过历史阅读值，不再同步
			return;
		}
		hashOpsForSync().put(key, field, "1");
		stringRedisTemplate.expire(key, 30, TimeUnit.MINUTES);
		// 获取当前战队的历史记录，如果某天没记录，不处理
		List<PkTeamRecordDto> pastTeamRecordTotal = remotePkTeamRecordService.getPastByTeamId(teamId);
		if(CollectionUtils.isEmpty(pastTeamRecordTotal)){
			return;
		}
		// 获取pk记录
		List<PkTeamRecordDto> pkTeamRecordDtoList = pastTeamRecordTotal.stream()
		                                                               .filter(x->ObjectUtils.equals(ShuQiPKRecordTypeEnum.TEAM_PK.getCode(), x.getRecordType())
				                                                               && ObjectUtils.compare(recordDateMillis(new Date()), x.getRecordDate()) > 0
				                                                               && ObjectUtils.equals(YesOrNoEnum.NO.getCode(), x.getSyncStatus()))
		                                                               .collect(Collectors.toList());
		if(CollectionUtils.isEmpty(pkTeamRecordDtoList)){
			stringRedisTemplate.expire(key, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
			return;
		}
		logger.info("历史阅读值同步，战队id：{}，需要同步的记录：{}", teamId, JSON.toJSON(pkTeamRecordDtoList));
		// 同步战队历史pk阅读值
		pkTeamRecordDtoList.forEach(pkTeamRecordDto -> sendMessageUpdateReadTime(true, pkTeamRecordDto.getTeamId(), pkTeamRecordDto.getRecordDate(), null));
	}

	private HashOperations<String, String, String> hashOpsForSync(){
		return stringRedisTemplate.opsForHash();
	}

    /**
     * 获取战队当前pk记录，没有就进行匹配操作；如果当前是pk结算期，不进行匹配
     *
     * @param duibaActivityId 兑吧侧活动ID
     * @param appId           应用ID
     * @param teamId          战队ID
     */
    private void battleMatch(Long duibaActivityId, Long appId, Long teamId) {
        if (checkIfPkEndToday() || afterOpenBigPrizeTime()) {
            return;
        }
        String key = redisKeyForTeamMatch(duibaActivityId, appId);
        String oldTeamId = stringRedisTemplate.opsForList().leftPop(key);
        // 注意极端情况，A进来，放到redis；B进来，取出A，尚未完成匹配；A进来，又往redis放；C进来，取到A：center层做了锁处理，不重复插入
        if (oldTeamId == null || ObjectUtils.equals(oldTeamId, String.valueOf(teamId))) {
            stringRedisTemplate.opsForList().rightPushAll(key, String.valueOf(teamId));
            stringRedisTemplate.expire(key, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
            return;
        }
	    sendMessageTeamMatch(teamId, Long.valueOf(oldTeamId), recordDateMillis(new Date()));
    }

	/**
	 * 战队匹配发消息
	 */
	private void sendMessageTeamMatch(Long teamId, Long enemyTeamId, Long recordDate){
		ShuQiTeamMatchMessage shuQiTeamMatchMessage = new ShuQiTeamMatchMessage();
		shuQiTeamMatchMessage.setTeamId(teamId);
		shuQiTeamMatchMessage.setEnemyTeamId(enemyTeamId);
		shuQiTeamMatchMessage.setRecordDate(recordDate);
		try {
			Message message = new Message(rocketMqMessageTopic.getShuQiTeamBattleTeamMatch(),
					ShuQiTeamMatchMessage.encode(shuQiTeamMatchMessage));
			message.setKeys(UUIDUtils.createSecureUUID());
			SendResult result = rocketMQProducer.send(message);
			if (!SendStatus.SEND_OK.equals(result.getSendStatus())) {
				logger.warn("书旗pk战队匹配发消息失败，message：{}，result：{}", JSON.toJSON(message), JSON.toJSON(result));
			}
		} catch (Exception e) {
			logger.warn("书旗pk战队匹配发消息失败", e);
		}
	}

	/**
	 * 同步阅读时长pk
	 * @return PkTeamRecordDto：当前pk状态；PkTeamRecordDto：需要结算的pk记录
	 */
	private void syncReadTimeAndPK(Long teamId, PkTeamRecordDto todayPkRecord){
		String key = redisKeyForSyncMarkToday(teamId, new Date());
		String syncMark = stringRedisTemplate.opsForValue().get(key);
		// 判断当前是否是pk结算期
		if(checkIfPkEndToday()){
			if(ObjectUtils.equals(YesOrNoEnum.NO.getCode(), todayPkRecord.getSyncStatus())
					&& (null == syncMark || !Objects.equals(ShuQiPKSyncMarkEnum.LAST_SYNC.getCode(), syncMark))){
				// 将redis标记设为2，即已进行当天最后一次同步
				stringRedisTemplate.opsForValue().set(key, ShuQiPKSyncMarkEnum.LAST_SYNC.getCode(), 10, TimeUnit.MINUTES);
				// 当天未完成整体同步，需要进行当天最后一次同步操作
				sendMessageUpdateReadTime(true, teamId, todayPkRecord.getRecordDate(), key);
			}
		}else {
			if(null == syncMark){
				// 将redis标记设为1，即最近已有同步
				stringRedisTemplate.opsForValue().set(key, ShuQiPKSyncMarkEnum.COMMON_SYNC.getCode(), 30, TimeUnit.MINUTES);
				// 当天阅读值同步
				sendMessageUpdateReadTime(false, teamId, todayPkRecord.getRecordDate(), key);
			}
		}
	}

	/**
	 * 更新阅读值发消息
	 */
	private void sendMessageUpdateReadTime(Boolean lastSync, Long teamId, Long recordDate, String redisKey){
		ShuQiUpdateReadTimeMessage shuQiUpdateReadTimeMessage = new ShuQiUpdateReadTimeMessage();
		shuQiUpdateReadTimeMessage.setLastSync(lastSync);
		shuQiUpdateReadTimeMessage.setTeamId(teamId);
		shuQiUpdateReadTimeMessage.setRecordDate(recordDate);
		shuQiUpdateReadTimeMessage.setRedisKey(redisKey);
		try {
			Message message = new Message(rocketMqMessageTopic.getShuQiTeamBattleUpdateReadTime(),
					ShuQiUpdateReadTimeMessage.encode(shuQiUpdateReadTimeMessage));
			message.setKeys(UUIDUtils.createSecureUUID());
			SendResult result = rocketMQProducer.send(message);
			if (!SendStatus.SEND_OK.equals(result.getSendStatus())) {
				logger.warn("书旗pk更新阅读值发消息失败，message：{}，result：{}", JSON.toJSON(message), JSON.toJSON(result));
			}
		} catch (Exception e) {
			logger.warn("书旗pk更新阅读值发消息失败", e);
		}
	}

	/**
	 * 发奖判断
	 */
	private Pair<Boolean, String> checkOpenPrize(PkTeamRecordDto pkTeamRecordDto, AppSimpleDto appSimpleDto, ConsumerDto consumerDto, HttpServletRequest request){
		if(null == pkTeamRecordDto
				|| ObjectUtils.equals(YesOrNoEnum.NO.getCode(), pkTeamRecordDto.getSyncStatus())
				|| pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) <= 0){
			// 当天没有pk，不能发奖；未结束当天的同步，也就是阅读值同步未完成，不能发奖；未获胜，不能发奖
			return Pair.with(false, null);
		}
		String key = RedisKeyFactory.K161.toString() + "_" + pkTeamRecordDto.getId() + "_" + consumerDto.getId();
		RedisLock redisLock = redisAtomicClient.getLock(key, 2);
		if(null == redisLock){
			return Pair.with(false, null);
		}
		try {
			// 获取用户当天阅读值记录
			List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(Collections.singletonList(pkTeamRecordDto.getId()), consumerDto.getId());
			if(CollectionUtils.isEmpty(pkTeamMemberRecordDtoList)){
				logger.info("用户当天的阅读值记录不存在，pk记录：{}", JSON.toJSON(pkTeamRecordDto));
				return Pair.with(false, null);
			}
			PkTeamMemberRecordDto pkTeamMemberRecordDto = pkTeamMemberRecordDtoList.get(0);
			if(null != pkTeamMemberRecordDto.getOrderNum()){
				return Pair.with(false, pkTeamMemberRecordDto.getOrderNum());
			}
			Long pkPluginId = shuQiTeamBattleConstant.getPkPrizePluginId();
			Long standbyPluginId = shuQiTeamBattleConstant.getShuQiStandbyPluginId();
			if(null == pkPluginId || null == standbyPluginId){
				logger.warn("pk出奖插件未配置");
				return Pair.with(false, null);
			}
			String orderNum = openPrize(appSimpleDto, consumerDto, pkPluginId, standbyPluginId, request);
			// 保存出奖订单号
			pkTeamMemberRecordDto.setOrderNum(orderNum);
			remotePkTeamMemberRecordService.updateOrderNum(pkTeamMemberRecordDto);
			return Pair.with(true, orderNum);
		}catch (Exception e){
			logger.warn("首页最近一次pk发奖判断异常，pk记录：{}，用户id：{}", JSON.toJSON(pkTeamRecordDto), consumerDto.getId(), e);
			return Pair.with(false, null);
		}finally {
			redisLock.unlock();
		}
	}

	private String openPrize(AppSimpleDto appSimpleDto, ConsumerDto consumerDto, Long pluginId, Long standbyPluginId, HttpServletRequest request) throws BizException{
		PlaceOrderByActivityStandbyRequest orderRequest = new PlaceOrderByActivityStandbyRequest();
		orderRequest.setAppId(appSimpleDto.getId());
		orderRequest.setActivityId(pluginId);
		orderRequest.setActivityType(ActivityPluginDto.PLUGDRAW);
		orderRequest.setConsumerId(consumerDto.getId());
		orderRequest.setPartnerUserId(consumerDto.getPartnerUserId());
		orderRequest.setCredits(0L);
		orderRequest.setStandbyActivityId(standbyPluginId);
		orderRequest.setRequestParams(RequestParams.parse(request));
		PlaceOrderResponse placeOrderResponse = remoteActivityOrderService.placeOrder(orderRequest);
		if(!placeOrderResponse.isSuccess()){
			logger.warn("书旗春节组队pk-pk开奖失败，传参：{}，开奖返回信息：{}", JSON.toJSON(orderRequest), JSON.toJSON(placeOrderResponse));
		}
		return placeOrderResponse.getOrderNum();
	}

	/**
	 * 组装首页战队成员头像列表
	 */
	private List<TeamBattlePKMemberVO> assembleTeamMemberAvatars(Long teamId){
		// 战队尚未匹配，只取自己战队成员的头像昵称，不用获取战队成员阅读值，也不用获取敌方战队成员信息
		List<TeamBattlePKMemberVO> myTeamMembers = new ArrayList<>();
		List<PkTeamMemberInfoDto> pkTeamMemberInfoDtoList = remotePkTeamMemberInfoService.getListByTeamId(teamId);
		if(CollectionUtils.isEmpty(pkTeamMemberInfoDtoList)){
			logger.warn("战队异常，成员为空了。战队id:{}", teamId);
			return myTeamMembers;
		}
		List<Long> consumerIdList = pkTeamMemberInfoDtoList.stream().map(PkTeamMemberInfoDto::getConsumerId).collect(Collectors.toList());
		List<ConsumerExtraDto> consumerExtraDtoList = consumerCacheService.getConsumerExtraListCache(consumerIdList);
		if(CollectionUtils.isEmpty(consumerExtraDtoList)){
			pkTeamMemberInfoDtoList.forEach(pkTeamMemberInfoDto -> {
				TeamBattlePKMemberVO teamBattlePKMemberVO = new TeamBattlePKMemberVO();
				teamBattlePKMemberVO.setConsumerId(pkTeamMemberInfoDto.getConsumerId());
				myTeamMembers.add(teamBattlePKMemberVO);
			});
			return myTeamMembers;
		}
		Map<Long, String> consumerAvatarMap = consumerExtraDtoList.stream()
		                                                          .filter(x->StringUtils.isNotBlank(x.getAvatar()))
		                                                          .collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, ConsumerExtraDto::getAvatar));

		pkTeamMemberInfoDtoList.forEach(pkTeamMemberInfoDto -> {
			TeamBattlePKMemberVO teamBattlePKMemberVO = new TeamBattlePKMemberVO();
			teamBattlePKMemberVO.setConsumerId(pkTeamMemberInfoDto.getConsumerId());
			teamBattlePKMemberVO.setAvatar(consumerAvatarMap.get(pkTeamMemberInfoDto.getConsumerId()));
			myTeamMembers.add(teamBattlePKMemberVO);
		});
		return myTeamMembers;
	}

	/**
	 * 组装首页战队成员信息
	 */
	private Pair<List<TeamBattlePKMemberVO>, List<TeamBattlePKMemberVO>> assembleTeamMemberInfo(Boolean matched, Long teamId, PkTeamRecordDto myTeamRecord){
		if(!matched){
			return Pair.with(assembleTeamMemberAvatars(teamId), null);
		}
		// 战队已匹配的，取双方战队成员的阅读值及头像昵称
		List<TeamBattlePKMemberVO> myTeamMembers = new ArrayList<>();
		List<TeamBattlePKMemberVO> enemyTeamMembers = new ArrayList<>();
		if(null == myTeamRecord){
			logger.warn("数据异常，战队id{}今天的pk记录为空了", teamId);
			return Pair.with(myTeamMembers, enemyTeamMembers);
		}
		Map<Long, Long> teamRecordIdMap = new HashMap<>();
		Long myTeamRecordId = myTeamRecord.getId();
		teamRecordIdMap.put(myTeamRecordId, myTeamRecord.getTeamId());
		PkTeamRecordDto enemyPkRecord = remotePkTeamRecordService.getByTeamIdAndDate(myTeamRecord.getRelateId(), recordDateMillis(new Date()));
		if(null == enemyPkRecord){
			logger.warn("数据异常，战队id{}今天的pk对手的pk记录为空了，pk记录：{}", teamId, JSON.toJSON(myTeamRecord));
			return Pair.with(myTeamMembers, enemyTeamMembers);
		}
		Long enemyTeamRecordId = enemyPkRecord.getId();
		teamRecordIdMap.put(enemyTeamRecordId, enemyPkRecord.getTeamId());
		List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(Arrays.asList(myTeamRecordId, enemyTeamRecordId), null);
		if(CollectionUtils.isEmpty(pkTeamMemberRecordDtoList)){
			logger.warn("数据异常，战队id{}和对手今天的成员记录都为空了，pk记录：{}", teamId, JSON.toJSON(myTeamRecord));
			return Pair.with(myTeamMembers, enemyTeamMembers);
		}
		// 设置pk记录敌我双方当前总阅读值
		calculateTotalReadValue(myTeamRecord, teamRecordIdMap, pkTeamMemberRecordDtoList);

		List<Long> consumerIdList = pkTeamMemberRecordDtoList.stream().map(PkTeamMemberRecordDto::getConsumerId).collect(Collectors.toList());
		Map<Long, Long> consumerTeamRecordMap = pkTeamMemberRecordDtoList.stream().collect(Collectors.toMap(PkTeamMemberRecordDto::getConsumerId, PkTeamMemberRecordDto::getTeamRecordId));
		// 获取partnerUserId
		List<ConsumerDto> consumerDtoList = consumerCacheService.findConsumerByIds(consumerIdList);
		Map<Long, String> consumerUidMap = consumerDtoList.stream()
		                                                  .filter(x->StringUtils.isNotBlank(x.getPartnerUserId()))
		                                                  .collect(Collectors.toMap(ConsumerDto::getId, ConsumerDto::getPartnerUserId));
		// 获取用户扩展信息
		List<ConsumerExtraDto> consumerExtraDtoList = consumerCacheService.getConsumerExtraListCache(consumerIdList);
		if(CollectionUtils.isEmpty(consumerExtraDtoList)){
			pkTeamMemberRecordDtoList.forEach(pkTeamMemberRecordDto -> {
				TeamBattlePKMemberVO teamBattlePKMemberVO = new TeamBattlePKMemberVO();
				teamBattlePKMemberVO.setConsumerId(pkTeamMemberRecordDto.getConsumerId());
				teamBattlePKMemberVO.setReadTime(pkTeamMemberRecordDto.getReadTime());
				teamBattlePKMemberVO.setPraiseNum(pkTeamMemberRecordDto.getPraiseCount());
				if(ObjectUtils.equals(consumerTeamRecordMap.get(pkTeamMemberRecordDto.getConsumerId()), myTeamRecordId)){
					myTeamMembers.add(teamBattlePKMemberVO);
				}else {
					enemyTeamMembers.add(teamBattlePKMemberVO);
				}
			});
			return Pair.with(myTeamMembers, enemyTeamMembers);
		}
		// 组装头像、昵称
		Map<Long, String> consumerAvatarMap = consumerExtraDtoList.stream()
		                                                          .filter(x->StringUtils.isNotBlank(x.getAvatar()))
		                                                          .collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, ConsumerExtraDto::getAvatar));
		Map<Long, String> consumerNickMap = consumerExtraDtoList.stream()
		                                                        .filter(x->StringUtils.isNotBlank(x.getNickname()))
		                                                        .collect(Collectors.toMap(ConsumerExtraDto::getConsumerId, ConsumerExtraDto::getNickname));
		pkTeamMemberRecordDtoList.forEach(pkTeamMemberRecordDto -> {
			TeamBattlePKMemberVO teamBattlePKMemberVO = new TeamBattlePKMemberVO();
			teamBattlePKMemberVO.setConsumerId(pkTeamMemberRecordDto.getConsumerId());
			teamBattlePKMemberVO.setReadTime(pkTeamMemberRecordDto.getReadTime());
			teamBattlePKMemberVO.setPraiseNum(pkTeamMemberRecordDto.getPraiseCount());
			teamBattlePKMemberVO.setPartnerUserId(consumerUidMap.get(pkTeamMemberRecordDto.getConsumerId()));
			teamBattlePKMemberVO.setAvatar(consumerAvatarMap.get(pkTeamMemberRecordDto.getConsumerId()));
			teamBattlePKMemberVO.setNickName(consumerNickMap.get(pkTeamMemberRecordDto.getConsumerId()));
			if(ObjectUtils.equals(consumerTeamRecordMap.get(pkTeamMemberRecordDto.getConsumerId()), myTeamRecordId)){
				myTeamMembers.add(teamBattlePKMemberVO);
			}else {
				enemyTeamMembers.add(teamBattlePKMemberVO);
			}
		});
		return Pair.with(myTeamMembers, enemyTeamMembers);
	}

	// 设置pk记录敌我双方当前总阅读值
	private void calculateTotalReadValue(PkTeamRecordDto myTeamRecord, Map<Long, Long> teamRecordIdMap, List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList){
		if(YesOrNoEnum.YES.getCode().equals(myTeamRecord.getSyncStatus())){
			return;
		}
		Long totalReadValue = 0L;
		Long totalEnemyReadValue = 0L;
		for(PkTeamMemberRecordDto pkTeamMemberRecordDto: pkTeamMemberRecordDtoList){
			if(ObjectUtils.equals(myTeamRecord.getTeamId(), teamRecordIdMap.get(pkTeamMemberRecordDto.getTeamRecordId()))){
				totalReadValue = totalReadValue + calculateUserReadValue(pkTeamMemberRecordDto.getPraiseCount(), pkTeamMemberRecordDto.getReadTime());
			}else if(ObjectUtils.equals(myTeamRecord.getRelateId(), teamRecordIdMap.get(pkTeamMemberRecordDto.getTeamRecordId()))){
				totalEnemyReadValue = totalEnemyReadValue + calculateUserReadValue(pkTeamMemberRecordDto.getPraiseCount(), pkTeamMemberRecordDto.getReadTime());
			}
		}
		myTeamRecord.setReadValue(totalReadValue);
		myTeamRecord.setEnemyReadValue(totalEnemyReadValue);
	}

	private Long calculateUserReadValue(Integer praiseCount, Long readTime){
		return praiseCount + new BigDecimal(readTime).divide(BigDecimal.TEN, 0, BigDecimal.ROUND_HALF_UP).longValue();
	}

	@Override
	public TeamBattleRankListVO getRankingList(Long duibaActivityId, Long appId, Long consumerId) {
		TeamBattleRankListVO teamBattleRankListVO = new TeamBattleRankListVO();
		teamBattleRankListVO.setTimeNow(System.currentTimeMillis());
		teamBattleRankListVO.setTimeEnd(DateUtils.getSecondDate(shuQiTeamBattleConstant.getOpenBigPrizeTime()).getTime());
		teamBattleRankListVO.setPrizeType(ShuQiPrizeTypeEnum.NONE.getStatus());
		// 获取排行榜前10名战队id和阅读值
		String rankKey = redisKeyForRank(duibaActivityId, appId);
		Set<ZSetOperations.TypedTuple<String>> teamIdValueSet = stringRedisTemplate.opsForZSet().reverseRangeWithScores(rankKey, 0, 9);
		if(CollectionUtils.isEmpty(teamIdValueSet)){
			return teamBattleRankListVO;
		}
		// 获取战队名称
		List<Long> teamIdList = teamIdValueSet.stream().map(x->Long.valueOf(x.getValue())).collect(Collectors.toList());
		List<PkTeamInfoDto> pkTeamInfoDtoList = remotePkTeamInfoService.getListByIds(teamIdList);
		if(CollectionUtils.isEmpty(pkTeamInfoDtoList)){
			logger.warn("排行榜战队id对应的战队信息都不存在");
			return teamBattleRankListVO;
		}
		Map<Long, String> teamIdNameMap = pkTeamInfoDtoList.stream().collect(Collectors.toMap(PkTeamInfoDto::getId, PkTeamInfoDto::getTeamName));
		// 组装前10名战队信息
		List<TeamBattleRankInfoVO> teamRankingList = new ArrayList<>();
		int i = 1;
		for(ZSetOperations.TypedTuple<String> typedTuple: teamIdValueSet){
			TeamBattleRankInfoVO teamBattleRankInfoVO = new TeamBattleRankInfoVO();
			teamBattleRankInfoVO.setRankId(i);
			teamBattleRankInfoVO.setTeamTitle(teamIdNameMap.get(Long.valueOf(typedTuple.getValue())));
			teamBattleRankInfoVO.setTotalReadValue(typedTuple.getScore().longValue());
			teamRankingList.add(teamBattleRankInfoVO);
			i ++;
		}
		teamBattleRankListVO.setTeamRankingList(teamRankingList);
		// 查询用户所属战队id
		PkTeamMemberInfoDto teamMemberInfo = getTeamMemberByConsumerId(consumerId);
		if(null == teamMemberInfo){
			logger.info("查询排行榜，用户{}没有战队", consumerId);
			return teamBattleRankListVO;
		}
		PkTeamInfoDto teamInfo = getTeamById(teamMemberInfo.getTeamId());
		if(null == teamInfo){
			logger.warn("查询排行榜异常，战队信息不存在，用户战队:{}", JSON.toJSON(teamMemberInfo));
			return teamBattleRankListVO;
		}
		// 查询用户战队id在排行榜中的排名
		Long rank = stringRedisTemplate.opsForZSet().reverseRank(rankKey, String.valueOf(teamMemberInfo.getTeamId()));
		teamBattleRankListVO.setRank(null == rank || rank.compareTo(999L) >= 0 ? "999+" : String.valueOf(rank + 1));
		teamBattleRankListVO.setReadValue(teamInfo.getTotalReadValue());
		// 判断是否是过了开大奖时间，以及战队排名是否是前十
		if(afterOpenBigPrizeTime() && null != rank && rank.compareTo(10L) < 0){
			// 组装战队奖品信息
			String key = redisKeyForPrize(appId, duibaActivityId);
			TeamMemberPrizeBO teamMemberPrizeBO = hashOpsForBigPrize().get(key, String.valueOf(consumerId));
			if(null == teamMemberPrizeBO){
				teamMemberPrizeBO = assemblePrizeInfo(rank.intValue() + 1, teamInfo.getId(), consumerId);
				hashOpsForBigPrize().put(key, String.valueOf(consumerId), teamMemberPrizeBO);
				stringRedisTemplate.expire(key, 30, TimeUnit.DAYS);
			}
			teamBattleRankListVO.setPrizeType(teamMemberPrizeBO.getPrizeType());
			teamBattleRankListVO.setAmount(teamMemberPrizeBO.getAmount());
			teamBattleRankListVO.setHasRegister(StringUtils.isNotBlank(teamMemberPrizeBO.getContactName())
					&& StringUtils.isNotBlank(teamMemberPrizeBO.getContactPhone())
					&& StringUtils.isNotBlank(teamMemberPrizeBO.getContactText()));
		}
		return teamBattleRankListVO;
	}

	private HashOperations<String, String, TeamMemberPrizeBO> hashOpsForBigPrize(){
		return redisTemplate.opsForHash();
	}

	@Override
	public void setRank(Long duibaActivityId, Long appId, Long teamId, Long readValue) {
		String rankKey = redisKeyForRank(duibaActivityId, appId);
		stringRedisTemplate.opsForZSet().add(rankKey, String.valueOf(teamId), readValue);
		stringRedisTemplate.expire(rankKey, 30, TimeUnit.DAYS);
	}

	@Override
	public void removeRank(Long duibaActivityId, Long appId){
		String rankKey = redisKeyForRank(duibaActivityId, appId);
		stringRedisTemplate.delete(rankKey);
	}

    @Override
    public List<TeamMemberPrizeBO> getBigPrizeInfo(Long duibaActivityId, Long appId) {
		String key = redisKeyForPrize(appId, duibaActivityId);
		return hashOpsForBigPrize().values(key);
	}

	@Override
	public void removeBigPrizeInfo(Long duibaActivityId, Long appId){
		String key = redisKeyForPrize(appId, duibaActivityId);
		redisTemplate.delete(key);
	}

	@Override
	public boolean checkContainSenWord(String title) throws BizException {
		if (redisAtomicClient.getLong(senWordRedisKey(title)) != null) {
			return true;
		}
		SensitiveWordDto sensitiveWord = remoteSensitiveWordService.checkContainSenWord(title);
		boolean exist = sensitiveWord != null && sensitiveWord.getContain();
		if (exist) {
			redisAtomicClient.incrBy(senWordRedisKey(title), 1, 15, TimeUnit.DAYS);
		}
		return exist;
	}

	@Override
	public void removeSyncPastRedisMark(Long teamId){
		String key = redisKeyForSyncMarkPast(teamId, DateUtils.getDayStr(new Date()));
		stringRedisTemplate.delete(key);
	}

	@Override
	public String getSyncPastRedisMark(Long teamId){
		String key = redisKeyForSyncMarkPast(teamId, DateUtils.getDayStr(new Date()));
		String field = String.valueOf((teamId % 500));
		return hashOpsForSync().get(key, field);
	}

	/**
	 * 组装奖品信息
	 */
	private TeamMemberPrizeBO assemblePrizeInfo(Integer rank, Long teamId, Long consumerId){
		TeamMemberPrizeBO teamMemberPrizeBO = new TeamMemberPrizeBO();
		teamMemberPrizeBO.setRank(rank);
		teamMemberPrizeBO.setTeamId(teamId);
		teamMemberPrizeBO.setConsumerId(consumerId);
		if(rank.equals(1)){
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.CASH.getStatus());
			teamMemberPrizeBO.setAmount(calculatePrizeAmount(teamId, consumerId, 10000L));
		}else if(rank.equals(2)){
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.CASH.getStatus());
			teamMemberPrizeBO.setAmount(calculatePrizeAmount(teamId, consumerId, 3000L));
		}else if(rank.compareTo(3) >= 0 && rank.compareTo(5) <= 0){
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.CASH.getStatus());
			teamMemberPrizeBO.setAmount(calculatePrizeAmount(teamId, consumerId, 1000L));
		}else if (rank.compareTo(6) >= 0 && rank.compareTo(8) <= 0){
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.MI_BAND.getStatus());
		}else if(rank.equals(9) || rank.equals(10)){
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.BLUETOOTH_HEADSET.getStatus());
		}else {
			teamMemberPrizeBO.setPrizeType(ShuQiPrizeTypeEnum.NONE.getStatus());
		}
		return teamMemberPrizeBO;
	}

	/**
	 * 计算战队成员奖金
	 */
	private String calculatePrizeAmount(Long teamId, Long consumerId, Long amount){
		List<PkTeamMemberInfoDto> pkTeamMemberInfoDtoList = remotePkTeamMemberInfoService.getListByTeamId(teamId);
		Long totalReadValue = pkTeamMemberInfoDtoList.stream().mapToLong(PkTeamMemberInfoDto::getTotalReadValue).sum();
		PkTeamMemberInfoDto pkTeamMemberInfoDto = pkTeamMemberInfoDtoList.stream().filter(x->consumerId.equals(x.getConsumerId())).findAny().orElse(null);
		Long consumerReadValue = null == pkTeamMemberInfoDto || null == pkTeamMemberInfoDto.getTotalReadValue() ? 0L : pkTeamMemberInfoDto.getTotalReadValue();
		return new BigDecimal(amount).multiply(new BigDecimal(consumerReadValue))
				                             .divide(new BigDecimal(totalReadValue), 2, BigDecimal.ROUND_HALF_DOWN).toString();
	}

	@Override
	public List<TeamBattlePKRecordVO> getTeamRecords(Long teamId, Long consumerId) {
		List<PkTeamRecordDto> pastTeamRecordTotal = getPastByTeamId(teamId);
		if(CollectionUtils.isEmpty(pastTeamRecordTotal)){
			return Collections.emptyList();
		}
		// 对于pk的，只取已结算的
		List<PkTeamRecordDto> hasClearRecordList = pastTeamRecordTotal.stream()
		                                                              .filter(x->(!x.getRecordType().equals(ShuQiPKRecordTypeEnum.TEAM_PK.getCode())) || x.getSyncStatus().equals(YesOrNoEnum.YES.getCode()))
		                                                              .collect(Collectors.toList());
		if(CollectionUtils.isEmpty(hasClearRecordList)){
			return Collections.emptyList();
		}
		List<TeamBattlePKRecordVO> teamBattlePKRecordVOList = new ArrayList<>();
		// 获取获胜记录对应的用户记录
		List<Long> winTeamRecordIds = hasClearRecordList.stream()
		                                                 .filter(x->x.getRecordType().equals(ShuQiPKRecordTypeEnum.TEAM_PK.getCode()) && ObjectUtils.compare(x.getReadValue(), x.getEnemyReadValue()) > 0)
		                                                 .map(PkTeamRecordDto::getId).collect(Collectors.toList());
		List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(winTeamRecordIds, consumerId);
		Map<Long, PkTeamMemberRecordDto> teamMemberRecordPrizeMap = new HashMap<>();
		if(CollectionUtils.isNotEmpty(pkTeamMemberRecordDtoList)){
			teamMemberRecordPrizeMap = pkTeamMemberRecordDtoList.stream().collect(Collectors.toMap(PkTeamMemberRecordDto::getTeamRecordId, x->x));
		}
		for(PkTeamRecordDto pkTeamRecordDto: hasClearRecordList){
			TeamBattlePKRecordVO teamBattlePKRecordVO = new TeamBattlePKRecordVO();
			teamBattlePKRecordVO.setTeamRecordId(pkTeamRecordDto.getId());
			teamBattlePKRecordVO.setRecordDate(assembleDateStr(pkTeamRecordDto.getRecordDate()));
			teamBattlePKRecordVO.setRecordStatus(assembleDetail(pkTeamRecordDto));
			teamBattlePKRecordVO.setReadValue(assembleReadValue(pkTeamRecordDto));
			teamBattlePKRecordVO.setOpenPrizeStatus(assembleOpenPrizeStatus(pkTeamRecordDto, teamMemberRecordPrizeMap));
			teamBattlePKRecordVOList.add(teamBattlePKRecordVO);
		}
		return teamBattlePKRecordVOList;
	}

	private String assembleDateStr(Long recordDate){
		String day = DateUtils.getOnlyDayStr(recordDate * 1000);
		return StringUtils.replace(day, "-", ".");
	}

	private Integer assembleDetail(PkTeamRecordDto pkTeamRecordDto){
		if(pkTeamRecordDto.getRecordType().equals(ShuQiPKRecordTypeEnum.MEMBER_JOIN_TEAM.getCode())){
			return ShuQiTeamRecordStatusEnum.MEMBER_JOIN.getStatus();
		}
		if(pkTeamRecordDto.getRecordType().equals(ShuQiPKRecordTypeEnum.TEAM_COMPLETE.getCode())){
			return ShuQiTeamRecordStatusEnum.TEAM_COMPLETE.getStatus();
		}
		if(pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) > 0){
			return ShuQiTeamRecordStatusEnum.PK_WIN.getStatus();
		}
		if(pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) == 0){
			return ShuQiTeamRecordStatusEnum.PK_DRAW.getStatus();
		}
		return ShuQiTeamRecordStatusEnum.PK_LOSE.getStatus();
	}

	private Long assembleReadValue(PkTeamRecordDto pkTeamRecordDto){
		if(ShuQiPKRecordTypeEnum.TEAM_PK.getCode().equals(pkTeamRecordDto.getRecordType()) && pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) > 0){
			// 如果是pk的记录，且战队获胜，战队会额外+100阅读值
			return pkTeamRecordDto.getReadValue() + 100;
		}else {
			return pkTeamRecordDto.getReadValue();
		}
	}

	private Integer assembleOpenPrizeStatus(PkTeamRecordDto pkTeamRecordDto, Map<Long, PkTeamMemberRecordDto> teamMemberRecordPrizeMap){
		if(!pkTeamRecordDto.getRecordType().equals(ShuQiPKRecordTypeEnum.TEAM_PK.getCode())
				|| pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) < 1
				|| MapUtils.isEmpty(teamMemberRecordPrizeMap)
				|| null == teamMemberRecordPrizeMap.get(pkTeamRecordDto.getId())){
			return ShuQiTeamBattlePrizeStatusEnum.CAN_NOT_OPEN_PRIZE.getStatus();
		}
		PkTeamMemberRecordDto pkTeamMemberRecordDto = teamMemberRecordPrizeMap.get(pkTeamRecordDto.getId());
		return StringUtils.isNotBlank(pkTeamMemberRecordDto.getOrderNum())
				? ShuQiTeamBattlePrizeStatusEnum.HAS_OPEN_PRIZE.getStatus()
				: ShuQiTeamBattlePrizeStatusEnum.CAN_OPEN_PRIZE.getStatus();
	}

	@Override
	public String openBattlePrize(Long teamRecordId, AppSimpleDto appSimpleDto, ConsumerDto consumerDto, HttpServletRequest request) throws BizException {
		PkTeamRecordDto pkTeamRecordDto = remotePkTeamRecordService.getById(teamRecordId);
		if(null == teamRecordId){
            logger.warn("书旗春节组队pk-开奖，pk记录不存在");
			throw new BizException("pk记录不存在");
		}
		if(!ObjectUtils.equals(ShuQiPKRecordTypeEnum.TEAM_PK.getCode(), pkTeamRecordDto.getRecordType())
				|| ObjectUtils.equals(YesOrNoEnum.NO.getCode(), pkTeamRecordDto.getSyncStatus())){
			logger.warn("书旗春节组队pk-开奖，pk记录数据有误，不能开奖，pk记录：{}", JSON.toJSON(pkTeamRecordDto));
			throw new BizException("数据有误，不能领奖");
		}
		if(pkTeamRecordDto.getReadValue().compareTo(pkTeamRecordDto.getEnemyReadValue()) <= 0){
			throw new BizException("未获胜，不能领奖");
		}
        List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(Collections.singletonList(teamRecordId), consumerDto.getId());
		if(CollectionUtils.isEmpty(pkTeamMemberRecordDtoList)){
			logger.warn("书旗春节组队pk-开奖，用户记录不存在，pkTeamRecordDto：{}, consumerId：{}", JSON.toJSON(pkTeamRecordDto), consumerDto.getId());
			throw new BizException("用户记录不存在");
		}
		PkTeamMemberRecordDto pkTeamMemberRecordDto = pkTeamMemberRecordDtoList.get(0);
		if(null != pkTeamMemberRecordDto.getOrderNum()){
			throw new BizException("已领过，不能再领");
		}
		Long pkPluginId = shuQiTeamBattleConstant.getPkPrizePluginId();
		Long standbyPluginId = shuQiTeamBattleConstant.getShuQiStandbyPluginId();
		if(null == pkPluginId || null == standbyPluginId){
			logger.warn("pk出奖插件未配置");
			throw new BizException("出奖配置未配置");
		}
		String orderNum = openPrize(appSimpleDto, consumerDto, pkPluginId, standbyPluginId, request);
		// 保存出奖订单号
		pkTeamMemberRecordDto.setOrderNum(orderNum);
		remotePkTeamMemberRecordService.updateOrderNum(pkTeamMemberRecordDto);
		return orderNum;
	}

	@Override
	public String praise(Long operateActivityId, Long duibaActivityId, String shareCode, Long appId, Long consumerId) {
		// 当前时间校验
		if(checkIfPkEndToday() || afterOpenBigPrizeTime()){
			return ErrorCode.E0200301.getErrorCode();
		}
		// 分享码格式校验
		if(!ShareCodeValidator.isUserShareCode(shareCode)) {
			return ActivityComCenterErrorEnum.CODE_2002002013.getCode();
		}
		// 校验是否是给自己战队点赞
		Pair<String, Long>  pairTeam = checkPraiseSelfTeam(operateActivityId, shareCode, appId, consumerId);
		if(StringUtils.isNotBlank(pairTeam.getValue0())){
			return pairTeam.getValue0();
		}
		// 分享码通用校验
		Pair<String, Long> pairShareCode = checkShareCode(operateActivityId, duibaActivityId, shareCode, appId, consumerId);
		if(StringUtils.isNotBlank(pairShareCode.getValue0())){
			return pairShareCode.getValue0();
		}
		try {
			Boolean result = remotePkTeamMemberRecordService.updatePraiseCount(pairTeam.getValue1(), recordDateMillis(new Date()), pairShareCode.getValue1());
			return result ? null : String.valueOf(HelpShareErrorCodeEnum.E100001.getErrorCode());
		}catch (BizException e){
			logger.info("点赞异常，teamId：{}，consumerId：{}", pairTeam.getValue1(), pairShareCode.getValue1(), e);
			return String.valueOf(HelpShareErrorCodeEnum.E100001.getErrorCode());
		}
	}

	private Pair<String, Long> checkShareCode(Long operateActivityId, Long duibaActivityId, String shareCode, Long appId, Long consumerId){
		try {
			UserInviteParam userInviteParam = new UserInviteParam();
			userInviteParam.setActivityId(operateActivityId);
			userInviteParam.setDuibaActivityId(duibaActivityId);
			userInviteParam.setAppId(appId);
			userInviteParam.setInviteConsumerId(consumerId);
			userInviteParam.setShareCode(shareCode);
			userInviteParam.setShareCodeActivityTypeEnum(ShareCodeActivityTypeEnum.HDTOOL);
			userInviteParam.setSingleLimitType(LimitScopeEnum.EVERY_DAY.getId());
			userInviteParam.setSingleLimitTimes(1);
			InviteResponseDto inviteResponseDto = remoteUserShareCodeService.userInvite(userInviteParam);
			return Pair.with(null, inviteResponseDto.getInviter());
		}catch (BizException e){
			logger.warn("点赞失败", e);
			return Pair.with(e.getCode(), null);
		}
	}

	private Pair<String, Long> checkPraiseSelfTeam(Long operateActivityId, String shareCode, Long appId, Long consumerId){
		Long sharedConsumerId;
		try {
			sharedConsumerId = remoteNewConsumerShareCodeService.getConsumerIdByShareCode(appId, operateActivityId, shareCode);
		}catch (BizException e){
			logger.warn("书旗春节组队pk-点赞失败，获取分享码所属用户异常", e);
			return Pair.with(ActivityComCenterErrorEnum.CODE_2002002012.getCode(), null);
		}
		PkTeamMemberInfoDto sharedMemberInfo = getTeamMemberByConsumerId(sharedConsumerId);
		if(null == sharedMemberInfo){
			logger.warn("点赞失败，分享者没有战队信息，分享者id：{}", sharedConsumerId);
			return Pair.with(String.valueOf(HelpShareErrorCodeEnum.E100001.getErrorCode()), null);
		}
		// 判断是否有加入战队
		PkTeamMemberInfoDto selfMemberInfo = getTeamMemberByConsumerId(consumerId);
		if(null == selfMemberInfo){
			return Pair.with(null, sharedMemberInfo.getTeamId());
		}
		// 判断是否是给自己战队点赞
		if(sharedMemberInfo.getTeamId().equals(selfMemberInfo.getTeamId())){
			return Pair.with(ErrorCode.E0200302.getErrorCode(), null);
		}
		return Pair.with(null, sharedMemberInfo.getTeamId());
	}

	@Override
	public Result createTeam(Long consumerId, Long appId, String teamName, Long duibaActivityId) {
		if(afterOpenBigPrizeTime()){
			return ResultBuilder.fail(ResultCode.C100063.getCode(), ResultCode.C100063.getDescription());
		}
        PkTeamMemberInfoDto teamMemberInfo = remotePkTeamMemberInfoService.getByConsumerId(consumerId);
        if (teamMemberInfo != null) {
            return ResultBuilder.fail(ResultCode.C100902.getCode(), ResultCode.C100902.getDescription());
        }
		if (redisAtomicClient.getLong(teamNameRedisKey(teamName)) != null) {
			return ResultBuilder.fail(ResultCode.C100904.getCode(), ResultCode.C100904.getDescription());
		}
		redisAtomicClient.incrBy(teamNameRedisKey(teamName), 1, 15, TimeUnit.DAYS);
		try {
			PkTeamInfoDto teamInfo = new PkTeamInfoDto();
			teamInfo.setActivityId(duibaActivityId);
			teamInfo.setTeamName(teamName);
			teamInfo.setAppId(appId);
			teamInfo.setMemberNum(0);
			remotePkTeamInfoService.insert(teamInfo, consumerId);
			return ResultBuilder.success();
		} catch (Exception e) {
			logger.error("创建战队失败，consumerId:{}", consumerId, e);
		}
        return ResultBuilder.fail(ResultCode.C100403.getCode(), ResultCode.C100403.getDescription());
	}

    @Override
	public Result<TeamBattlePKMemberVO> joinTeam(Long consumerId, Long appId, Long duibaActivityId, Long inviteConsumerId) {
	    if(afterOpenBigPrizeTime()){
		    return ResultBuilder.fail(ResultCode.C100063.getCode(), ResultCode.C100063.getDescription());
	    }
		Long teamId = getTeamMemberByConsumerId(inviteConsumerId).getTeamId();
		if (teamId == null) {
			return ResultBuilder.fail(ResultCode.C100907.getDescription(), ResultCode.C100907.getDescription());
		}
		// 不经过缓存查询用户是否已经加入战队
	    PkTeamMemberInfoDto teamMemberInfo = remotePkTeamMemberInfoService.getByConsumerId(consumerId);
	    if (teamMemberInfo != null) {
		    return ResultBuilder.fail(ResultCode.C100902.getCode(), ResultCode.C100902.getDescription());
	    }
	    teamMemberInfo = new PkTeamMemberInfoDto();
		teamMemberInfo.setActivityId(duibaActivityId);
		teamMemberInfo.setConsumerId(consumerId);
		teamMemberInfo.setAppId(appId);
		teamMemberInfo.setTeamId(teamId);
		String key = RedisKeyFactory.K167.toString() + teamId;
        try (RedisLock lock = redisAtomicClient.getLock(key, 2)){
            if (lock != null) {
                return joinTeam(teamMemberInfo);
            }
        } catch (Exception e) {
            logger.error("加入战队失败，{}", teamId, e);
        }
        return ResultBuilder.fail(ResultCode.C100906.getCode(),ResultCode.C100906.getDescription());
    }


    private Result<TeamBattlePKMemberVO> joinTeam(PkTeamMemberInfoDto teamMemberInfo) throws BizException {
	    Long teamId = teamMemberInfo.getTeamId();
	    Long duibaActivityId = teamMemberInfo.getActivityId();
	    Long appId = teamMemberInfo.getAppId();
	    Long consumerId = teamMemberInfo.getConsumerId();
        PkTeamInfoDto teamInfo = getTeamById(teamId);
        if (null == teamInfo.getMemberNum() || ObjectUtils.compare(teamInfo.getMemberNum(), MAX_TEAM_MEMBER) >= 0) {
            return ResultBuilder.fail(ResultCode.C100905.getCode(), ResultCode.C100905.getDescription());
        }
        Integer result = remotePkTeamMemberInfoService.insert(teamMemberInfo);
        if (result == 0) {
            return ResultBuilder.fail(ResultCode.C100906.getCode(), ResultCode.C100906.getDescription());
        }
        if (MIN_TEAM_MEMBER.equals(result)) {
            battleMatch(duibaActivityId, appId, teamId);
        }
        PkTeamRecordDto matchRecordDto = remotePkTeamRecordService.getByTeamIdAndDate(teamId, recordDateMillis(new Date()));
        if(null != matchRecordDto && YesOrNoEnum.NO.getCode().equals(matchRecordDto.getSyncStatus())){
	        List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(
			        Collections.singletonList(matchRecordDto.getId()), consumerId);
	        if (CollectionUtils.isEmpty(pkTeamMemberRecordDtoList)) {
		        PkTeamMemberRecordDto dto = new PkTeamMemberRecordDto();
		        dto.setActivityId(duibaActivityId);
		        dto.setAppId(appId);
		        dto.setRecordDate(matchRecordDto.getRecordDate());
		        dto.setConsumerId(consumerId);
		        dto.setTeamRecordId(matchRecordDto.getId());
		        remotePkTeamMemberRecordService.insert(dto);
		        logger.info("生成用户pk记录，consumerId:{}", consumerId);
	        }
        }
        TeamBattlePKMemberVO memberInfo = buildNickAvatar(consumerId);
        return ResultBuilder.success(memberInfo);
    }

    @Override
    public Result<Boolean> commitAwardInfo(Long consumerId, String name, String mobile, String alipay, String address) {
        if (!afterOpenBigPrizeTime()) {
            return ResultBuilder.fail(ResultCode.C100909.getCode(), ResultCode.C100909.getDescription());
        }
        PkTeamMemberInfoDto teamMemberInfo = getTeamMemberByConsumerId(consumerId);
        if (teamMemberInfo == null) {
            logger.info("查询不到用户战队信息");
            return ResultBuilder.fail(ResultCode.C100907.getCode(), ResultCode.C100907.getDescription());
        }
        List<TeamMemberPrizeBO> list = getBigPrizeInfo(teamMemberInfo.getActivityId(), teamMemberInfo.getAppId());
        if (CollectionUtils.isEmpty(list)) {
            return ResultBuilder.fail(ResultCode.C100801.getCode(), ResultCode.C100801.getDescription());
        }
        Map<Long, TeamMemberPrizeBO> prizeMap = list.stream().collect(Collectors.toMap(TeamMemberPrizeBO::getConsumerId, f -> f));
        if (!prizeMap.containsKey(consumerId)) {
            return ResultBuilder.fail(ResultCode.C100803.getCode(), ResultCode.C100803.getDescription());
        }
        String key = redisKeyForPrize(teamMemberInfo.getAppId(), teamMemberInfo.getActivityId());
        TeamMemberPrizeBO prizeBO = prizeMap.get(consumerId);
        prizeBO.setContactName(name);
        prizeBO.setContactPhone(mobile);
        prizeBO.setContactText(address == null ? alipay : address);
        hashOpsForBigPrize().put(key, String.valueOf(consumerId), prizeBO);
        return ResultBuilder.success(true);
    }

    private TeamBattlePKMemberVO buildNickAvatar (Long consumerId) {
	    ConsumerDto consumerDto = consumerCacheService.findConsumerById(consumerId);
		ConsumerExtraDto consumerExtra = consumerCacheService.getConsumerExtraByConsumerId(consumerId);
		TeamBattlePKMemberVO memberInfo = new TeamBattlePKMemberVO();
		memberInfo.setEnd(false);
		memberInfo.setConsumerId(consumerId);
	    memberInfo.setPartnerUserId(consumerDto.getPartnerUserId());
	    if (consumerExtra != null) {
			memberInfo.setAvatar(consumerExtra.getAvatar());
			memberInfo.setNickName(consumerExtra.getNickname());
		}
		return memberInfo;
	}

    @Override
    public TeamBattlePKMemberVO getFriendInfo(Long consumerId) {
        TeamBattlePKMemberVO teamMember = new TeamBattlePKMemberVO();
        if (afterOpenBigPrizeTime()) {
            teamMember.setEnd(true);
            return teamMember;
        }
		PkTeamMemberInfoDto teamMemberInfo = remotePkTeamMemberInfoService.getByConsumerId(consumerId);
		if (teamMemberInfo != null) {
			return buildNickAvatar(consumerId);
		}
		return null;
    }

    @Override
    public TeamBattlePKRecordVO getTeamInfo(Long consumerId) {
        TeamBattlePKRecordVO teamInfo = new TeamBattlePKRecordVO();
        if (afterOpenBigPrizeTime()) {
            teamInfo.setEnd(true);
	        return teamInfo;
        }
        teamInfo.setEnd(false);
        PkTeamMemberInfoDto teamMemberInfo = remotePkTeamMemberInfoService.getByConsumerId(consumerId);
        if(null == teamMemberInfo){
        	return teamInfo;
        }
        PkTeamRecordDto teamRecord = remotePkTeamRecordService.getByTeamIdAndDate(teamMemberInfo.getTeamId(), recordDateMillis(new Date()));
        if (teamRecord == null) {
            PkTeamInfoDto info = getTeamById(teamMemberInfo.getTeamId());
            teamInfo.setTeamName(info.getTeamName());
            return teamInfo;
        }
        PkTeamRecordDto enemyPkRecord = remotePkTeamRecordService.getByTeamIdAndDate(teamRecord.getRelateId(), recordDateMillis(new Date()));
        Map<Long, Long> teamRecordIdMap = Maps.newHashMap();
        teamRecordIdMap.put(teamRecord.getId(), teamRecord.getTeamId());
        teamRecordIdMap.put(enemyPkRecord.getId(), enemyPkRecord.getTeamId());
		List<PkTeamMemberRecordDto> pkTeamMemberRecordDtoList = remotePkTeamMemberRecordService.getByTeamRecordIds(Arrays.asList(teamRecord.getId(), enemyPkRecord.getId()), null);
        calculateTotalReadValue(teamRecord, teamRecordIdMap,pkTeamMemberRecordDtoList);

        teamInfo.setTeamName(teamRecord.getTeamName());
        teamInfo.setReadValue(teamRecord.getReadValue());
        teamInfo.setEnemyReadValue(teamRecord.getEnemyReadValue());
        return teamInfo;

    }

    @Override
    public List<TeamBattlePKMemberVO> individualContribution(Long consumerId) {
		List<TeamBattlePKMemberVO> list = new ArrayList<>();
        PkTeamMemberInfoDto teamMemberInfo = getTeamMemberByConsumerId(consumerId);
		if(null == teamMemberInfo){
			return Collections.emptyList();
		}
	    // 获取当前战队的历史记录
	    List<PkTeamRecordDto> totalTeamRecords = getPastByTeamId(teamMemberInfo.getTeamId());
		if(CollectionUtils.isEmpty(totalTeamRecords)){
            return Collections.emptyList();
		}
	    // 获取pk记录
	    List<Long> teamRecordIds = totalTeamRecords.stream()
	                                               .filter(x-> ObjectUtils.equals(x.getRecordType(), ShuQiPKRecordTypeEnum.TEAM_PK.getCode())
			                                               && ObjectUtils.equals(x.getSyncStatus(), YesOrNoEnum.YES.getCode()))
	                                               .map(PkTeamRecordDto::getId).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(teamRecordIds)){
            return Collections.emptyList();
		}
		List<PkTeamMemberRecordDto> records = remotePkTeamMemberRecordService.getByTeamRecordIds(teamRecordIds, consumerId);
		records.forEach(f -> {
			TeamBattlePKMemberVO vo = new TeamBattlePKMemberVO();
			vo.setConsumerId(consumerId);
			vo.setRecordDate(assembleDateStr(f.getRecordDate()));
			vo.setPraiseNum(f.getPraiseCount());
			vo.setReadTime(f.getReadTime());
			list.add(vo);
		});
        return list;
    }

	private String teamNameRedisKey(String teamName) {
		return RedisKeyFactory.K168 + teamName;
	}

	private String senWordRedisKey(String teamName) {
		return RedisKeyFactory.K169 + teamName;
	}
}
