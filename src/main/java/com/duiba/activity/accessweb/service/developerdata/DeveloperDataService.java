package com.duiba.activity.accessweb.service.developerdata;


/**
 * 通过http请求获取开发者数据
 * Created by hww on 2017/12/25 上午11:44.
 */
public interface DeveloperDataService {

    /**
     * 请求开发者数据
     * @param url
     * @return
     */
    String sendGet(String url);

    /**
     * 麦拉流量策略判断
     * @param ip
     * @param appId
     * @return
     */
    boolean maiLaFlowCheck(String ip, Long appId);
}
