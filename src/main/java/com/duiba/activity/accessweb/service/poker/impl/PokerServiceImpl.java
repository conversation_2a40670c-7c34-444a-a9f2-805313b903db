package com.duiba.activity.accessweb.service.poker.impl;

import com.duiba.activity.accessweb.service.poker.PokerService;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 扑克牌实现类
 * Created by hww on 2018/1/23 下午2:21.
 */
@Service
public class PokerServiceImpl implements PokerService {

    /** 表示黑桃花色 */
    private static final int SPADE = 1;
    /** 表示梅花花色 */
    private static final int CLUB = 2;
    /** 表示红桃花色 */
    private static final int HEART = 3;
    /** 表示方片花色 */
    private static final int DIAMOND = 4;

    /** 表示整副牌 */
    private static final ImmutableSet<Integer> POKER;

    static {
        List<Integer> list = Lists.newArrayList();
        for (int i = 1; i < 14; i++) {
            list.add(i * 10 + SPADE);
            list.add(i * 10 + CLUB);
            list.add(i * 10 + HEART);
            list.add(i * 10 + DIAMOND);
        }
        POKER = ImmutableSet.copyOf(list);
    }

    @Override
    public List<Integer> getFUllPoker() {
        return Lists.newArrayList(POKER);
    }

    @Override
    public Integer getRandomFromFull() {
        return POKER.asList().get((int) (Math.random() * 52));
    }

    @Override
    public List<Integer> getAllRed() {
        return POKER.stream()
                .filter(i -> i % 10 == HEART || i % 10 == DIAMOND)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getAllBlack() {
        return POKER.stream()
                .filter(i -> i % 10 == SPADE || i % 10 == CLUB)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getAllGreater(int offset) {
        return POKER.stream()
                .filter(i -> i / 10 > offset )
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getAllLess(int offset) {
        return POKER.stream()
                .filter(i -> i / 10 < offset)
                .collect(Collectors.toList());
    }

}
