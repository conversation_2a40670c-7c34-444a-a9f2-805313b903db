package com.duiba.activity.accessweb.service.handler.insurance;

import cn.com.duiba.activity.center.api.dto.activity.InsuranceResultDto;
import cn.com.duiba.activity.center.api.dto.activity.InsuranceUserInfoDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteInsuranceResultService;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteInsuranceUserInfoService;

import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.enums.InsuranceMsgEnum;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.exception.InsuranceException;
import com.duiba.activity.accessweb.service.HttpService;
import com.duiba.activity.accessweb.service.handler.insurance.param.PolicyTerms;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.Map;
import java.util.concurrent.Semaphore;

/**
 * Created by wubo on 2017/3/20.
 */
@Component
public class SuYiInsuranceHandler implements InsuranceHandler {

    @Autowired
    private RemoteInsuranceUserInfoService remoteInsuranceUserInfoService;
    @Autowired
    private RemoteInsuranceResultService   remoteInsuranceResultService;
    @Autowired
    private HttpService                    httpService;

    private Semaphore                      semaphore = new Semaphore(50);

    private static Logger                  log       = LoggerFactory.getLogger(SuYiInsuranceHandler.class);

    @Override
    public InsuranceResultDto handle(InsuranceUserInfoDto userInfo, Map<String, String> extParams)
                                                                                                  throws InsuranceException {
        boolean success = semaphore.tryAcquire();
        if (!success) {
            throw new AccessActivityRuntimeException("系统繁忙!");
        }

        try {
            String data = extParams.get("json");
            String data2;

            
            if(StringUtils.isBlank(data)){
                throw new AccessActivityRuntimeException("数据为空");
            }else{
                data2 = URLDecoder.decode(data, "utf-8");
            }
            JSONObject json = JSONObject.parseObject(data2);
            
            int code = json.getIntValue("errCode");
            
            extParams.put(PolicyTerms.POLICY_TERMS, json.getString("bxArticle"));
            
            if(0==code){
                String bxtypeName = json.getString("bxtypeName");
                extParams.put(PolicyTerms.COMPANY_NAME, json.getString("brandName"));
                extParams.put(PolicyTerms.POLICY_TIME, json.getString("bxExpire"));
                extParams.put(PolicyTerms.POLICY_NAME, bxtypeName);
                throw new InsuranceException(true,"0",InsuranceMsgEnum.M023003.getMessage(bxtypeName),"");
            }else{
                throw new InsuranceException(true,InsuranceMsgEnum.M006003);
            }

        } catch (InsuranceException e) {
            throw e;
        } catch (Exception e) {
            log.error("速易接口异常,error:", e);
            throw new InsuranceException(InsuranceMsgEnum.M006001);
        } finally {
            semaphore.release();
        }

    }
    
}
