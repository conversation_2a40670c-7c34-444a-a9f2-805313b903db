package com.duiba.activity.accessweb.service.luckycode.helper;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 幸运码生成相关辅助工具
 *
 * <AUTHOR>
 * @Date 2019/3/21
 */
public class LuckyCodeHelper {
    //幸运码字符数
    public static final int LUCKY_CODE_SIZE = 9;

    /**
     * 生成抽奖码
     *
     * @param luckyCode      幸运码
     * @param unknownCodeSize 未知数个数
     * @return 抽奖码字符数组
     */
    public static char[] generateDrawCode(char[] luckyCode, int unknownCodeSize) {

        int codeSize = luckyCode.length;

        //未知数最多与幸运码字符个数相等
        if (unknownCodeSize > codeSize) {
            unknownCodeSize = codeSize;
        }

        // 随机选择未知数索引
        return generateDrawCode(luckyCode, randomUnknownCodeIndex(codeSize, unknownCodeSize));
    }

    private static int[] randomUnknownCodeIndex(int codeSize, int unknownCodeSize) {

        // 幸运码字符索引数组
        int[] codeIndexArr = new int[codeSize];
        for (int i = 0; i < codeSize; ++i) {
            codeIndexArr[i] = i;
        }
        // Knuth-Durstenfeld Shuffle 洗牌算法随机未知数索引
        for (int i = 0; i < unknownCodeSize; ++i) {
            int end = codeSize - i;
            int random = RandomUtils.nextInt(0, end);
            int randomIndex = codeIndexArr[random];
            int tmp = end - 1;
            // 将已随机到的值与数组尾部进行交换
            codeIndexArr[random] = codeIndexArr[tmp];
            codeIndexArr[tmp] = randomIndex;
        }

        return Arrays.copyOfRange(codeIndexArr, codeSize - unknownCodeSize, codeSize);
    }

    /**
     * 根据指定位置设置未知数，生成抽奖码
     * @param luckyCode 幸运码
     * @param unknownIndexArr 未知数的位置索引
     * @return
     */
    public static char[] generateDrawCode(char[] luckyCode, int[] unknownIndexArr) {
        char[] result = Arrays.copyOf(luckyCode, luckyCode.length);
        if (ArrayUtils.isEmpty(unknownIndexArr)) {
            return result;
        }
        for (int codeIndex : unknownIndexArr) {
            result[codeIndex] = randomGroup(luckyCode[codeIndex]);
        }
        return result;
    }

    /**
     * 概率命中
     *
     * @param rate 命中率 0 - 100
     * @return 是否命中
     */
    public static boolean randomHit(int rate) {
        if (rate < 0 || rate > 100) {
            throw new IllegalStateException("概率数值有误，请输入0-100之间的数值：" + rate);
        }
        return RandomUtils.nextInt(0, 100) < rate;
    }

    /**
     * 随机获取数字码的一个分组
     *
     * @param code 数子码
     * @return 分组码
     */
    public static char randomGroup(char code) {
        List<LuckyCodeGroup> groups = LuckyCodeGroup.getCodeGroups(code);
        if (CollectionUtils.isEmpty(groups)) {
            throw new IllegalStateException("不合法的幸运码字符：" + code);
        }
        return groups.get(RandomUtils.nextInt(0, groups.size())).getGroupCode();
    }

    /**
     * 同组随机一个数字
     *
     * @param groupCode     分组码
     * @param exclusiveCode 排除的数字
     * @return
     */
    public static char randomCodeInGroup(char groupCode, char exclusiveCode) {
        LuckyCodeGroup group = LuckyCodeGroup.of(groupCode);
        if (group == null) {
            throw new IllegalStateException("不合法的分组码：" + groupCode);
        }
        char[] codes = group.getCodes();
        int index = RandomUtils.nextInt(0, codes.length);
        char randomNum = codes[index];
        if (randomNum == exclusiveCode) {
            if (index == (codes.length - 1)) {
                return codes[index - 1];
            } else {
                return codes[index + 1];
            }
        } else {
            return randomNum;
        }
    }

    /**
     * 随机一个数字码
     *
     * @param trueCode 正确的数字
     * @param prevCode 前一个数字
     * @return 0 - 9 之间随机字符（除了正确数字和前一个数字）
     */
    public static char randomCode(char trueCode, char prevCode) {
        char[] arr = new char[trueCode == prevCode ? 9 : 8];
        for (int i = 0, j = 0; i < 10; i++) {
            char c = (char) (48 + i);
            if (c != trueCode && c != prevCode) {
                arr[j++] = c;
            }
        }
        return arr[RandomUtils.nextInt(0, arr.length)];
    }

    /**
     * 随机一个数字码
     *
     * @param exclusiveCode 排除的数字码
     * @return 0 - 9 之间随机字符（除了被排除的数字）
     */
    public static char randomCode(char exclusiveCode) {
        //因为要排除一个数字，所以在9个数字中随机
        int code = RandomUtils.nextInt('0', '9');
        //随机数字小于当前数字，直接返回
        if (code < exclusiveCode) {
            return (char) code;
        }
        //随机数字大于等于当前数字，需要+1
        return (char) (code + 1);
    }

    /**
     * 解析抽奖码
     *
     * @param drawCodeStr 抽奖码字符串
     * @return 抽奖码
     */
    public static char[] parseDrawCode(String drawCodeStr) {
        if (drawCodeStr == null || drawCodeStr.length() != LUCKY_CODE_SIZE) {
            throw new IllegalArgumentException("抽奖码格式有误：" + drawCodeStr);
        }
        return drawCodeStr.toCharArray();
    }

    /**
     * 解析幸运码
     *
     * @param luckyCodeLong 幸运码数值
     * @return
     */
    public static char[] parseLuckyCode(Long luckyCodeLong) {
        if (luckyCodeLong == null) {
            throw new IllegalStateException("幸运码配置为空");
        }
        String luckyCodeStr = luckyCodeLong.toString();
        char[] luckyCode = new char[LUCKY_CODE_SIZE];
        int j = luckyCode.length - 1;
        for (int i = luckyCodeStr.length() - 1; i >= 0; i--, j--) {
            luckyCode[j] = luckyCodeStr.charAt(i);
        }
        for (; j >= 0; j--) {
            luckyCode[j] = '0';
        }
        return luckyCode;
    }

    /**
     * 奖char数组转化成string数组
     *
     * @param charArr
     * @return
     */
    public static String[] parseCharArr2StringArr(char[] charArr) {
        if (charArr == null) {
            return null;
        }
        String[] strArr = new String[charArr.length];
        for (int i = 0; i < charArr.length; i++) {
            strArr[i] = String.valueOf(charArr[i]);
        }
        return strArr;
    }

    public static void main(String[] args) {
        for (int i = 0; i < 20; i++) {
            System.out.println(generateDrawCode(new char[]{'1', '2', '3', '4', '5', '6', '7', '8', '9'}, 3));
        }
    }
}
