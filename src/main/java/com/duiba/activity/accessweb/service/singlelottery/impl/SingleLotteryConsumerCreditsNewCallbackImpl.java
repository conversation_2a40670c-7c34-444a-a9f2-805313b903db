package com.duiba.activity.accessweb.service.singlelottery.impl;

import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryOrderService;
import cn.com.duiba.api.bo.subcredits.SubCreditsResultMsgDto;
import cn.com.duiba.api.enums.HttpRequestResultType;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import com.duiba.activity.accessweb.core.exception.DeveloperConsumeFailedException;
import com.duiba.activity.accessweb.service.singlelottery.SingleLotteryConsumerCreditsNewCallback;
import com.duiba.activity.accessweb.service.singlelottery.SingleLotteryConsumerCreditsService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @Date 2020-04-28
 * @Email: <EMAIL>
 * @Description:
 */
@Service
public class SingleLotteryConsumerCreditsNewCallbackImpl implements SingleLotteryConsumerCreditsNewCallback {

    private static final Logger logger = LoggerFactory.getLogger(SingleLotteryConsumerCreditsNewCallbackImpl.class);



    @Autowired
    private RemoteSingleLotteryOrderService remoteSingleLotteryOrderService;

    @Autowired
    private SingleLotteryConsumerCreditsService singleLotteryConsumerCreditsService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;

    /**
     * 新版扣积分回调
     *
     * @param message
     * <AUTHOR>
     */
    @Override
    public void creditsCallback(SubCreditsResultMsgDto message) {
        if(null == message || StringUtils.isBlank(message.getRelationId())){
            return;
        }
        // 查询自订单信息
        SingleLotteryOrderDto order = remoteSingleLotteryOrderService.find(Long.parseLong(message.getRelationId()));
        if(order == null || order.getStatus()!= SingleLotteryOrderDto.StatusCreate){
            return;
        }
        // 查询自订单信息
        if (Objects.equals(HttpRequestResultType.COMPLETED,message.getResultType())) {
            completed(message);
        } else if (Objects.equals(HttpRequestResultType.FAILED,message.getResultType())) {
            failed(message);
        } else if (Objects.equals(HttpRequestResultType.CANCELLED,message.getResultType())) {
            cancelled(message);
        }
    }


    private void cancelled(final SubCreditsResultMsgDto message){
        final DeveloperConsumeFailedException exception =
                new DeveloperConsumeFailedException("","请求被取消",false,
                        message.getHttpUrl(),message.getResponse(),
                        DeveloperConsumeFailedException.FailTypeDefault
                        ,DeveloperConsumeFailedException.ConsumerCreditsType);
        executorService.submit(() -> singleLotteryConsumerCreditsService.onCreditsFail(
                message.getParams(),message.getRelationId(), exception));

    }

    private void completed(final SubCreditsResultMsgDto message){
        Exception exception = null;
        try{
            successBlock(message);
        }catch(Exception e){
            exception = e;
            if(!(e instanceof DeveloperConsumeFailedException)){
                exception = new DeveloperConsumeFailedException("","向开发者请求扣积分，网络请求出错",false,
                        message.getHttpUrl(),message.getResponse(),
                        DeveloperConsumeFailedException.FailTypeDefault
                        ,DeveloperConsumeFailedException.ConsumerCreditsType);
            }
        }
        final Exception finallyE=exception;
        executorService.submit(() -> {
            if(finallyE == null){
                singleLotteryConsumerCreditsService.onCreditsSuccess(message.getParams()
                        ,String.valueOf(message.getConsumerId()),message.getRelationId());
            }else{
                singleLotteryConsumerCreditsService.onCreditsFail(
                        message.getParams(),message.getRelationId(), finallyE);
            }
        });
    }

    private void failed(final SubCreditsResultMsgDto message){
        final Exception finallyE = new DeveloperConsumeFailedException("","向开发者请求扣积分，网络请求出错",false,
                message.getHttpUrl(),message.getResponse(),
                DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
        executorService.submit(() -> singleLotteryConsumerCreditsService.onCreditsFail(
                message.getParams(),message.getRelationId(), finallyE));
    }

    private void successBlock(SubCreditsResultMsgDto message) throws DeveloperConsumeFailedException{
        if (Objects.equals(message.getCode(),SubCreditsResultMsgDto.CODE_SUCCESS)) {
            final Long credits = message.getCredits();
            String orderNum = message.getRelationId();
            if (credits != null && credits >= 0) {
                remoteConsumerService.updateCredits(message.getConsumerId(), credits);
            }
            final String bizId = message.getBizId();
            if(org.apache.commons.lang3.StringUtils.isBlank(bizId)){
                throw new DeveloperConsumeFailedException("","开发者服务器响应开发订单号为空",false,
                        message.getHttpUrl(),message.getResponse(),
                        DeveloperConsumeFailedException.FailTypeJson,DeveloperConsumeFailedException.ConsumerCreditsType);
            }
            remoteSingleLotteryOrderService.updateDeveloperBizId(Long.parseLong(orderNum), bizId);
        } else {
            throw new DeveloperConsumeFailedException("",message.getErrorMessage(),true,
                    message.getHttpUrl(),message.getResponse(),
                    DeveloperConsumeFailedException.FailTypeDefault,DeveloperConsumeFailedException.ConsumerCreditsType);
        }
    }

}
