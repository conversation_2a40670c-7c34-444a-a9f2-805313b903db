package com.duiba.activity.accessweb.service;

import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 联想活动加次数定制
 *
 * <AUTHOR>
 * @date 2021-09-02
 */
@Service
public class LianXiangTimesService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LianXiangTimesService.class);

    @Autowired
    private RemoteConsumerService remoteConsumerService;

    private  static final String HDCSZJJKSFZCWDLYHQQ_liukai = "HDCSZJJKSFZCWDLYHQQ_liukai";


    /**
     * 如果用户未访问过兑吧，这里生成基本用户记录
     *
     * @param appId
     * @param uid
     */
    public void customCheckConsumer(Long appId, String uid) {
        if(!WhiteAccessUtil.matchWhiteList(appId,HDCSZJJKSFZCWDLYHQQ_liukai)){
            return;
        }
        if (StringUtils.isBlank(uid)) {
            LOGGER.info("联想定制活动加次数，uid为空 uid={}", uid);
            return;
        }
        ConsumerDto consumerDto = remoteConsumerService.findByAppAndPartnerUserId(appId, uid);
        //为空，用户未进入兑吧，生成基本用户信息，给用户加次数
        if (consumerDto == null) {
            ConsumerDto consumer = new ConsumerDto(true);
            consumer.setAppId(appId);
            consumer.setPartnerUserId(uid);
            consumer.setCredits(0L);
            remoteConsumerService.insert(consumer);
        }

    }

}
