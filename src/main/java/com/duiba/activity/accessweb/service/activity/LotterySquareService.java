package com.duiba.activity.accessweb.service.activity;

import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareConfigDto;
import cn.com.duiba.activity.center.api.enums.LSBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.LimitScopeEnum;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountCashDrawsRequest;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareAccountDetailVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareCashDrawsInfoVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareRecordPopVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/12/17
 */
public interface LotterySquareService {
    /**
     * 邀请码绑定
     * @param configDto
     * @param shareCode
     * @param opId
     * @param consumer
     * @param transfer
     * @param ip
     * @return
     * @throws BizException
     */
    Integer bindShareCode(LotterySquareConfigDto configDto,String shareCode,Long opId, ConsumerDto consumer, String transfer, String ip) throws BizException;

    /**
     * 邀请链接点击，校验是否有奖励（有效分享）
     * @param shareCode 分享码
     * @param uid 用户partnerUserId
     * @param optId 活动入库ID
     */
    void shareLinkClick(String uid,Long optId,String shareCode);

    /**
     * 首次分享记录
     * @param actId
     * @param consumerId
     */
    void firstShare(Long actId,Long consumerId);

    /**
     * 提现
     * @param request
     * @throws BizException
     */
    void cashDraws(AccountCashDrawsRequest request) throws BizException;

    /**
     * 获取未读梯度奖励，获取后将其置为已读
     * @param activityId
     * @param consumerId
     * @throws BizException
     */
    List<LotterySquareRecordPopVo> getUnReadBonusAndUpdate(Long activityId, Long consumerId, LSBonusTypeEnum bonusType,AppSimpleDto app);


    /**
     * 资金流水
     * @param actId
     * @param consumerId
     * @param currentPage
     * @param pageSize
     * @return
     * @throws BizException
     */
    List<LotterySquareAccountDetailVo> queryAccountDetail(Long actId, Long consumerId, Integer currentPage, Integer pageSize) throws BizException;

    /**
     * 确认当前用户是否为新用户
     * @param consumerId
     * @return
     */
    boolean checkNewUser(LotterySquareConfigDto configDto, Long optId, Long consumerId, String partnerUserId);

    /**
     * 确认当前用户是否已首次分享
     * @param actId
     * @param consumerId
     * @param limitScopeEnum
     * @return
     */
    boolean isUserFirstShared(Long actId, Long consumerId, LimitScopeEnum limitScopeEnum);

    /**
     * 公众号未关注用户信息记录
     * @param partnerUserId
     * @param opId
     */
    void newPublicUserNotify(String partnerUserId, Long opId);

    /**
     * 提现页面-提现按钮状态
     * @param actId
     * @param consumerId
     * @param developerId
     * @return
     */
    LotterySquareCashDrawsInfoVo cashIndexInfo(Long actId, Long consumerId, Long developerId);
}
