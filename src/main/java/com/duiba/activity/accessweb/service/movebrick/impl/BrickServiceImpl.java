package com.duiba.activity.accessweb.service.movebrick.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickAccountDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickConfPrizesDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickConfigDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickExchangeRecordDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickMarkedDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickMobileDateDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickPrizeDto;
import cn.com.duiba.activity.center.api.dto.movebrick.DuibaBrickWorkerDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.movebrick.RemoteDuibaBrickConfBackendService;
import cn.com.duiba.activity.center.api.remoteservice.movebrick.RemoteDuibaBrickMobileDataService;
import cn.com.duiba.activity.common.center.api.dto.asyntask.AsynTaskDto;
import cn.com.duiba.activity.common.center.api.dto.asyntask.BrickAysnTaskInfo;
import cn.com.duiba.activity.common.center.api.enums.asyntask.AsynTaskTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.asyntask.RemoteAsynTaskService;
import cn.com.duiba.activity.common.center.api.remoteservice.wallet.RemoteRedAccPeriodService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccPeriodRechargeRequest;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountModifyResponse;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.bo.subcredits.SubCreditsMsgDto;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.RedisKeySpace;
import cn.com.duiba.api.enums.subcredits.SubCreditsOuterType;
import cn.com.duiba.api.enums.subcredits.SubCreditsType;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.CreditConsumeParams;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemStockConsumeDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.kvtable.service.api.enums.ActAccessWebHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbConsisHashKvService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.BrickConstants;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.service.email.EmailService;
import com.duiba.activity.accessweb.service.movebrick.BrickService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.tool.AppIdConstant;
import com.duiba.activity.accessweb.tool.CatLogTool;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.tool.result.ResultUtil;
import com.duiba.activity.accessweb.vo.activity.movebrick.DuibaBrickIndexVO;
import com.duiba.activity.accessweb.vo.activity.movebrick.DuibaBrickReturnPrizeVO;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: lufeng
 * @Description:
 * @Date: Created in 2018/12/28
 */
@Service
public class BrickServiceImpl implements BrickService {

    @Autowired
    private RemoteDuibaBrickConfBackendService remoteDuibaBrickConfBackendService;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private RemoteDuibaBrickMobileDataService remoteDuibaBrickMobileDataService;
    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private DefaultMQProducer rocketMqProducer;
    @Autowired
    private RocketMqMessageTopic rocketMqMessageTopic;
    @Resource(name = "stringRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private RemoteHbConsisHashKvService remoteHbConsisHashKvService;
    @Autowired
    private RemoteAppItemGoodsService remoteAppItemGoodsService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private RemoteAppService remoteAppService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private RemoteAsynTaskService remoteAsynTaskService;
    @Autowired
    private RemoteRedAccPeriodService remoteRedAccPeriodService;
    @Autowired
    private BrickConstants brickConstants;
    @Autowired
    private RiskService riskService;

    private static final Logger LOGGER = LoggerFactory.getLogger(BrickServiceImpl.class);

    private Cache<Long, DuibaBrickConfPrizesDto> brickConfPrizesDtoCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(100)
            .build();

    private Cache<Long, DuibaBrickConfigDto> brickConfigDtoCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(100)
            .build();

    private Cache<String, Long> brickNumCache = Caffeine.newBuilder()
            .expireAfterAccess(1, TimeUnit.DAYS)
            .initialCapacity(1000)
            .maximumSize(1000)
            .build();

    private Cache<String, String> redPacketInitCache = Caffeine.newBuilder()
            .expireAfterAccess(1,TimeUnit.DAYS)
            .initialCapacity(1000)
            .maximumSize(1000)
            .build();


    @Override
    public Result<DuibaBrickIndexVO> getUserBrick(Long appId, ConsumerDto consumerDto) {
        //获取活动含奖品配置
        DuibaBrickConfPrizesDto brickConfPrizesDto = brickConfPrizesDtoCache.get(appId,
                cacheKey -> remoteDuibaBrickConfBackendService.findBrickConfDetails(appId, BrickConstants.CURRENTPERIOD));
        if (brickConfPrizesDto == null) {
            return ResultUtil.fail(ErrorCode.E0700001);
        }

        ErrorCode errorCode = checkOpStatus(brickConfPrizesDto.getDuibaBrickConfigDto());
        if (errorCode != null) {
            return ResultUtil.fail(errorCode);
        }

        DuibaBrickConfigDto brickConfigDto = brickConfPrizesDto.getDuibaBrickConfigDto();
        List<DuibaBrickPrizeDto> brickPrizeDtos = brickConfPrizesDto.getPrizes();
        if (CollectionUtils.isEmpty(brickPrizeDtos)) {
            return ResultUtil.fail(ErrorCode.E0700005);
        }

        //处理活动小周期
        dealPeriod(brickConfigDto.getId(), brickConfigDto.getStartTime(), brickConfigDto.getEndTime());

        //对首页访问次数监控
        CatLogTool.logMetric(CatLogTool.METRIC_BRICK_INDEX);

        //未登录情况下需要的数据
        DuibaBrickIndexVO brickIndexVO = copyBrickIndexVOWithNoLogin(brickConfigDto, brickPrizeDtos);
        if (consumerDto == null || consumerDto.isNotLoginUser() || ConsumerDto.PREVIEWUSERID.equals(consumerDto.getPartnerUserId())) {
            brickIndexVO.setLogined(2);
            accessLogIndexNoLogin(brickConfigDto.getId(), brickConfigDto.getOpId());
            return ResultUtil.success(brickIndexVO, "用户未登录");
        }
        copyDuibaBrickIndexVOWithOther(brickConfigDto, brickIndexVO);
        Long consumerId = consumerDto.getId();
        try {
            Integer addBrickNum = remoteDuibaBrickMobileDataService.getWorkerBrickNum(brickConfigDto.getId(), consumerId);
            brickIndexVO.setAddBrickNum(addBrickNum);

            DuibaBrickMobileDateDto brickMobileDateDto = remoteDuibaBrickMobileDataService.getBrickMobieDate(brickConfigDto.getId(), consumerId);
            copyDuibaBrickIndexVOWithMobileData(brickMobileDateDto, brickIndexVO);
            int prizeTypeOld = brickIndexVO.getPrizeType();
            //无奖品配置，说明该玩家第一次进来，自动分配奖品
            if (prizeTypeOld == 0) {
                //创建第一个账号等一些列逻辑
                DuibaBrickAccountDto brickAccountDto = createFirstAccount(brickConfPrizesDto, consumerId, 0);
                brickIndexVO.setPrizeType(brickAccountDto.getPrizeType());
                brickIndexVO.setBrickNum(brickAccountDto.getBrickNum());
                brickIndexVO.setPrizeImage(brickAccountDto.getPrizeImage());
                brickIndexVO.setRedPacket(brickAccountDto.getRedPacket());
                brickIndexVO.setExchangeBrick(brickAccountDto.getExchangeBrick());
                brickIndexVO.setBrickPrizeId(brickAccountDto.getBrickPrizeId());
                //首次访问
                brickIndexVO.setFirstVisit(1);
                AccessLogFilter.putExPair("firstSignDay", new Date());
            }
            //获取累计登录天数
            Long cumulativeLogin = getLoginDay(brickConfigDto.getId(), consumerId);
            brickIndexVO.setCumulativeLogin(cumulativeLogin);
            //获取搬砖工已召唤次数属性值
            getWorkCallCountRedisKeys(brickIndexVO,consumerId);
            //日志埋点
            accessLogIndex(brickConfigDto.getId(), brickConfigDto.getOpId(), brickIndexVO.getPrizeType(), addBrickNum, cumulativeLogin);
            return ResultUtil.success(brickIndexVO, "加载成功");
        } catch (Exception e) {
            LOGGER.error("加载搬砖活动首页异常，appId={}, consumerId={}", appId, consumerId, e);
            return ResultUtil.fail(ErrorCode.E9999999);
        }
    }

    /**
     * 获取搬砖工已召唤次数属性值
     * @param brickIndexVO
     * @param consumerId
     */
    private void getWorkCallCountRedisKeys(DuibaBrickIndexVO brickIndexVO, Long consumerId) {
        List<String> redisKeys = Lists.newArrayList();
        for (int i = 1; i < 4; i++) {
            redisKeys.add(getRedisCallKey(consumerId, i));
        }
        List<String> redisCallCounts = redisTemplate.opsForValue().multiGet(redisKeys);
        List<Integer> callCounts = new ArrayList<>();
        for (String callCount : redisCallCounts) {
            if (StringUtils.isEmpty(callCount)) {
                callCounts.add(0);
            }else {
                callCounts.add(Integer.valueOf(callCount));
            }
        }
        brickIndexVO.setPrimaryWorkCallCount(callCounts.get(0));
        brickIndexVO.setMiddleWorkCallCount(callCounts.get(1));
        brickIndexVO.setSeniorWorkCallCount(callCounts.get(2));
    }

    //未登录情况下需要的数据
    private DuibaBrickIndexVO copyBrickIndexVOWithNoLogin(DuibaBrickConfigDto brickConfigDto, List<DuibaBrickPrizeDto> brickPrizeDtos) {
        DuibaBrickIndexVO brickIndexVO = new DuibaBrickIndexVO();
        brickIndexVO.setAppId(RequestLocal.getAppId());
        brickIndexVO.setId(brickConfigDto.getId());
        brickIndexVO.setTitle(brickConfigDto.getTitle());
        brickIndexVO.setBrickRule(brickConfigDto.getBrickRule());
        brickIndexVO.setCredits(brickConfigDto.getCredits());
        brickIndexVO.setDayLimit(brickConfigDto.getDayLimit());
        brickIndexVO.setStartTime(brickConfigDto.getStartTime().getTime());
        brickIndexVO.setEndTime(brickConfigDto.getEndTime().getTime());
        //奖品图片 用户规则展现(只获取实物的奖品图片)
        List<String> prizeImageList = Lists.newArrayList();
        String redPacketSplit = "";
        //如果是红包，需要获取红包的拆分规则。如果是实物，需要获取实物的图片地址
        for (DuibaBrickPrizeDto brickPrizeDto : brickPrizeDtos) {
            if (Objects.equals(brickPrizeDto.getPrizeType(), 1)) {
                prizeImageList.add(brickPrizeDto.getPrizeImage());
            } else {
                redPacketSplit = brickPrizeDto.getRedBacketSplit();
            }
        }
        brickIndexVO.setRedPacketSplit(redPacketSplit);
        brickIndexVO.setPrizeImageList(prizeImageList);
        return brickIndexVO;
    }

    //拷贝其他数据
    private void copyDuibaBrickIndexVOWithOther(DuibaBrickConfigDto brickConfigDto, DuibaBrickIndexVO brickIndexVO) {
        brickIndexVO.setLogined(1);
        brickIndexVO.setClickTime(brickConfigDto.getClickTime());
        brickIndexVO.setPrimaryCallInterval(brickConfigDto.getPrimaryCallInterval());
        brickIndexVO.setMiddleCallInterval(brickConfigDto.getMiddleCallInterval());
        brickIndexVO.setSeniorCallInterval(brickConfigDto.getSeniorCallInterval());
        brickIndexVO.setPrimaryWork(brickConfigDto.getPrimaryWork());
        brickIndexVO.setMiddleWork(brickConfigDto.getMiddleWork());
        brickIndexVO.setSeniorWork(brickConfigDto.getSeniorWork());
    }

    //拷贝动态数据
    private void copyDuibaBrickIndexVOWithMobileData(DuibaBrickMobileDateDto brickMobileDateDto, DuibaBrickIndexVO brickIndexVO) {
        brickIndexVO.setPrizeType(brickMobileDateDto.getPrizeType());
        brickIndexVO.setBrickNum(brickMobileDateDto.getBrickNum());
        brickIndexVO.setBrickPrizeId(brickMobileDateDto.getBrickPrizeId());
        brickIndexVO.setPrizeImage(brickMobileDateDto.getPrizeImage());
        brickIndexVO.setRedPacket(brickMobileDateDto.getRedPacket());
        brickIndexVO.setExchangeBrick(brickMobileDateDto.getExchangeBrick());
        brickIndexVO.setWorkerTotalNum(brickMobileDateDto.getWorkerTotalNum());
        brickIndexVO.setCallEndTimeList(brickMobileDateDto.getCallEndTimeList());
        brickIndexVO.setBrickMobileWorkerList(brickMobileDateDto.getBrickMobileWorkerList());
    }



    //创建账户。两种情况会创建账户，1新用户第一次进来，2已领取过奖品的用户再次创建
    private DuibaBrickAccountDto createFirstAccount(DuibaBrickConfPrizesDto brickConfPrizesDto, Long consumerId, Integer brickNum) {
        DuibaBrickConfigDto brickConfigDto = brickConfPrizesDto.getDuibaBrickConfigDto();
        List<DuibaBrickPrizeDto> brickPrizeDtos = brickConfPrizesDto.getPrizes();
        //无账户，需要给他新建一个账户信息
        DuibaBrickAccountDto brickAccountDto = new DuibaBrickAccountDto();
        brickAccountDto.setBrickConfigId(brickConfigDto.getId());
        brickAccountDto.setConsumerId(consumerId);
        brickAccountDto.setBrickNum(brickNum);
        //未兑换
        brickAccountDto.setExchanged(2);
        //随机分配奖品
        DuibaBrickPrizeDto brickPrizeDto = brickPrizeDtos.get(new Random().nextInt(brickPrizeDtos.size()));
        Integer prizeType = brickPrizeDto.getPrizeType();
        brickAccountDto.setPrizeType(prizeType);
        brickAccountDto.setPrizeImage(brickPrizeDto.getPrizeImage());
        brickAccountDto.setBrickPrizeId(brickPrizeDto.getId());

        if (prizeType == 1) {
            //实物奖品
            brickAccountDto.setExchangeBrick(brickConfigDto.getExchangeTotalQuantity());
        } else if (prizeType == 2) {
            //红包奖品
            String redPacketSplit = redPacketInitCache.get(brickConfigDto.getId() + "_" + consumerId,
                    cacheKey -> remoteHbConsisHashKvService.getStringByKey(getRedPacketSplitKey(brickConfigDto.getId(), consumerId)));
            if (StringUtils.isEmpty(redPacketSplit)) {
                redPacketSplit = dealRedPacketSplit(brickPrizeDto.getRedBacketSplit());
                remoteHbConsisHashKvService.upsertKStrV(getRedPacketSplitKey(brickConfigDto.getId(), consumerId), redPacketSplit);
            }
            //拆分后的最小红包
            int minRedPacket = getMinRedPacket(redPacketSplit);
            //人命币 元兑换成分
            brickAccountDto.setRedPacket(minRedPacket);
            brickAccountDto.setExchangeBrick(getUpBrick(brickConfigDto.getExchangeTotalQuantity(), minRedPacket, BrickConstants.TOTALACCOUNT));
        }
        remoteDuibaBrickMobileDataService.insertBrickAccount(brickAccountDto);
        //随机标记
        randomMark(consumerId, brickPrizeDto);
        return brickAccountDto;
    }

    //获取最小红包
    private int getMinRedPacket(String redPacketSplit) {
        if (StringUtils.isBlank(redPacketSplit)) {
            return 0;
        }
        String[] strings = redPacketSplit.split(BrickConstants.REDBACKETSPLIT);
        return Integer.valueOf(strings[0]);
    }

    //随机得到红包
    private String dealRedPacketSplit(String redPacketSpilt) {
        if (StringUtils.isEmpty(redPacketSpilt)) {
            return "";
        }
        String[] strings = redPacketSpilt.split(BrickConstants.REDBACKETSPLIT);
        Arrays.sort(strings);
        String first = strings[0];
        String last = strings[strings.length-2];
        List<String> list = Lists.newArrayList();
        if (strings.length == 4) {
            list = Lists.newArrayList(strings[1],strings[strings.length-1]);
        } else if (strings.length == 5){
            list = Lists.newArrayList(strings[1],strings[2],strings[strings.length-1]);
        }
        Collections.shuffle(list);
        List<String> newList = Lists.newArrayList();
        newList.add(first);
        newList.addAll(list);
        newList.add(last);
        StringBuilder sb = new StringBuilder();
        for (String s : newList) {
            sb.append(s);
            sb.append("+");
        }
        return sb.toString().substring(0, sb.length()-1);
    }

    @Override
    public Result<String> doJoin(AppSimpleDto appSimpleDto, Long brickConfigId, ConsumerDto consumerDto) {
        //获取活动不含奖品配置
        DuibaBrickConfigDto brickConfigDto = brickConfigDtoCache.get(brickConfigId, cacheKey -> remoteDuibaBrickConfBackendService.findById(brickConfigId));
        if (brickConfigDto == null) {
            return ResultUtil.fail(ErrorCode.E0700001);
        }

        if (!brickConfigDto.getAppId().equals(RequestLocal.getConsumerAppDO().getId())) {
            return ResultUtil.fail(ErrorCode.E0100012);
        }

        ErrorCode errorCode = checkOpStatus(brickConfigDto);
        if (errorCode != null) {
            return ResultUtil.fail(errorCode);
        }

        Long credits = brickConfigDto.getCredits();
        if (consumerDto.getCredits() < credits) {
            //查询开发者配置的积分不足跳转链接
            String earnCreditsUrl = remoteAppService.getSimpleApp(appSimpleDto.getId()).getResult().getEarnCreditsUrl();
            Result<String> result = new Result<>();
            result.setCode(ErrorCode.E0700016.getErrorCode());
            result.setSuccess(false);
            result.setDesc(ErrorCode.E0700016.getDesc());
            result.setData(earnCreditsUrl);
            return result;
        }
        //风控
        StormEngineResultDto riskResult = riskService.joinRisk(brickConfigDto.getOpId(),brickConfigId,ActivityUniformityTypeEnum.BrickMover.getCode());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultUtil.fail(riskResult.getCopy());
        }

        ActivityOrderDto orderDto = new ActivityOrderDto();
        orderDto.setConsumerId(consumerDto.getId());
        orderDto.setAppId(appSimpleDto.getId());
        orderDto.setPartnerUserId(consumerDto.getPartnerUserId());
        orderDto.setAppActivityId(brickConfigDto.getId());
        orderDto.setActivityType(ActivityOrderDto.TypeBrick);
        orderDto.setConsumeCredits(credits);
        orderDto.setExchangeStatus(ActivityOrderDto.ExchangeInit);
        orderDto.setConsumeCreditsStatus(
                credits == 0 ? ActivityOrderDto.ConsumeCreditsSuccess : ActivityOrderDto.ConsumeCreditsProcessing);
        orderDto.setIp(RequestLocal.getIp());
        String orderNum = remoteActivityOrderService.createOrder(orderDto).getResult();
        if (StringUtils.isBlank(orderNum)) {
            LOGGER.error("订单创建失败，返回为空");
            return ResultUtil.fail(ErrorCode.E0700006);
        }
        if (credits > 0) {
            subCreditsAndSendMessage(consumerDto, appSimpleDto, orderNum, credits, brickConfigDto);
        }
        return ResultUtil.success(orderNum, "创建订单成功");
    }

    //先扣除本地积分，再异步发送mq消息
    private void subCreditsAndSendMessage(ConsumerDto consumerDto,
                                          AppSimpleDto appSimpleDto,
                                          String orderNum,
                                          Long credits,
                                          DuibaBrickConfigDto brickConfigDto) {
        //订单新建成功后，预先扣本地积分
        subLocalCredits(credits, consumerDto.getId());
        final RequestParams requestParams = RequestParams.parse(RequestLocal.getRequest());
        SubCreditsMsgDto subCreditsMsg = getSubCreditsMsg(appSimpleDto, requestParams, credits, consumerDto.getPartnerUserId(), orderNum, brickConfigDto);
        //mq扣积分
        executorService.submit(() -> sendMq(subCreditsMsg));
    }

    @Override
    public Result<Integer> getOrderStatus(String orderNum) {
        try {
            DubboResult<ActivityOrderDto> orderDtoDubboResult = remoteActivityOrderService.findByOrderNum(orderNum);
            if (!orderDtoDubboResult.isSuccess()) {
                return ResultUtil.fail(ErrorCode.E9999999);
            }
            ActivityOrderDto orderDto = orderDtoDubboResult.getResult();
            if (orderDto == null) {
                return ResultUtil.fail(ErrorCode.E0700007);
            }
            if (!orderDto.getConsumerId().equals(RequestLocal.getCid())) {
                return ResultUtil.fail(ErrorCode.E0700009);
            }
            if (orderDto.getConsumeCreditsStatus().equals(ActivityOrderDto.ConsumeCreditsSuccess)) {
                return ResultUtil.success(ActivityOrderDto.ConsumeCreditsSuccess, "订单扣积分成功");
            } else if (orderDto.getConsumeCreditsStatus().equals(ActivityOrderDto.ConsumeCreditsFail)) {
                return ResultUtil.success(ActivityOrderDto.ConsumeCreditsFail, "订单扣积分失败");
            }
            return ResultUtil.success(ActivityOrderDto.ConsumeCreditsProcessing, "等待扣积分结果返回");
        } catch (Exception e) {
            LOGGER.error("查询订单异常,orderNum={},consumerId={}", orderNum, RequestLocal.getCid(), e);
            return ResultUtil.fail(ErrorCode.E9999999);
        }
    }

    @Override
    public Result<DuibaBrickWorkerDto> continueClick(Long brickConfigId, String orderNum, Integer clickNum, Integer workerLevel) {
        //获取活动不含奖品配置
        DuibaBrickConfigDto brickConfigDto = brickConfigDtoCache.get(brickConfigId, cacheKey -> remoteDuibaBrickConfBackendService.findById(brickConfigId));
        if (brickConfigDto == null) {
            return ResultUtil.fail(ErrorCode.E0700001);
        }

        if (!brickConfigDto.getAppId().equals(RequestLocal.getConsumerAppDO().getId())) {
            return ResultUtil.fail(ErrorCode.E0100012);
        }

        ErrorCode errorCode = checkOpStatus(brickConfigDto);
        if (errorCode != null) {
            return ResultUtil.fail(errorCode);
        }

        Long consumerId = RequestLocal.getCid();
        //再次校验扣积分是否成功
        Result<Integer> orderResult = this.getOrderStatus(orderNum);
        if (!orderResult.getSuccess() || !orderResult.getData().equals(ActivityOrderDto.ConsumeCreditsSuccess)) {
            return ResultUtil.failWithDesc(orderResult.getDesc(), orderResult.getCode());
        }

        //判断订单号是否已被使用
        Long orderNumValue = remoteHbConsisHashKvService.getLongByKey(getOrderNumKey(brickConfigId, orderNum));
        if (orderNumValue != null) {
            return ResultUtil.fail(ErrorCode.E0700012);
        }

        //风控
        StormEngineResultDto riskResult = riskService.joinRisk(brickConfigDto.getOpId(),brickConfigId,ActivityUniformityTypeEnum.BrickMover.getCode());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultBuilder.fail(riskResult.getCopy());
        }

        DuibaBrickWorkerDto brickWorkerDto = null;

        try {
            //限制可点击次数 最大值：10*可点击时间
            Integer maxClickNum = brickConfigDto.getClickTime() * 10;
            clickNum = clickNum < maxClickNum ? clickNum : maxClickNum;

            //将首次用户点击数记录在hbase中,并做本地缓存
            Long firstClickNum = brickNumCache.get(brickConfigId + "_" +consumerId + "_" + workerLevel, cacheKey -> remoteHbConsisHashKvService.getLongByKey(getBrickNumKey(brickConfigId, consumerId, workerLevel)));
            if (firstClickNum == null) {
                remoteHbConsisHashKvService.upsertKLongV(getBrickNumKey(brickConfigId, consumerId, workerLevel), clickNum);
                firstClickNum = Long.valueOf(clickNum);
            }

            //每天召唤次数上限校验
            Integer dayLimit = brickConfigDto.getDayLimit();
            Integer count = Integer.valueOf(ObjectUtils.defaultIfNull(redisTemplate.opsForValue().get(getRedisCallKey(consumerId, workerLevel)), "0"));
            if (dayLimit <= count) {
                return ResultUtil.fail(ErrorCode.E0700014);
            }

            //将点击参数放入对应worker中
            brickWorkerDto = new DuibaBrickWorkerDto();
            brickWorkerDto.setBrickConfigId(brickConfigId);
            brickWorkerDto.setConsumerId(consumerId);
            brickWorkerDto.setWorkerLevel(workerLevel);
            brickWorkerDto.setWorkerNum(clickNum); //点击数等于搬砖工个数
            if (Objects.equals(clickNum,0)) {
                brickWorkerDto.setBrickNum(0);
            } else {
                brickWorkerDto.setBrickNum(getBrickNum(brickConfigDto, consumerId, workerLevel, clickNum, firstClickNum.intValue()));
            }
            brickWorkerDto.setWorkStartTime(new Date());
            brickWorkerDto.setCallStartTime(new Date());

            //检验搬砖工等级并设置搬砖工时间，
            ErrorCode errorCode1 = setBrickWorkerTime(brickWorkerDto, workerLevel, brickConfigDto, consumerId);
            if (errorCode1 != null) {
                return ResultUtil.fail(errorCode1);
            }

            brickWorkerDto.setDied(2); //默认未死亡
            Long workerId = remoteDuibaBrickMobileDataService.addBrickWorker(brickWorkerDto);
            brickWorkerDto.setId(workerId);
            //召唤次数+1
            incrementCallCount(RequestLocal.getCid(), workerLevel);
            //hbase存储订单号，表示该订单号已使用
            remoteHbConsisHashKvService.upsertKLongV(getOrderNumKey(brickConfigId, orderNum), 1);
            //日志埋点
            accessLogClick(brickConfigId, brickConfigDto.getOpId(), orderNum, brickWorkerDto.getBrickNum());
            return ResultUtil.success(brickWorkerDto, "召唤成功");
        } catch (Exception e) {
            LOGGER.error("连续点击异常，brickConfigId={},orderNum={}", brickConfigId, orderNum, e);
            return ResultUtil.fail(ErrorCode.E9999999);
        }
    }

    //设置搬砖工时间
    private ErrorCode setBrickWorkerTime(DuibaBrickWorkerDto brickWorkerDto, Integer workerLevel, DuibaBrickConfigDto brickConfigDto, Long consumerId) {
        if (workerLevel == 1) {
            brickWorkerDto.setWorkEndTime(setEndTime(brickConfigDto.getPrimaryWork()));
            brickWorkerDto.setCallEndTime(setEndTime(brickConfigDto.getPrimaryCallInterval()));
        } else if (workerLevel == 2) {
            //判断累计登录天使是否满足条件
            Long cumulativeLogin = getLoginDay(brickConfigDto.getId(), consumerId);
            if (cumulativeLogin < BrickConstants.openMiddleDays) {
                return ErrorCode.********;
            }
            brickWorkerDto.setWorkEndTime(setEndTime(brickConfigDto.getMiddleWork()));
            brickWorkerDto.setCallEndTime(setEndTime(brickConfigDto.getMiddleCallInterval()));
        } else if (workerLevel == 3) {
            //判断累计登录天使是否满足条件
            Long cumulativeLogin = getLoginDay(brickConfigDto.getId(), consumerId);
            if (cumulativeLogin < BrickConstants.openSeniorDays) {
                return ErrorCode.********;
            }
            brickWorkerDto.setWorkEndTime(setEndTime(brickConfigDto.getSeniorWork()));
            brickWorkerDto.setCallEndTime(setEndTime(brickConfigDto.getSeniorCallInterval()));
        }
        return null;
    }


    @Override
    public Result<DuibaBrickReturnPrizeVO> exchange(Long appId, Long brickConfigId, Long consumerId) {
        //获取活动含奖品配置
        DuibaBrickConfPrizesDto brickConfPrizesDto = brickConfPrizesDtoCache.get(appId,
                cacheKey -> remoteDuibaBrickConfBackendService.findBrickConfDetails(appId, BrickConstants.CURRENTPERIOD));
        if (brickConfPrizesDto == null) {
            return ResultUtil.fail(ErrorCode.E0700001);
        }

        if (!brickConfPrizesDto.getDuibaBrickConfigDto().getAppId().equals(appId)) {
            return ResultUtil.fail(ErrorCode.E0100012);
        }

        ErrorCode errorCode = checkOpStatus(brickConfPrizesDto.getDuibaBrickConfigDto());
        if (errorCode != null) {
            return ResultUtil.fail(errorCode);
        }

        List<DuibaBrickPrizeDto> brickPrizeDtos = brickConfPrizesDto.getPrizes();
        DuibaBrickAccountDto brickAccountDto = remoteDuibaBrickMobileDataService.getBrickAccount(brickConfigId, consumerId);
        if (brickAccountDto == null) {
            return ResultUtil.fail(ErrorCode.E0700010);
        }

        //校验砖块数是否满足
        Integer exchangeBrick = brickAccountDto.getExchangeBrick();
        Integer brickNum = brickAccountDto.getBrickNum();
        if (exchangeBrick > brickNum) {
            return ResultUtil.fail(ErrorCode.E0700011);
        }

        //风控
        StormEngineResultDto riskResult = riskService.joinRisk(brickConfPrizesDto.getDuibaBrickConfigDto().getOpId(),brickConfigId,ActivityUniformityTypeEnum.BrickMover.getCode());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultBuilder.fail(riskResult.getCopy());
        }

        Integer prizeType = brickAccountDto.getPrizeType();
        //当前账户对应的奖品
        DuibaBrickPrizeDto brickPrizeDto = new DuibaBrickPrizeDto();
        //生成本地奖品兑换记录
        DuibaBrickExchangeRecordDto brickExchangeRecordDto = new DuibaBrickExchangeRecordDto();
        brickExchangeRecordDto.setAppId(appId);
        brickExchangeRecordDto.setBrickConfigId(brickConfigId);
        brickExchangeRecordDto.setConsumerId(consumerId);
        brickExchangeRecordDto.setPrizeType(prizeType);
        for (DuibaBrickPrizeDto brickPrizeDto1 : brickPrizeDtos) {
            if (brickPrizeDto1.getId().equals(brickAccountDto.getBrickPrizeId())) {
                brickPrizeDto = brickPrizeDto1;
                break;
            }
        }
        if (prizeType == 1) {
            return dealExchangeObject(consumerId, brickConfPrizesDto, brickAccountDto, brickPrizeDto, brickExchangeRecordDto);
        } else {
            return dealExchangeRedPacket(consumerId, brickConfPrizesDto, brickAccountDto, brickPrizeDto, brickExchangeRecordDto);
        }
    }

    //兑换实物
    private Result<DuibaBrickReturnPrizeVO> dealExchangeObject(Long consumerId, DuibaBrickConfPrizesDto brickConfPrizesDto, DuibaBrickAccountDto brickAccountDto, DuibaBrickPrizeDto brickPrizeDto, DuibaBrickExchangeRecordDto brickExchangeRecordDto) {
        DuibaBrickConfigDto brickConfigDto = brickConfPrizesDto.getDuibaBrickConfigDto();
        List<DuibaBrickPrizeDto> brickPrizeDtos = brickConfPrizesDto.getPrizes();
        Long brickConfigId = brickConfigDto.getId();
        Integer exchangeBrick = brickAccountDto.getExchangeBrick();
        Integer remaindBrick;
        DuibaBrickReturnPrizeVO returnPrizeVO;
        try {
            AppItemDto appItemDto = remoteAppItemGoodsService.find(brickPrizeDto.getPrizeId()).getResult();
            //校验商品是否有效
            if (Objects.equals(appItemDto.getStatus(), AppItemDto.StatusOff) || Objects.equals(appItemDto.getSubStatus(), AppItemDto.SubStatusExpired)) {
                return ResultUtil.fail(ErrorCode.E0700013);
            }

            //新版库存不在商品上
            ItemKeyDto itemKeyDto = new ItemKeyDto(appItemDto, null, brickConfigDto.getAppId());
            DubboResult<Long> stockResult = remoteItemKeyService.findStock(itemKeyDto);
            //校验库存是否足够
            if (!stockResult.isSuccess() || stockResult.getResult() <= 0) {
                return ResultUtil.fail(ErrorCode.E0700013);
            }
            //校验中奖人数 校验是否需要库存预警
            if (!checkStock(brickPrizeDto, brickPrizeDtos, brickConfigDto, stockResult.getResult())) {
                return ResultUtil.fail(ErrorCode.E0700013);
            }

            brickExchangeRecordDto.setObjectName(brickPrizeDto.getPrizeName());
            brickExchangeRecordDto.setBrickNum(exchangeBrick);
            remaindBrick = brickAccountDto.getBrickNum() - brickConfigDto.getExchangeTotalQuantity();
            //奖品是实物， 生成兑换记录
            remoteDuibaBrickMobileDataService.insertBrickExchangeRecord(brickExchangeRecordDto);
            //需要清空当前账户，再新建一个账户。
            remoteDuibaBrickMobileDataService.deleteBrickAccount(brickConfigId, consumerId);
            //奖品领取数+1
            incrementTakePrizeCount(brickConfigId, brickAccountDto.getBrickPrizeId(), brickConfigDto.getEndTime());
            //取消该用户的标记
            remoteDuibaBrickMobileDataService.cancelMarkedUser(brickConfigId, consumerId);
            //再次新建一个账户
            DuibaBrickAccountDto brickAccountDtoNew = createFirstAccount(brickConfPrizesDto, consumerId, remaindBrick);
            //添加兑换记录,并返回兑换商品详情页
            String url = createConsumerExchangeRecord(brickConfigDto, brickPrizeDto, consumerId, appItemDto);
            returnPrizeVO = BeanUtils.copy(brickAccountDtoNew, DuibaBrickReturnPrizeVO.class);
            returnPrizeVO.setPrizeUrl(url);
            return ResultUtil.success(returnPrizeVO, "兑换实物成功");
        } catch (Exception e) {
            LOGGER.error("兑换实物异常,consumerId={}", consumerId, e);
            return ResultUtil.fail(ErrorCode.E9999999);
        }
    }



    /**
     *  兑换红包
        针对红包，因为是分多个小红包的，所以逻辑比较复杂
        1.先在兑换记录表内新增兑换记录
        2.再根据兑换的红包是否是最后一个小红包来决定是否要删除当前账户记录。如果是最后一个小红包，需要删除当前账户记录,并在已领取redis数+1
        3.新建一个账户来存储多出来的砖块
     * @param consumerId
     * @param brickConfPrizesDto
     * @param brickAccountDto
     * @param brickPrizeDto
     * @param brickExchangeRecordDto
     * @return
     */
    private Result<DuibaBrickReturnPrizeVO> dealExchangeRedPacket(Long consumerId, DuibaBrickConfPrizesDto brickConfPrizesDto, DuibaBrickAccountDto brickAccountDto, DuibaBrickPrizeDto brickPrizeDto, DuibaBrickExchangeRecordDto brickExchangeRecordDto) {
        DuibaBrickConfigDto brickConfigDto = brickConfPrizesDto.getDuibaBrickConfigDto();
        Long brickConfigId = brickConfigDto.getId();
        Integer exchangeBrick = brickAccountDto.getExchangeBrick();
        Integer remaindBrick;
        DuibaBrickReturnPrizeVO returnPrizeVO;

        try {
            brickExchangeRecordDto.setBrickNum(exchangeBrick);
            brickExchangeRecordDto.setRedPacket(brickAccountDto.getRedPacket());
            Long brickExchangeRecordId = remoteDuibaBrickMobileDataService.insertBrickExchangeRecord(brickExchangeRecordDto);

            //红包总账户添加金额，入账控制
            ErrorCode errorCode = dealRedPacketAccount(brickAccountDto, brickExchangeRecordId);
            if (errorCode != null) {
                return ResultUtil.fail(ErrorCode.E9999999);
            }

            //将当前账户设为已兑换
            remoteDuibaBrickMobileDataService.exchangeAccountBrickNum(brickAccountDto.getId());

            remaindBrick = brickAccountDto.getBrickNum() - exchangeBrick;

            String redPacketSplit = redPacketInitCache.get(brickConfigDto.getId() + "_" + consumerId,
                    cacheKey -> remoteHbConsisHashKvService.getStringByKey(getRedPacketSplitKey(brickConfigDto.getId(), consumerId)));
            if (StringUtils.isEmpty(redPacketSplit)) {
                LOGGER.error("从hbase中获取分配好的红包失败,consumerId={}", consumerId);
                return ResultUtil.fail(ErrorCode.E9999999);
            }
            String[] redPackets = redPacketSplit.split(BrickConstants.REDBACKETSPLIT);
            //15块的红包被分了几等份
            Integer length = redPackets.length;
            List<DuibaBrickAccountDto> brickAccountDtos = remoteDuibaBrickMobileDataService.listBrickAccountHaveExchange(brickConfigId, consumerId);
            if (brickAccountDtos.size() == length) {
                //账户的兑换记录不为空，且等于小红包个数-1，说明当前这个兑换红包为最后一个小红包了。需要清空当前账户，再新建一个账户。
                remoteDuibaBrickMobileDataService.deleteBrickAccount(brickConfigId, consumerId);
                //奖品领取数+1
                incrementTakePrizeCount(brickConfigId, brickAccountDto.getBrickPrizeId(), brickConfigDto.getEndTime());
                //取消该用户的标记
                remoteDuibaBrickMobileDataService.cancelMarkedUser(brickConfigId, consumerId);
                //再次新建一个账户
                DuibaBrickAccountDto brickAccountDtoNew = createFirstAccount(brickConfPrizesDto, consumerId, remaindBrick);
                returnPrizeVO = BeanUtils.copy(brickAccountDtoNew, DuibaBrickReturnPrizeVO.class);
                returnPrizeVO.setRedPacket(brickAccountDtoNew.getRedPacket());
            } else if (brickAccountDtos.size() == length - 1) {
                //再分最后一个红包
                returnPrizeVO = createLastRedPacketAccount(brickAccountDto, brickConfigDto.getExchangeTotalQuantity(), Integer.valueOf(redPackets[brickAccountDtos.size()]), brickAccountDtos, remaindBrick);
            } else {
                //再分下一个红包
                returnPrizeVO = createNextRedPacketAccount(brickAccountDto, brickConfigDto, Integer.valueOf(redPackets[brickAccountDtos.size()]), remaindBrick);
            }
            return ResultUtil.success(returnPrizeVO, "兑换成功");
        } catch (Exception e) {
            LOGGER.error("领取红包异常,consumerId={}", consumerId, e);
            return ResultUtil.fail(ErrorCode.E9999999);
        }
    }

    /**
     * 领取最后第二个红包，分最后一个红包时，红包所需要的砖块数需要总砖块减去之前以兑换的砖块
     * @param accountDto
     * @param exchangeTotalQuantity
     * @param redPacket
     * @param brickAccountDtos
     * @param remaindBrick
     * @return
     */
    private DuibaBrickReturnPrizeVO createLastRedPacketAccount(DuibaBrickAccountDto accountDto, Integer exchangeTotalQuantity, Integer redPacket, List<DuibaBrickAccountDto> brickAccountDtos, Integer remaindBrick) {
        DuibaBrickReturnPrizeVO returnPrizeVO = new DuibaBrickReturnPrizeVO();
        //新建一个账户信息
        DuibaBrickAccountDto brickAccountDto = BeanUtils.copy(accountDto, DuibaBrickAccountDto.class);
        brickAccountDto.setId(null);
        //人命币 元兑换成分
        brickAccountDto.setRedPacket(redPacket);
        Integer total = brickAccountDtos.stream().mapToInt(DuibaBrickAccountDto::getExchangeBrick).sum();
        brickAccountDto.setExchangeBrick(exchangeTotalQuantity - total);
        brickAccountDto.setBrickNum(remaindBrick);
        //未兑换
        brickAccountDto.setExchanged(2);
        remoteDuibaBrickMobileDataService.insertBrickAccount(brickAccountDto);
        returnPrizeVO.setBrickPrizeId(brickAccountDto.getBrickPrizeId());
        returnPrizeVO.setPrizeImage(brickAccountDto.getPrizeImage());
        returnPrizeVO.setPrizeType(brickAccountDto.getPrizeType());
        returnPrizeVO.setRedPacket(brickAccountDto.getRedPacket());
        returnPrizeVO.setExchangeBrick(brickAccountDto.getExchangeBrick());
        return returnPrizeVO;
    }

    /**
     * 创建下一个红包账户（除去最后一次和最后第二次）
     * @param accountDto
     * @param brickConfigDto
     * @param redPacket
     * @param remaindBrick
     * @return
     */
    private DuibaBrickReturnPrizeVO createNextRedPacketAccount(DuibaBrickAccountDto accountDto, DuibaBrickConfigDto brickConfigDto, Integer redPacket, Integer remaindBrick) {
        DuibaBrickReturnPrizeVO returnPrizeVO = new DuibaBrickReturnPrizeVO();
        //新建一个账户信息
        DuibaBrickAccountDto brickAccountDto = BeanUtils.copy(accountDto, DuibaBrickAccountDto.class);
        brickAccountDto.setId(null);
        //人命币 元兑换成分
        brickAccountDto.setRedPacket(redPacket);
        brickAccountDto.setExchangeBrick(getUpBrick(brickConfigDto.getExchangeTotalQuantity(), redPacket, BrickConstants.TOTALACCOUNT));
        brickAccountDto.setBrickNum(remaindBrick);
        //未兑换
        brickAccountDto.setExchanged(2);
        remoteDuibaBrickMobileDataService.insertBrickAccount(brickAccountDto);
        returnPrizeVO.setBrickPrizeId(brickAccountDto.getBrickPrizeId());
        returnPrizeVO.setPrizeImage(brickAccountDto.getPrizeImage());
        returnPrizeVO.setPrizeType(brickAccountDto.getPrizeType());
        returnPrizeVO.setRedPacket(brickAccountDto.getRedPacket());
        returnPrizeVO.setExchangeBrick(brickAccountDto.getExchangeBrick());
        return returnPrizeVO;
    }

    @Override
    public Result<Integer> getWorkerBrickNum(Long brickConfigId, Long consumerId) {
        Integer addBrickNum = remoteDuibaBrickMobileDataService.getWorkerBrickNum(brickConfigId, consumerId);
        return ResultUtil.success(addBrickNum, "增加砖块成功");
    }

    //实物领奖 添加兑换记录
    private String createConsumerExchangeRecord(DuibaBrickConfigDto brickConfigDto, DuibaBrickPrizeDto brickPrizeDto, Long consumerId, AppItemDto appItemDto) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        //先生成一条虚拟子订单
        ActivityOrderDto orderDto = new ActivityOrderDto();
        orderDto.setConsumerId(consumerDto.getId());
        orderDto.setAppId(RequestLocal.getAppId());
        orderDto.setPartnerUserId(consumerDto.getPartnerUserId());
        orderDto.setAppActivityId(brickConfigDto.getId());
        orderDto.setActivityType(ActivityOrderDto.TypePlugin);
        orderDto.setConsumeCredits(0L);
        orderDto.setExchangeStatus(ActivityOrderDto.ExchangeInit);
        orderDto.setConsumeCreditsStatus(ActivityOrderDto.ConsumeCreditsSuccess);
        orderDto.setIp(RequestLocal.getIp());
        orderDto.setAppItemId(brickPrizeDto.getPrizeId());
        orderDto.setActivityOptionType(ItemDto.TypeObject);
        orderDto.setActivityOptionName(brickPrizeDto.getPrizeName());
        String orderNum = remoteActivityOrderService.createOrder(orderDto).getResult();
        if (StringUtils.isBlank(orderNum)) {
            LOGGER.error("订单创建失败，返回为空");
            return "";
        }

        ItemKeyDto key = new ItemKeyDto(appItemDto, null, RequestLocal.getAppId());
        DubboResult<Boolean> dubboResult = remoteItemKeyService.consumeStock(key, orderNum, ItemStockConsumeDto.BIZ_SOURCE_ACTIVITY);
        if (!dubboResult.isSuccess() || !dubboResult.getResult()) {
            LOGGER.error("扣库存失败,{}", dubboResult.getMsg());
            return "";
        }

        //再生成一条兑换记录
        ConsumerExchangeRecordDto consumerExchangeRecordDto = new ConsumerExchangeRecordDto(true);
        consumerExchangeRecordDto.setConsumerId(consumerId);
        consumerExchangeRecordDto.setType(ConsumerExchangeRecordDto.TypePluginLottery);
        if (StringUtils.isNotBlank(orderNum) && StringUtils.isNumeric(orderNum)) {
            consumerExchangeRecordDto.setRelationId(Long.valueOf(orderNum));
        }
        consumerExchangeRecordDto.setAppId(RequestLocal.getAppId());
        //设置活动名称
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("trueActivityTitle", brickConfigDto.getTitle());
        consumerExchangeRecordDto.setJson(jsonObject.toJSONString());
        consumerExchangeRecordDto.setPrizeName(brickPrizeDto.getPrizeName());
        consumerExchangeRecordDto.setAppItemId(brickPrizeDto.getPrizeId());
        DubboResult<ConsumerExchangeRecordDto> recordResult = remoteConsumerExchangeRecordService.insert(consumerExchangeRecordDto);
        if (!recordResult.isSuccess()) {
            LOGGER.error("插入兑换记录失败,brickConfigId={},consumerId={},mes={}", brickConfigDto.getId(), consumerId, recordResult.getMsg());
            return "";
        }
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
        return domainConfigDto.getActivityDomain() + "/activity/takePrizeNew?recordId=" + recordResult.getResult().getId();
    }

    //查询库存，判断是否需要库存报警
    private boolean checkStock(DuibaBrickPrizeDto brickPrizeDto, List<DuibaBrickPrizeDto> brickPrizeDtos, DuibaBrickConfigDto brickConfigDto, Long remaining) {
        //奖品id == 实物商品id
        Long appItemId = brickPrizeDto.getPrizeId();
        AppItemDto appItemDto = remoteAppItemGoodsService.find(appItemId).getResult();
        if (appItemDto == null) {
            LOGGER.error("通过搬砖活动配置奖品id查询商品信息为空,appItemId={}", appItemId);
            return false;
        }

        //该奖品可领取人数
        Integer prizeWinPrizeNum = brickPrizeDto.getWinPrizeNum();
        //已获取该奖品用户数
        Integer takePrizedNum = Integer.valueOf(ObjectUtils.defaultIfNull(redisTemplate.opsForValue().get(getRedisTakePrizeKey(brickPrizeDto.getBrickConfigId(), brickPrizeDto.getId())), "0"));
        if (takePrizedNum >= prizeWinPrizeNum) {
            return false;
        }

        //判断当前  redis 数据 有无更新
        String emailKey = getRedisEmailKey(brickPrizeDto.getBrickConfigId());
        String data = redisTemplate.opsForValue().get(emailKey);
        if (StringUtils.isNotBlank(data)) {
            //库存报警邮箱只需要一天发送一次
            return true;
        }
        //能够获取该奖品的剩余用户数
        Integer remaindNum = prizeWinPrizeNum - takePrizedNum;
        LOGGER.info("准备发库存预警邮件，remaining={}, remaindNum={}", remaining, remaindNum);
        if (remaining < remaindNum) {
            //异步查询所有商品库存信息，并发送邮件
            executorService.submit(() -> checkAllStock(brickPrizeDtos, brickConfigDto.getObjectPrizeWarnEmail(), brickConfigDto.getId()));
        }
        return true;
    }

    //查询所有商品库存,并发送告警邮件
    private void checkAllStock(List<DuibaBrickPrizeDto> brickPrizeDtos, String toEmail, Long brickConfigId) {
        if (CollectionUtils.isEmpty(brickPrizeDtos)) {
            return;
        }
        List<Long> appItemIds = brickPrizeDtos.stream().map(DuibaBrickPrizeDto::getPrizeId).collect(Collectors.toList());
        List<AppItemDto> appItemDtos = remoteAppItemGoodsService.findByIds(appItemIds).getResult();
        Map<String, Integer> prizeMap = Maps.newConcurrentMap();
        for (DuibaBrickPrizeDto brickPrizeDto : brickPrizeDtos) {
            for (AppItemDto appItemDto : appItemDtos) {
                if (brickPrizeDto.getPrizeId().equals(appItemDto.getId())) {
                    //库存剩余数
                    Integer remaining = appItemDto.getRemaining() == null?0:appItemDto.getRemaining();
                    //该奖品可领取人数
                    Integer prizeWinPrizeNum = brickPrizeDto.getWinPrizeNum();
                    //已获取该奖品用户数
                    Integer takePrizedNum = Integer.valueOf(ObjectUtils.defaultIfNull(redisTemplate.opsForValue().get(getRedisTakePrizeKey(brickPrizeDto.getBrickConfigId(), brickPrizeDto.getId())), "0"));
                    //能够获取该奖品的剩余用户数
                    Integer remaindNum = prizeWinPrizeNum - takePrizedNum;
                    if (remaining < remaindNum) {
                        prizeMap.put(brickPrizeDto.getPrizeName(), remaindNum);
                    }
                }
            }
        }
        sendMailToDevelop(toEmail, brickConfigId, prizeMap);

    }

    //发送邮件给开发者
    private void sendMailToDevelop(String toEmail, Long brickConfigId, Map<String, Integer> prizeMap) {
        LOGGER.info("开始发送邮件");
        //发送邮件
        if (MapUtils.isNotEmpty(prizeMap)) {
            //发送邮件
            //标题
            String subject = "搬砖工活动奖品库存预警邮件";
            //内容
            StringBuilder sb = new StringBuilder();
            sb.append("以下奖品库存不足剩余可中奖人数，请及时在补充该奖品库存 \n");
            for (Map.Entry<String, Integer> entry : prizeMap.entrySet()) {
                sb.append("奖品名：");
                sb.append(entry.getKey());
                sb.append(",该奖品剩余可领奖人数：");
                sb.append(entry.getValue());
                sb.append("\n");
            }
            try {
                LOGGER.info("开始发送邮件，toEmail={}, sb={}", toEmail, sb);
                emailService.sendEmail(toEmail, subject, sb.toString(), false);
            } catch (MessagingException e) {
                LOGGER.warn("发送邮件失败", e);
            }
            //设置redis 表示当天已发送，一天一次
            redisTemplate.opsForValue().set(getRedisEmailKey(brickConfigId), "1");
            redisTemplate.expireAt(getRedisEmailKey(brickConfigId), new Date(DateUtil.getToday23Date().getTime() + new Random().nextInt(10)-5));
        }
    }


    //奖品是红包，需要调用红包总账户接口 加上对应金额()
    private ErrorCode dealRedPacketAccount(DuibaBrickAccountDto brickAccountDto, Long brickExchangeRecordId) {
        try {
            AccPeriodRechargeRequest accPeriodRechargeRequest = new AccPeriodRechargeRequest();
            accPeriodRechargeRequest.setAppId(RequestLocal.getAppId());
            accPeriodRechargeRequest.setConsumerId(RequestLocal.getCid());
            accPeriodRechargeRequest.setPartnerUserId(RequestLocal.getPartnerUserId());
            accPeriodRechargeRequest.setNeedLog(true);
            accPeriodRechargeRequest.setBizType(AccountBizTypeEnum.BRICK);
            //兑换记录表id
            accPeriodRechargeRequest.setBizId(brickExchangeRecordId.toString());
            accPeriodRechargeRequest.setBizDescription("搬砖活动获取红包");
            accPeriodRechargeRequest.setChangeMoney((long) brickAccountDto.getRedPacket());
            accPeriodRechargeRequest.setClientIp(RequestLocal.getIp());
            accPeriodRechargeRequest.setActType(ActivityUniformityTypeEnum.BrickMover);
            accPeriodRechargeRequest.setActId(brickAccountDto.getId());

            AccountModifyResponse accountModifyResponse = remoteRedAccPeriodService.redAccActBonusRecharge(accPeriodRechargeRequest);
            if (!accountModifyResponse.isSuccess()) {
                LOGGER.info("搬砖活动获取红包失败，brickConfigId={}, consumerId={}, 错误码={},错误原因={}",
                        brickAccountDto.getBrickConfigId(), RequestLocal.getCid(), accountModifyResponse.getErrorCode(), accountModifyResponse.getErrorMsg());
                return ErrorCode.E9999999;
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("搬砖活动增加红包金额异常,appId={}, brickConfigId={}, consumerId={}",
                    RequestLocal.getAppId(), brickAccountDto.getBrickConfigId(), RequestLocal.getCid(), e);
            return ErrorCode.E9999999;
        }
    }

    private Integer getUpBrick(Integer fenzi1, Integer fenzi2, Integer fenmu) {
        Integer a = fenzi1 * fenzi2 / fenmu;
        if ((fenzi1 * fenzi2) % fenmu != 0) {
            a++;
        }
        return a;
    }

    //获取某批搬砖工能够搬的砖块数量
    private int getBrickNum(DuibaBrickConfigDto brickConfigDto, Long consumerId, Integer workerLevel, Integer clickNum, Integer firstClickNum) { //NOSONAR
        Long brickConfigId = brickConfigDto.getId();
        DuibaBrickAccountDto brickAccountDto = remoteDuibaBrickMobileDataService.getBrickAccount(brickConfigId, consumerId);
        if (brickAccountDto == null) {
            return 0;
        }

        long count = 0;
        //判断是否需要走兼容方案
        if (checkNeedCompatible(brickConfigDto)) {
           //二期上线以前的活动全部走活动自身的标记
            List<DuibaBrickMarkedDto> brickMarkedDtos = remoteDuibaBrickMobileDataService.getMarkedUser(brickConfigId, brickAccountDto.getBrickPrizeId());
            count = brickMarkedDtos.stream().filter(brickMarkedDto -> brickMarkedDto.getConsumerId().equals(consumerId)).count();
        } else {
            if (Objects.equals(brickConfigDto.getRewardType(), 1)) {
                //实物 走 活动自身的标记规则
                List<DuibaBrickMarkedDto> brickMarkedDtos = remoteDuibaBrickMobileDataService.getMarkedUser(brickConfigId, brickAccountDto.getBrickPrizeId());
                count = brickMarkedDtos.stream().filter(brickMarkedDto -> brickMarkedDto.getConsumerId().equals(consumerId)).count();
            } else {
                //红包 走总账户标记规则（之后红包还会分总账户和自定义账户）
                Boolean isMarkUser = remoteRedAccPeriodService.checkMarkUser(brickConfigDto.getAppId(), consumerId);
                if (isMarkUser != null && isMarkUser) {
                    count = 1;
                    LOGGER.info("该用户为红包标记用户,consumerId={}", consumerId);
                }
            }
        }

        //奖品类型
        Integer prizeType = brickAccountDto.getPrizeType();
        int brickNum;
        if (count > 0) { //说明该用户是被标记用户
            brickNum = getBrickNumMarked(brickConfigDto, workerLevel, clickNum, firstClickNum, brickAccountDto.getBrickNum(), consumerId);
            LOGGER.info("该用户为标记用户,consumerId={},能得到的砖块数={}", consumerId, brickNum);
        } else {
            if (prizeType == 1) {
                brickNum = getBrickNumNoMarkObject(brickConfigDto, workerLevel, clickNum, firstClickNum, brickAccountDto.getBrickNum(), consumerId);
            } else if (prizeType == 2) {
                brickNum = getBrickNumNoMarkRedPacket(brickConfigDto, workerLevel, clickNum, firstClickNum, brickAccountDto.getBrickNum(), consumerId);
            } else {
                LOGGER.info("奖品类型异常,consumerId={}, prizeType={}", consumerId, prizeType);
                return 0;
            }
        }
        return brickNum;
    }

    //上线的兼容方案：配置的活动开始时间<搬砖活动二期上线时间且活动结束时间>搬砖活动二期上线时间，都走活动标记。
    private boolean checkNeedCompatible(DuibaBrickConfigDto brickConfigDto) {
        Date brickSecondStartTime = DateUtil.getDaySecond(brickConstants.getBrickSecondStartTime());
        if (brickSecondStartTime != null) {
            return brickConfigDto.getStartTime().before(brickSecondStartTime) && brickConfigDto.getEndTime().after(brickSecondStartTime);
        }
        return false;
    }

    //标记用户搬砖工能够获取的砖块数
    private int getBrickNumMarked(DuibaBrickConfigDto brickConfigDto, Integer workerLevel, Integer clickNum, Integer firstClickNum, Integer brickNum, Long consumerId) {
        Long brickConfigId = brickConfigDto.getId();
        //总砖块数
        Integer exchangeTotalQuantity = brickConfigDto.getExchangeTotalQuantity();
        //可召唤次数
        Integer dayLimit = brickConfigDto.getDayLimit();
        //本周活动周期天数
        Integer days = daysBetween(brickConfigDto.getStartTime(), brickConfigDto.getEndTime());

        //获取用户已经得到的砖块数
        Integer haveGetBrickNum = getHaveGetBrickNum(brickNum, consumerId, brickConfigId);
        //用户已获取砖块数大于可获取砖块数，下发0块砖
        if (exchangeTotalQuantity <= haveGetBrickNum) {
            return 0;
        }

        //在该周期的最后一次搬砖时将该标记用户可得的剩余砖块数全部下发直至等于总砖块数
        Integer lastBrickNum = getLastBrickNum(brickConfigDto, haveGetBrickNum, consumerId);
        if (lastBrickNum != null) {
            return lastBrickNum;
        }

        //被标记用户搬砖规则：每次搬砖平均值公式=总砖块数／【各等级可召唤次数*3*（本期活动周期天数-1)
        Integer b = dayLimit * 3 * (days - 1);
        //标记用户每次补的砖块
        Integer remedyBrickNumOnce = getRemedyBrickNumOnce(brickConfigDto, haveGetBrickNum, consumerId);
        Integer baseBrickNum = 0;
        if (workerLevel == 1) {
            baseBrickNum = getPrimaryBrickNum(clickNum, firstClickNum, exchangeTotalQuantity, b);
        } else if (workerLevel == 2) {
            baseBrickNum = getMiddleBrickNum(clickNum, firstClickNum, exchangeTotalQuantity, b);
        } else if (workerLevel == 3) {
            baseBrickNum = getSeniorBrickNum(clickNum, firstClickNum, exchangeTotalQuantity, b);
        } else {
            LOGGER.info("搬砖工级别异常,consumerId={},workerLevel={}", RequestLocal.getCid(), workerLevel);
            return 0;
        }

        //用户理论可领取的砖块数 大于 用户可领取的剩余砖块数，则发用户可领取的剩余砖块数
        if (baseBrickNum + remedyBrickNumOnce > exchangeTotalQuantity - haveGetBrickNum) {
            return exchangeTotalQuantity - haveGetBrickNum;
        } else {
            return baseBrickNum + remedyBrickNumOnce;
        }
    }

    //已经获取到的砖块数 = 账户上的砖块数 + 兑换记录里的砖块数
    private Integer getHaveGetBrickNum(Integer brickNum, Long consumerId, Long brickConfigId) {
        List<DuibaBrickExchangeRecordDto> exchangeRecordDtos = remoteDuibaBrickMobileDataService.listBrickExchangeRecord(brickConfigId, consumerId);
        if (CollectionUtils.isNotEmpty(exchangeRecordDtos)) {
            Integer haveExchangedBrickNum = exchangeRecordDtos.stream().mapToInt(DuibaBrickExchangeRecordDto::getBrickNum).sum();
            return haveExchangedBrickNum + brickNum;
        } else {
            return brickNum;
        }
    }

    //在该周期的最后一次搬砖时将该标记用户可得的剩余砖块数全部下发直至等于总砖块数
    private Integer getLastBrickNum(DuibaBrickConfigDto brickConfigDto, Integer haveGetBrickNum, Long consumerId) {
        Date endTime = dealLingDian(brickConfigDto.getEndTime());
        if (!sameDate(endTime, new Date())) {
            return null;
        }
        List<String> redisKeys = Lists.newArrayList();
        for (int i = 1; i < 4; i++) {
            redisKeys.add(getRedisCallKey(consumerId, i));
        }
        List<String> redisCallCounts = redisTemplate.opsForValue().multiGet(redisKeys);
        Integer total = 0;
        for (String callCount : redisCallCounts) {
            if (StringUtils.isNotBlank(callCount)) {
                total += Integer.valueOf(callCount);
            }
        }
        if (!total.equals(3 * brickConfigDto.getDayLimit() - 1)) {
            return null;
        }
        //剩余需下发砖块数 = 中奖用户兑换所需总砖块数 -  账户上的砖块数 - 已兑换的砖块数
        //中奖用户兑换所需总砖块数
        Integer exchangeTotalQuantity = brickConfigDto.getExchangeTotalQuantity();
        return exchangeTotalQuantity - haveGetBrickNum <0?0:exchangeTotalQuantity - haveGetBrickNum;
    }

    //获取标记用户每次需补的砖块
    private Integer getRemedyBrickNumOnce(DuibaBrickConfigDto brickConfigDto, Integer haveGetBrickNum, Long consumerId) {
        Long brickConfigId = brickConfigDto.getId();
        //当前活动已开始时间的天数
        int days1 = daysBetween(brickConfigDto.getStartTime(), new Date());
        Integer nowPeriod = days1 / BrickConstants.SMALLPERIODDAYS;
        if (nowPeriod == 0) {
            //第一个周期并不需要补砖
            return 0;
        }

        //每次需补的砖块,先从redis获取，没有的情况下再计算
        int remedyBrickNumOnce = Integer.parseInt(ObjectUtils.defaultIfNull(redisTemplate.opsForValue().get(getRedisRemedyBrickNumOnceKey(brickConfigId, consumerId)), "0"));
        if (remedyBrickNumOnce != 0) {
            return remedyBrickNumOnce;
        }

        //活动持续天数
        int day2 = daysBetween(brickConfigDto.getStartTime(), brickConfigDto.getEndTime());
        //总砖块数
        Integer exchangeTotalQuantity = brickConfigDto.getExchangeTotalQuantity();
        //需补的砖块=【（当前活动已开始时间的天数／活动时间持续天数）*总砖块数】-已得砖块数；结果向下取整；若为0，则不补砖。若大于0，则需要补砖
        Integer remedyBrickNumTotal = days1 * exchangeTotalQuantity / day2 - haveGetBrickNum;
        remedyBrickNumTotal = remedyBrickNumTotal < 0?0:remedyBrickNumTotal;
        Integer days = BrickConstants.SMALLPERIODDAYS;
        Integer totalPeriod = day2 / BrickConstants.SMALLPERIODDAYS;
        if (Objects.equals(nowPeriod, totalPeriod)) {
            days = day2 % BrickConstants.SMALLPERIODDAYS;
            days = days == 0 ? BrickConstants.SMALLPERIODDAYS : days;
        }
        //每次需补的砖块=【需补的砖块数／（未来一个小周期的天数*可召唤次数*3）】
        remedyBrickNumOnce = remedyBrickNumTotal / (days * brickConfigDto.getDayLimit() * 3);
        //将每次需补的砖块放入redis内
        redisTemplate.opsForValue().set(getRedisRemedyBrickNumOnceKey(brickConfigId, consumerId), String.valueOf(remedyBrickNumOnce));
        redisTemplate.expire(getRedisRemedyBrickNumOnceKey(brickConfigId, consumerId), days, TimeUnit.DAYS);
        return remedyBrickNumOnce;
    }

    //未标记 实物 发砖块逻辑
    //每次搬砖平均值公式=实物奖品未中奖用户可得总砖块数／【各等级可召唤次数*3*本期活动周期天数（即清零天数）】
    private int getBrickNumNoMarkObject(DuibaBrickConfigDto brickConfigDto, Integer workerLevel, Integer clickNum, Integer firstClickNum, Integer brickNum, Long consumerId) {
        Long brickConfigId = brickConfigDto.getId();
        //可召唤次数
        Integer dayLimit = brickConfigDto.getDayLimit();
        //本周活动周期天数
        Integer days = daysBetween(brickConfigDto.getStartTime(), brickConfigDto.getEndTime());
        //实物奖品未中奖用户可的总砖块数
        Integer objectNotWinLimit = brickConfigDto.getObjectNotWinLimit();

        //获取用户已经得到的砖块数
        Integer haveGetBrickNum = getHaveGetBrickNum(brickNum, consumerId, brickConfigId);
        //用户已获取砖块数大于可获取砖块数，下发0块砖
        if (objectNotWinLimit <= haveGetBrickNum) {
            return 0;
        }

        Integer b = dayLimit * 3 * days;
        Integer baseBrickNum;
        if (workerLevel == 1) {
            baseBrickNum = getPrimaryBrickNum(clickNum, firstClickNum, objectNotWinLimit, b);
        } else if (workerLevel == 2) {
            baseBrickNum = getMiddleBrickNum(clickNum, firstClickNum, objectNotWinLimit, b);
        } else if (workerLevel == 3) {
            baseBrickNum = getSeniorBrickNum(clickNum, firstClickNum, objectNotWinLimit, b);
        } else {
            LOGGER.info("搬砖工级别异常,consumerId={},workerLevel={}", RequestLocal.getCid(), workerLevel);
            return 0;
        }

        if (baseBrickNum > objectNotWinLimit - haveGetBrickNum) {
            return objectNotWinLimit - haveGetBrickNum;
        } else {
            return baseBrickNum;
        }

    }

    //未标记 红包 发砖块逻辑
    //每次搬砖平均值公式=【（总砖块数／红包商品总额）*单个非中奖用户发放额度】／【各等级可召唤次数*3*本期活动周期天数】
    private int getBrickNumNoMarkRedPacket(DuibaBrickConfigDto brickConfigDto, Integer workerLevel, Integer clickNum, Integer firstClickNum, Integer brickNum, Long consumerId) {
        Long brickConfigId = brickConfigDto.getId();
        //总砖块数
        Integer exchangeTotalQuantity = brickConfigDto.getExchangeTotalQuantity();
        //可召唤次数
        Integer dayLimit = brickConfigDto.getDayLimit();
        //本周活动周期天数
        Integer days = daysBetween(brickConfigDto.getStartTime(), brickConfigDto.getEndTime());
        //单个非中奖用户发放额度
        Integer redPacketNotWinLimit = brickConfigDto.getRedPacketNotWinLimit();
        //最大可获取砖块数
        Integer maxBrickNum = exchangeTotalQuantity * redPacketNotWinLimit / (BrickConstants.TOTALACCOUNT * 100);

        //获取用户已经得到的砖块数
        Integer haveGetBrickNum = getHaveGetBrickNum(brickNum, consumerId, brickConfigId);
        //用户已获取砖块数大于可获取砖块数，下发0块砖
        if (maxBrickNum <= haveGetBrickNum) {
            return 0;
        }

        Integer a = exchangeTotalQuantity * redPacketNotWinLimit;
        Integer b = BrickConstants.TOTALACCOUNT * 100 * dayLimit * 3 * days;
        Integer baseBrickNum;
        if (workerLevel == 1) {
            baseBrickNum =  getPrimaryBrickNum(clickNum, firstClickNum, a, b);
        } else if (workerLevel == 2) {
            baseBrickNum =  getMiddleBrickNum(clickNum, firstClickNum, a, b);
        } else if (workerLevel == 3) {
            baseBrickNum =  getSeniorBrickNum(clickNum, firstClickNum, a, b);
        } else {
            LOGGER.info("搬砖工级别异常,consumerId={},workerLevel={}", RequestLocal.getCid(), workerLevel);
            return 0;
        }

        if (baseBrickNum > maxBrickNum - haveGetBrickNum) {
            return maxBrickNum - haveGetBrickNum;
        } else {
            return baseBrickNum;
        }
    }

    @NotNull
    private Integer getSeniorBrickNum(Integer clickNum, Integer firstClickNum, Integer a, Integer b) {
        //高级搬砖工
        Integer num = clickNum - firstClickNum;
        Integer brickNum;
        if (num <= 5) {
            brickNum = BigDecimal.valueOf(a * 1.1).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else if (num <= 10) {
            brickNum = BigDecimal.valueOf(a * 1.2).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else {
            brickNum = BigDecimal.valueOf(a * 1.3).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        }
        return brickNum == 0?1:brickNum;
    }

    @NotNull
    private Integer getMiddleBrickNum(Integer clickNum, Integer firstClickNum, Integer a, Integer b) {
        //中级搬砖工
        Integer num = clickNum - firstClickNum;
        Integer brickNum;
        if (num <= 5) {
            brickNum =  BigDecimal.valueOf(a * 0.8).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else if (num <= 10) {
            brickNum = BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else {
            brickNum =  BigDecimal.valueOf(a * 1.1).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        }
        return brickNum == 0?1:brickNum;
    }

    @NotNull
    private Integer getPrimaryBrickNum(Integer clickNum, Integer firstClickNum, Integer a, Integer b) {
        //初级搬砖工
        Integer num = clickNum - firstClickNum;
        Integer brickNum;
        if (num <= 5) {
            brickNum = BigDecimal.valueOf(a * 0.4).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else if (num <= 10) {
            brickNum = BigDecimal.valueOf(a * 0.45).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        } else {
            brickNum = BigDecimal.valueOf(a * 0.5).divide(BigDecimal.valueOf(b), 0, BigDecimal.ROUND_DOWN).intValue();
        }
        return brickNum == 0?1:brickNum;
    }

    //获取某批搬砖工下班时间或结束召唤时间
    private Date setEndTime(Integer time) {
        return DateUtils.minutesAddOrSub(new Date(), time);
    }

    /**
     * 扣除本地积分
     */
    private void subLocalCredits(Long credits, Long consumerId) {
        if (credits <= 0) {
            return;
        }
        try {
            remoteConsumerService.decrementCredits(consumerId, credits);
        } catch (Exception e) {
            LOGGER.error("扣除本地积分失败,consumerId={}", consumerId, e);
        }
    }

    //拼装消息体
    private SubCreditsMsgDto getSubCreditsMsg(AppSimpleDto appSimpleDto,
                                              RequestParams requestParams,
                                              Long credits,
                                              String partnerUserId,
                                              String orderNum,
                                              DuibaBrickConfigDto brickConfigDto) {
        CreditConsumeParams creditConsumeParams = new CreditConsumeParams();
        creditConsumeParams.setAppKey(appSimpleDto.getAppKey());
        creditConsumeParams.setFacePrice(0);
        creditConsumeParams.setActualPrice(0);
        creditConsumeParams.setTimestamp(new Date());
        creditConsumeParams.setCredits(credits);
        creditConsumeParams.setIp(requestParams.getIp());
        creditConsumeParams.setTransfer(requestParams.getTransfer());
        creditConsumeParams.setType(SubCreditsOuterType.HDTOOL.getCode());
        creditConsumeParams.setUid(partnerUserId);
        creditConsumeParams.setOrderNum(BrickConstants.CREDITS_MESSAGE_BRICK + orderNum);
        if (AppIdConstant.isOppoApp(appSimpleDto.getId())) {
            creditConsumeParams.setDescription(brickConfigDto.getTitle());
        } else {
            creditConsumeParams.setDescription(BrickConstants.DESCRIPTION + brickConfigDto.getTitle());
        }
        Map<String, String> map = creditConsumeParams.toRequestMap(appSimpleDto.getAppSecret());

        SubCreditsMsgDto subCreditsMsg = new SubCreditsMsgDto();
        subCreditsMsg.setAppId(appSimpleDto.getId());
        subCreditsMsg.setAppSecret(appSimpleDto.getAppSecret());
        subCreditsMsg.setConsumerId(requestParams.getConsumerId());
        subCreditsMsg.setCreditConsumeParams(creditConsumeParams);
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("brickConfigId", String.valueOf(brickConfigDto.getId()));
        hashMap.put("partnerUserId", partnerUserId);
        hashMap.put(BrickConstants.CREDITS, String.valueOf(credits));
        hashMap.put("transfer", requestParams.getTransfer());
        subCreditsMsg.setParams(hashMap);
        subCreditsMsg.setRelationId(orderNum);
        subCreditsMsg.setRelationType(SubCreditsType.BRICK);
        subCreditsMsg.setCreditsConsumeRequestUrl(appSimpleDto.getCreditsConsumeRequestUrl());
        subCreditsMsg.setAuthParams(map);
        subCreditsMsg.setCallbackTopic(rocketMqMessageTopic.getBrickSubCreditsCallback());

        return subCreditsMsg;
    }

    //发送mq消息
    private void sendMq(SubCreditsMsgDto subCreditsMsg) {
        try {
            Message message = new Message(rocketMqMessageTopic.getSubCreditsTopic(), SubCreditsMsgDto.encode(subCreditsMsg));
            SendResult result = rocketMqProducer.send(message);
            if (!SendStatus.SEND_OK.equals(result.getSendStatus())) {
                rollBackOrder(subCreditsMsg.getRelationId(), subCreditsMsg.getConsumerId(), Long.valueOf(subCreditsMsg.getParams().get(BrickConstants.CREDITS)));
                LOGGER.error("扣积分失败{}", message);
            }
        } catch (Exception e) {
            rollBackOrder(subCreditsMsg.getRelationId(), subCreditsMsg.getConsumerId(), Long.valueOf(subCreditsMsg.getParams().get(BrickConstants.CREDITS)));
            LOGGER.error("扣除积分mq发送失败", e);
        }
    }

    // 搬砖活动扣积分发消息异常回退处理
    private void rollBackOrder(String orderNum, Long consumerId, Long credits) {
        LOGGER.info("搬砖活动-用户{}扣积分失败回退，子订单'{}'状态更新为处理失败，并将扣掉的本地积分加回来", consumerId, orderNum);
        // 将订单状态更新为处理失败
        remoteActivityOrderService.consumeCreditsFail(
                orderNum,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "扣积分失败",
                null,
                null);
        // 将扣掉的本地积分加回来
        remoteConsumerService.increaseCredits(consumerId, credits);
    }

    //校验活动状态
    private ErrorCode checkOpStatus(DuibaBrickConfigDto brickConfigDto) {
        //入库表数据状态
        OperatingActivityDto operatingActivityDto = remoteOperatingActivityServiceNew.find(brickConfigDto.getOpId());
        if (operatingActivityDto == null || operatingActivityDto.getStatus() == null) {
            return ErrorCode.E0700003;
        }
        //关闭状态
        if (!Objects.equals(operatingActivityDto.getStatus(), OperatingActivityDto.StatusIntOpen)) {
            return ErrorCode.E0700004;
        }

        Date now = new Date();
        //校验活动是否开始
        if (brickConfigDto.getStartTime().compareTo(now) > 0) {
            return ErrorCode.E0700017;
        }

        //校验活动是否结束
        if (brickConfigDto.getEndTime().compareTo(new Date()) < 0) {
            return ErrorCode.E0700001;
        }
        return null;
    }

    //获取连续登录的天数
    private Long getLoginDay(Long brickConfigId, Long consumerId) {
        //判断当前  redis 数据 有无更新
        String todayKey = getRedisKey(brickConfigId, consumerId);
        //Hbase 中key
        String key = getHbaseKey(brickConfigId, consumerId);
        String data = redisTemplate.opsForValue().get(todayKey);
        if (!StringUtils.isBlank(data)) {
            return remoteHbConsisHashKvService.getLongByKey(key);
        }
        //设置过期key
        redisTemplate.opsForValue().set(todayKey, "ok");
        //redis失效时间 随机在12点边缘，防止因大面积过期导致异常情况
        redisTemplate.expireAt(todayKey, new Date(DateUtil.getToday23Date().getTime() + new Random().nextInt(10)-5));
        return remoteHbConsisHashKvService.increaseByKey(key, 1);
    }

    //每天登录hbasekey
    private String getHbaseKey(Long brickConfigId, Long consumerId) {
        return ActAccessWebHBaseKeyEnum.K037.toString() + brickConfigId + "_" + consumerId;
    }

    //订单号hbasekey
    private String getOrderNumKey(Long brickConfigId, String orderNum) {
        return ActAccessWebHBaseKeyEnum.K038.toString() + brickConfigId + "_" + orderNum;
    }

    //首次召唤搬砖工数hbaseKey(区分级别)
    private String getBrickNumKey(Long brickConfigId, Long consumerId, Integer workerLevel) {
        return ActAccessWebHBaseKeyEnum.K039.toString() + brickConfigId + "_" + consumerId + "_" + workerLevel;
    }

    //首次登录分配红包拆分顺序hBaseKey
    private String getRedPacketSplitKey(Long brickConfigId, Long consumerId) {
        return ActAccessWebHBaseKeyEnum.K042.toString() + brickConfigId + "_" + consumerId;
    }

    //每天登录key
    private String getRedisKey(Long brickConfigId, Long consumerId) {
        return RedisKeySpace.K075.toString() + brickConfigId + "_" +consumerId + "_" + DateUtil.getDayStr(new Date());
    }

    //每天召唤次数key
    private String getRedisCallKey(Long consumerId, Integer level) {
        return RedisKeySpace.K076.toString() + consumerId + "_" + level + "_" + DateUtil.getDayStr(new Date());
    }

    //已领取某个奖品的人数
    private String getRedisTakePrizeKey(Long brickConfigId, Long brickPrizeId) {
        return RedisKeySpace.K077.toString() + brickConfigId + "_" + brickPrizeId;
    }

    //搬砖活动实物库存不足发送邮件
    private String getRedisEmailKey(Long brickConfigId) {
        return RedisKeySpace.K078.toString() + brickConfigId + "_" + DateUtil.getDayStr(new Date());
    }

    //搬砖活动周期标识
    private String getRedisPeriodMarkKey(Long brickConfigId) {
        return RedisKeySpace.K079.toString() + brickConfigId;
    }

    //标记用户每次需补的砖块
    private String getRedisRemedyBrickNumOnceKey(Long brickConfigId, Long consumerId) {
        return RedisKeySpace.K080.toString() + brickConfigId + "_" +consumerId;
    }

    //领取奖品人数+1
    private void incrementTakePrizeCount(Long brickConfigId, Long brickPrizeId, Date endTime) {
        redisTemplate.opsForValue().increment(getRedisTakePrizeKey(brickConfigId, brickPrizeId), 1);
        redisTemplate.expireAt(getRedisTakePrizeKey(brickConfigId, brickPrizeId), endTime);
    }

    //召唤次数+1
    private void incrementCallCount(Long consumerId, Integer level) {
        redisTemplate.opsForValue().increment(getRedisCallKey(consumerId, level), 1);
        redisTemplate.expireAt(getRedisCallKey(consumerId, level), new Date(DateUtil.getToday23Date().getTime() + new Random().nextInt(10)-5));
    }

    //搬砖活动第一位访问用户设置周期标识
    private boolean periodMarkLock(Long brickConfigId, Date endTime) {
        if (redisTemplate.opsForValue().setIfAbsent(getRedisPeriodMarkKey(brickConfigId), "1")) {
            redisTemplate.expireAt(getRedisPeriodMarkKey(brickConfigId), endTime);
            return true;
        }
        return false;
    }

    //处理活动小周期
    private void dealPeriod(Long brickConfigId, Date startTime, Date endTime) {
        if (periodMarkLock(brickConfigId, endTime)) {
            //需要按活动周期分若等分的小周期
            endTime = DateUtils.changeByDay(endTime, -1);
            int betweenDays = daysBetween(startTime, endTime);
            int periods = betweenDays / BrickConstants.SMALLPERIODDAYS;
            if (betweenDays % BrickConstants.SMALLPERIODDAYS == 0) {
                periods--;
            }
            Date date = startTime;
            List<AsynTaskDto> asynTaskDtos = Lists.newArrayList();
            for (int i=1; i<=periods; i++) {
                date = DateUtils.daysAddOrSub(date, BrickConstants.SMALLPERIODDAYS);
                //任务信息插入数据库
                AsynTaskDto asynTaskDto = new AsynTaskDto();
                asynTaskDto.setTaskType(AsynTaskTypeEnum.BrickMark);
                asynTaskDto.setNextTime(date);
                //小周期持续的天数
                Integer days = BrickConstants.SMALLPERIODDAYS;
                if (Objects.equals(i, periods)) {
                    days = betweenDays % BrickConstants.SMALLPERIODDAYS;
                    days = days == 0 ? BrickConstants.SMALLPERIODDAYS : days;
                }
                BrickAysnTaskInfo brickAysnTaskInfo = new BrickAysnTaskInfo(brickConfigId, periods, i, days);
                asynTaskDto.setTaskInfo(JSONObject.toJSONString(brickAysnTaskInfo));
                asynTaskDtos.add(asynTaskDto);
            }
            remoteAsynTaskService.batchInsert(asynTaskDtos);
        }
    }

    //随机标记
    private void randomMark(Long consumerId, DuibaBrickPrizeDto brickPrizeDto) {
        //用户id最后一位==当前时间毫秒数最后一位
        Long luckNumber1 = consumerId % 10;
        Long luckNumber2 = new Date().getTime() % 10;
        if (!luckNumber1.equals(luckNumber2)) {
            return;
        }
        //具体到奖品是否被标记
        Integer prizeType = brickPrizeDto.getPrizeType();
        Long brickPrizeId = brickPrizeDto.getId();
        //该奖品可领取人数
        Integer prizeWinPrizeNum = brickPrizeDto.getWinPrizeNum();
        //已获取该奖品用户数
        Integer takePrizedNum = Integer.valueOf(ObjectUtils.defaultIfNull(redisTemplate.opsForValue().get(getRedisTakePrizeKey(brickPrizeDto.getBrickConfigId(), brickPrizeId)), "0"));
        //能够获取该奖品的剩余用户数
        if (takePrizedNum >= prizeWinPrizeNum) {
            return;
        }

        //已标记用户集合
        List<DuibaBrickMarkedDto> brickMarkedDtos = remoteDuibaBrickMobileDataService.getMarkedUser(brickPrizeDto.getBrickConfigId(), brickPrizeId);
        //已标记用户大于等于可领取人数，直接返回
        if (brickMarkedDtos.size() >= prizeWinPrizeNum) {
            return;
        }
        long count = brickMarkedDtos.stream().filter(brickMarkedDto -> brickMarkedDto.getConsumerId().equals(consumerId)).count();
        //该用户已标记，直接返回
        if (count > 0) {
            return;
        }

        //插入标记表
        remoteDuibaBrickMobileDataService.markUser(brickPrizeDto.getBrickConfigId(), consumerId, prizeType, brickPrizeId);
    }

    //未登录日志埋点
    private void accessLogIndexNoLogin(Long id, Long opId) {
        AccessLogFilter.putExPair("type", 301);
        AccessLogFilter.putExPair("sub_type", 1);
        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("id", id);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", 56);
        AccessLogFilter.putExPair("loginStatus", 2);
        AccessLogFilter.putExPair("userCredits", 0);
        AccessLogFilter.putExPair("pageBizId", 208);
        AccessLogFilter.putExPair("from", 1);
    }

    //搬砖活动首页日志埋点
    private void accessLogIndex(Long id, Long opId, Integer prizeType, Integer addBrickNum, Long cumulativeLogin) {
        AccessLogFilter.putExPair("type", 301);
        AccessLogFilter.putExPair("sub_type", 1);
        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("id", id);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", 56);
        AccessLogFilter.putExPair("loginStatus", 1);
        AccessLogFilter.putExPair("userCredits", RequestLocal.getConsumerDO().getCredits());
        AccessLogFilter.putExPair("pageBizId", 208);
        AccessLogFilter.putExPair("from", 1);
        AccessLogFilter.putExPair("goods", prizeType);
        AccessLogFilter.putExPair("brick", addBrickNum);
        //累计登录2天解锁中级搬砖工，累计登录3天解锁高级搬砖工
        if (Objects.equals(cumulativeLogin, BrickConstants.openMiddleDays)) {
            AccessLogFilter.putExPair("unlock", 1);
        } else if (Objects.equals(cumulativeLogin, BrickConstants.openSeniorDays)) {
            AccessLogFilter.putExPair("unlock", 2);
        }
    }

    //搬砖活动参与日志
    private void accessLogClick(Long brickConfigId, Long opId, String orderNum, Integer brickNum) {
        AccessLogFilter.putExPair("type", 301);
        AccessLogFilter.putExPair("sub_type", 2);
        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("id", brickConfigId);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", 56);
        AccessLogFilter.putExPair("orderNum", orderNum);
        AccessLogFilter.putExPair("loginStatus", 1);
        AccessLogFilter.putExPair("userCredits", RequestLocal.getConsumerDO().getCredits());
        AccessLogFilter.putExPair("pageBizId", 237);
        AccessLogFilter.putExPair("from", 1);
        AccessLogFilter.putExPair("quantiity", brickNum);
    }

    //获取两个时间差的天数，不足一天按一天算
    private int daysBetween(Date smdate, Date bdate) {
        Long value = bdate.getTime() - smdate.getTime();
        long betweenDays = value / (1000 * 3600 * 24);
        if (value % (1000 * 3600 * 24) != 0) {
            betweenDays++;
        }
        return Integer.parseInt(String.valueOf(betweenDays));
    }

    //如果是 2019-03-04 00:00:00, 需要当成2019-03-03 处理
    private Date dealLingDian(Date date) {
        String str = DateUtils.getSecondStr(date);
        if (str.contains("00:00:00")) {
            return DateUtils.secondsAddOrSub(date, -1);
        }
        return date;
    }

    //比对两个时间是否在同一天
    private static boolean sameDate(Date d1, Date d2) {
        LocalDate localDate1 = ZonedDateTime.ofInstant(d1.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = ZonedDateTime.ofInstant(d2.toInstant(), ZoneId.systemDefault()).toLocalDate();
        return localDate1.isEqual(localDate2);
    }
}
