package com.duiba.activity.accessweb.service.newgamecenter;

import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.vo.newgamecenter.NewGameCenterAccountInfoVO;
import com.duiba.activity.accessweb.vo.newgamecenter.NewGameCenterIndexInfoVO;
import com.duiba.activity.accessweb.vo.newgamecenter.PopupVO;
import com.duiba.activity.accessweb.vo.newgamecenter.RankListVO;

/**
 * <AUTHOR>
 * @date 2018/09/04
 */
public interface NewGameCenterService {
    /**
     * 获取用户相关信息
     * @param consumer 用户dto
     * @return Result<NewGameCenterAccountInfoVO>
     */
    Result<NewGameCenterAccountInfoVO> getAccountInfo(ConsumerDto consumer);

    /**
     * 获取首页信息
     * @param consumer 用户dto
     * @param appId appId
     * @return Result<NewGameCenterIndexInfoVO>
     */
    Result<NewGameCenterIndexInfoVO> getIndexInfo(ConsumerDto consumer, Long appId);

    /**
     * 获取当前用户是否需要获奖弹窗
     * 弹窗只显示最近两个赛季的获奖信息
     * @param consumerDto 用户dto
     * @return Result<PopupVO>
     */
    Result<PopupVO> getPopup(ConsumerDto consumerDto);

    /**
     * 领取红包
     * @param redPacket 红包的key
     * @param consumerDto consumer
     * @return Result
     */
    Result doDraw(String redPacket, ConsumerDto consumerDto);

    /**
     * 获取排行榜列表
     * @param seasonConfigId 赛季配置id
     * @param consumerDto consumer
     * @param appId appId
     * @return Result<RankListVO>
     */
    Result<RankListVO> getRankList(Long seasonConfigId, ConsumerDto consumerDto, Long appId);

    /**
     * 获取用户ext信息
     */
    ConsumerExtraDto getConsumerExtraDto(Long consumerId);


}
