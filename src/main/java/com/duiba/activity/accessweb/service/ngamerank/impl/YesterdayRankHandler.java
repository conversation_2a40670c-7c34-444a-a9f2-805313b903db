package com.duiba.activity.accessweb.service.ngamerank.impl;

import cn.com.duiba.activity.center.api.dto.ngame.SimpleConsumerRankDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.duiba.activity.accessweb.enums.NGameRankTypeEnum;
import com.duiba.activity.accessweb.pjv.req.NgameRealTimeRankReq;
import com.duiba.activity.accessweb.pjv.rsp.NgameRealTimeRankRsp;
import com.duiba.activity.accessweb.service.ngame.NgameRankConfig;
import com.duiba.activity.accessweb.service.ngame.NgameRankService;
import com.duiba.activity.accessweb.service.ngame.NgameService;
import com.duiba.activity.accessweb.service.ngamerank.NGameRankHandler;
import com.duiba.activity.accessweb.vo.ngame.NGameConfigVO;
import com.duiba.activity.accessweb.vo.ngame.PrizeUser;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by hww on 2018/4/12 下午2:51.
 */
@Component
public class YesterdayRankHandler implements NGameRankHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(YesterdayRankHandler.class);

    /**
     * 昨日实时排名缓存，10分钟
     */
    private static final Cache<String, List<PrizeUser>> REAL_TIME_YESTERDAY_RANK_CACHE = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).maximumSize(1000).build();

    @Autowired
    private NgameRankService ngameRankService;
    @Autowired
    private NgameService ngameService;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    /**
     * 获取处理器类型，用于注册到管理器
     *
     * @return
     */
    @Override
    public Integer getRankType() {
        return NGameRankTypeEnum.YESTERDAY_RANK.getCode();
    }

    /**
     * 获取排名信息数据
     *
     * @param rankReq
     * @return
     */
    @Override
    public NgameRealTimeRankRsp getRankData(NgameRealTimeRankReq rankReq) {
        //创建响应信息
        NgameRealTimeRankRsp rankRsp = new NgameRealTimeRankRsp();
        //获取游戏的基础配置信息
        NGameConfigVO config = ngameService.getNgameConfig(rankReq.getId());
        if (config == null) {
            //获取配置信息失败，返回空响应对象
            return rankRsp;
        }
        return packageRankRsp(rankRsp, rankReq, config);
    }

    /**
     * 将信息加入返回信息中
     * @param rankRsp
     * @param config
     */
    private NgameRealTimeRankRsp packageRankRsp(NgameRealTimeRankRsp rankRsp, NgameRealTimeRankReq rankReq, NGameConfigVO config) {
        List<PrizeUser> userList = getRankUserList(rankReq, config);
        if (CollectionUtils.isEmpty(userList)) {
            return rankRsp;
        }
        Long consumerId = rankReq.getConsumerId();
        List<PrizeUser> userListForRsp = userList.stream().map(user -> {
            PrizeUser prizeUser = BeanUtils.copy(user, PrizeUser.class);
            String cid = prizeUser.getCid();
            //比对当前用户是否在排行榜信息中
            if (Objects.equals(String.valueOf(consumerId), cid)) {
                rankRsp.setUser(prizeUser);
                //标记用户在排行榜中
                rankRsp.setInRank(true);
            }
            if (cid.length() > 3) {
                //将用户id加*处理
                prizeUser.setCid("***" + cid.substring(3, cid.length()));
            }
            return prizeUser;
        }).collect(Collectors.toList());
        rankRsp.setUserList(userListForRsp);
        if (rankRsp.getUser() != null) {
            return rankRsp;
        }
        //当前用户不在排行榜信息中
        PrizeUser userInfo = new PrizeUser();
        //将构建的用户信息加入响应信息中
        rankRsp.setUser(userInfo);
        Long gameId = config.getGameId();
        NgameRankConfig rankConfig = ngameRankService.getYesterdayRankConfig(gameId, config.isRankDesc());
        Long score = rankConfig.getScore(String.valueOf(consumerId));
        Long rank = rankConfig.getRank(String.valueOf(consumerId));
        if (rank != null) {
            userInfo.setRank(String.valueOf(rank));
        }
        if (score != null) {
            userInfo.setMaxScore(String.valueOf(score));
        }
        //定制需求查询1000名的成绩
        rankRsp.setLastScore(rankConfig.getScoreByRank(1000));
        return rankRsp;
    }

    /**
     * 获取排行榜信息
     * @param rankReq
     * @param config
     * @return
     */
    private List<PrizeUser> getRankUserList(NgameRealTimeRankReq rankReq, NGameConfigVO config) {
        Long opId = rankReq.getId();
        String key = opId + "-" + rankReq.getCount();
        try {
            return REAL_TIME_YESTERDAY_RANK_CACHE.get(key, () -> getRankUserListNoCache(rankReq, config));
        } catch (Exception e) {
            LOGGER.warn("查询昨日排行榜信息失败", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 通过查询数据库获取排行榜信息
     * @param rankReq
     * @return
     */
    private List<PrizeUser> getRankUserListNoCache(NgameRealTimeRankReq rankReq, NGameConfigVO config) {
        //查询排名
        List<SimpleConsumerRankDto> consumerRank = ngameRankService.getYesterdayRankConfig(config.getGameId(), config.isRankDesc()).getConsumerRank(rankReq.getCount() - 1);
        if (CollectionUtils.isEmpty(consumerRank)) {
            return Lists.newArrayList();
        }
        DubboResult<List<ConsumerExtraDto>> co = remoteConsumerExtraService.findAllByConsumerIds(Lists.transform(consumerRank, (c) -> Long.valueOf(c.getConsumerId())));
        List<ConsumerExtraDto> ce = co.getResult();
        Map<Long, ConsumerExtraDto> consumerExtraDtoMap = Maps.uniqueIndex(ce, ConsumerExtraDto::getConsumerId);
        return consumerRank.stream().map((c) -> {
            PrizeUser prizeUser = new PrizeUser();
            prizeUser.setCid(c.getConsumerId());
            ConsumerExtraDto consumerExtraDto = consumerExtraDtoMap.get(Long.valueOf(c.getConsumerId()));
            if (consumerExtraDto != null) {
                //昵称
                prizeUser.setNickName(consumerExtraDto.getNickname());
                //头像
                prizeUser.setAvatar(consumerExtraDto.getAvatar());
            }
            prizeUser.setMaxScore(c.getMaxScoreStr());
            prizeUser.setRank(c.getRank());
            return prizeUser;
        }).collect(Collectors.toList());
    }

}
