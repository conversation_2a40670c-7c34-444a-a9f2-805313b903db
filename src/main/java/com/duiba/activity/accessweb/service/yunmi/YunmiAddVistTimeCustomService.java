package com.duiba.activity.accessweb.service.yunmi;

/**
 * @Description: 云米商城定制-云米请求活动加次数接口时，生成Redis记录，供其他业务使用
 * @Author: fxr
 * @Date: 2019/6/17
 */
public interface YunmiAddVistTimeCustomService {

    /**
     * 记录某个时间段内，用户增加活动次数，好友助力次数Redis记录，供其他业务场景使用
     *  @param appId        应用id
     * @param activityId    活动id
     * @param partnerUserId 用户uid
     * @param validType     添加次数生效范围 0：永久 1：当日
     * @param addTimes      添加次数
     * @param activityType  活动类型
     */
    void addVistTimesRecord(Long appId, Long activityId, String partnerUserId, String validType, Long addTimes, String activityType);
}
