package com.duiba.activity.accessweb.service.shuqi.bo;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019/1/3 15:40
 * @description: 战队成员中奖信息
 */
public class TeamMemberPrizeBO implements Serializable {

	private static final long serialVersionUID = -8968930989751386929L;
	/**
	 * 用户id
	 */
	private Long consumerId;
	/**
	 * 战队id
	 */
	private Long teamId;
	/**
	 * 战队排名
 	 */
	private Integer rank;
	/**
	 * 奖品类型
	 * @see com.duiba.activity.accessweb.enums.ShuQiPrizeTypeEnum
	 */
	private Integer prizeType;
	/**
	 * 现金
	 */
	private String amount;
	/**
	 * 用户姓名
	 */
	private String contactName;
	/**
	 * 用户手机号
	 */
	private String contactPhone;
	/**
	 * 支付宝账号或地址
	 */
	private String contactText;

	public Long getConsumerId() {
		return consumerId;
	}

	public void setConsumerId(Long consumerId) {
		this.consumerId = consumerId;
	}

	public Long getTeamId() {
		return teamId;
	}

	public void setTeamId(Long teamId) {
		this.teamId = teamId;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public Integer getPrizeType() {
		return prizeType;
	}

	public void setPrizeType(Integer prizeType) {
		this.prizeType = prizeType;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactPhone() {
		return contactPhone;
	}

	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	public String getContactText() {
		return contactText;
	}

	public void setContactText(String contactText) {
		this.contactText = contactText;
	}
}
