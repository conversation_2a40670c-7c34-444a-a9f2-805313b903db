package com.duiba.activity.accessweb.service;

import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.thirdparty.api.hsbc.RemoteHsbcBankServcie;
import cn.com.duiba.thirdparty.dto.hf.HsbcScrmEventParam;
import cn.com.duiba.thirdparty.dto.hsbc.HsbcTagCheckDto;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.vo.hsbc.HsbcJoinConfig;
import com.duiba.activity.accessweb.vo.hsbc.UserLimit;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class HsbcService {

    @Resource
    private RemoteHsbcBankServcie remoteHsbcBankServcie;

    public static Logger LOGGER = LoggerFactory.getLogger(HsbcService.class);

    public static String PROJECT_NAME = "汇丰汇选活动";

    /**
     * 汇丰参与限制
     */
    public String checkUserLimit(HsbcJoinConfig hsbcJoinConfig, Long appId, String uid) {
        UserLimit userLimitInfo = getUserLimitInfo(hsbcJoinConfig, appId, uid);
        // 0-不在名单，toast提示“亲，感谢参与！您好！该活动仅面向特邀用户开放”（针对APP白名单、SCRM白名单、TMS标签）
        if (userLimitInfo.isAppLimit() || userLimitInfo.isScrmLimit() || userLimitInfo.isTmsTagLimit()) {
            return "您好！该活动仅面向特邀用户开放";
        }
        // 1-在名单但未加微（是否加微根据「SCRM事件」的「与当前PWP的双向加微」类型判断），toast提示“亲，您还未完成活动的指定任务，请联系您的规划师了解详情”
        if (userLimitInfo.isScrmEvent()) {
            return "您还未完成活动的指定任务，请联系您的规划师了解详情";
        }
        return null;
    }

    /**
     * 获取行方用户限制
     */
    public UserLimit getUserLimitInfo(HsbcJoinConfig hsbcJoinConfig, Long appId, String uid) {
        boolean appLimit = hsbcJoinConfig.isAppWhiteSwitch();
        boolean scrmEventLimit = hsbcJoinConfig.isScrmEventSwitch();
        boolean scrmWhiteListLimit = hsbcJoinConfig.isScrmWhiteSwitch();
        boolean tmsTagLimit = hsbcJoinConfig.isTmsTagSwitch();

        return getUserLimitInfo(hsbcJoinConfig, appId, uid, appLimit, scrmWhiteListLimit, scrmEventLimit, tmsTagLimit);
    }


    /**
     * 获取行方用户限制
     */
    public  UserLimit getUserLimitInfo(HsbcJoinConfig hsbcJoinConfig, Long appId, String uid, Boolean appLimit, Boolean scrmLimit, Boolean scrmEvent, Boolean tmsTagLimit) {
        LOGGER.info("{}获取用户限制信息 appId:{},uid:{},appLimit:{},scrmLimit:{},scrmEvent:{},tmsTagLimit;{}", PROJECT_NAME, appId, uid, appLimit, scrmLimit, scrmEvent, tmsTagLimit);
        TimeInterval timer = DateUtil.timer();
        List<CompletableFuture<Map<String, Boolean>>> requests = new ArrayList<>();

        if (appLimit) {
            String businessId = hsbcJoinConfig.getAppBusinessId();
            requests.add(CompletableFuture.supplyAsync(() -> appLimitRequest(appId, uid, businessId)));
        }
        if (scrmLimit) {
            String businessId = hsbcJoinConfig.getScrmWhiteBusinessId();
            requests.add(CompletableFuture.supplyAsync(() -> scrmLimitRequest(appId, uid, businessId)));
        }
        if (scrmEvent) {
            String scrmEvent1 = hsbcJoinConfig.getScrmEvent();
            requests.add(CompletableFuture.supplyAsync(() -> scrmEventRequest(appId, uid, scrmEvent1)));
        }
        if (tmsTagLimit) {
            String tmsTag = hsbcJoinConfig.getTmsTag();
            requests.add(CompletableFuture.supplyAsync(() -> checkUserTagRequest(appId, uid, tmsTag)));
        }

        UserLimit userLimit = new UserLimit();
        if (CollectionUtils.isEmpty(requests)) {
            return userLimit;
        }

        List<Map<String, Boolean>> responses = requests
                .stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        // 处理每个请求的结果
        for (Map<String, Boolean> item : responses) {
            Boolean appLimit1 = item.get("appLimit");
            if (appLimit1 != null) {
                userLimit.setAppLimit(appLimit1);
            }
            Boolean scrmLimit1 = item.get("scrmLimit");
            if (scrmLimit1 != null) {
                userLimit.setScrmLimit(scrmLimit1);
            }
            Boolean scrmEvent1 = item.get("scrmEvent");
            if (scrmEvent1 != null) {
                userLimit.setScrmEvent(scrmEvent1);
            }
            Boolean tmsTagLimit1 = item.get("tmsTagLimit");
            if (tmsTagLimit1 != null) {
                userLimit.setTmsTagLimit(tmsTagLimit1);
            }

        }
        LOGGER.info("{}获取用户限制信息 appId:{},uid:{},appLimit:{},scrmLimit:{},scrmEvent:{},tmsTagLimit;{},结果:{},耗时:{}ms", PROJECT_NAME, appId, uid, appLimit, scrmLimit, scrmEvent, tmsTagLimit, JSON.toJSONString(userLimit), timer.intervalMs());
        return userLimit;
    }

    /**
     * 获取行方用户限制
     *
     * @param appId appId
     * @param uid   uid
     * @return Map<String, Boolean> String:appLimit,scrmLimit,scrmEvent Boolean:true - 需要限制, false - 不需要限制
     */
    private  Map<String, Boolean> appLimitRequest(Long appId, String uid, String businessId) {
        try {
            Map<String, Boolean> resultMap = new HashMap<>();
            String data;
            if (SpringEnvironmentUtils.isTestEnv()){
                Map<String, Object> map = new HashMap<>();
                map.put("appId", appId);
                map.put("uid", uid);
                map.put("businessId", StringUtils.isBlank(businessId) ? "20230301_001" : businessId);
                String response = HttpUtil.get("https://docs.dui88.com/mock/1785/mockD", map, 5000);
                JSONObject result = JSONObject.parseObject(response, JSONObject.class);
                if (Objects.isNull(result)) {
                    resultMap.put("scrmLimit", true);
                    return resultMap;
                }
                if (ObjectUtils.notEqual(Boolean.TRUE, result.getBoolean("success"))) {
                    resultMap.put("scrmLimit", true);
                    return resultMap;
                }
                data = result.getString("data");
            }else {
                data = remoteHsbcBankServcie.checkuserlabel(uid, appId, businessId, null);
            }
            LOGGER.info("{} appLimitRequest appId={} uid={} businessId={} res={}",PROJECT_NAME,appId,uid,null,data);
            resultMap.put("appLimit", !Objects.equals("1", data));
            return resultMap;
        } catch (Exception e) {
            LOGGER.warn("{}-appLimitRequest error appId:{},uid:{}", PROJECT_NAME, appId, uid, e);
            return new HashMap<>();
        }
    }

    private  Map<String, Boolean> scrmLimitRequest(Long appId, String uid, String businessId) {
        try {
            Map<String, Boolean> resultMap = new HashMap<>();
            String data;
            if (SpringEnvironmentUtils.isTestEnv()){
                Map<String, Object> map = new HashMap<>();
                map.put("appId", appId);
                map.put("uid", uid);
                map.put("businessId", StringUtils.isBlank(businessId) ? "20230301_001" : businessId);
                map.put("type", "DUIBA_SCRM_WHITELIST");

                String response = HttpUtil.get("https://docs.dui88.com/mock/1785/mockC", map, 5000);
                JSONObject result = JSONObject.parseObject(response, JSONObject.class);

                if (Objects.isNull(result)) {
                    resultMap.put("scrmEvent", true);
                    return resultMap;
                }
                if (ObjectUtils.notEqual(Boolean.TRUE, result.getBoolean("success"))) {
                    resultMap.put("scrmEvent", true);
                    return resultMap;
                }
                data = result.getString("data");
            }else {
                data = remoteHsbcBankServcie.checkuserlabel(uid, appId, businessId, "DUIBA_SCRM_WHITELIST");
            }
            LOGGER.info("{} t appId={} uid={} businessId={} res={}",PROJECT_NAME,appId,uid,businessId,data);
            resultMap.put("scrmLimit", !Objects.equals("1", data));
            return resultMap;
        } catch (Exception e) {
            LOGGER.warn("{}-scrmLimitRequest error", PROJECT_NAME, e);
            return new HashMap<>();
        }
    }

    private  Map<String, Boolean> scrmEventRequest(Long appId, String uid, String scrmEvent) {
        try {
            Map<String, Boolean> resultMap = new HashMap<>();
            HsbcScrmEventParam hsbcScrmEventParam = new HsbcScrmEventParam();
            hsbcScrmEventParam.setAppId(appId.toString());
            hsbcScrmEventParam.setUid(uid);
            hsbcScrmEventParam.setAttributeList(Lists.newArrayList(scrmEvent));
            String data;
            if (SpringEnvironmentUtils.isTestEnv()) {
                Map<String, Object> map = new HashMap<>();
                map.put("appId", appId);
                map.put("uid", uid);
                List<String> bizList = new ArrayList<>();
                bizList.add(scrmEvent);
                map.put("attributeList", bizList);

                String response = HttpUtil.post("https://docs.dui88.com/mock/1785/mockB", JSON.toJSONString(map), 5000);
                JSONObject result = JSONObject.parseObject(response, JSONObject.class);
                if (result == null) {
                    resultMap.put("tmsTagLimit", true);
                    return resultMap;
                }
                if (ObjectUtils.notEqual(Boolean.TRUE, result.getBoolean("success"))) {
                    resultMap.put("tmsTagLimit", true);
                    return resultMap;
                }
                data = result.getString("data");
            }else {
                data = remoteHsbcBankServcie.queryUserScrmEvent(hsbcScrmEventParam);
            }
            LOGGER.info("{} scrmEventRequest appId={} uid={} scrmEvent={} res={}",PROJECT_NAME,appId,uid,scrmEvent,data);
            JSONObject attributeResultMap = JSONObject.parseObject(data, JSONObject.class);
            String scrmEventResult = attributeResultMap.getString(scrmEvent);
            resultMap.put("scrmEvent", !Objects.equals("TRUE", scrmEventResult));
            return resultMap;
        } catch (Exception e) {
            LOGGER.warn("{}-scrmEventRequest error", PROJECT_NAME, e);
            return new HashMap<>();
        }

    }

    private  Map<String, Boolean> checkUserTagRequest(Long appId, String uid, String tagCode) {
        try {
            Map<String, Boolean> resultMap = new HashMap<>();
            List<HsbcTagCheckDto> hsbcTagCheckDtos;
            if (SpringEnvironmentUtils.isTestEnv()) {
                Map<String, Object> map = new HashMap<>();
                map.put("appId", appId);
                map.put("uid", uid);
                map.put("attributeList", Lists.newArrayList(tagCode));
                String response = HttpUtil.post("https://docs.dui88.com/mock/1785/mockA", JSON.toJSONString(map),5000);
                JSONObject result = JSONObject.parseObject(response, JSONObject.class);
                String data = result.getString("data");
                hsbcTagCheckDtos = JSONObject.parseArray(data, HsbcTagCheckDto.class);
            }else {
                hsbcTagCheckDtos = remoteHsbcBankServcie.checkUserTag(appId, uid, Lists.newArrayList(tagCode));
            }
            LOGGER.info("{} checkUserTag appId={} uid={} tagCode={} res={}",PROJECT_NAME,appId,uid,tagCode,JSON.toJSONString(hsbcTagCheckDtos));
            if (CollectionUtils.isEmpty(hsbcTagCheckDtos)) {
                resultMap.put("tmsTagLimit", true);
                return resultMap;
            }
            boolean hadTmsTag = hsbcTagCheckDtos.stream().anyMatch(x -> tagCode.equals(x.getTagCode()) && x.getTagFlag());
            resultMap.put("tmsTagLimit",!hadTmsTag);
            return resultMap;
        } catch (Exception e) {
            LOGGER.warn("{}-checkUserTagRequest", PROJECT_NAME, e);
            return new HashMap<>();
        }
    }
}
