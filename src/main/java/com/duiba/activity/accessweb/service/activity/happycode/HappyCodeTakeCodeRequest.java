package com.duiba.activity.accessweb.service.activity.happycode;

/**
 * 开心码发码请求
 * Created by hww on 2017/12/12
 */
public class HappyCodeTakeCodeRequest {

    /** 赛事id */
    private Long basicId;
    /** 应用id */
    private Long appId;
    /** 兑吧用户id */
    private Long consumerId;
    /** 开发者用户id */
    private String partnerUserId;
    /** 请求来源 */
    private Integer origin;

    public Long getBasicId() {
        return basicId;
    }

    public void setBasicId(Long basicId) {
        this.basicId = basicId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public Integer getOrigin() {
        return origin;
    }

    public void setOrigin(Integer origin) {
        this.origin = origin;
    }
}
