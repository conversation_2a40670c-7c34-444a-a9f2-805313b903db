package com.duiba.activity.accessweb.service.hf;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityExtInfoDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.config.HsbcConfig;
import com.duiba.activity.accessweb.vo.TakePrizeVo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description
 * @Date 2023/3/3 11:27
 * <AUTHOR>
 */
@Service
public class HfSubAccountService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HfSubAccountService.class);

    private static final String PROJECT_LOG = "汇丰子账户";

    @Resource
    private HsbcConfig hsbcConfig;
    @Resource
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;



    public void fillOrderSubAccountExtInfo(TakePrizeVo takePrize, OrdersDto ordersDto){
        try {
            AppSimpleDto app = takePrize.getApp();
            if (Objects.isNull(app) || !hsbcConfig.getAppIds().contains(app.getId())) {
                return;
            }
            OperatingActivityDto activityDto = remoteOperatingActivityServiceNew.find(takePrize.getOperatingActivityId());
            if (Objects.isNull(activityDto)){
                return ;
            }
            if (StringUtils.isBlank(activityDto.getExtendJson())){
                return;
            }
            JSONObject extend = JSONObject.parseObject(activityDto.getExtendJson());
            if (Objects.isNull(extend)){
                return;
            }
            if (Objects.isNull(extend.getLong(OperatingActivityExtInfoDto.SUB_ACCOUNT_ID))){
                return;
            }

            String orderExt = StringUtils.isBlank(ordersDto.getExtraInfo()) ? "{}" : ordersDto.getExtraInfo();

            JSONObject orderExtInfo = JSONObject.parseObject(orderExt);
            orderExtInfo.put(OrdersDto.SUB_ACCOUNT_ID,extend.getLong(OperatingActivityExtInfoDto.SUB_ACCOUNT_ID));

            ordersDto.setExtraInfo(orderExtInfo.toJSONString());
        } catch (Exception e) {
            LOGGER.warn("{} 构建订单额外信息异常 takePrize:{} order:{}",PROJECT_LOG, JSONObject.toJSONString(takePrize), JSONObject.toJSONString(ordersDto), e);
        }
    }
}
