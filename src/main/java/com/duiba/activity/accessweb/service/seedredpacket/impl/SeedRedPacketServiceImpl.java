package com.duiba.activity.accessweb.service.seedredpacket.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.bet.BetConfigDto;
import cn.com.duiba.activity.center.api.dto.fakeuser.FakeUserDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.LandInfoDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketGrownTimeDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketTaskDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketUserMarkDto;
import cn.com.duiba.activity.center.api.enums.BetBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.ConfigStatusEnum;
import cn.com.duiba.activity.center.api.enums.LandStatusEnum;
import cn.com.duiba.activity.center.api.enums.RedPacketStatusEnum;
import cn.com.duiba.activity.center.api.enums.SeedRedPacketLandTaskTypeEnum;
import cn.com.duiba.activity.center.api.enums.SeedRedPacketMaturationTimeEnum;
import cn.com.duiba.activity.center.api.params.SeedRedPacketAdConfig;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.fakeuser.RemoteFakeUserService;
import cn.com.duiba.activity.center.api.remoteservice.newgamecenter.RemoteSeasonConfigService;
import cn.com.duiba.activity.center.api.remoteservice.seedredpacket.RemoteAppRelateSeedActService;
import cn.com.duiba.activity.center.api.remoteservice.seedredpacket.RemoteAppSeedRedPacketMarkService;
import cn.com.duiba.activity.center.api.remoteservice.seedredpacket.RemoteAppSeedRedPacketService;
import cn.com.duiba.activity.center.api.remoteservice.seedredpacket.RemoteBackendSeedRedPacketService;
import cn.com.duiba.activity.center.api.tool.RedisKeySpace;
import cn.com.duiba.activity.common.center.api.dto.flow.ActivityFlowRuleDto;
import cn.com.duiba.activity.common.center.api.dto.wallet.RedAccCustomDto;
import cn.com.duiba.activity.common.center.api.dto.wallet.WalletAccountDto;
import cn.com.duiba.activity.common.center.api.dto.wallet.WalletAccountRelationDto;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountActionTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountSubTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteCashDrawService;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteConsumerAccountService;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteRedAccCustomService;
import cn.com.duiba.activity.common.center.api.remoteservice.wallet.RemoteRedAccPeriodService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccPeriodRechargeRequest;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccRechargeRequest;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountAmountModifyRequest;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountModifyResponse;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.bo.subcredits.SubCreditsMsgDto;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.api.enums.subcredits.SubCreditsOuterType;
import cn.com.duiba.api.enums.subcredits.SubCreditsType;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.CreditConsumeParams;
import cn.com.duiba.dcommons.enums.ExchangeStatusEnums;
import cn.com.duiba.developer.center.api.domain.dto.AppNewExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.kvtable.service.api.dto.DuibaKvtableDto;
import cn.com.duiba.kvtable.service.api.enums.HbaseKeySpaceEnum;
import cn.com.duiba.kvtable.service.api.params.HbaseKvParam;
import cn.com.duiba.kvtable.service.api.params.HbaseVKeyParam;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteDuibaKvtableService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.ActivityCommCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.constant.RefreshConstant;
import com.duiba.activity.accessweb.enums.YesOrNoEnum;
import com.duiba.activity.accessweb.log.SeedRedPacketUnlockLandLog;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.plugin.ActivityPluginService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.seedredpacket.SeedRedPacketService;
import com.duiba.activity.accessweb.service.seedredpacket.UnlockLandService;
import com.duiba.activity.accessweb.service.wallet.WalletAccountHbaseService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.InnerLogUtil;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.seedredpacket.LandInfoVO;
import com.duiba.activity.accessweb.vo.seedredpacket.RedPacketLandInfoVO;
import com.duiba.activity.accessweb.vo.seedredpacket.SeedRedPacketIndexVO;
import com.duiba.activity.accessweb.vo.seedredpacket.SeedRedPacketResultVO;
import com.duiba.activity.accessweb.vo.seedredpacket.StealLandInfoVO;
import com.duiba.activity.accessweb.vo.seedredpacket.StealRedPacketVO;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 18/7/5
 * @description
 */
@Service
public class SeedRedPacketServiceImpl extends SeedRedPacketCacheUtil implements SeedRedPacketService {

	private static Logger logger = LoggerFactory.getLogger(SeedRedPacketServiceImpl.class);

	@Resource
	private RemoteDuibaKvtableService remoteDuibaKvtableService;
	@Resource
	private UnlockLandService unlockLandService;
	@Resource(name = "stringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private RemoteActivityOrderService remoteActivityOrderService;
	@Resource
	private RemoteConsumerAccountService remoteConsumerAccountService;
	@Resource
	private ActivityPluginService activityPluginService;
	@Resource
	private RemoteAppSeedRedPacketMarkService remoteAppSeedRedPacketMarkService;
	@Resource
	private RemoteConsumerService remoteConsumerService;
	@Resource
	private DefaultMQProducer rocketMQProducer;
	@Resource
	private RocketMqMessageTopic rocketMqMessageTopic;
	@Resource
	private WalletAccountHbaseService walletAccountHbaseService;
	@Resource
	private CommonService commonService;
 	@Resource
	private RemoteSeasonConfigService remoteSeasonConfigService;
 	@Resource(name = "redisTemplate")
	private RedisTemplate<String,Long> redisTemplate;
	@Resource
	private DeveloperCacheService developerCacheService;
	@Resource
	private ActivityCommCacheService activityCommCacheService;
	@Autowired
	private ActivityCacheService activityCacheService;
	@Resource
    private RemoteCashDrawService remoteCashDrawService;
	@Resource
	private RemoteAppRelateSeedActService remoteAppRelateSeedActService;
	@Resource
    private RemoteConsumerExtraService remoteConsumerExtraService;
	@Resource
	private RemoteFakeUserService remoteFakeUserService;
	@Autowired
	private RemoteBackendSeedRedPacketService remoteBackendSeedRedPacketService;
	@Autowired
	private RemoteAppSeedRedPacketService remoteAppSeedRedPacketService;
	@Autowired
	private RemoteRedAccPeriodService remoteRedAccPeriodService;

	@Resource(name="stringRedisTemplate")
	private RedisAtomicClient redisAtomicClient;
	@Autowired
	private RefreshConstant refreshConstant;
	@Autowired
	private RemoteRedAccCustomService remoteRedAccCustomService;
	@Autowired
	private RiskService riskService;

	private Random random = new Random();
	private static final String SUNDAY_NUM = "7"; // 周日
//	private static final Long SLOT_ID = 38566L;// 积分商城广告位id
//	private static final int STRATEY_OFF = 0;// 流量策略关闭
//	private static final int PROXY_ON = 1;// 启用代理屏蔽
	private final String ACT_NOT_SPECIFY = "活动未定向";


	// 每日种红包数量上限8个(每日默认可中奖个数)
	private final int SEED_RED_PACKET_NUMBER_EVERYDAY = 8;
	// 土地块数，总数8块
	private final int LAND_NUMBER = 8;
	// 红包递减金额
	private final int DECR_AMOUNT = 1;

	@Override public RedPacketLandInfoVO getLandList(ConsumerDto consumerDto, AppSimpleDto appSimpleDto, Long floorId)
			throws BizException {
		Long appId = appSimpleDto.getId();
		Long consumerId = consumerDto.getId();
		// 增加用户连续登陆天数
		updateLoginDays(consumerId, appId);
		// 校验钱包账户是否开启，未开启则不可见
		WalletAccountDto walletAccountDto = getWalletAccountInfoCache(appId);
		if (null == walletAccountDto || !Objects.equals(YesOrNoEnum.YES.getCode(), walletAccountDto.getState())) {
			throw new BizException("未开启钱包账户");
		}
		Long specifyActId = getAppSpecifyActivityId(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 校验活动是否存在，是否开启
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);
		//访问日志
		AccessLogExUtil.putAccessLogExPair(true, 303, 1, PageBizTypeEnum.SEED_REDPACKET_INDEX, seedRedPacketDto.getId(),
                getAppSpecifyId(appId), ActivityUniformityTypeEnum.SeedRedPacket_Old, consumerDto.isNotLoginUser(),
				consumerDto.getCredits(), null, null, null, null);
		// 判断用户是否被标记为可提现用户，如果被标记，更新登录标记
		String loginMarkKey = getRedisKeyForSeedRedPacketLogin(consumerId, appId);
		String loginMark = stringRedisTemplate.opsForValue().get(loginMarkKey);
		if (null != loginMark) {
			stringRedisTemplate.expire(loginMarkKey, getSecondFor23Days(), TimeUnit.SECONDS);
		}
		String redisKeyForDrawNum = getRedisKeyForDrawNum(consumerId, appId);// 今日可中奖次数redisKey
		initDrawNum(seedRedPacketDto.getDrawNumDaily(), redisKeyForDrawNum); // 初始化今日可中奖红包数量
		// 获取用户土地信息
		List<String> vkeyList = new ArrayList<>();
		for (int i = 1; i < LAND_NUMBER + 1; i++) {
			vkeyList.add(getHbaseKeyForUserLands(appId, relateActId, Long.valueOf(i)));
		}
		List<LandInfoDto> landInfoOldList = getBatchUserLandInfo(vkeyList, consumerId);
		List<LandInfoDto> updateList = new ArrayList<>();
		if (CollectionUtils.isEmpty(landInfoOldList) || landInfoOldList.size() < LAND_NUMBER) {
			// 初始化土地
			// 第一个赠送红包的金额，0.20元
			Long firstRedPacketAmount = 20L;
			// 创建子订单
			ActivityOrderDto activityOrderDto = createOrder(consumerDto, relateActId,
					0L, 1L, 0L, firstRedPacketAmount, floorId,
					ActivityOrderDto.ConsumeCreditsSuccess);
			// 初始化土地，其中1号土地为已解锁，且1号土地上有个可收割的红包
			landInfoOldList = initUserLandInfo(activityOrderDto.getOrderNum(), firstRedPacketAmount);
			updateList.addAll(landInfoOldList);
			// 设置今日已种红包数量为1
			Integer expireTime = DateUtil.getToTomorrowSeconds() + random.nextInt(300);
			stringRedisTemplate.opsForValue().set(getRedisKeyForTodaySeedNumber(consumerId, appId, relateActId),
					"1", expireTime, TimeUnit.SECONDS);
			// 今日已种红包可领金额增加
			stringRedisTemplate.opsForValue().set(getRedisKeyForCanGetMoney(consumerId, appId, relateActId),
					"20", expireTime, TimeUnit.SECONDS);
            stringRedisTemplate.opsForValue().increment(redisKeyForDrawNum, -1);  // 今日可中奖次数减1
		}
		// 校验土地是否解锁
		Map<Long, LandInfoDto> oldLandMap = landInfoOldList.stream().collect(Collectors.toMap(x -> x.getLandId(), x -> x));
		Map<Long, LandInfoDto> updateLandMap = CollectionUtils.isEmpty(updateList) ?
				new HashMap<>() :
				updateList.stream().collect(Collectors.toMap(x -> x.getLandId(), x -> x));
		Integer conLoginDays = unlockLandService
				.unlockLandByLogin(updateLandMap, oldLandMap, consumerId, appId);
		// 转换积分价值
		Long unlockNeedCredits = activityPluginService.getNeedCredits(seedRedPacketDto.getUnlockLandCreditsPrice().intValue(),
				appSimpleDto.getCreditsRate());
		Long remainCredits = unlockLandService.unlockLandByCredits(updateLandMap, oldLandMap, relateActId,
				consumerId, appId, unlockNeedCredits);
		unlockLandService.unlockByExchangeItem(updateLandMap, oldLandMap, consumerId, seedRedPacketDto.getGmtCreate());
		unlockLandService.unlockByWithdrawCash(updateLandMap, oldLandMap, consumerId, seedRedPacketDto.getGmtCreate(), appId, relateActId);
		// 组装返回数据
		RedPacketLandInfoVO redPacketLandInfoVO = new RedPacketLandInfoVO();
		redPacketLandInfoVO.setCreditsPrice(
				activityPluginService.getNeedCredits(seedRedPacketDto.getSeedCreditsPrice().intValue(), appSimpleDto.getCreditsRate()));
		redPacketLandInfoVO.setLoginDays(conLoginDays);
		// 展示给页面的仍需要的积分，需要按倍率换算
		redPacketLandInfoVO.setRemainCredits(remainCredits);
		//展示给页面赚取积分链接
		redPacketLandInfoVO.setEarnCreditsUrl(appSimpleDto.getEarnCreditsUrl());
		//展示给页面开发者设置的积分单位
		redPacketLandInfoVO.setUnitName(appSimpleDto.getUnitName());
		// 封装提现类型返回
		redPacketLandInfoVO.setDrawType(walletAccountDto.getDrawType());
		// 如果有能解锁的，更新土地状态
		if (!updateLandMap.isEmpty()) {
			boolean updateResult = batchUpdateLandInfo(new ArrayList<>(updateLandMap.values()), consumerId, appId, relateActId);
			if (!updateResult) {
				// 更新失败返回旧的
				logger.warn("种红包-更新土地失败,app：{}，用户id：{}，旧土地信息：{}，新土地信息：{}", appId, consumerId,
						JSON.toJSON(landInfoOldList), JSON.toJSON(oldLandMap));
				redPacketLandInfoVO.setLandList(convertLandInfoList(landInfoOldList));
				return redPacketLandInfoVO;
			}
		}
		redPacketLandInfoVO.setLandList(convertLandInfoList(new ArrayList<>(oldLandMap.values())));
		// 判断今日已种数量是否已满N个
		String seedNumberStr = stringRedisTemplate.opsForValue().get(getRedisKeyForTodaySeedNumber(consumerId, appId, relateActId));
		if (checkIfCanSeed(seedRedPacketDto.getPlantNumDaily(), seedNumberStr)) {
			redPacketLandInfoVO.setNumberIsFull(true);
		}
        return redPacketLandInfoVO;
	}

	@Override
	public RedPacketLandInfoVO getLandListNew(ConsumerDto consumer, AppSimpleDto app, Long pageId) throws BizException {
		final Long appId = app.getId();
		final Long consumerId = consumer.getId();
		// 查询种红包活动 若无正在进行的活动 将抛出biz异常
		SeedRedPacketDto activity = getAppSpecifyActivity(appId);
		// 增加统一访问日志定制参数
		doAccessLog(activity, consumer, pageId, null, 1, PageBizTypeEnum.SEED_REDPACKET_INDEX.getBizPageId());
		doInnerLog(activity);
		// 增加用户连续登陆天数
		updateLoginDays(consumerId, appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, activity.getId());
		String redisKeyForDrawNum = getRedisKeyForDrawNumNew(consumerId, activity.getId());// 今日可中奖次数redisKey
		initDrawNumNew(activity.getDrawNumDaily(), redisKeyForDrawNum); // 初始化今日可中奖红包数量
		// 获取用户土地信息
		List<String> vkeyList = new ArrayList<>();
		for (int i = 1; i < LAND_NUMBER + 1; i++) {
			vkeyList.add(getHbaseKeyForUserLands(appId, relateActId, Long.valueOf(i)));
		}
		List<LandInfoDto> landInfoOldList = getBatchUserLandInfo(vkeyList, consumerId);
		List<LandInfoDto> landInfoNew = BeanUtils.copyList(landInfoOldList, LandInfoDto.class);
		// 需要更新的土地信息 主要是初次进来初始化和土地可解锁时使用
		List<LandInfoDto> updateList;
		List<SeedRedPacketTaskDto> tasks = remoteBackendSeedRedPacketService.findTasksByActivityId(activity.getId());
		Map<Long, SeedRedPacketTaskDto> taskMapping = Maps.uniqueIndex(tasks, t -> t.getLandId());
		if (CollectionUtils.isEmpty(landInfoNew) || landInfoNew.size() < LAND_NUMBER) {
			// 初始化土地
			updateList = initLandNew(taskMapping);
			// 将初始化的土地列表 赋值给用于返回数据的土地列表
			landInfoNew = updateList;
		} else {
			//校验是否需要解锁的土地
			updateList = landInfoNew.stream()
					.filter(l -> {
						return checkTaskForLand(appId, consumerId, activity, relateActId, taskMapping, l);
					})
					.map(l -> {
						// 把本身的土地列表状态也改掉 后续要返回给前端
						l.setLandStatus(LandStatusEnum.CAN_UNLOCK.getStatus());
						return l;
					})
					.collect(Collectors.toList());

		}
		// 校验土地是否解锁
		Map<Long, LandInfoDto> updateLandMap = Maps.uniqueIndex(updateList, LandInfoDto::getLandId);

		// 组装返回数据
		RedPacketLandInfoVO redPacketLandInfoVO = new RedPacketLandInfoVO();
		redPacketLandInfoVO.setCreditsPrice(activity.getSeedCreditsPrice());
		//展示给页面赚取积分链接
		redPacketLandInfoVO.setEarnCreditsUrl(app.getEarnCreditsUrl());
		//展示给页面开发者设置的积分单位
		redPacketLandInfoVO.setUnitName(app.getUnitName());

		// 如果有能解锁的，更新土地状态
		if (CollectionUtils.isNotEmpty(updateList)) {
			boolean updateResult = batchUpdateLandInfo(updateList, consumerId, appId, relateActId);
			if (!updateResult) {
				// 更新失败返回旧的 若是新用户 会返回空集合
				logger.warn("种红包-更新土地失败,app：{}，用户id：{}，旧土地信息：{}，新土地信息：{}", appId, consumerId,
						JSON.toJSON(landInfoOldList), JSON.toJSON(landInfoNew));
				redPacketLandInfoVO.setLandList(convertLandInfoList(landInfoOldList));
				return redPacketLandInfoVO;
			}
		}
		redPacketLandInfoVO.setLandList(convertLandInfoList(landInfoNew));
		// 判断今日已种数量是否已满N个
		String seedNumberStr = stringRedisTemplate.opsForValue().get(getRedisKeyForTodaySeedNumberNew(consumerId, activity.getId()));
		if (checkIfCanSeed(activity.getPlantNumDaily(), seedNumberStr)) {
			redPacketLandInfoVO.setNumberIsFull(true);
		}
		return redPacketLandInfoVO;
	}

	private void doInnerLog(SeedRedPacketDto activity) {
		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activity.getOperatingActivityId());
		if(operatingActivityDto!=null) {
			riskService.visitRiskLog(operatingActivityDto);
		}
	}

	/**
	 * 初始化用户今日可中奖个数
	 * @param drawNumDaily
	 * @param redisKeyForDrawNum
	 */
	private void initDrawNumNew(Long drawNumDaily, String redisKeyForDrawNum) {
		Long number = redisAtomicClient.getLong(redisKeyForDrawNum);
		if (number == null) {
			redisAtomicClient.incrBy(redisKeyForDrawNum, drawNumDaily == null ? 0L : drawNumDaily, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
		}
	}

	private void doAccessLog(SeedRedPacketDto activity, ConsumerDto consumer, Long pageId, Integer status, Integer subType, String pageBizId) {
		// 2019-03-15 0点 切成新版本
		if (System.currentTimeMillis() > 1552579200000L) {
			doAccessLogNewVersion(activity, consumer, pageId, status, subType, pageBizId);
			return;
		}
		// 页面类型
		AccessLogFilter.putExPair("type", 303);
		// 页面子类型（活动类型）
		AccessLogFilter.putExPair("sub_type", 1);
		// 活动短ID 非必填
		AccessLogFilter.putExPair("id", activity.getId());
		// 活动长ID
		AccessLogFilter.putExPair("activityid", activity.getOperatingActivityId());
		// 活动类型
		AccessLogFilter.putExPair("activitytype", ActivityUniformityTypeEnum.SeedRedPacket.getCode());
		// 用户是否登陆状态
		AccessLogFilter.putExPair("loginStatus", consumer.isNotLoginUser() ? 2 : 1);
		// 登陆用户携带积分
		AccessLogFilter.putExPair("userCredits", consumer.getCredits());
		// 页面访问类型
		AccessLogFilter.putExPair("pageBizId", PageBizTypeEnum.SEED_REDPACKET_INDEX.getBizPageId());
		// 组件化页面
		AccessLogFilter.putExPair("pageID", pageId);

		// 用户操作类型 1 种植; 2 收割; 3 解锁土地;
		if (status != null ) {
			AccessLogFilter.putExPair("status", status);
		}
	}

	private void doAccessLogNewVersion(SeedRedPacketDto activity, ConsumerDto consumer, Long pageId, Integer status, Integer subType, String pageBizId) {
		// 页面类型
		AccessLogFilter.putExPair("type", 303);
		// 页面子类型（活动类型）
		AccessLogFilter.putExPair("sub_type", subType);
		// 活动短ID 非必填
		AccessLogFilter.putExPair("id", activity.getId());
		// 活动长ID
		AccessLogFilter.putExPair("activityid", activity.getOperatingActivityId());
		// 活动类型
		AccessLogFilter.putExPair("activitytype", ActivityUniformityTypeEnum.SeedRedPacket.getCode());
		// 用户是否登陆状态
		AccessLogFilter.putExPair("loginStatus", consumer.isNotLoginUser() ? 2 : 1);
		// 登陆用户携带积分
		AccessLogFilter.putExPair("userCredits", consumer.getCredits());
		// 页面访问类型
		AccessLogFilter.putExPair("pageBizId", pageBizId);
		// 组件化页面
		AccessLogFilter.putExPair("pageID", pageId);

		// 用户操作类型 1 种植; 2 收割; 3 解锁土地;
		if (status != null ) {
			AccessLogFilter.putExPair("status", status);
		}
	}

	/**
	 * 校验土地是否需要修改为可解锁状态
	 * @param appId
	 * @param consumerId
	 * @param activity
	 * @param relateActId
	 * @param taskMapping
	 * @param landInfo
	 * @return
	 */
	private boolean checkTaskForLand(Long appId, Long consumerId, SeedRedPacketDto activity, Long relateActId, Map<Long, SeedRedPacketTaskDto> taskMapping, LandInfoDto landInfo) {
		if (!Objects.equals(LandStatusEnum.LOCK.getStatus(), landInfo.getLandStatus())) {
            return false;
        }
		SeedRedPacketTaskDto task = taskMapping.get(landInfo.getLandId());
		if (task == null) {
			return false;
		}
		long latestValue;
		if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.NONE.getCode())) {
            // 不需要条件解锁
            landInfo.setLandStatus(LandStatusEnum.CAN_UNLOCK.getStatus());
            return true;
        } else if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.LOGIN.getCode())) {
            // 判断连续登录解锁条件是否达成
            latestValue = unlockLandService.getLoginCount(consumerId, appId);
        } else if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.TOTAL_CREDITS.getCode())) {
            // 判断积分消耗解锁条件是否达成
            latestValue = unlockLandService.getCreditsCount(relateActId, consumerId, appId);
        } else if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.TOTAL_EXCHANGE.getCode())) {
            // 判断兑换解锁条件是否达成
            latestValue = unlockLandService.getExchangeItemCount(consumerId, activity.getStartTime());
        } else if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.TOTAL_PLANTING.getCode())) {
            latestValue = unlockLandService.getPlantCount(consumerId);
        } else {
            return false;
        }
		if (task.getTaskTarget() != null && latestValue >= task.getTaskTarget()) {
            landInfo.setLandStatus(LandStatusEnum.CAN_UNLOCK.getStatus());
            return true;
        } else {
            landInfo.setLatestValue(latestValue);
            landInfo.setTaskType(task.getTaskType());
            landInfo.setTargetValue(task.getTaskTarget());
            return false;
        }
	}

	/**
	 * 初始化今日可得总金额
	 */
	private void initTotalBudget(boolean marked, SeedRedPacketDto activity, Long consumerId, Long appId) {
		String key = getRedisKeyForTodayTotalMoney(consumerId, appId, activity.getId());
		String todayTotalMoney = stringRedisTemplate.opsForValue().get(key);
		if (todayTotalMoney == null) {
			stringRedisTemplate.opsForValue().set(key, getTodayBudget(marked, activity, consumerId) + "", 1, TimeUnit.DAYS);
		}
	}

	/**
	 * 根据土地任务初始化 新土地
	 * @param taskMapping
	 * @return
	 */
	private List<LandInfoDto> initLandNew(Map<Long, SeedRedPacketTaskDto> taskMapping) {
		List<LandInfoDto> landInfoDtoList = new ArrayList<>();
		for (long i = 1; i < 9; i++) {
			//初始化8块土地
			SeedRedPacketTaskDto task = taskMapping.get(i);
			LandInfoDto landInfoDto = new LandInfoDto();
			landInfoDto.setLandId(i);
			// 为初始化土地设置状态
			landInfoDto.setLandStatus(parseLandInitStatus(task));
			landInfoDto.setTargetValue(task.getTaskTarget());
			landInfoDto.setTaskType(task.getTaskType());
			if (Objects.equals(SeedRedPacketLandTaskTypeEnum.LOGIN.getCode(), task.getTaskType())) {
				landInfoDto.setLatestValue(1L);
			}
			if (Objects.equals(SeedRedPacketLandTaskTypeEnum.TOTAL_CREDITS.getCode(), task.getTaskType())) {
				landInfoDto.setLatestValue(0L);
			}
			if (Objects.equals(SeedRedPacketLandTaskTypeEnum.TOTAL_EXCHANGE.getCode(), task.getTaskType())) {
				landInfoDto.setLatestValue(0L);
			}
			if (Objects.equals(SeedRedPacketLandTaskTypeEnum.TOTAL_PLANTING.getCode(), task.getTaskType())) {
				landInfoDto.setLatestValue(0L);
			}
			landInfoDtoList.add(landInfoDto);
		}
		return landInfoDtoList;
	}

	/**
	 * 获取土地初始化状态
	 * 未设置解锁任务  则直接解锁土地
	 * 设置连续登陆类型解锁任务 并且目标值是1天 则将土地状态设置为可解锁
	 * 否则初始化状态为锁定
	 * @param task
	 * @return
	 */
	private Integer parseLandInitStatus(SeedRedPacketTaskDto task) {
		if (task == null) {
			return LandStatusEnum.LOCK.getStatus();
		}
		if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.NONE.getCode())) {
			return LandStatusEnum.BLANK.getStatus();
		}
		if (Objects.equals(task.getTaskType(), SeedRedPacketLandTaskTypeEnum.LOGIN.getCode()) && Objects.equals(task.getTaskTarget(), 1)) {
			return LandStatusEnum.CAN_UNLOCK.getStatus();
		}
		return LandStatusEnum.LOCK.getStatus();
	}



	/**
	 * 组件化 获取当前周期的种红包活动
	 * 需要校验活动开启状态和周期可用情况
	 * @param appId
	 * @return
	 */
	private SeedRedPacketDto getAppSpecifyActivity(Long appId) throws BizException {
		SeedRedPacketDto current = remoteAppSeedRedPacketService.getCurrentActivity(appId);
		if (current == null) {
			throw new BizException("暂无进行中活动");
		}
		return current;
	}

	// 校验种植次数是否已满
	private boolean checkIfCanSeed(Long plantNumDaily, String seedNumberStr){
		return plantNumDaily != null && StringUtils.isNotBlank(seedNumberStr) && Integer.valueOf(seedNumberStr).compareTo(plantNumDaily.intValue()) >= 0;
	}

	// 初始化今日可中奖红包数量
    private void initDrawNum(Long drawNumDaily, String redisKeyForDrawNum) {
		Long drawNumConfig = drawNumDaily != null ? drawNumDaily : SEED_RED_PACKET_NUMBER_EVERYDAY; // 兼容可中奖个数未配置的情况  默认可中奖个数为8
        String drawNum = stringRedisTemplate.opsForValue().get(redisKeyForDrawNum);
        if(StringUtils.isBlank(drawNum)){ // 初始化当日可中奖个数
            stringRedisTemplate.opsForValue().set(redisKeyForDrawNum, String.valueOf(drawNumConfig) ,DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
        }
    }

    @Override public void unlockLand(Long landId, Long consumerId, Long appId, Long floorId, String dpm, String dcm)
			throws BizException {
		Long specifyActId = getAppSpecifyActivityId(appId);;
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 记录统一访问日志
		accessLog(specifyActId, "", null, null, "");
		// 校验活动是否存在，是否开启
        SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);

		//日志
		ConsumerDto consumer = RequestLocal.getConsumerDO();
		AccessLogExUtil.putAccessLogExPair(true, 303, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN, seedRedPacketDto.getId(),
                getAppSpecifyId(appId), ActivityUniformityTypeEnum.SeedRedPacket_Old, consumer.isNotLoginUser(),
				consumer.getCredits(), null, null, null, null);
		InnerLogUtil.joinInnerLog (ActivityUniformityTypeEnum.SeedRedPacket_Old.getCode(), seedRedPacketDto.getId(), getAppSpecifyId(appId));

		// 获取土地信息并校验
		String vkey = getHbaseKeyForUserLands(appId, relateActId, landId);
		LandInfoDto landInfoDto = getUserLandInfoByUserId(vkey, consumerId);
		if (!Objects.equals(LandStatusEnum.CAN_UNLOCK.getStatus(), landInfoDto.getLandStatus())) {
			logger.info("种红包-app'{}'用户'{}'解锁地块'{}'异常，地块状态不是可解锁状态，土地信息：{}", appId, consumerId, landId,
					JSON.toJSON(landInfoDto));
			throw new BizException("用户地块不是可解锁状态");
		}
		// 如果是可解锁状态，进行解锁操作
		landInfoDto.setLandStatus(LandStatusEnum.BLANK.getStatus());
		boolean updateResult = updateLandInfo(vkey, consumerId, landInfoDto);
		if (!updateResult) {
			logger.warn("种红包-app'{}'用户'{}'解锁土地失败，土地信息：{}", appId, consumerId, JSON.toJSON(landInfoDto));
			throw new BizException("解锁土地失败");
		}
		// 记录解锁土地内部日志
		SeedRedPacketUnlockLandLog.log(consumerId, appId, specifyActId, landId, floorId, 48, dpm, dcm);
	}

	@Override
	public void unlockLandNew(Long landId, ConsumerDto consumer, Long appId, Long pageId, String dpm, String dcm) throws BizException {
		final Long consumerId = consumer.getId();
		SeedRedPacketDto activity = getAppSpecifyActivity(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, activity.getId());
		// 记录统一访问日志
		doAccessLog(activity, consumer, pageId, 3, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN.getBizPageId());
		// 获取土地信息并校验
		String vkey = getHbaseKeyForUserLands(appId, relateActId, landId);
		LandInfoDto landInfoDto = getUserLandInfoByUserId(vkey, consumerId);
		if (!Objects.equals(LandStatusEnum.CAN_UNLOCK.getStatus(), landInfoDto.getLandStatus())) {
			logger.info("种红包-app'{}'用户'{}'解锁地块'{}'异常，地块状态不是可解锁状态，土地信息：{}", appId, consumerId, landId,
					JSON.toJSON(landInfoDto));
			throw new BizException("用户地块不是可解锁状态");
		}
		// 如果是可解锁状态，进行解锁操作
		landInfoDto.setLandStatus(LandStatusEnum.BLANK.getStatus());
		boolean updateResult = updateLandInfo(vkey, consumerId, landInfoDto);
		if (!updateResult) {
			logger.warn("种红包-app'{}'用户'{}'解锁土地失败，土地信息：{}", appId, consumerId, JSON.toJSON(landInfoDto));
			throw new BizException("解锁土地失败");
		}
		// 记录解锁土地内部日志
		SeedRedPacketUnlockLandLog.log(consumerId, appId, activity.getId(), landId, pageId, 48, dpm, dcm);
	}

	@Override public SeedRedPacketResultVO seed(Long landId, Long floorId, ConsumerDto consumerDto, AppSimpleDto appSimpleDto,
			String dpm, String dcm) throws BizException {
		SeedRedPacketResultVO seedRedPacketResultVO = new SeedRedPacketResultVO();
		Long consumerId = consumerDto.getId();
		Long appId = appSimpleDto.getId();
		Long specifyActId = getAppSpecifyActivityId(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 校验活动是否存在，是否开启
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);

		//打印日志
		AccessLogExUtil.putAccessLogExPair(true, 303, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN, seedRedPacketDto.getId(),
                getAppSpecifyId(appId), ActivityUniformityTypeEnum.SeedRedPacket_Old, consumerDto.isNotLoginUser(),
				consumerDto.getCredits(), null, null, null, null);
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(null,seedRedPacketDto.getId(), ActivityUniformityTypeEnum.SeedRedPacket_Old.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}

		// 转换积分价值
		Long seedNeedCredits = activityPluginService.getNeedCredits(seedRedPacketDto.getSeedCreditsPrice().intValue(), appSimpleDto.getCreditsRate());
		// 判断用户积分是否足够
		if (null == consumerDto.getCredits() || consumerDto.getCredits().compareTo(seedNeedCredits) < 0) {
			seedRedPacketResultVO.setLackCredits(true);
			return seedRedPacketResultVO;
		}
		// 判断今日已种数量是否已满N个
		String seedNumberRedisKey = getRedisKeyForTodaySeedNumber(consumerId, appId, relateActId);
		String seedNumberStr = stringRedisTemplate.opsForValue().get(seedNumberRedisKey);
		if (checkIfCanSeed(seedRedPacketDto.getPlantNumDaily(), seedNumberStr)) {
			logger.info("种红包-app'{}'用户'{}'种红包失败，今日次数已满{}次，今日次数：{}", appId, consumerId, seedRedPacketDto.getPlantNumDaily(), seedNumberStr);
			seedRedPacketResultVO.setNumberIsFull(true);
			return seedRedPacketResultVO;
		}
        Integer seedNumber = StringUtils.isBlank(seedNumberStr) ? 0 : Integer.valueOf(seedNumberStr);
		Integer redPacketNumber = seedNumber + 1;  // 第几个红包
		Long grownNeedTime = seedRedPacketDto.getGrownTime(redPacketNumber >= 8 ? 8 : redPacketNumber);  // 红包成长所需时间
		if (null == grownNeedTime) {
			logger.info("种红包活动配置异常，第{}个红包的成长时间为空，异常信息：{}", redPacketNumber, JSON.toJSON(seedRedPacketDto));
			throw new BizException("活动配置异常");
		}
		// 判断土地状态，是否是已解锁的空地
		String vkey = getHbaseKeyForUserLands(appId, relateActId, landId);
		LandInfoDto landInfoDto = getUserLandInfoByUserId(vkey, consumerId);
		if (!Objects.equals(LandStatusEnum.BLANK.getStatus(), landInfoDto.getLandStatus())) {
			logger.info("种红包-app'{}'用户'{}'种红包失败，地块'{}'状态不是已解锁的空地，土地信息：{}", appId, consumerId, landId,
					JSON.toJSON(landInfoDto));
			throw new BizException("用户地块不是已解锁的空地");
		}
		// 获取钱包账户配置
		WalletAccountDto walletAccountDto = getWalletAccountInfoCache(appId);
		if (null == walletAccountDto) {
			throw new BizException("app钱包账户为空");
		}
		// 先增加种红包次数，防止多种；如果最后种失败了，再减次数
		Integer expireTime = DateUtil.getToTomorrowSeconds();
		stringRedisTemplate.opsForValue().set(seedNumberRedisKey, redPacketNumber.toString(), expireTime, TimeUnit.SECONDS);
		// 可提现标记逻辑：如果未被标记，且被标记人数未满，标记该用户
		String loginMarkKey = getRedisKeyForSeedRedPacketLogin(consumerId, appId);
		String loginMark = stringRedisTemplate.opsForValue().get(loginMarkKey);
		Boolean marked = null == loginMark ? markUser(consumerId, appId, relateActId, loginMarkKey, walletAccountDto) : true;
		// 计算红包可得金额
		long redPacketAmount = getCanGetMoney(marked, consumerDto, appId, relateActId, walletAccountDto, seedNumber,seedRedPacketDto);
        if(redPacketAmount > 0){ // 红包金额>0
            String redisKeyForDrawNum = getRedisKeyForDrawNum(consumerId, appId);// 今日可中奖次数redisKey
            stringRedisTemplate.opsForValue().increment(redisKeyForDrawNum, -1);  // 今日可中奖次数减1
        }
		// 生成子订单，异步扣积分操作
		ActivityOrderDto activityOrderDto = createOrder(consumerDto, relateActId, seedNeedCredits,
				landId, grownNeedTime, redPacketAmount, floorId, ActivityOrderDto.ConsumeCreditsProcessing);
		// 扣积分发消息
		subCreditsAndSendMessage(consumerDto, appSimpleDto, activityOrderDto.getOrderNum(), seedRedPacketDto.getTitle(), relateActId,
				seedNeedCredits, landId, redPacketAmount, grownNeedTime);
		// 增加种红包活动所扣的积分
		seedCreditsRecord(consumerId, appId, relateActId, seedNeedCredits);
		// 存储当前红包信息到redis中(k:AAW_K151_appId_redPacketId,v:consumerId)
		String redisKeyForConsumerId = getRedisKeyForConsumerId(activityOrderDto.getOrderNum(), appId);
		stringRedisTemplate.opsForValue().set(redisKeyForConsumerId, String.valueOf(consumerId), grownNeedTime, TimeUnit.SECONDS);
		String redisKeyForRedPacketList = getRedisKeyForRedPacketIdList(appId);
		// 将红包id存到redisList结构中.(k:AAW_K152_appId)
		stringRedisTemplate.opsForZSet().add(redisKeyForRedPacketList, activityOrderDto.getOrderNum(),0);
		stringRedisTemplate.expire(redisKeyForRedPacketList, DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
		// 记录统一访问日志
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(calendar.getTimeInMillis() + grownNeedTime * 1000);
		Date grownDate = calendar.getTime();
		accessLog(specifyActId, activityOrderDto.getOrderNum(), seedNeedCredits, redPacketAmount, DateUtil.getSecondStr(grownDate));

		seedRedPacketResultVO.setOrderNum(activityOrderDto.getOrderNum());
		seedRedPacketResultVO.setGrownNeedTime(grownNeedTime);
		seedRedPacketResultVO.setGrownRemainTime(grownNeedTime);
		return seedRedPacketResultVO;
	}

	@Override
	public SeedRedPacketResultVO seedNew(Long landId, Long floorId, ConsumerDto consumer, AppSimpleDto app, String dpm, String dcm) throws BizException {
		SeedRedPacketResultVO seedRedPacketResultVO = new SeedRedPacketResultVO();
		Long consumerId = consumer.getId();
		Long appId = app.getId();

		SeedRedPacketDto seedRedPacketDto = getAppSpecifyActivity(appId);
		Long specifyActId = seedRedPacketDto.getId();
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 积分数
		Long seedNeedCredits = seedRedPacketDto.getSeedCreditsPrice();
		// 判断用户积分是否足够
		if (null == consumer.getCredits() || consumer.getCredits() < seedNeedCredits) {
			seedRedPacketResultVO.setLackCredits(true);
			return seedRedPacketResultVO;
		}
		// 判断今日已种数量是否已满N个
		String seedNumberRedisKey = getRedisKeyForTodaySeedNumberNew(consumerId, specifyActId);
		String seedNumberStr = stringRedisTemplate.opsForValue().get(seedNumberRedisKey);

		if (checkIfCanSeed(seedRedPacketDto.getPlantNumDaily(), seedNumberStr)) {
			logger.info("种红包-app'{}'用户'{}'种红包失败，今日次数已满{}次，今日次数：{}", appId, consumerId, seedRedPacketDto.getPlantNumDaily(), seedNumberStr);
			seedRedPacketResultVO.setNumberIsFull(true);
			return seedRedPacketResultVO;
		}
		Integer seedNumber = StringUtils.isBlank(seedNumberStr) ? 0 : Integer.valueOf(seedNumberStr);
		Integer redPacketNumber = seedNumber + 1;  // 第几个红包

		long grownNeedTime = getGrownNeedTime(seedRedPacketDto, redPacketNumber);
		// 判断土地状态，是否是已解锁的空地
		String vkey = getHbaseKeyForUserLands(appId, relateActId, landId);
		LandInfoDto landInfoDto = getUserLandInfoByUserId(vkey, consumerId);
		if (!Objects.equals(LandStatusEnum.BLANK.getStatus(), landInfoDto.getLandStatus())) {
			logger.info("种红包-app'{}'用户'{}'种红包失败，地块'{}'状态不是已解锁的空地，土地信息：{}", appId, consumerId, landId,
					JSON.toJSON(landInfoDto));
			throw new BizException("用户地块不是已解锁的空地");
		}
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(seedRedPacketDto.getOperatingActivityId(),seedRedPacketDto.getId(),ActivityUniformityTypeEnum.SeedRedPacket.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}
		// 先增加种红包次数，防止多种；如果最后种失败了，再减次数
		Integer expireTime = DateUtil.getToTomorrowSeconds();
		stringRedisTemplate.opsForValue().set(seedNumberRedisKey, redPacketNumber.toString(), expireTime, TimeUnit.SECONDS);
		// 生成子订单，异步扣积分操作
		ActivityOrderDto activityOrderDto = createOrder(consumer, relateActId, seedNeedCredits,
				landId, grownNeedTime, 0L, floorId, ActivityOrderDto.ConsumeCreditsProcessing);

		if (seedNeedCredits > 0) {
			// 扣积分发消息
			subCreditsAndSendMessage(consumer, app, activityOrderDto.getOrderNum(), seedRedPacketDto.getTitle(), relateActId,
					seedNeedCredits, landId, 0L, grownNeedTime);
			// 增加种红包活动所扣的积分
			seedCreditsRecord(consumerId, appId, relateActId, seedNeedCredits);
		} else {
			// 红包不需要积分 则不再发起扣积分请求 直接处理土地信息和订单信息
			landInfoDto.setLandStatus(LandStatusEnum.HAS_RED_PACKET.getStatus());
			landInfoDto.setRedPacketId(activityOrderDto.getOrderNum());
			landInfoDto.setSeedTime(Calendar.getInstance().getTimeInMillis());
			landInfoDto.setGrownNeedTime(grownNeedTime);
			// 更新订单状态
			doUnwantedCredits(activityOrderDto.getOrderNum(), consumerId, appId, specifyActId, updateLandInfo(vkey, consumerId, landInfoDto));
		}

		// 增加种红包活动总种植次数
		seedPlantRecord(consumerId);

		// 存储当前红包信息到redis中(k:AAW_K151_appId_redPacketId,v:consumerId)
		String redisKeyForConsumerId = getRedisKeyForConsumerId(activityOrderDto.getOrderNum(), appId);
		stringRedisTemplate.opsForValue().set(redisKeyForConsumerId, String.valueOf(consumerId), grownNeedTime, TimeUnit.SECONDS);
		String redisKeyForRedPacketList = getRedisKeyForRedPacketIdList(appId);
		// 将红包id存到redisList结构中.(k:AAW_K152_appId)
		stringRedisTemplate.opsForZSet().add(redisKeyForRedPacketList, activityOrderDto.getOrderNum(),0);
		stringRedisTemplate.expire(redisKeyForRedPacketList, DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
		// 记录统一访问日志
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(calendar.getTimeInMillis() + grownNeedTime * 1000);
		Date grownDate = calendar.getTime();

		doAccessLog(seedRedPacketDto, consumer, floorId, 1, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN.getBizPageId());
		AccessLogFilter.putExPair("timeSection", DateUtil.getSecondStr(grownDate));
		AccessLogFilter.putExPair("consumeCredits", seedNeedCredits);

		seedRedPacketResultVO.setOrderNum(activityOrderDto.getOrderNum());
		seedRedPacketResultVO.setGrownNeedTime(grownNeedTime);
		seedRedPacketResultVO.setGrownRemainTime(grownNeedTime);
		// 更新标记用户
		boolean marked = isMarkedUser(consumerId, appId, seedRedPacketDto);
		initTotalBudget(marked, seedRedPacketDto, consumerId, appId); // 初始化今日可得金额数

		return seedRedPacketResultVO;
	}

	/** 获取当前红包成熟时间 */
	private long getGrownNeedTime(SeedRedPacketDto seedRedPacketDto, Integer redPacketNumber) {
		// 非虚拟货币模式
		if (!Objects.equals(seedRedPacketDto.getRewardType(), 2)) {
			return SeedRedPacketMaturationTimeEnum.getDurationBySerialNumber(redPacketNumber >= 8 ? 8 : redPacketNumber);
		}
		// 获取配置的成熟时间配置
		List<SeedRedPacketGrownTimeDto> times = seedRedPacketDto.getGrownTimeList();
		if (CollectionUtils.isEmpty(times)) {
			return SeedRedPacketMaturationTimeEnum.getDurationBySerialNumber(redPacketNumber >= 8 ? 8 : redPacketNumber);
		}
		// 按照红包序列号排序
		times.sort((t1, t2) -> { return t1.getRedPacketId() - t2.getRedPacketId();});
		int size = times.size();
		Long grownNeedTime;
		// 若当前红包序列已经超过了配置的个数，则取最后一个配置  否则取对应的配置
		if (redPacketNumber >= size) {
			grownNeedTime = times.get(size - 1).getGrownTime();
		} else {
			grownNeedTime = times.get(redPacketNumber - 1).getGrownTime();
		}
		// 最后校验一下配置的时间是否合理 若不合理 返回默认时间
		if (grownNeedTime == null || grownNeedTime <= 0) {
			return SeedRedPacketMaturationTimeEnum.getDurationBySerialNumber(redPacketNumber >= 8 ? 8 : redPacketNumber);
		}
		return grownNeedTime;
	}

	private void doUnwantedCredits(String orderNum, Long consumerId, Long appId, Long activityId, boolean success) {
		// 更新土块信息
		if (success) {
			// 更新子订单状态为成功
			remoteActivityOrderService.consumeCreditsSuccess(orderNum,
					null,
					null,
					null,
					null,
					null,
					null,
					null,
					null,
					null);
		} else {
			// 如果失败，更新子订单状态为失败
			remoteActivityOrderService.consumeCreditsFail(
					orderNum,
					null,
					null,
					null,
					null,
					null,
					null,
					null,
					null,
					null,
					"土地更新失败",
					null,
					null);
			// 当天的每日已种红包数量-1
			subTodaySeedNumber(consumerId, appId, activityId);
		}
	}

	// 当天的每日已种红包数量-1
	private void subTodaySeedNumber(Long consumerId, Long appId, Long activityId) {
		Integer expireTime = DateUtil.getToTomorrowSeconds() + random.nextInt(300);
		String seedNumberRedisKey = getRedisKeyForTodaySeedNumberNew(consumerId, activityId);
		String seedNumber = stringRedisTemplate.opsForValue().get(seedNumberRedisKey);
		Integer currentNumber = StringUtils.isBlank(seedNumber) ? 0 : Integer.valueOf(seedNumber) - 1;
		currentNumber = currentNumber.compareTo(0) < 0 ? 0 : currentNumber;
		stringRedisTemplate.opsForValue().set(seedNumberRedisKey, currentNumber.toString(), expireTime, TimeUnit.SECONDS);
	}

	@Override public Integer getOrderStatus(String orderId, Long consumerId) throws BizException {
		if(StringUtils.isBlank(orderId)){
			logger.info("种红包-用户'{}'查询红包'{}'状态异常，红包id为空", consumerId, orderId);
			return ActivityOrderDto.ConsumeCreditsFail;
		}
		String orderIdTrimed = StringUtils.trimToNull(orderId);
		//获取订单信息
		DubboResult<ActivityOrderDto> dubboResult = remoteActivityOrderService.findByOrderNum(orderIdTrimed);
		ActivityOrderDto activityOrderDto = dubboResult.getResult();
		if (!dubboResult.isSuccess() || null == activityOrderDto || !Objects.equals(consumerId, activityOrderDto.getConsumerId())) {
			logger.info("种红包-用户'{}'查询红包'{}'状态异常，红包信息：{}", consumerId, orderId, JSON.toJSON(dubboResult));
			throw new BizException("无权查看");
		}
		return activityOrderDto.getConsumeCreditsStatus();
	}

	@Override public JSONObject reap(String redPacketId, ConsumerDto consumerDto, Long appId, String dpm, String dcm)
			throws BizException {//NOSONAR
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("isDecr",false);
		jsonObject.put("isFirstReap", false);
		jsonObject.put("steal", false);
		Long consumerId = consumerDto.getId();
		Long specifyActId = getAppSpecifyActivityId(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 校验活动是否存在，是否开启
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);

		//日志
		AccessLogExUtil.putAccessLogExPair(true, 303, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN, seedRedPacketDto.getId(),
                getAppSpecifyId(appId), ActivityUniformityTypeEnum.SeedRedPacket_Old, consumerDto.isNotLoginUser(),
				consumerDto.getCredits(), null, null, null, null);

		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(null,seedRedPacketDto.getId(), ActivityUniformityTypeEnum.SeedRedPacket_Old.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}

		setIsFirstReap(appId, jsonObject, consumerId, relateActId);
		jsonObject = judgeIsSteal(redPacketId, appId, jsonObject);
        // 获取红包信息
		DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(redPacketId);
		if (null == orderResult || !orderResult.isSuccess()) {
			throw new BizException("红包不存在");
		}
		if (orderResult.getResult() == null) {
			// 未查询到红包信息
			// 检测土地上的红包是否已经失效，如果已经失效 则清空土地
			// 此次收割金额按照0元返回
			doClearLandInfo(consumerId, appId, relateActId, redPacketId);
			jsonObject.put("redPacketAmount", BigDecimal.ZERO);
			return jsonObject;
		}
		ActivityOrderDto activityOrderDto = orderResult.getResult();
		// 记录统一访问日志
		if (!Objects.equals(ExchangeStatusEnums.ExchangeStatusWait.getStatus(), activityOrderDto.getExchangeStatus())) {
			logger.info("种红包-领取红包'{}'异常，红包状态不不是待领取，红包信息：{}", redPacketId, JSON.toJSON(activityOrderDto));
			throw new BizException("红包不是待领取状态");
		}
		Long grownNeedTime = Long.valueOf(activityOrderDto.getFacePrice()) * 1000;
		Long createTime = activityOrderDto.getGmtCreate().getTime();
		Long grownTime = createTime + grownNeedTime;
		// 记录统一访问日志
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(grownTime);
		Date grownDate = calendar.getTime();
		accessLog(specifyActId, redPacketId, null, activityOrderDto.getAddCredits(), DateUtil.getSecondStr(grownDate));

		Long now = Calendar.getInstance().getTimeInMillis();
		if (now.compareTo(grownTime) < 0) {
			logger.info("种红包-领取红包'{}'失败，红包尚未成熟，红包信息：{},当前时间now:{},成熟所需时间:{}", redPacketId, JSON.toJSON(activityOrderDto),now,grownTime);
			throw new BizException("红包尚未成熟");
		}
		// 可以收割，更新土地状态
		String vkey = getHbaseKeyForUserLands(appId, relateActId, activityOrderDto.getCouponId());
		LandInfoDto oldLand = getUserLandInfoByUserId(vkey, consumerId);
		LandInfoDto newLand = new LandInfoDto();
		newLand.setLandId(oldLand.getLandId());
		newLand.setLandStatus(LandStatusEnum.BLANK.getStatus());
		boolean updateResult = updateLandInfo(vkey, consumerId, newLand);
		if (!updateResult) {
			logger.warn("种红包-app'{}'用户'{}'收割红包更新土地信息失败，土地信息：{}", appId, consumerId, JSON.toJSON(newLand));
			throw new BizException("更新土地失败");
		}
		// 红包状态更新为已收割
		DubboResult<Boolean> exchangeStatusResult = remoteActivityOrderService.exchangeStatusToSuccess(redPacketId);
		if (null == exchangeStatusResult || !exchangeStatusResult.isSuccess()
				|| null == exchangeStatusResult.getResult() || !exchangeStatusResult.getResult()) {
			logger.warn("种红包-领取红包'{}'异常，更新红包状态失败:{}", redPacketId, JSON.toJSON(exchangeStatusResult));
			throw new BizException("更新红包状态失败");
		}
		// 将红包金额存入用户钱包账户
		Long amount = activityOrderDto.getAddCredits();
		String facePrice = activityOrderDto.getFacePrice();//红包成熟时间
		Long diffTime = now - grownTime;
		// 红包衰减开关开状态时
		if(seedRedPacketDto.getDecrSwitch() &&  diffTime.compareTo(seedRedPacketDto.getDecrBeginTime() * 60 * 1000) > 0
				&& !Objects.equals("0",facePrice) && !Objects.equals(0L, amount)){ // 用户首次可得红包成熟时间为0，或者本身红包金额就为0，不进行衰减逻辑
			jsonObject.put("isDecr",true);
			Long decrBeginTime = seedRedPacketDto.getDecrBeginTime() * 60 * 1000;
			Long decrAmount = amount - diffTime / decrBeginTime * DECR_AMOUNT;
			amount = decrAmount <= 1 ? 1 : decrAmount;
		}

        if(Objects.equals(0L, amount) || !reapRedPacketToAccount(appId, consumerDto, redPacketId, amount, oldLand, vkey, null, false)){
			jsonObject.put("redPacketAmount", BigDecimal.ZERO);
			return jsonObject;
		}
		// 展示给页面的转换成元
		BigDecimal redPacketAmount = BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_DOWN);
		jsonObject.put("redPacketAmount", redPacketAmount);
		return jsonObject;
	}

	private void setIsFirstReap(Long appId, JSONObject jsonObject, Long consumerId, Long relateActId) {
		String redisKeyForTodayIsFirstSeap = getRedisKeyForTodayIsFirstSeap(consumerId, appId, relateActId);
		String value = stringRedisTemplate.opsForValue().get(redisKeyForTodayIsFirstSeap);
		if (StringUtils.isBlank(value)) {
			jsonObject.put("isFirstReap", true);
			stringRedisTemplate.opsForValue().set(redisKeyForTodayIsFirstSeap, "1", DateUtil.getToTomorrowSeconds() + random.nextInt(300), TimeUnit.SECONDS);
		}
	}

	@Override
	public JSONObject reapNew(String redPacketId, ConsumerDto consumer, Long appId, String dpm, String dcm, Long pageId) throws BizException {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("isDecr",false);
		jsonObject.put("isFirstReap", false);
		jsonObject.put("steal", false);
		Long consumerId = consumer.getId();
		SeedRedPacketDto activity = getAppSpecifyActivity(appId);

		Long specifyActId = activity.getId();
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		// 校验活动是否存在，是否开启
		setIsFirstReap(appId, jsonObject, consumerId, relateActId);

		// 获取红包信息
		DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(redPacketId);
		if (null == orderResult || !orderResult.isSuccess()) {
			throw new BizException("红包不存在");
		}
		if (orderResult.getResult() == null) {
			// 未查询到红包信息
			// 检测土地上的红包是否已经失效，如果已经失效 则清空土地
			// 此次收割金额按照0元返回
			doClearLandInfo(consumerId, appId, relateActId, redPacketId);
			jsonObject.put("redPacketAmount", BigDecimal.ZERO);
			return jsonObject;
		}
		ActivityOrderDto activityOrderDto = orderResult.getResult();
		// 记录统一访问日志
		if (!Objects.equals(activityOrderDto.getConsumerId(), consumerId)) {
			logger.info("种红包-领取红包-当前用户与红包所属用户不一致，红包不是当前用户的，cid={}, redId={}", consumerId, redPacketId);
			throw new BizException("无法收割别人的红包");
		}
		if (!Objects.equals(ExchangeStatusEnums.ExchangeStatusWait.getStatus(), activityOrderDto.getExchangeStatus())) {
			logger.info("种红包-领取红包'{}'异常，红包状态不不是待领取，红包信息：{}", redPacketId, JSON.toJSON(activityOrderDto));
			throw new BizException("红包不是待领取状态");
		}
		Long grownNeedTime = Long.valueOf(activityOrderDto.getFacePrice()) * 1000;
		Long createTime = activityOrderDto.getGmtCreate().getTime();
		Long grownTime = createTime + grownNeedTime;
		// 记录统一访问日志
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(grownTime);
		Date grownDate = calendar.getTime();
		accessLog(specifyActId, redPacketId, null, activityOrderDto.getAddCredits(), DateUtil.getSecondStr(grownDate));

		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(activity.getOperatingActivityId(),activity.getId(), ActivityUniformityTypeEnum.SeedRedPacket.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}

		Long now = Calendar.getInstance().getTimeInMillis();
		if (now.compareTo(grownTime) < 0) {
			logger.info("种红包-领取红包'{}'失败，红包尚未成熟，红包信息：{},当前时间now:{},成熟所需时间:{}", redPacketId, JSON.toJSON(activityOrderDto),now,grownTime);
			throw new BizException("红包尚未成熟");
		}
		// 可以收割，更新土地状态
		String vkey = getHbaseKeyForUserLands(appId, relateActId, activityOrderDto.getCouponId());
		LandInfoDto oldLand = getUserLandInfoByUserId(vkey, consumerId);
		LandInfoDto newLand = new LandInfoDto();
		newLand.setLandId(oldLand.getLandId());
		newLand.setLandStatus(LandStatusEnum.BLANK.getStatus());
		boolean updateResult = updateLandInfo(vkey, consumerId, newLand);
		if (!updateResult) {
			logger.warn("种红包-app'{}'用户'{}'收割红包更新土地信息失败，土地信息：{}", appId, consumerId, JSON.toJSON(newLand));
			throw new BizException("更新土地失败");
		}
		// 红包状态更新为已收割
		DubboResult<Boolean> exchangeStatusResult = remoteActivityOrderService.exchangeStatusToSuccess(redPacketId);
		if (null == exchangeStatusResult || !exchangeStatusResult.isSuccess()
				|| null == exchangeStatusResult.getResult() || !exchangeStatusResult.getResult()) {
			logger.warn("种红包-领取红包'{}'异常，更新红包状态失败:{}", redPacketId, JSON.toJSON(exchangeStatusResult));
			throw new BizException("更新红包状态失败");
		}
		// 计入收割次数
		inputDailyReapCount(activity.getId(), consumerId);
		// 是否是标记用户 包含更新逻辑
		boolean marked = isMarkedUser(consumerId, appId, activity);
		initTotalBudget(marked, activity, consumerId, appId); // 初始化今日可得金额数
		// 计算用户可得金额
		long amount = getCanGetMoneyNew(consumerId, appId, activity, relateActId, marked);

		//统一访问日志
		doAccessLog(activity, consumer, pageId, 2, 2, PageBizTypeEnum.SEED_REDPACKET_JOIN.getBizPageId());
		AccessLogFilter.putExPair("addPrice", amount);

		// 将红包金额存入用户钱包账户
		if (amount == 0L || !reapRedPacketToAccountNew(marked, appId, consumer, redPacketId, amount, oldLand, vkey, activity)) {
			jsonObject.put("redPacketAmount", BigDecimal.ZERO);
			AccessLogFilter.putExPair("addPrice", 0);
			return jsonObject;
		}
		// 展示给页面的转换成元
		jsonObject.put("redPacketAmount", BigDecimal.valueOf(amount).movePointLeft(2));
		return jsonObject;
	}

	private void doClearLandInfo(Long consumerId, Long appId, Long relateActId, String redId) {
		List<String> landKeys = Lists.newArrayList();
		for (long i = 1; i < 9; i++) {
			landKeys.add(getHbaseKeyForUserLands(appId, relateActId, i));
		}
		List<LandInfoDto> lands = getBatchUserLandInfo(landKeys, consumerId);
		if (CollectionUtils.isEmpty(lands)) {
			return;
		}
		for (LandInfoDto land : lands) {
			if (Objects.equals(redId, land.getRedPacketId())) {
				// 无法查询的红包 确实是某一块土地上的红包
				LandInfoDto newLand = new LandInfoDto();
				newLand.setLandId(land.getLandId());
				newLand.setLandStatus(LandStatusEnum.BLANK.getStatus());
				// 清理旧土地数据 成为一块空地
				updateLandInfo(getHbaseKeyForUserLands(appId, relateActId, land.getLandId()), consumerId, newLand);
				logger.info("组件化种红包活动-执行空包清理 appId={}, cid={}, landId={}, redId={}", appId, consumerId, land.getLandId(), redId);
				break;
			}
		}
	}

	/**
	 * 计算可收割多少红包金额
	 */
	private long getCanGetMoneyNew(Long consumerId, Long appId, SeedRedPacketDto activity, Long relateActId, boolean marked) {
		String redisKeyForDrawNum = getRedisKeyForDrawNumNew(consumerId, activity.getId());
		// 剩余可中奖次数
		Long canDrawNumDaily = redisAtomicClient.getLong(redisKeyForDrawNum);
		if (canDrawNumDaily == null || canDrawNumDaily <= 0) {
			return 0L;
		}
		// 今日已收割次数 表示当前红包是第几个被收割的语义
		long reapCountDaily = getDailyReapCount(activity.getId(), consumerId);

		if ((null == activity.getPlantNumDaily() || activity.getPlantNumDaily() - reapCountDaily >= canDrawNumDaily) && !checkIfWin()){
			return 0L;  // 种植个数为无限制/当前可种植个数>当前可中奖个数，有50%的概率不中奖
		}
		stringRedisTemplate.opsForValue().increment(redisKeyForDrawNum, -1);  // 今日可中奖次数减1

		// 获取今日种红包活动可得总金额
		String key = getRedisKeyForTodayTotalMoney(consumerId, appId, activity.getId());
		String todayTotalMoney = stringRedisTemplate.opsForValue().get(key);

		if (StringUtils.isBlank(todayTotalMoney)) {
			return 0L;
		}

		long todaySeeRedPacketTotalAmount = Long.valueOf(todayTotalMoney);
		if (todaySeeRedPacketTotalAmount == 0L) {
			return 0L;
		}

		// 获取今日已得金额
		long canGetSeedRedPacketAmount = getDailyBonus(activity.getId(), consumerId);

		// 计算可中红包金额：= 每日种红包可得总金额-已种的红包可得金额
		long maxAmount = todaySeeRedPacketTotalAmount - canGetSeedRedPacketAmount;

		// 如果可中红包金额小于1分
		if (maxAmount <= 0) {
			return 0L;
		}

		// 被标记 and 是最后一次可中奖或最后一次可种植 发完红包金额
		if(marked && isLastDrawOrLastPlant(activity.getPlantNumDaily(), canDrawNumDaily, reapCountDaily)){
			return maxAmount;
		}

		// 计算红包额度随机范围的上限：可中红包金额 - 0.01 * 剩余可中红包个数
		long currentMaxAmount = maxAmount - 1 * canDrawNumDaily;

		// 如果上限1分，被标记用户发1分；未被标记用户中奖金额为0
		if (currentMaxAmount <= 0) {
			return 0L;
		}

		// 红包额度随机范围[0.01,可中红包金额-0.01*剩余可中红包个数]，此处是以分为单位，所以没有除以100
		return this.random.nextInt((int) currentMaxAmount) + 1L;
	}

	/**
	 * 获取今日种红包活动可得总金额
	 * @param marked
	 * @param activity
	 * @return
	 */
	private long getTodayBudget(boolean marked, SeedRedPacketDto activity, Long consumerId) {
		// 用户当前周期获得的总金额
		long bonus = getTotalBonus(activity.getId(), consumerId);
		// 用户获得的金额上限
		long bonumLimit = marked ? activity.getBonusLimit() : activity.getUnBonusLimit();
		if (bonumLimit <= bonus) {
			return 0L;
		}
		long differDay = getDifferDay(activity.getEndTime());
		if (differDay == 0L) {
			return 0L;
		}
		return (bonumLimit - bonus) / differDay;
	}

	// 每天的毫秒数
	private static final long MILLISECONDS_FOR_DAY = 86400000L;

	/**
	 * 计算当前周期还有多少天
	 */
	private long getDifferDay(Date date){
		Calendar curDate = Calendar.getInstance();
		if (curDate.getTime().after(date)) {
			return 0l;
		}
		Calendar tommorowDate = new GregorianCalendar(curDate.get(Calendar.YEAR), curDate.get(Calendar.MONTH),
				curDate.get(Calendar.DATE) + 1, 0, 0, 0);
		if (tommorowDate.getTime().after(date)) {
			return 1l;
		}
		return Math.abs(date.getTime() - tommorowDate.getTimeInMillis()) / MILLISECONDS_FOR_DAY +
				(Math.abs(date.getTime() - tommorowDate.getTimeInMillis()) % MILLISECONDS_FOR_DAY == 0 ? 1 : 2);
	}

	/**
	 * 是否是最后一次种植机会 或 最后一次中奖机会
	 * @param plantNumDaily
	 * @param canDrawNumDaily
	 * @param seedNumDaily
	 * @return
	 */
	private boolean isLastDrawOrLastPlant(Long plantNumDaily, long canDrawNumDaily, long seedNumDaily) {
		if (canDrawNumDaily == 1) {
			return true;
		}
		if (null == plantNumDaily) {
			return false;
		}
		return plantNumDaily <= seedNumDaily;
	}

	private JSONObject judgeIsSteal(String redPacketId, Long appId, JSONObject jsonObject) {
		String redisKeyForIsSteal = getRedisKeyForIsSteal(appId, redPacketId);
		Object o = stringRedisTemplate.opsForHash().get(redisKeyForIsSteal, String.valueOf(redPacketId));
		if(!Objects.isNull(o)){ // 红包被偷
			jsonObject.put("steal", true);
			return jsonObject;
		}
		stringRedisTemplate.opsForHash().putIfAbsent(redisKeyForIsSteal, String.valueOf(redPacketId), "1"); // 标记红包被偷
		stringRedisTemplate.expire(redisKeyForIsSteal, DateUtil.getSecondsToTomorrowWith5Min(), TimeUnit.SECONDS);
		return jsonObject;
	}

	@Override public LandInfoDto updateUserLand(Long appId, Long activityId, Long landId, Long consumerId, Integer landStatus, String redPacketId)
			throws BizException {
		LandInfoDto landInfoDto = new LandInfoDto();
		landInfoDto.setLandId(landId);
		landInfoDto.setLandStatus(landStatus);
		if(StringUtils.isNotBlank(redPacketId)){
			String orderIdTrimed = StringUtils.trimToNull(redPacketId);
			DubboResult<ActivityOrderDto> dubboResult = remoteActivityOrderService.findByOrderNum(orderIdTrimed);
			ActivityOrderDto activityOrderDto = dubboResult.getResult();
			if (!dubboResult.isSuccess() || null == activityOrderDto) {
				throw new BizException("获取红包详情异常");
			}
			landInfoDto.setRedPacketId(orderIdTrimed);
			landInfoDto.setAmount(activityOrderDto.getAddCredits());
			landInfoDto.setSeedTime(activityOrderDto.getGmtCreate().getTime());
			Long needTime = activityOrderDto.getFacePrice() == null ? 0L: Long.valueOf(activityOrderDto.getFacePrice());
			landInfoDto.setGrownNeedTime(needTime);
		}
		String vkey = getHbaseKeyForUserLands(appId, activityId, landId);
		DuibaKvtableDto duibaKvtableNew = new DuibaKvtableDto();
		duibaKvtableNew.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		duibaKvtableNew.setVkey(vkey);
		duibaKvtableNew.setConsumerId(consumerId);
		duibaKvtableNew.setStrValue(JSON.toJSONString(landInfoDto));
		if(!remoteDuibaKvtableService.upsertKVTable(duibaKvtableNew)){
			throw new BizException("更新失败");
		}
		return landInfoDto;
	}

	@Override public List<LandInfoDto> getUserLand(Long appId, Long activityId, Long consumerId) {
		// 获取用户土地信息
		List<String> vkeyList = new ArrayList<>();
		for (int i = 1; i < LAND_NUMBER + 1; i++) {
			vkeyList.add(getHbaseKeyForUserLands(appId, activityId, Long.valueOf(i)));
		}
		return getBatchUserLandInfo(vkeyList, consumerId);
	}

	@Override public DuibaKvtableDto findHBaseInfoByVKey(String vkey, Long consumerId) {
		HbaseKvParam hbaseVKeyParam = new HbaseKvParam();
		hbaseVKeyParam.setConsumerId(consumerId);
		hbaseVKeyParam.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		hbaseVKeyParam.setVkey(vkey);
		return remoteDuibaKvtableService.findByVkey(hbaseVKeyParam);
	}

	@Override
	public SeedRedPacketDto selectSeedRedPacketDto(Long appId, String ip) throws BizException{
		Long specifyActId = getAppSpecifyActivityId(appId);
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);
		// 关闭广告投放开关
		if(Objects.equals(seedRedPacketDto.getAdSwitch(), false)) {
		    return changeStatus(seedRedPacketDto);
		}
        String appIds = seedRedPacketDto.getAppIds4Adver();
        if (StringUtils.isBlank(appIds)) {
            return changeStatus(seedRedPacketDto);
        }
        List<String> appIdFromJudge = Splitter.on(",").omitEmptyStrings().splitToList(appIds).stream().filter(appIdFromDB -> Objects.equals(Long.valueOf(appIdFromDB), appId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIdFromJudge)) { // 未配置该app
            return changeStatus(seedRedPacketDto);
        }
        AppNewExtraDto appNewExtraDto = developerCacheService.getAppNewExtra(appId);
        // 广告流量策略关闭
        if (!(appNewExtraDto != null && appNewExtraDto.getFlowRuleAdverSwitch() != null && appNewExtraDto.getFlowRuleAdverSwitch())) {
            return seedRedPacketDto;
        }
        ActivityFlowRuleDto activityFlowRuleDto;
        try {
            activityFlowRuleDto = activityCommCacheService.getActivityFlowRuleDto(appId, ActivityFlowRuleDto.RULE_TYPE_SPECIAL, ActivityFlowRuleDto.ACTIVITY_TYPE_ADVER);
        } catch (Exception e) {
            logger.info("流量策略dto获取失败", e);
            throw new BizException("流量策略获取失败");
        }
        if (activityFlowRuleDto == null) {
            return seedRedPacketDto;
        }
        String validRegions4Activity = activityFlowRuleDto.getValidRegions(); //有效地域
        String validPeriod4Activity = activityFlowRuleDto.getValidPeriod(); //有效时段
        if (StringUtils.isBlank(validRegions4Activity) && StringUtils.isBlank(validPeriod4Activity)) {
            return seedRedPacketDto;
        }
        // 不在有效地域内
        if (!checkRegionStrategy(validRegions4Activity, ip)) {
            return changeStatus(seedRedPacketDto);
        }
        // 不在有效时段内
        if (checkPeriodStrategy(validPeriod4Activity)) {
            return changeStatus(seedRedPacketDto);
        }
		return seedRedPacketDto;
	}

	private SeedRedPacketDto changeStatus(SeedRedPacketDto seedRedPacketDto) {
		seedRedPacketDto.setAdSwitch(false);
		seedRedPacketDto.setAdPositions(null);
		return seedRedPacketDto;
	}

	private boolean checkPeriodStrategy(String validPeriod4Activity) {
		// 无限制
		if(StringUtils.isBlank(validPeriod4Activity)){
			return false;
		}
		Date date = new Date();
		int weekOfDay = getWeekOfDate(date);
        Integer curHour = Integer.valueOf(DateUtils.getMinuteOnlyStr(date).substring(0, 2));
		JSONObject jsonObject = JSONObject.parseObject(validPeriod4Activity);
        String jsonString = jsonObject.getString(weekOfDay == 0 ? SUNDAY_NUM : String.valueOf(weekOfDay));
        if(StringUtils.isBlank(jsonString)) {
            return true;
        }
        JSONArray jsonArray = JSONObject.parseArray(jsonString);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject1 = jsonArray.getJSONObject(i);
            Integer startHour = Integer.valueOf(jsonObject1.getString("s"));
            Integer endHour = Integer.valueOf(jsonObject1.getString("e"));
            //开启时间段 <= 当前时间段 && 结束时间段 > 当前时间段，在可发放券的时间段
            if (startHour <= curHour && endHour > curHour) {
                return false;
            }
        }
        return true;
	}

	private boolean checkRegionStrategy(String validRegions4Activity, String ip) {
		// 无限制
		if(StringUtils.isBlank(validRegions4Activity)){
			return true;
		}
		String cityCode = commonService.findCodeByIp(ip); //根据ip获取城市编码
		if(StringUtils.isBlank(cityCode)){
			return false;
		}
		List<Long> list = JSONObject.parseArray(validRegions4Activity, Long.class);
		return 	list.contains(Long.valueOf(cityCode));
	}

	private static int getWeekOfDate(Date dt) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		int weekDay = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (weekDay < 0){
			weekDay = 0;
		}
		return weekDay;
	}

	@Override
	public List<LandInfoDto> getAllHasUnlockLand(Long landId, ConsumerDto consumerDto, AppSimpleDto appSimpleDto) throws BizException{
		Long appId = appSimpleDto.getId();
		Long specifyActId = getAppSpecifyActivityId(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);
        List<LandInfoDto> hasUnlockLandInfoDto = Lists.newArrayList();
        //解锁四号土地所需积分
        Long unlockNeedCredits = activityPluginService.getNeedCredits(seedRedPacketDto.getUnlockLandCreditsPrice().intValue(),
                appSimpleDto.getCreditsRate());
        String fourthLandVkey = getHbaseKeyForUserLands(appId, relateActId,4L);
        DuibaKvtableDto fourthLandDuibaKvtableDto = findHBaseInfoByVKey(fourthLandVkey, consumerDto.getId());
        LandInfoDto fourthLandInfoDto = JSONObject.parseObject(fourthLandDuibaKvtableDto.getStrValue(), LandInfoDto.class);
        if(fourthLandInfoDto.getLandStatus().equals(LandStatusEnum.LOCK.getStatus())){
            unlockLandService.unlockLandByCredits(relateActId,
                    consumerDto.getId(), appId, unlockNeedCredits, fourthLandInfoDto, fourthLandVkey);
        }
		if(landId != null){
			String vkey = getHbaseKeyForUserLands(appId, relateActId,landId);
			DuibaKvtableDto duibaKvtableDto = findHBaseInfoByVKey(vkey, consumerDto.getId());
			if(StringUtils.isNotBlank(duibaKvtableDto.getStrValue())) {
				LandInfoDto landInfoDto = JSONObject.parseObject(duibaKvtableDto.getStrValue(), LandInfoDto.class);
				if(Objects.equals(landInfoDto.getLandStatus(),LandStatusEnum.CAN_UNLOCK.getStatus())) {
					hasUnlockLandInfoDto.add(landInfoDto);
				}
			}
		} else {
			// 获取用户土地信息
			List<String> vkeyList = new ArrayList<>();
			for (int i = 1; i < LAND_NUMBER + 1; i++) {
				vkeyList.add(getHbaseKeyForUserLands(appId, relateActId, Long.valueOf(i)));
			}
			List<LandInfoDto> userLandInfo = getBatchUserLandInfo(vkeyList, consumerDto.getId());
			hasUnlockLandInfoDto = userLandInfo.stream().filter(landInfoDto -> Objects.equals(landInfoDto.getLandStatus(), LandStatusEnum.CAN_UNLOCK.getStatus())).collect(Collectors.toList());
		}
		return hasUnlockLandInfoDto;

	}

    @Override
    public List<LandInfoDto> getAllHasUnlockLandNew(Long landId, ConsumerDto consumer, AppSimpleDto app) throws BizException {
        final Long appId = app.getId();
        final Long consumerId = consumer.getId();
        // 查询种红包活动 若无正在进行的活动 将抛出biz异常
        SeedRedPacketDto activity = getAppSpecifyActivity(appId);
        // 获取映射的种红包活动id
        Long relateActId = getRelateActId(appId, activity.getId());

        // 获取用户土地信息
        List<String> vkeyList = Lists.newArrayList();

        if (landId != null) {
            vkeyList.add(getHbaseKeyForUserLands(appId, relateActId, landId));
        } else {
            for (long i = 1; i < LAND_NUMBER + 1; i++) {
                vkeyList.add(getHbaseKeyForUserLands(appId, relateActId, i));
            }
        }
        List<SeedRedPacketTaskDto> tasks = remoteBackendSeedRedPacketService.findTasksByActivityId(activity.getId());
        Map<Long, SeedRedPacketTaskDto> taskMapping = Maps.uniqueIndex(tasks, t -> t.getLandId());
        // 原本土地信息
        List<LandInfoDto> landInfoOld = getBatchUserLandInfo(vkeyList, consumerId);
        // 返回的土地信息 拷贝原本信息 尝试解锁时如果可以解锁 会改变对应土地的状态
        List<LandInfoDto> landInfoNew = BeanUtils.copyList(landInfoOld, LandInfoDto.class);
        // 收集可解锁的土地 用于更新操作
        List<LandInfoDto> updateList = landInfoNew.stream()
                .filter(l -> {
                    // 校验土地是否可以解锁
                    return checkTaskForLand(appId, consumerId, activity, relateActId, taskMapping, l);
                })
                .map(l -> {
                    // 把本身的土地列表状态也改掉 后续要返回给前端
                    l.setLandStatus(LandStatusEnum.CAN_UNLOCK.getStatus());
                    return l;
                })
                .collect(Collectors.toList());

        // 如果有能解锁的，更新土地状态
        if (CollectionUtils.isNotEmpty(updateList)) {
            boolean updateResult = batchUpdateLandInfo(updateList, consumerId, appId, relateActId);
            if (!updateResult) {
                // 更新失败返回旧的
                logger.warn("种红包-更新土地失败,app：{}，用户id：{}，旧土地信息：{}，新土地信息：{}", appId, consumerId, JSON.toJSON(landInfoOld), JSON.toJSON(landInfoNew));
                landInfoNew = landInfoOld;
            }
        }
        // 删除非可解锁状态的土地
        landInfoNew.removeIf(l -> !Objects.equals(l.getLandStatus(), LandStatusEnum.CAN_UNLOCK.getStatus()));
        return landInfoNew;
    }

    @Override public Integer removeAppRelateAct(Long appId) {
		if(null == appId){
			return null;
		}
		return remoteAppRelateSeedActService.removeByAppId(appId);
	}

	@Override
	public StealLandInfoVO getStealLandList(ConsumerDto consumerDto, AppSimpleDto appSimpleDto) throws BizException {
        StealLandInfoVO stealLandInfoVO = new StealLandInfoVO();
		Long appId = appSimpleDto.getId();
		// 校验钱包账户是否开启，未开启则不可见
		WalletAccountDto walletAccountDto = getWalletAccountInfoCache(appId);
		if (null == walletAccountDto || !Objects.equals(YesOrNoEnum.YES.getCode(), walletAccountDto.getState())) {
			logger.info("种红包-查询土地信息，app{}未开启钱包账户", appId);
			throw new BizException("未开启钱包账户");
		}
		Long specifyActId = getAppSpecifyActivityId(appId);
		SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);

        //日志
        AccessLogExUtil.putAccessLogExPair(true, 502, 1, PageBizTypeEnum.STEAL_REDPACKET_INDEX, seedRedPacketDto.getId(),
                null, ActivityUniformityTypeEnum.StealRedPacket, consumerDto.isNotLoginUser(),
                consumerDto.getCredits(), null, null, null, null);

		// 偷红包开关未开启
		if(Objects.equals(seedRedPacketDto.getStealRedPacketSwitch(), false)){
			logger.info("偷红包活动未开启,app->{}",appId);
			throw new BizException("偷红包活动未开启");
		}
		// 统一访问日志
		AccessLogFilter.putExPair("suc", 1);
		AccessLogFilter.putExPair("id", specifyActId);
		// 偷红包土地集合redis
		String redisKeyForStealLandInfoList = getRedisKeyForStealLandInfoList(appId, consumerDto.getId());
		List<String> stealLandStrings = stringRedisTemplate.opsForList().range(redisKeyForStealLandInfoList, 0, -1);
		if(CollectionUtils.isNotEmpty(stealLandStrings)){
			List<LandInfoVO> landInfoVos = stealLandStrings.stream().map(stealLandString -> JSONObject.parseObject(stealLandString, LandInfoVO.class)).collect(Collectors.toList());
			landInfoVos.forEach(landInfoVO -> landInfoVO.setGrownRemainTime(stringRedisTemplate.getExpire(getRedisKeyForConsumerId(landInfoVO.getRedPacketId(), appId)) == -2 ?
                    0 : stringRedisTemplate.getExpire(getRedisKeyForConsumerId(landInfoVO.getRedPacketId(), appId))));// 设置红包成熟剩余时间
            landInfoVos.forEach(landInfoVO -> landInfoVO.setRedPacketStatus(
                    landInfoVO.getGrownRemainTime() <= 0 && landInfoVO.getRedPacketStatus() != 3 ? RedPacketStatusEnum.GROWN.getStatus() : landInfoVO.getRedPacketStatus()));// 设置红包是否成熟
			stealLandInfoVO.setLandList(sortList(landInfoVos));
			return stealLandInfoVO;
		}
		String redisKeyForRedPacketList = getRedisKeyForRedPacketIdList(appId);
        Set<String> rangeList = stringRedisTemplate.opsForZSet().range(redisKeyForRedPacketList, 0, -1);
        if(CollectionUtils.isEmpty(rangeList)){ // 当前redis中没有红包列表
            // 生成8个机器人用户
	        List<LandInfoVO> landInfoVos = initStealLandWithFakeUser(seedRedPacketDto, appId, 8);
			setLandId(landInfoVos);
			saveToRedis(landInfoVos, redisKeyForStealLandInfoList);
            stealLandInfoVO.setLandList(sortList(landInfoVos));
			return stealLandInfoVO;
		}
        // 过滤出失效的key
        Set<String> invalidRangeList = rangeList.stream().filter(i -> StringUtils.isBlank(stringRedisTemplate.opsForValue().get(getRedisKeyForConsumerId(i, appId)))).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(invalidRangeList)) {
            stringRedisTemplate.opsForZSet().remove(redisKeyForRedPacketList, invalidRangeList.toArray());// 清空redis里面的红包id集合
        }
        Set<String> range = stringRedisTemplate.opsForZSet().range(redisKeyForRedPacketList, 0, -1);
		List<LandInfoVO> landInfoVos = initStealLandInfo(range, appId, consumerDto.getId());
		List<LandInfoVO> fakeLandInfoVos = initStealLandWithFakeUser(seedRedPacketDto, appId,8 - landInfoVos.size());// 不足的机器人补上
		if(CollectionUtils.isNotEmpty(fakeLandInfoVos)){
			landInfoVos.addAll(fakeLandInfoVos);
		}
		setLandId(landInfoVos);
		saveToRedis(landInfoVos, redisKeyForStealLandInfoList);
        stealLandInfoVO.setLandList(sortList(landInfoVos));
        return stealLandInfoVO;
    }

    private void setLandId(List<LandInfoVO> landInfoVos) {
        long landId = 1;
        int count = 0;
		List<FakeUserDto> fakeUserList = Lists.newArrayList();
        List<LandInfoVO> emptyAvatarList = landInfoVos.stream().filter(landInfoVO -> StringUtils.isBlank(landInfoVO.getAvatar())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(emptyAvatarList)){
			fakeUserList = remoteFakeUserService.getRandomUsersWithCount(emptyAvatarList.size());
		}
		for(LandInfoVO landInfoVO : landInfoVos){
            landInfoVO.setLandId(landId++);
            if(StringUtils.isBlank(landInfoVO.getAvatar()) && fakeUserList.size() >= count){
            	landInfoVO.setAvatar(fakeUserList.get(count++).getAvatar());
			}
		}
    }

    private void saveToRedis(List<LandInfoVO> landInfoVos, String redisKeyForStealLandInfoList) {
		landInfoVos.forEach(landInfoVO -> {
			stringRedisTemplate.opsForList().rightPush(redisKeyForStealLandInfoList, JSONObject.toJSONString(landInfoVO));
		});
		stringRedisTemplate.expire(redisKeyForStealLandInfoList, DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
	}

	@Override
    public StealRedPacketVO stealRedPacket(ConsumerDto consumerDto, AppSimpleDto appSimpleDto, Long landId, String redPacketId) throws BizException {
	    StealRedPacketVO stealRedPacketVO = new StealRedPacketVO();
	    stealRedPacketVO.setSteal(false);
        Long appId = appSimpleDto.getId();
        Long consumerId = consumerDto.getId();
		// 统一访问日志
		AccessLogFilter.putExPair("suc", 1);
		AccessLogFilter.putExPair("orderNum", redPacketId);
		AccessLogFilter.putExPair("consumeCredits", 0);
		AccessLogFilter.putExPair("status", 2);
		// 校验钱包账户是否开启，未开启则不可见
		WalletAccountDto walletAccountDto = getWalletAccountInfoCache(appId);
		if (null == walletAccountDto || !Objects.equals(YesOrNoEnum.YES.getCode(), walletAccountDto.getState())) {
			logger.info("种红包-查询土地信息，app{}未开启钱包账户", appId);
			throw new BizException("未开启钱包账户");
		}
		// 获取定向的活动id
		Long specifyActId = getAppSpecifyActivityId(appId);
		// 获取映射的种红包活动id
		Long relateActId = getRelateActId(appId, specifyActId);
		AccessLogFilter.putExPair("id", specifyActId);
		// 校验活动是否存在，是否开启
        SeedRedPacketDto seedRedPacketDto = checkActivity(specifyActId);

		//日志
		AccessLogExUtil.putAccessLogExPair(true, 502, 2, PageBizTypeEnum.STEAL_REDPACKET_JOIN, seedRedPacketDto.getId(),
                null, ActivityUniformityTypeEnum.StealRedPacket, consumerDto.isNotLoginUser(),
				consumerDto.getCredits(), null, null, null, null);
		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(null,seedRedPacketDto.getId(), ActivityUniformityTypeEnum.StealRedPacket.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			throw new BizException(riskResult.getCopy());
		}

        Boolean stealRedPacketSwitch = seedRedPacketDto.getStealRedPacketSwitch();
        Long monthBudget = seedRedPacketDto.getMonthBudget();
        // 偷红包开关未开启
        if(Objects.equals(stealRedPacketSwitch, false) || Objects.isNull(monthBudget)){
        	logger.info("偷红包活动未开启,app->{}",appId);
        	throw new BizException("偷红包活动未开启");
        }
        // 校验偷红包次数
		Long stealTime = checkStealTime(appId, consumerId);
        // 判断是否成熟
		checkRedPacket(redPacketId, appId);
        // 偷红包土地集合redis
        String redisKeyForStealLandInfoList = getRedisKeyForStealLandInfoList(appId, consumerId);
        String landVoJson = stringRedisTemplate.opsForList().index(redisKeyForStealLandInfoList, landId - 1);
        if(StringUtils.isBlank(landVoJson)){
            logger.info("土地id异常,consumerId->{},activityId->{},appId->{}",consumerId, specifyActId, appId);
            throw new BizException("土地id异常");
        }
        LandInfoVO landInfoVO = JSONObject.parseObject(landVoJson, LandInfoVO.class);
        if(RedPacketStatusEnum.STEAL.getStatus().equals(landInfoVO.getRedPacketStatus())){
        	logger.info("红包已被偷取,consumerId->{},activityId->{},appId->{}",consumerId, specifyActId, appId);
        	throw new BizException("请不要重复偷取");
		}

        setLandHasReap(landInfoVO, redisKeyForStealLandInfoList, landId);// 设置土地已收割
		// 是否被标记
		String loginMarkKey = getRedisKeyForSeedRedPacketLogin(consumerId, appId);
		String loginMark = stringRedisTemplate.opsForValue().get(loginMarkKey);
		Boolean marked = null == loginMark ? markUser(consumerId, appId, relateActId, loginMarkKey, walletAccountDto) : true;

        String redisKeyForIsSteal = getRedisKeyForIsSteal(appId, redPacketId);
		// 如果是真实红包 红包id存在且被偷过（除了标记用户当天最后一个红包，不管是否已被偷，都要发完）
	    if(landInfoVO.getConsumerId() != null && Objects.equals(stringRedisTemplate.opsForHash().get(redisKeyForIsSteal,redPacketId), "1" )
			    && !(marked && stealTime == 8)){
	        stealRedPacketVO.setRedPacketAmount(BigDecimal.ZERO);
	        stealRedPacketVO.setSteal(true);
		    AccessLogFilter.putExPair("status", 3);
	        return stealRedPacketVO;
        }
		AccessLogFilter.putExPair("status", 1);
        // 将红包标记成已被偷取
        if(landInfoVO.getConsumerId() != null && StringUtils.isNotBlank(redPacketId)) {
			stringRedisTemplate.opsForHash().put(redisKeyForIsSteal, String.valueOf(redPacketId), "1"); // 标记红包被偷
			stringRedisTemplate.expire(redisKeyForIsSteal, DateUtil.getSecondsToTomorrowWith5Min(), TimeUnit.SECONDS);
		}
		// 然后判断月预算
		String redisKeyForMonthBudget = getRedisKeyForMonthBudget(appId, consumerId);
        long monthBudgetRemain = getMonthBudgetRemain(consumerId, seedRedPacketDto.getMonthBudget(), redisKeyForMonthBudget);
        if(monthBudgetRemain <= 0){ // 月预算 <= 0
           calculateRedPacketAmount(stealRedPacketVO, marked, consumerId, seedRedPacketDto.getGmtCreate());
           AccessLogFilter.putExPair("stolenPrice", stealRedPacketVO.getRedPacketAmount());
           return stealRedPacketVO;
        }
        long dailyBudget = calculateDailyBudget(consumerId, walletAccountDto, monthBudgetRemain, marked, appId);
        long dailyBudgetRemain = getDailyBudgetRemain(appId, dailyBudget, consumerId);
        long redPacketAmount = calculateRedPacketAmount(dailyBudgetRemain, marked, stealTime, redisKeyForMonthBudget, consumerDto, redPacketId);
		BigDecimal amount =  BigDecimal.valueOf(redPacketAmount).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_DOWN);
		AccessLogFilter.putExPair("stolenPrice", amount);
		stealRedPacketVO.setRedPacketAmount(amount);
        return stealRedPacketVO;
    }

	@Override
	public boolean checkUseVersion(Long appId) {
		return super.isUseNewVersion(appId);
	}

	@Override
	public SeedRedPacketIndexVO getIndex(AppSimpleDto app) throws BizException {
		SeedRedPacketIndexVO index = new SeedRedPacketIndexVO();
		index.setRmbModel(app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint));
		SeedRedPacketDto activity = getAppSpecifyActivity(app.getId());
		index.setDataJson(activity.getDataJson());
		index.setRule(activity.getRule());
		index.setAccountType(activity.getAccountType());
		index.setRewardType(activity.getRewardType());
		index.setId(activity.getId());
        SeedRedPacketAdConfig adConfig = JSON.parseObject(activity.getAdConfig(), SeedRedPacketAdConfig.class);
        if (adConfig != null && adConfig.isEnable()) {
            index.setAdConfig(true);
            index.setGuidingText(adConfig.getGuidingText());
            index.setAdList(adConfig.getList());
        }
		AppNewExtraDto appConfig = developerCacheService.getAppNewExtra(app.getId());
		if (appConfig != null) {
			index.setVirtualMoneyUnit(appConfig.getVirtualMoneyUnit());
		}
		return index;
	}

	/**
	 * 计算页面展示用红包金额
	 * @param dailyBudgetRemain
	 * @param marked
	 * @param stealTime
	 * @param redisKeyForMonthBudget
	 * @param consumer
	 * @param redPacketId
	 * @return
	 * @throws BizException
	 */
    private long calculateRedPacketAmount(long dailyBudgetRemain, Boolean marked, Long stealTime, String redisKeyForMonthBudget, ConsumerDto consumer, String redPacketId) throws BizException {
		long redPacketAmount = getMoneyForSteal(dailyBudgetRemain, marked, stealTime);
		if (redPacketAmount <= 0L) {
			return 0L;
		}
		Long consumerId = consumer.getId();
		Long appId = consumer.getAppId();
		stringRedisTemplate.opsForHash().increment(redisKeyForMonthBudget, String.valueOf(consumerId), - redPacketAmount); // 扣除当前月预算
		String redisKeyForDailyBudget = getRedisKeyForDailyBudget(consumer.getAppId(), consumerId);
		stringRedisTemplate.opsForHash().increment(redisKeyForDailyBudget, String.valueOf(consumerId), - redPacketAmount); // 扣除当前日预算
		if (!reapRedPacketToAccount(appId, consumer, redPacketId, redPacketAmount)) {
			return 0L;
		}
		return redPacketAmount;
	}




	// 判断是否成熟
    private void checkRedPacket(String redPacketId, Long appId) throws BizException{
	    String redisKeyForConsumerId = getRedisKeyForConsumerId(redPacketId, appId);
	    Long expire = stringRedisTemplate.getExpire(redisKeyForConsumerId);
	    if(expire > 0){
		    logger.info("红包尚未成熟,appId:{},redPacketId:{}", appId, redPacketId);
		    throw new BizException("红包尚未成熟");
	    }
    }

    // 判断当日偷红包次数
	private Long checkStealTime(Long appId, Long consumerId) throws BizException{
		String redisKeyForStealCount = getRedisKeyForStealCount(appId, consumerId);
		String stealTimeStr = stringRedisTemplate.opsForValue().get(redisKeyForStealCount);
		if(StringUtils.isBlank(stealTimeStr)){
			stringRedisTemplate.opsForValue().set(redisKeyForStealCount, "1", DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
			return 1L;
		}
		if(Long.valueOf(stealTimeStr).compareTo(8L) >= 0){
			logger.info("当日偷红包次数大于8,consumerId'{}'", consumerId);
			throw new BizException("偷红包异常");
		}
		stringRedisTemplate.opsForValue().increment(redisKeyForStealCount, 1L);// 偷红包次数+1
		return Long.valueOf(stealTimeStr) + 1;
	}

    // 获取月预算剩余值
    private long getMonthBudgetRemain(Long consumerId, Long monthBudget, String redisKeyForMonthBudget){
	    long monthBudgetRemain = 0;
	    Object monthValue = stringRedisTemplate.opsForHash().get(redisKeyForMonthBudget, String.valueOf(consumerId));
	    if(Objects.isNull(monthValue)){  // 初始化月预算
		    stringRedisTemplate.opsForHash().putIfAbsent(redisKeyForMonthBudget, String.valueOf(consumerId), String.valueOf(monthBudget));
		    stringRedisTemplate.expire(redisKeyForMonthBudget, getToMonthSeconds(), TimeUnit.SECONDS );
		    monthBudgetRemain = monthBudget;
	    }else{
		    monthBudgetRemain = Long.valueOf((String) monthValue);
	    }
	    return monthBudgetRemain;
    }

    private Long getAppSpecifyActivityId(Long appId) throws BizException{
	    // 判断活动是否定向给该app
	    WalletAccountRelationDto walletAccountRelationDto = getActivitySpecifyCacheByAppId(appId);
	    if (null == walletAccountRelationDto) {
		    throw new BizException(ACT_NOT_SPECIFY);
	    }
	    return walletAccountRelationDto.getWalletId();
    }

    private Long getAppSpecifyId(Long appId) throws BizException{
        // 判断活动是否定向给该app
        WalletAccountRelationDto walletAccountRelationDto = getActivitySpecifyCacheByAppId(appId);
        if (null == walletAccountRelationDto) {
            throw new BizException(ACT_NOT_SPECIFY);
        }
        return walletAccountRelationDto.getId();
    }

    private void setLandHasReap(LandInfoVO landInfoVO, String redisKeyForStealLandInfoList, Long landId) {
        landInfoVO.setLandStatus(LandStatusEnum.BLANK.getStatus());
        landInfoVO.setRedPacketStatus(RedPacketStatusEnum.STEAL.getStatus());
        landInfoVO.setRedPacketId(null);
        landInfoVO.setGrownRemainTime(0L);
        landInfoVO.setGrownNeedTime(0L);
        stringRedisTemplate.opsForList().set(redisKeyForStealLandInfoList, landId - 1, JSONObject.toJSONString(landInfoVO));
    }

    /**
     * 获取偷红包金额
     * 未被标记用户每次可偷取金额【0.01，今日可偷取总金额／今日剩余偷取次数（包含本次；计算结果取到分位）】区间上限不足0.01，取0.01
     *
     * 被标记用户每次可偷取金额【0.01，今日可偷取总金额／今日剩余偷取次数（包含本次；计算结果取到分位）】区间上限不足0.01，取0.01
     * 每天最后一次偷取，校验当日剩余可偷取金额，若剩余大于0.01，则将当日剩余金额全部下发。
      */
    private long getMoneyForSteal(long dailyBudgetRemain, Boolean marked, Long stealTime) {
        if(dailyBudgetRemain <= 0){ // 日预算已为0
            return 0L;
        }
        // 标记用户
        if(marked && stealTime == 8){ // 最后一次偷红包
//            amount = BigDecimal.valueOf(redPacketAmount).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_DOWN)
            return dailyBudgetRemain;
        }
        long redPacketAmount = 0;
        long closedInterval = dailyBudgetRemain / (SEED_RED_PACKET_NUMBER_EVERYDAY - stealTime + 1);
        redPacketAmount = closedInterval <= 0 ? 1L : random.nextInt((int)closedInterval) + 1;
        return redPacketAmount;
    }

    // 获取剩余日预算
    private long getDailyBudgetRemain(Long appId, long dailyBudget, Long consumerId) {
        String redisKeyForDailyBudget = getRedisKeyForDailyBudget(appId, consumerId);
        Object dailyValue = stringRedisTemplate.opsForHash().get(redisKeyForDailyBudget, String.valueOf(consumerId));
        if(Objects.isNull(dailyValue)){  // 初始化日预算
            long dailyBudgetRemain = dailyBudget;
            stringRedisTemplate.opsForHash().putIfAbsent(redisKeyForDailyBudget, String.valueOf(consumerId), String.valueOf(dailyBudgetRemain));
            stringRedisTemplate.expire(redisKeyForDailyBudget, DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS );
            return dailyBudgetRemain;
        }
        return Long.valueOf((String) dailyValue);
    }

    /**
     * 1、被标记用户
     * >每日可偷取金额=【x-已偷取金额】／【提现所需当月累计签到天数-1-（当月已累计签到天数-1）】
     * 2、未被标记用户
     * >每日可偷取金额=用户每天可偷取额度=【x-已偷取总金额】／本月剩余天数（算当天）
     */
    private long calculateDailyBudget(Long consumerId, WalletAccountDto walletAccountDto,long monthBudgetRemain, Boolean marked, Long appId) {
    	//  AAW_K158_consumerId_appId
		String redisKeyForTodayStealTotalMoney = getRedisKeyForTodayStealTotalMoney(consumerId, appId);
		Object o = stringRedisTemplate.opsForHash().get(redisKeyForTodayStealTotalMoney, String.valueOf(consumerId));
		if(!Objects.isNull(o)){
			return Long.valueOf((String)o);
		}
		// 获取本月已累计签到天数
        Long conLoginDays = walletAccountHbaseService.getLoginDay(consumerId);
        Integer connectLoginDays = conLoginDays == null ? 0 : conLoginDays.intValue();
        // 计算剩余天数
        Integer remainLoginDaysReal = marked ?
                walletAccountDto.getWithdrawDay() - 1 - (connectLoginDays - 1)
                : getRemainDay();
        // 如果剩余天数小于1，按1算，使得分母等于1，即达到签到天数后再签到，每次领取都是得剩余可得金额
        Integer remainLoginDays = remainLoginDaysReal.compareTo(1) < 0 ? 1 : remainLoginDaysReal;
        // 计算本周期剩余每天可偷红包总金额
        Long todayTotalAmount = monthBudgetRemain / remainLoginDays;
		stringRedisTemplate.opsForHash().put(redisKeyForTodayStealTotalMoney, String.valueOf(consumerId), String.valueOf(todayTotalAmount));
		stringRedisTemplate.expire(redisKeyForTodayStealTotalMoney, DateUtil.getToTomorrowSeconds(), TimeUnit.SECONDS);
        return todayTotalAmount;
    }

    // 月预算<=0时.
    private StealRedPacketVO calculateRedPacketAmount(StealRedPacketVO stealRedPacketVO, Boolean marked, Long consumerId, Date date) {
        long redPacketAmount = 0;
        Boolean hadWithdrawCash = remoteCashDrawService.hasCashDrawsRecord(consumerId, AccountTypeEnum.ALL_WALLET, date);
        if(marked || hadWithdrawCash){ // 标记用户/ 用户提现 金额取[0.01,0.03]
            redPacketAmount = random.nextInt(3) + 1;
            BigDecimal amount = BigDecimal.valueOf(redPacketAmount).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_DOWN);
            stealRedPacketVO.setRedPacketAmount(amount);
            return stealRedPacketVO;
        }
        // 未标记用户
        stealRedPacketVO.setRedPacketAmount(BigDecimal.ZERO);
        return stealRedPacketVO;
    }

    private List<LandInfoVO> initStealLandInfo(Set<String> range, Long appId, Long consumerIdNow) {
	    List<LandInfoVO> landInfoVos = Lists.newArrayList();
	    Set<String> set = Sets.newHashSet();// 用于过滤相同用户
        Iterator<String> iterator = range.iterator();
        String redisKeyForRedPacketList = getRedisKeyForRedPacketIdList(appId);
        while(iterator.hasNext()) {
            LandInfoVO landInfoVO = new LandInfoVO();
            String redPacketId = iterator.next();
            String redisKeyForConsumerId = getRedisKeyForConsumerId(redPacketId, appId);
            String consumerId = stringRedisTemplate.opsForValue().get(redisKeyForConsumerId);
            if(StringUtils.isBlank(consumerId) || set.contains(consumerId) || Objects.equals(Long.valueOf(consumerId),consumerIdNow)){
                continue;
            }
            set.add(consumerId);
            landInfoVO.setRedPacketId(redPacketId);
            landInfoVO.setConsumerId(Long.valueOf(consumerId));
            landInfoVO.setGrownNeedTime(stringRedisTemplate.getExpire(redisKeyForConsumerId));
            landInfoVO.setGrownRemainTime(stringRedisTemplate.getExpire(redisKeyForConsumerId));
            landInfoVO.setRedPacketStatus(RedPacketStatusEnum.GROW_UP.getStatus());
            landInfoVO.setLandStatus(LandStatusEnum.HAS_RED_PACKET.getStatus());
            ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(Long.valueOf(consumerId)).getResult();
            if(consumerExtraDto != null) {
                String avatar = consumerExtraDto.getAvatar();
                if (StringUtils.isNotBlank(avatar)) {
                	landInfoVO.setAvatar(avatar);
                }
            }
            stringRedisTemplate.opsForZSet().incrementScore(redisKeyForRedPacketList, redPacketId, 1);
            landInfoVos.add(landInfoVO);
            if(landInfoVos.size() >= 8){
            	break;
            }
        }
        return landInfoVos;
    }

    // 初始化机器人土地列表
	private List<LandInfoVO> initStealLandWithFakeUser(SeedRedPacketDto seedRedPacketDto, Long appId, Integer count) {
		List<LandInfoVO> landInfoVos = Lists.newArrayList();
	    if(count < 1){
	        return landInfoVos;
        }
        List<SeedRedPacketGrownTimeDto> grownTimeList = seedRedPacketDto.getGrownTimeList();
        List<Long> grownTimes = grownTimeList.stream().map(SeedRedPacketGrownTimeDto::getGrownTime).collect(Collectors.toList());
        Collections.shuffle(grownTimeList);
        for(int i = 0; i < count; i ++){
            LandInfoVO landInfoVO = new LandInfoVO();
            landInfoVO.setRedPacketStatus(RedPacketStatusEnum.GROW_UP.getStatus()); // 设置红包状态
            landInfoVO.setLandStatus(LandStatusEnum.HAS_RED_PACKET.getStatus()); // 设置土地状态
            Long grownTime = grownTimes.get(i);
            landInfoVO.setGrownNeedTime(grownTime); // 设置成熟需要时间
			String redPacketId = String.valueOf(random.nextDouble());
			redPacketId = redPacketId.substring(2, redPacketId.length());
			landInfoVO.setRedPacketId(redPacketId);
			landInfoVO.setGrownRemainTime(grownTime); // 设置成熟需要时间
            landInfoVos.add(landInfoVO);
            stringRedisTemplate.opsForValue().set(getRedisKeyForConsumerId(redPacketId, appId), "-1", grownTime, TimeUnit.SECONDS); // 机器人用户id对应的Value为-1
        }
        return landInfoVos;
	}

	// 排序
	private List<LandInfoVO> sortList(List<LandInfoVO> landInfoVos){
		Collections.sort(landInfoVos, Comparator.comparing(LandInfoVO::getGrownNeedTime));
		Map<Integer, List<LandInfoVO>> redStatusMap = landInfoVos.stream().collect(Collectors.groupingBy(x->x.getRedPacketStatus()));
		List<LandInfoVO> newLandInfoList = new ArrayList<>(landInfoVos.size());
		if(CollectionUtils.isNotEmpty(redStatusMap.get(RedPacketStatusEnum.GROWN.getStatus()))){
			newLandInfoList.addAll(redStatusMap.get(RedPacketStatusEnum.GROWN.getStatus()));
		}
		if(CollectionUtils.isNotEmpty(redStatusMap.get(RedPacketStatusEnum.GROW_UP.getStatus()))){
			newLandInfoList.addAll(redStatusMap.get(RedPacketStatusEnum.GROW_UP.getStatus()));
		}
		if(CollectionUtils.isNotEmpty(redStatusMap.get(RedPacketStatusEnum.STEAL.getStatus()))){
			newLandInfoList.addAll(redStatusMap.get(RedPacketStatusEnum.STEAL.getStatus()));
		}
		return newLandInfoList;
	}

	// 校验活动是否存在，是否开启
	private SeedRedPacketDto checkActivity(Long activityId) throws BizException {
		SeedRedPacketDto seedRedPacketDto = getActivityInfoCacheById(activityId);
		if (null == seedRedPacketDto) {
			throw new BizException("活动不存在");
		}
		if(!Objects.equals(YesOrNoEnum.YES.getCode(), seedRedPacketDto.getStatus())){
			throw new BizException("活动已关闭");
		}
		if (CollectionUtils.isEmpty(seedRedPacketDto.getGrownTimeList())) {
			logger.info("种红包活动{}配置异常，配置信息：{}", activityId, JSON.toJSON(seedRedPacketDto));
			throw new BizException("活动配置异常");
		}
		return seedRedPacketDto;
	}

	// 获取app映射的种红包活动id
	private Long getRelateActId(Long appId, Long activityId) throws BizException{
		Long relateActId = getAppRelateSeedAct(appId, activityId);
		if(null == relateActId){
			throw new BizException("活动未映射");
		}
		return relateActId;
	}

	// 增加用户连续登录天数
	private void updateLoginDays(Long consumerId, Long appId) {
		String today = DateUtil.getDayStr(new Date());
		String todayLoginDaysKey = getRedisKeyForLoginDays(consumerId, appId, today);
		String todayLoginDays = stringRedisTemplate.opsForValue().get(todayLoginDaysKey);
		if (null != todayLoginDays) {
			// 今日已更新过登录天数，不再更新
			return;
		}
		Date yesterday = DateUtil.daysAddOrSub(new Date(), -1);
		String yesterdayStr = DateUtil.getDayStr(yesterday);
		String yesterdayLoginDays = stringRedisTemplate.opsForValue().get(getRedisKeyForLoginDays(consumerId, appId, yesterdayStr));
		if(null == yesterdayLoginDays){
			// 之前没登录过，连续登录天数设为1
			stringRedisTemplate.opsForValue().set(todayLoginDaysKey, "1", getSecondFor2Days(), TimeUnit.SECONDS);
		}else{
			// 之前有登陆过，连续登录天数累加
			Integer loginDays = Integer.valueOf(yesterdayLoginDays) + 1;
			stringRedisTemplate.opsForValue().set(todayLoginDaysKey, loginDays.toString(), getSecondFor2Days(), TimeUnit.SECONDS);
		}
	}

	// 从hbase获取土地信息
	private LandInfoDto getUserLandInfoByUserId(String vkey, Long consumerId) throws BizException {
		if (StringUtils.isBlank(vkey) || null == consumerId) {
			return null;
		}
		HbaseKvParam hbaseKvParam = new HbaseKvParam();
		hbaseKvParam.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		hbaseKvParam.setVkey(vkey);
		hbaseKvParam.setConsumerId(consumerId);
		DuibaKvtableDto duibaKvtableDto = remoteDuibaKvtableService.findByVkey(hbaseKvParam);
		if (null == duibaKvtableDto || StringUtils.isBlank(duibaKvtableDto.getStrValue())) {
			logger.info("种红包-用户'{}'土地信息不存在, vkey:{}", consumerId, vkey);
			throw new BizException("用户土地信息不存在");
		}
		LandInfoDto landInfoDto = JSON.parseObject(duibaKvtableDto.getStrValue(), LandInfoDto.class);
		if (null == landInfoDto) {
			logger.info("种红包-用户'{}'土地信息不存在, vkey:{}, DuibaKvtableDto:{}", consumerId, vkey, JSON.toJSON(duibaKvtableDto));
			throw new BizException("用户土地信息不存在");
		}
		return landInfoDto;
	}

	// 从hbase批量获取土地信息
	private List<LandInfoDto> getBatchUserLandInfo(List<String> vkeyList, Long consumerId) {
		if (org.springframework.util.CollectionUtils.isEmpty(vkeyList) || null == consumerId) {
			return new ArrayList<>();
		}
		List<HbaseVKeyParam> vKeyParams = new ArrayList<>();
		for (String vkey : vkeyList) {
			HbaseVKeyParam hbaseVKeyParam = new HbaseVKeyParam();
			hbaseVKeyParam.setVkey(vkey);
			hbaseVKeyParam.setConsumerId(consumerId);
			hbaseVKeyParam.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
			vKeyParams.add(hbaseVKeyParam);
		}
		List<DuibaKvtableDto> duibaKvtableDtoList = remoteDuibaKvtableService.batchFindByVkeys(vKeyParams);
		if (org.springframework.util.CollectionUtils.isEmpty(duibaKvtableDtoList)) {
			return new ArrayList<>();
		}
		List<LandInfoDto> landInfoDtoList = new ArrayList<>();
		for (DuibaKvtableDto duibaKvtableDto : duibaKvtableDtoList) {
			if (StringUtils.isNotBlank(duibaKvtableDto.getStrValue())) {
				landInfoDtoList.add(JSON.parseObject(duibaKvtableDto.getStrValue(), LandInfoDto.class));
			}
		}
		return landInfoDtoList;
	}

	// 初始化土地，1号土地直接解锁，其他土地都是未解锁
	private List<LandInfoDto> initUserLandInfo(String redPacketId, Long amount) {
		List<LandInfoDto> landInfoDtoList = new ArrayList<>();
		// 第一块土地，初始即已解锁，且赠送一个可收割的红包
		LandInfoDto firstLand = new LandInfoDto();
		firstLand.setLandId(1L);
		firstLand.setLandStatus(LandStatusEnum.HAS_RED_PACKET.getStatus());
		firstLand.setRedPacketId(redPacketId);
		firstLand.setSeedTime(Calendar.getInstance().getTimeInMillis());
		firstLand.setGrownNeedTime(0L);
		firstLand.setAmount(amount);
		landInfoDtoList.add(firstLand);
		for (int i = 2; i < LAND_NUMBER + 1; i++) {
			LandInfoDto landInfoDto = new LandInfoDto();
			landInfoDto.setLandId(Long.valueOf(i));
			landInfoDto.setLandStatus(LandStatusEnum.LOCK.getStatus());
			landInfoDtoList.add(landInfoDto);
		}
		return landInfoDtoList;
	}

	// 转换土地返回对象
	private List<LandInfoVO> convertLandInfoList(List<LandInfoDto> landInfoDtoList) {
		List<LandInfoVO> landInfoVOList = new ArrayList<>();
		if (CollectionUtils.isEmpty(landInfoDtoList)) {
			return landInfoVOList;
		}
		for (LandInfoDto landInfoDto : landInfoDtoList) {
			LandInfoVO landInfoVO = new LandInfoVO();
			landInfoVO.setLandId(landInfoDto.getLandId());
			landInfoVO.setLandStatus(landInfoDto.getLandStatus());
			landInfoVO.setTargetValue(landInfoDto.getTargetValue());
			landInfoVO.setTaskType(landInfoDto.getTaskType());
			landInfoVO.setLatestValue(landInfoDto.getLatestValue());
			if (StringUtils.isNotBlank(landInfoDto.getRedPacketId())) {
				landInfoVO.setRedPacketId(landInfoDto.getRedPacketId());
				landInfoVO.setGrownNeedTime(landInfoDto.getGrownNeedTime());
				Long remainTime = landInfoDto.getSeedTime() + landInfoDto.getGrownNeedTime() * 1000 - Calendar.getInstance().getTimeInMillis();
				landInfoVO.setRedPacketStatus(remainTime > 0 ?
						RedPacketStatusEnum.GROW_UP.getStatus() : RedPacketStatusEnum.GROWN.getStatus());
				landInfoVO.setGrownRemainTime(remainTime > 0 ? remainTime / 1000 : 0L);
			}
			landInfoVOList.add(landInfoVO);
		}
		landInfoVOList = landInfoVOList.stream().sorted((x1, x2) -> (int) (x1.getLandId() - x2.getLandId())).collect(Collectors.toList());
		return landInfoVOList;
	}

	// 用户标记
	private Boolean markUser(Long consumerId, Long appId, Long activityId, String loginMarkKey, WalletAccountDto walletAccountDto) {
		// 如果未被标记，查询是否还有可标记名额
		Integer markCount = remoteAppSeedRedPacketMarkService.getCountByAppAndActivity(appId, activityId);
		if (null != markCount && null != walletAccountDto) {
			Integer totalCount = walletAccountDto.getEveryDayLimit() / walletAccountDto.getWithdrawLimit();
            Integer drawCashCount = remoteCashDrawService.getDrawCashCount(appId);// 获取当月提现次数
            totalCount -= drawCashCount == null ? 0 : drawCashCount;
			if (totalCount.compareTo(markCount) > 0) {
				SeedRedPacketUserMarkDto seedRedPacketUserMarkDto = new SeedRedPacketUserMarkDto();
				seedRedPacketUserMarkDto.setConsumerId(consumerId);
				seedRedPacketUserMarkDto.setAppId(appId);
				seedRedPacketUserMarkDto.setActivityId(activityId);
				remoteAppSeedRedPacketMarkService.add(seedRedPacketUserMarkDto);
				stringRedisTemplate.opsForValue().set(loginMarkKey, "1", getSecondFor23Days(), TimeUnit.SECONDS);
				return true;
			}
		}
		return false;
	}

	/**
	 * 是否可以标记用户
	 */
	private Boolean canMarked(Long consumerId, Long appId, String loginMarkKey, SeedRedPacketDto activity) {
		// 已标记人数
		Integer markCount = remoteAppSeedRedPacketMarkService.getCountByAppAndActivity(appId, activity.getId());
		// 标记人数上限
		Integer limit = activity.getBonusNumber();
		if (null != markCount && null != limit && limit > markCount) {
			SeedRedPacketUserMarkDto seedRedPacketUserMarkDto = new SeedRedPacketUserMarkDto();
			seedRedPacketUserMarkDto.setConsumerId(consumerId);
			seedRedPacketUserMarkDto.setAppId(appId);
			seedRedPacketUserMarkDto.setActivityId(activity.getId());
			remoteAppSeedRedPacketMarkService.add(seedRedPacketUserMarkDto);
			stringRedisTemplate.opsForValue().set(loginMarkKey, "1", getSecondFor23Days(), TimeUnit.SECONDS);
			return true;
		}
		return false;
	}

	// 获取用户当天从种红包活动中可得总金额：value0=true，代表剩余可得金额为0，特殊处理
	private Pair<Boolean, Long> setTotalMoneyForSeedEveryday(Boolean marked, ConsumerDto consumerDto, Long appId, Long activityId,
			WalletAccountDto walletAccountDto, SeedRedPacketDto seedRedPacketDto) {
		String todayTotalMoney = stringRedisTemplate.opsForValue().get(getRedisKeyForTodayTotalMoney(consumerDto.getId(), appId, activityId));
		Long value = redisTemplate.opsForValue().get(getRedisKey(appId));
		if(null != todayTotalMoney && value != null){
			// 今日种红包活动可得总金额有值，按正常逻辑处理
			return Pair.with(false, Long.valueOf(todayTotalMoney));
		}
		Long consumerId = consumerDto.getId();
		// 昨日账户余额
		Long yesterdayBalance = remoteConsumerAccountService.selectYesterdayAccount(consumerId, appId);
		if(yesterdayBalance == null){
			yesterdayBalance = 0L;
		}
		// 获取今日之前尚未收割的种红包金额
		Long hasNotReapMoney = 0L;
		List<ActivityOrderDto> activityOrderDtoList = remoteActivityOrderService.findByConsumerAndActivityAndStatus(consumerId, activityId,
				ActivityOrderDto.TypeSeedRedPacket, ActivityOrderDto.ConsumeCreditsSuccess, ActivityOrderDto.ExchangeWait);
		if (CollectionUtils.isNotEmpty(activityOrderDtoList)) {
			hasNotReapMoney = activityOrderDtoList.stream().mapToLong(x -> x.getAddCredits()).sum();
		}
		// 游戏中心配置金额
		Long gameCenterAmount = remoteSeasonConfigService.findAllCurrentMonthSeasonConfigDtoByAppId(appId);
		gameCenterAmount = gameCenterAmount == null ? 0 : gameCenterAmount;
		// 计算本周期剩余可得总金额：未标记用户总额减1块钱（100分），让他凑不齐可提现金额
		Long currentMonthTotalAmount = marked ?
				walletAccountDto.getWithdrawLimit() - gameCenterAmount - yesterdayBalance - hasNotReapMoney
				: walletAccountDto.getWithdrawLimit() - gameCenterAmount - 100 - yesterdayBalance - hasNotReapMoney;
		Long monthBudget = seedRedPacketDto.getMonthBudget();
		if(seedRedPacketDto.getStealRedPacketSwitch() && monthBudget != null){
			currentMonthTotalAmount -= monthBudget;// 偷红包月预算
		}
		if (currentMonthTotalAmount.compareTo(0L) <= 0) {
			// 本周期可得总金额小于等于0，按特殊逻辑处理，被标记用户单个红包额度随机范围[0.01,0.03]；未被标记用户中奖金额=0
			return Pair.with(true, marked ? random.nextInt(3) + 1 : 0L);
		}
		// 获取本月已累计签到天数
		Long conLoginDays = walletAccountHbaseService.getLoginDay(consumerId);
		Integer connectLoginDays = conLoginDays == null ? 0 : conLoginDays.intValue();
		// 计算剩余天数
		Integer remainLoginDaysReal = marked ?
				walletAccountDto.getWithdrawDay() - 1 - (connectLoginDays - 1)
				: DateUtil.getDaysForCurrentMonth() - (connectLoginDays - 1);
		// 如果剩余天数小于1，按1算，使得分母等于1，即达到签到天数后再签到，每次领取都是得剩余可得金额
		Integer remainLoginDays = remainLoginDaysReal.compareTo(1) < 0 ? 1 : remainLoginDaysReal;
		// 计算本周期剩余每天可得总金额（新人红包 + PK红包 + 种红包）
		Long todayTotalAmount = currentMonthTotalAmount / remainLoginDays;
		// 计算今日种红包活动可得总金额
		Long todaySeeRedPacketTotalAmount = getTodaySeeRedPacketTotalAmount(todayTotalAmount, consumerId, appId);
		if(todaySeeRedPacketTotalAmount.compareTo(0L) <= 0){
			// 今日其他活动可得金额超过今日可得总额，按特殊逻辑处理，无论用户是否被标记，单个红包金额均为为[0.01,0.03]
			return Pair.with(true, random.nextInt(3) + 1L);
		}
		stringRedisTemplate.opsForValue().set(getRedisKeyForTodayTotalMoney(consumerId, appId, activityId), todaySeeRedPacketTotalAmount.toString(), 1, TimeUnit.DAYS);
		return Pair.with(false, todaySeeRedPacketTotalAmount);
	}

	// 计算今日种红包活动可得总金额
	private Long getTodaySeeRedPacketTotalAmount(Long todayTotalAmount, Long consumerId, Long appId){
		// 获取今日已领新人红包金额
		Long newUserRedPacketAmount = remoteConsumerAccountService
				.selectDailyRecord(consumerId, AccountTypeEnum.ALL_WALLET, AccountBizTypeEnum.SEVEN_DAY_REWARD);
		newUserRedPacketAmount = newUserRedPacketAmount == null ? 0L : newUserRedPacketAmount;
		// 获取今日已领PK活动红包金额
		Long pkBetAmount = remoteConsumerAccountService
				.selectDailyRecord(consumerId, AccountTypeEnum.ALL_WALLET, AccountBizTypeEnum.PK_ACTIVITY_WALLET);
		pkBetAmount = pkBetAmount == null ? 0L : pkBetAmount;
		return todayTotalAmount.intValue() - newUserRedPacketAmount - pkBetAmount - getAmountFromPK(appId);
	}

    private String getRedisKey(Long appId) {
		return RedisKeySpace.K096.toString() + appId;
	}

	/**
	 * 计算红包可得金额，以分为单位
	 * 计算公式：
	 * 被标记：每日中红包总金额=（可提现金额- 游戏中心配置金额 -昨日账户余额 - 未领取红包金额）/[提现所需连续签到天数-1-（当月已连续签到天数-1）]-今日获得的其余红包总和（若PK未开奖按中奖算）
	 * 未被标记：每日中红包总金额=（可提现金额- 游戏中心配置金额 -1-昨日账户余额 - 未领取红包金额）/[当月天数-(当月已连续签到天数-1)]-今日获得的其余红包总和（若PK未开奖按中奖算）
	 * 单个红包金额规则：中红包概率100%
	 * 被标记： 当用户余额<可提现金额时，单个红包额度随机范围[0.01,可中种红包金额-0.01*剩余可种红包个数]，
	 * 当用户余额>=可提现金额时，单个红包额度随机范围[0.01,0.03]，
	 * 可中红包金额= 每日种红包可得总金额-已中红包金额，
	 * 最后一个红包金额=可中红包金额，
	 * 若范围上限<0.01，按0.01发放，
	 * 未被标记：可中红包金额= 每日种红包可得总金额-已中红包金额，
	 * 单个红包额度随机范围[0.01,可中红包金额-0.01*剩余可中红包个数]，
	 * 若范围上限<0.01，中奖金额=0
	 */
	private long getCanGetMoney(Boolean marked, ConsumerDto consumerDto, Long appId, Long activityId, WalletAccountDto walletAccountDto,
			Integer seedNumber, SeedRedPacketDto seedRedPacketDto) {
		Long consumerId = consumerDto.getId();
        String canDrawNumNowStr = stringRedisTemplate.opsForValue().get(getRedisKeyForDrawNum(consumerId, appId)); // 剩余可中奖可数
		Integer canDrawNumNow = StringUtils.isBlank(canDrawNumNowStr) ? 0 : Integer.valueOf(canDrawNumNowStr);
        if(canDrawNumNow.compareTo(0) <= 0){
	        return 0L;  // 当前可中奖个数<=0时，红包金额为0
        }
		// 获取今日剩余可种植次数
		Integer canPlantNumNow = null == seedRedPacketDto.getPlantNumDaily() ? null : seedRedPacketDto.getPlantNumDaily().intValue() - seedNumber;
		if((null == canPlantNumNow || canPlantNumNow.compareTo(canDrawNumNow) > 0) && !checkIfWin()){
			return 0L;  // 种植个数为无限制/当前可种植个数>当前可中奖个数，有50%的概率不中奖
		}
		// 获取今日种红包活动可得总金额
		Pair<Boolean, Long> todaySeeRedPacketTotalAmount = setTotalMoneyForSeedEveryday(marked, consumerDto, appId, activityId, walletAccountDto, seedRedPacketDto);
		// 如果value0=true，说明种红包活动可得金额为0，按特殊逻辑处理，直接返回特殊逻辑计算结果value1；
		// 如果value0=false，说明种红包活动仍旧可以得到金额，正常处理，value1为今日种红包活动可得总金额
		if (todaySeeRedPacketTotalAmount.getValue0()) {
			return todaySeeRedPacketTotalAmount.getValue1();
		}
		// 获取今日已种的红包可得金额
		String canGetSeedRedPacketAmountStr = stringRedisTemplate.opsForValue().get(getRedisKeyForCanGetMoney(consumerId, appId, activityId));
		Long canGetSeedRedPacketAmount = null == canGetSeedRedPacketAmountStr ? 0L : Long.valueOf(canGetSeedRedPacketAmountStr);
		// 计算可中红包金额：=每日种红包可得总金额-已种的红包可得金额
		Long maxAmount = todaySeeRedPacketTotalAmount.getValue1() - canGetSeedRedPacketAmount;
		// 如果可中红包金额小于1分
		if (maxAmount.compareTo(1L) < 0) {
			return getMoney(marked, consumerId);
		}
		// 被标记 and 是最后一次可中奖或最后一次可种植 发完红包金额
		if(marked && (Objects.equals(canDrawNumNow, 1) || Objects.equals(canPlantNumNow, 1))){
			return maxAmount;
		}
		// 计算红包额度随机范围的上限：可中红包金额 - 0.01 * 剩余可中红包个数
		Long currentMaxAmount = maxAmount - 1 * canDrawNumNow;
		// 如果上限1分，被标记用户发1分；未被标记用户中奖金额为0
		if (currentMaxAmount.compareTo(1L) < 0) {
			return getMoney(marked, consumerId);
		}
		// 红包额度随机范围[0.01,可中红包金额-0.01*剩余可中红包个数]，此处是以分为单位，所以没有除以100
		return this.random.nextInt(currentMaxAmount.intValue()) + 1L;
	}

	// 当红包金额小于等于0时，如果有领新人红包，按特殊逻辑，红包金额小于等于0时，单个红包金额均为为[0.01,0.03]
	private Long getMoney(Boolean marked, Long consumerId){
		// 获取今日已领新人红包金额
		Long newUserRedPacketAmount = remoteConsumerAccountService
				.selectDailyRecord(consumerId, AccountTypeEnum.ALL_WALLET, AccountBizTypeEnum.SEVEN_DAY_REWARD);
		if(null != newUserRedPacketAmount && newUserRedPacketAmount.compareTo(0L) > 0 ){
			// 如果有领新人红包，按特殊逻辑，红包金额小于等于0时，单个红包金额均为为[0.01,0.03]
			return random.nextInt(3) + 1L;
		}
		// 没有新人红包，走正常逻辑，被标记用户发1分；未被标记用户中奖金额为0
		return marked ? 1L : 0L;
	}

	// 获取当天尚未开奖的pk投注可领金额
	private Integer getAmountFromPK(Long appId) {
		Date startTime = DateUtil.getDayDate(new Date());
		Date endTime = DateUtil.getDayDate(DateUtil.daysAddOrSub(new Date(), 1));
		List<BetConfigDto> betConfigDtoList = getBetConfigCache(appId, ConfigStatusEnum.OPEN.getCode(), BetBonusTypeEnum.POCKET.getCode(), startTime,
				endTime);
		if (CollectionUtils.isEmpty(betConfigDtoList)) {
			return 0;
		}
		Integer amount = 0;
		for (BetConfigDto betConfigDto : betConfigDtoList) {
			if (StringUtils.isNotBlank(betConfigDto.getBonusAmount())) {
				BigDecimal bonusAmount = new BigDecimal((betConfigDto.getBonusAmount())).setScale(2, BigDecimal.ROUND_DOWN);
				amount = amount + bonusAmount.multiply(new BigDecimal(100)).intValue();
			}
		}
		return amount;
	}

	// 更新用户土地信息
	private boolean updateLandInfo(String vkey, Long consumerId, LandInfoDto landInfoDto) {
		DuibaKvtableDto duibaKvtableNew = new DuibaKvtableDto();
		duibaKvtableNew.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		duibaKvtableNew.setVkey(vkey);
		duibaKvtableNew.setConsumerId(consumerId);
		duibaKvtableNew.setStrValue(JSON.toJSONString(landInfoDto));
		return remoteDuibaKvtableService.upsertKVTable(duibaKvtableNew);
	}

	// 批量更新用户土地信息
	private boolean batchUpdateLandInfo(List<LandInfoDto> landInfoDtoList, Long consumerId, Long appId, Long activityId) {
		List<DuibaKvtableDto> duibaKvtableDtoList = new ArrayList<>();
		for (LandInfoDto landInfoDto : landInfoDtoList) {
			DuibaKvtableDto duibaKvtableDto = new DuibaKvtableDto();
			duibaKvtableDto.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
			duibaKvtableDto.setVkey(getHbaseKeyForUserLands(appId, activityId, landInfoDto.getLandId()));
			duibaKvtableDto.setConsumerId(consumerId);
			duibaKvtableDto.setStrValue(JSON.toJSONString(landInfoDto));
			duibaKvtableDtoList.add(duibaKvtableDto);
		}
		return remoteDuibaKvtableService.batchUpsert(duibaKvtableDtoList);
	}

	// 创建子订单
	private ActivityOrderDto createOrder(ConsumerDto consumerDto,
			Long activityId,
			Long seedNeedCredits,
			Long landId,
			Long grownTime,
			Long redPacketAmount,
			Long floorId,
			Integer orderStatus) throws BizException {
		ActivityOrderDto orderDto = new ActivityOrderDto();
		orderDto.setConsumerId(consumerDto.getId());
		orderDto.setAppId(consumerDto.getAppId());
		orderDto.setPartnerUserId(consumerDto.getPartnerUserId());
		orderDto.setDuibaActivityId(activityId);
		orderDto.setActivityType(ActivityOrderDto.TypeSeedRedPacket);
		orderDto.setConsumeCredits(seedNeedCredits);
		orderDto.setConsumeCreditsStatus(orderStatus);
		orderDto.setExchangeStatus(ActivityOrderDto.ExchangeWait);
		orderDto.setCouponId(landId);
		orderDto.setFacePrice(grownTime.toString());
		orderDto.setAddCredits(redPacketAmount);
		orderDto.setGid(floorId);
		orderDto.setIp(RequestLocal.getIp());
		Date now = new Date();
		orderDto.setGmtCreate(now);
		orderDto.setGmtModified(now);
		String orderNum = null;
		try {
			orderNum = remoteActivityOrderService.createOrder(orderDto).getResult();
		} catch (Exception e) {
			logger.warn("创建订单失败", e);
			throw new BizException("创建订单失败");
		}
		orderDto.setOrderNum(orderNum);
		return orderDto;
	}

	// 种红包扣积分发消息
	private void subCreditsAndSendMessage(
			ConsumerDto consumerDto,
			AppSimpleDto appSimpleDto,
			String orderNum,
			String title,
			Long relateActId,
			Long seedNeedCredits,
			Long landId,
			Long amount,
			Long grownNeedTime) throws BizException {
		try {
			//先本地扣积分
			remoteConsumerService.decrementCredits(consumerDto.getId(), seedNeedCredits);
		} catch (Exception e) {
			logger.warn("种红包-扣除本地积分失败", e);
		}
		SubCreditsMsgDto subCreditsMsgDto = getSubCreditsMsg(consumerDto, appSimpleDto, orderNum, title, relateActId,
				seedNeedCredits, landId, amount, grownNeedTime);
		try {
			Message message = new Message(rocketMqMessageTopic.getSubCreditsTopic(),
					SubCreditsMsgDto.encode(subCreditsMsgDto));
			SendResult result = rocketMQProducer.send(message);
			if (!SendStatus.SEND_OK.equals(result.getSendStatus())) {
				logger.warn("种红包-扣积分失败{}", message);
				rollBackOrder(orderNum, consumerDto.getId(), seedNeedCredits);
				throw new BizException("扣积分失败");
			}
		} catch (Exception e) {
			logger.warn("种红包-扣除积分mq发送失败", e);
			rollBackOrder(orderNum, consumerDto.getId(), seedNeedCredits);
			throw new BizException("扣积分消息发送失败");
		}
	}

	// 种红包活动扣积分记录
	private void seedCreditsRecord(Long consumerId, Long appId, Long activityId, Long subCredits) {
		String vkey = getHbaseKeyForUsedCredits(appId, activityId);
		HbaseKvParam hbaseKvParam = new HbaseKvParam();
		hbaseKvParam.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		hbaseKvParam.setVkey(vkey);
		hbaseKvParam.setConsumerId(consumerId);
		DuibaKvtableDto oldDto = remoteDuibaKvtableService.findByVkey(hbaseKvParam);
		Long totalCredits = null == oldDto || null == oldDto.getIntValue() ? subCredits : oldDto.getIntValue() + subCredits;

		DuibaKvtableDto newDto = new DuibaKvtableDto();
		newDto.setVkey(vkey);
		newDto.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		newDto.setConsumerId(consumerId);
		newDto.setIntValue(totalCredits);
		remoteDuibaKvtableService.upsertKVTable(newDto);
	}

	// 种红包活动总种植次数记录
	private void seedPlantRecord(Long consumerId) {
		String vkey = getHbaseKeyForPlantCount(consumerId);
		HbaseKvParam hbaseKvParam = new HbaseKvParam();
		hbaseKvParam.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		hbaseKvParam.setVkey(vkey);
		hbaseKvParam.setConsumerId(consumerId);
		DuibaKvtableDto oldDto = remoteDuibaKvtableService.findByVkey(hbaseKvParam);
		Long totalPlant = null == oldDto || null == oldDto.getIntValue() ? 1L : oldDto.getIntValue() + 1L;

		DuibaKvtableDto newDto = new DuibaKvtableDto();
		newDto.setVkey(vkey);
		newDto.setKeySpaceEnum(HbaseKeySpaceEnum.K01);
		newDto.setConsumerId(consumerId);
		newDto.setIntValue(totalPlant);
		remoteDuibaKvtableService.upsertKVTable(newDto);
	}

	// 种红包扣积分发消息异常回退处理
	private void rollBackOrder(String orderNum, Long consumerId, Long credits) {
		logger.info("种红包-用户{}扣积分失败回退，子订单'{}'状态更新为处理失败，并将扣掉的本地积分加回来", consumerId, orderNum);
		// 将订单状态更新为处理失败
		remoteActivityOrderService.consumeCreditsFail(
				orderNum,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				"扣积分失败",
				null,
				null);
		// 将扣掉的本地积分加回来
		remoteConsumerService.increaseCredits(consumerId, credits);
	}

	// 构建扣积分消息
	private SubCreditsMsgDto getSubCreditsMsg(
			ConsumerDto consumerDto,
			AppSimpleDto appSimpleDto,
			String orderNum,
			String title,
			Long relateActId,
			Long seedNeedCredits,
			Long landId,
			Long amount,
			Long grownNeedTime) {
		RequestParams requestParams = RequestParams.parse(RequestLocal.getRequest());

		CreditConsumeParams creditConsumeParams = new CreditConsumeParams();
		creditConsumeParams.setAppKey(appSimpleDto.getAppKey());
		creditConsumeParams.setFacePrice(0);
		creditConsumeParams.setActualPrice(0);
		creditConsumeParams.setCredits(seedNeedCredits);
		creditConsumeParams.setIp(requestParams.getIp());
		creditConsumeParams.setTransfer(requestParams.getTransfer());
		creditConsumeParams.setType(SubCreditsOuterType.HDTOOL.getCode());
		creditConsumeParams.setUid(consumerDto.getPartnerUserId());
		creditConsumeParams.setOrderNum(SubCreditsType.SEED_RED_PACKET + "-" + orderNum);
		creditConsumeParams.setDescription(title);

		Map<String, String> map = creditConsumeParams.toRequestMap(appSimpleDto.getAppSecret());

		SubCreditsMsgDto subCreditsMsg = new SubCreditsMsgDto();
		subCreditsMsg.setAppId(appSimpleDto.getId());
		subCreditsMsg.setAppSecret(appSimpleDto.getAppSecret());
		subCreditsMsg.setConsumerId(requestParams.getConsumerId());
		subCreditsMsg.setCreditConsumeParams(creditConsumeParams);
		HashMap<String, String> hashMap = new HashMap<>();
		hashMap.put("activityId", String.valueOf(relateActId));
		hashMap.put("landId", landId.toString());
		hashMap.put("landStatus", LandStatusEnum.HAS_RED_PACKET.getStatus().toString());
		hashMap.put("redPacketId", orderNum);
		hashMap.put("amount", amount.toString());
		hashMap.put("seedTime", String.valueOf(Calendar.getInstance().getTimeInMillis()));
		hashMap.put("grownNeedTime", grownNeedTime.toString());
		hashMap.put("seedCreditsPrice", seedNeedCredits.toString());
		hashMap.put("partnerUserId", consumerDto.getPartnerUserId());
        if (StringUtils.isNotBlank(creditConsumeParams.getTransfer())) {
            hashMap.put("transfer", creditConsumeParams.getTransfer());
        }
		subCreditsMsg.setParams(hashMap);
		subCreditsMsg.setRelationId(orderNum);
		subCreditsMsg.setRelationType(SubCreditsType.SEED_RED_PACKET);
		subCreditsMsg.setCreditsConsumeRequestUrl(appSimpleDto.getCreditsConsumeRequestUrl());
		subCreditsMsg.setAuthParams(map);
		subCreditsMsg.setCallbackTopic(rocketMqMessageTopic.getSeedRedPacketSubCreditsCallback());

		return subCreditsMsg;
	}

	// 收割红包，将红包金额存入账户
	private boolean reapRedPacketToAccount(Long appId, ConsumerDto consumerDto, String redPacketId, Long amount, LandInfoDto landInfoDto, String vkey, SeedRedPacketDto activity, boolean isNewVersion) throws BizException {
		AccountAmountModifyRequest request = new AccountAmountModifyRequest();
		request.setAppId(appId);
		request.setConsumerId(consumerDto.getId());
		request.setAccActType(AccountActionTypeEnum.ACTION_IN);
		request.setPartnerUserId(consumerDto.getPartnerUserId());
		request.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
		request.setBizType(AccountBizTypeEnum.SEED_RED_PACKET);
		request.setBizId(redPacketId);
		request.setBizDescription("种红包活动用户领取红包收入");
		request.setChangeMoney(amount);
		request.setAccountType(AccountTypeEnum.ALL_WALLET);
		request.setNeedLog(Boolean.TRUE);
		AccountModifyResponse response = remoteConsumerAccountService.accountModify(request);

		if (null != response && response.isSuccess()) {
		    if (isNewVersion) {
                // 将收割金额记录起来
                inputTotalBonus(activity, amount, consumerDto.getId());
                inputDailyBonus(activity.getId(), amount, consumerDto.getId());
            }
			return true;
		}

		if (null != response && !response.isSuccess() && response.getErrorCode() == AccountModifyResponse.ERROR_CODE_FOR_UN_BUDGET) {
			logger.warn("种红包-领取红包'{}'异常，红包金额存入账户失败，返回信息：{}，传参：{}", redPacketId, JSON.toJSON(response), JSON.toJSON(request));
			return false;
		}

		rollbackStatus(appId, consumerDto, redPacketId, landInfoDto, vkey);
		throw new BizException("红包入账失败");
	}

	private boolean reapRedPacketToAccountNew(boolean marked, Long appId, ConsumerDto consumerDto, String redPacketId, Long amount, LandInfoDto landInfoDto, String vkey, SeedRedPacketDto activity) throws BizException {
		AccountModifyResponse response;
		// 自定义账户 走自定义账户充值逻辑
		if (Objects.equals(activity.getAccountType(), 1)) {
			// 此校验理论上来说可以放到最外面 但是....SGSC
			if (!checkTotalBudge(marked, activity, amount, consumerDto.getId())) {
				return false;
			}
			AccRechargeRequest r = new AccRechargeRequest();
			r.setAppId(appId);
			r.setConsumerId(consumerDto.getId());
			r.setPartnerUserId(consumerDto.getPartnerUserId());
			r.setBizType(AccountBizTypeEnum.SEED_RED_PACKET);
			r.setBizId(redPacketId);
			r.setBizDescription("种红包活动用户领取红包收入");
			r.setChangeMoney(amount);
			// 需要先查一下账户 查到了才能入账
			RedAccCustomDto accCustom = activityCommCacheService.findRedAccCustomByKey(appId, ActivityUniformityTypeEnum.SeedRedPacket, activity.getId());
			if (accCustom == null) {
				return false;
			}
			r.setRelId(accCustom.getId());
			r.setNeedLog(true);
			response = remoteRedAccCustomService.redAccActBonusRechargeForScrapeRedPacket(r);
		} else if (activity.getStartTime().after(refreshConstant.getSeedRedPacketNewVersionDate())) {
			// 标记逻辑移交至总账户
			AccPeriodRechargeRequest r = new AccPeriodRechargeRequest();
			r.setActType(ActivityUniformityTypeEnum.SeedRedPacket);
			r.setActId(activity.getId());
			r.setAppId(appId);
			r.setConsumerId(consumerDto.getId());
			r.setPartnerUserId(consumerDto.getPartnerUserId());
			r.setBizType(AccountBizTypeEnum.SEED_RED_PACKET);
			r.setBizId(redPacketId);
			r.setBizDescription("种红包活动用户领取红包收入");
			r.setChangeMoney(amount);
			r.setNeedLog(Boolean.TRUE);
			response = remoteRedAccPeriodService.redAccActBonusRecharge(r);
		} else {
			AccountAmountModifyRequest request = new AccountAmountModifyRequest();
			request.setAppId(appId);
			request.setConsumerId(consumerDto.getId());
			request.setAccActType(AccountActionTypeEnum.ACTION_IN);
			request.setPartnerUserId(consumerDto.getPartnerUserId());
			request.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
			request.setBizType(AccountBizTypeEnum.SEED_RED_PACKET);
			request.setBizId(redPacketId);
			request.setBizDescription("种红包活动用户领取红包收入");
			request.setChangeMoney(amount);
			request.setAccountType(AccountTypeEnum.ALL_WALLET);
			request.setNeedLog(Boolean.TRUE);
			response = remoteConsumerAccountService.accountModify(request);
		}
		if (null != response && response.isSuccess()) {
			// 将收割金额记录起来
			inputTotalBonus(activity, amount, consumerDto.getId());
			inputDailyBonus(activity.getId(), amount, consumerDto.getId());
			return true;
		}
		if (null != response && !response.isSuccess() && Objects.equals(response.getErrorCode(), AccountModifyResponse.ERROR_CODE_FOR_UN_BUDGET)) {
			logger.warn("种红包-领取红包'{}'异常，红包金额存入账户失败，返回信息：{}", redPacketId, JSON.toJSON(response));
			return false;
		}
		rollbackStatus(appId, consumerDto, redPacketId, landInfoDto, vkey);
		throw new BizException("红包入账失败");
	}

	private void rollbackStatus(Long appId, ConsumerDto consumerDto, String redPacketId, LandInfoDto landInfoDto, String vkey) {
		logger.warn("种红包-领取红包,红包入账失败 回滚红包状态 id={}", redPacketId);
		// 回滚订单红包收割状态
		DubboResult<Boolean> rollbackResult = remoteActivityOrderService.exchangeStatusToWait(redPacketId);
		if (null == rollbackResult || !rollbackResult.isSuccess()
                || null == rollbackResult.getResult() || !rollbackResult.getResult()) {
            logger.warn("种红包-领取红包'{}'异常，回滚订单红包收割状态失败:{}", redPacketId, JSON.toJSON(rollbackResult));
        }else{
            // 红包状态回滚成功，再回滚土地状态
            boolean updateResult = updateLandInfo(vkey, consumerDto.getId(), landInfoDto);
            if (!updateResult) {
                logger.warn("种红包-app'{}'用户'{}'收割红包'{}'失败后，回滚土地状态失败，土地信息：{}",
                        appId, consumerDto.getId(), redPacketId, JSON.toJSON(landInfoDto));
            }
        }
	}

	// 收割红包，将红包金额存入账户
	private boolean reapRedPacketToAccount(Long appId, ConsumerDto consumerDto, String redPacketId, Long amount) throws BizException {
		AccountAmountModifyRequest request = new AccountAmountModifyRequest();
		request.setAppId(appId);
		request.setConsumerId(consumerDto.getId());
		request.setAccActType(AccountActionTypeEnum.ACTION_IN);
		request.setPartnerUserId(consumerDto.getPartnerUserId());
		request.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
		request.setBizType(AccountBizTypeEnum.STEAL_RED_PACKET);
		request.setBizId(redPacketId + consumerDto.getId());
		request.setBizDescription("偷红包活动用户偷红包领取红包收入");
		request.setChangeMoney(amount);
		request.setAccountType(AccountTypeEnum.ALL_WALLET);
		request.setNeedLog(Boolean.TRUE);
		AccountModifyResponse response = remoteConsumerAccountService.accountModify(request);
		if (null != response && response.isSuccess()) {
			return true;
		}

		if (null != response && !response.isSuccess() && response.getErrorCode() == AccountModifyResponse.ERROR_CODE_FOR_UN_BUDGET) {
			logger.warn("偷红包-领取红包'{}'异常，红包金额存入账户失败，返回信息：{}，传参：{}", redPacketId, JSON.toJSON(response), JSON.toJSON(request));
			return false;
		}
		throw new BizException("红包入账失败");
	}

	// 记录统一访问日志
	private void accessLog(Long activityId, String orderNum, Long seedNeedCredits, Long redPacketAmount, String grownTime){
		AccessLogFilter.putExPair("suc", 1);
		if(null != activityId){
			AccessLogFilter.putExPair("id", activityId);
		}
		if(StringUtils.isNotBlank(orderNum)){
			AccessLogFilter.putExPair("orderNum", orderNum);
		}
		if(null != seedNeedCredits){
			AccessLogFilter.putExPair("consumeCredits", seedNeedCredits);
		}
		if(null != redPacketAmount){
			AccessLogFilter.putExPair("addPrice", redPacketAmount);
		}
		if(StringUtils.isNotBlank(grownTime)){
			AccessLogFilter.putExPair("timeSection", grownTime);
		}
	}

    /**
     * 获取当前时间到下月第一天0分0秒
     */
    public int getToMonthSeconds(){
        Calendar curDate = Calendar.getInstance();
        long nextMonthFirstDayTimeInMillis = DateUtils.getNextMonthFirstDayTimeInMillis();
        return Math.max((int) ((nextMonthFirstDayTimeInMillis - curDate.getTimeInMillis()) / 1000), 1);
    }

    /**
     * 获取当月还剩天数(包括当天)
     */
    public int getRemainDay(){
        Calendar c = Calendar.getInstance();
        int day = c.get(Calendar.DAY_OF_MONTH);
        int actualMaximum = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        int remainDay = actualMaximum - day + 1;
        return remainDay;
    }

	/**
	 * 标记
	 */
	private boolean isMarkedUser(Long consumerId, Long appId, SeedRedPacketDto activity) {
		if (Objects.equals(activity.getAccountType(), 0) && activity.getStartTime().after(refreshConstant.getSeedRedPacketNewVersionDate())) {
			// 标记逻辑移交至总账户
			return remoteRedAccPeriodService.checkMarkUser(appId, consumerId);
		}
		String loginMarkKey = getRedisKeyForSeedRedPacketLoginNew(consumerId, appId, activity.getId());
		String loginMark = stringRedisTemplate.opsForValue().get(loginMarkKey);
		boolean marked = null != loginMark;
		if (marked) {
			stringRedisTemplate.expire(loginMarkKey, getSecondFor23Days(), TimeUnit.SECONDS);
		} else {
			//尝试标记用户
			marked = canMarked(consumerId, appId, loginMarkKey, activity);
		}
		return marked;
	}
}
