package com.duiba.activity.accessweb.service.activityshare.impl;

import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryOrderService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.activityshare.ActivityTitleBannerProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zzy on 2017/9/21.
 */
@Service
public class SingleLotteryTitleBannerProvider implements ActivityTitleBannerProvider {
    @Autowired
    private CommonService commonService;
    @Autowired
    private RemoteSingleLotteryOrderService remoteSingleLotteryOrderService;

    @Override
    public int getCrecordType() {
        return ConsumerExchangeRecordDto.TypeSingleLottery;
    }

    @Override
    public String[] getTitleAndBanner(Long relationId, Long consumerId, Long appId) {
        SingleLotteryOrderDto order = remoteSingleLotteryOrderService.find(relationId);
        if (null == order) {
            return getDefaultResult();
        }
        ItemKeyDto itemKey = commonService.findItemKeyDto(order.getAppItemId(), order.getItemId(), appId);
        return new String[]{order.getPrizeName(), getCandidateBanner(itemKey, getBannerImage(itemKey))};
    }
}
