package com.duiba.activity.accessweb.service.equity;

import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import com.duiba.activity.accessweb.pjv.req.EquityPrizeExchangeReq;
import com.duiba.activity.accessweb.pjv.req.MatchMerchantExchangeReq;
import com.duiba.activity.accessweb.vo.equity.MatchMerchantOrderExchangeVO;

/**
 * @program: activity-access-web
 * @description: 权益奖品业务层
 * @author: Simba
 * @create: 2019-07-11 10:38
 **/
public interface EquityPrizeService {

    /**
     * 获取滑块id
     *
     * @return
     */
    String getCaptchId();

    /**
     * 校验滑块
     *
     * @param validate
     * @param consumerDto
     * @throws BizException
     */
    void verifyCaptch(String validate, ConsumerDto consumerDto) throws BizException;

    /**
     * 兑换并返回所兑换商品标题 - 匹配兑换码兑换
     *
     * @param request
     * @param consumerDto
     * @return
     * @throws BizException
     */
    String exchange(EquityPrizeExchangeReq request, ConsumerDto consumerDto, RequestParams requestParams) throws BizException;

    /**
     * 兑换并返回所兑换商品标题 - 匹配商家码兑换
     *
     * @param request
     * @param consumerDto
     * @return
     * @throws BizException
     */
    MatchMerchantOrderExchangeVO matchMerchantExchange(MatchMerchantExchangeReq request, ConsumerDto consumerDto, RequestParams requestParams) throws BizException;

}

