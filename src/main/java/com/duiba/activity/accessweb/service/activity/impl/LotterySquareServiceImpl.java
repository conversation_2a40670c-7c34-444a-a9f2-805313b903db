package com.duiba.activity.accessweb.service.activity.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareBonusConfigDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareBonusRecordDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareConfigDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareGradientRewardDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareWithdrawConfigDto;
import cn.com.duiba.activity.center.api.enums.ActThrowChannelEnum;
import cn.com.duiba.activity.center.api.enums.LSBonusStyleEnum;
import cn.com.duiba.activity.center.api.enums.LSBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.LSExchangeStatusEnum;
import cn.com.duiba.activity.center.api.enums.LSPrizeTypeEnum;
import cn.com.duiba.activity.center.api.enums.LSShareEffectiveConditonEnum;
import cn.com.duiba.activity.center.api.enums.LimitScopeEnum;
import cn.com.duiba.activity.center.api.params.LotterySquareRecordQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareBonusConfigService;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareGradientRewardService;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareRecordService;
import cn.com.duiba.activity.center.api.tool.RedisKeySpace;
import cn.com.duiba.activity.common.center.api.dto.ConsumerAccountUniquenessKeyDto;
import cn.com.duiba.activity.common.center.api.dto.ConsumerShareCodeInfoDto;
import cn.com.duiba.activity.common.center.api.dto.consumeraccount.ConsumerAccountsDto;
import cn.com.duiba.activity.common.center.api.dto.consumeraccount.ConsumerAccountsLogDto;
import cn.com.duiba.activity.common.center.api.enums.ShareCodeActivityTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountActionTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountSubTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountTypeEnum;
import cn.com.duiba.activity.common.center.api.params.UserInviteParam;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteCashDrawService;
import cn.com.duiba.activity.common.center.api.remoteservice.consumeraccount.RemoteConsumerAccountService;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteNewConsumerShareCodeService;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteSimpleShareCodeService;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteUserShareCodeService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountAmountModifyRequest;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountCashDrawsRequest;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountCashDrawsResponse;
import cn.com.duiba.activity.common.center.api.rsp.consumeraccounts.AccountModifyResponse;
import cn.com.duiba.activity.common.center.api.rsp.sharecode.InviteResponseDto;
import cn.com.duiba.api.bo.mq.alarmsms.LotterySquareBudgetOutAlarmSmsParam;
import cn.com.duiba.api.bo.mq.alarmsms.LotterySquareBudgetWarnAlarmSmsParam;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.addcredits.AddCreditsOuterType;
import cn.com.duiba.api.enums.addcredits.AddCreditsType;
import cn.com.duiba.api.enums.mq.RocketMqTagEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerActivityRelationDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.enums.ConsumerBizJoinEnum;
import cn.com.duiba.consumer.center.api.paramQuery.ConsumerActivityQueryParams;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerActivityRelationService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.AddCreditsParams;
import cn.com.duiba.credits.sdk.AssembleTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.RemainingMoneyDto;
import cn.com.duiba.developer.center.api.domain.enums.saas.redpacketsmanagetool.PerAmountLimitTypeEnum;
import cn.com.duiba.developer.center.api.remoteservice.RemoteRemainingMoneyService;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.kvtable.service.api.enums.ActAccessWebHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbConsisHashKvService;
import cn.com.duiba.order.center.api.dto.CreditsMessage;
import cn.com.duiba.thirdparty.api.RemoteCreditsService;
import cn.com.duiba.thirdparty.dto.CreditsMessageDto;
import cn.com.duiba.thirdparty.enums.CallbackChannelTypeEnum;
import cn.com.duiba.thirdparty.mq.msg.AddCreditsResultMsg;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.NumberUtils;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.config.CommConfig;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.enums.HelpShareErrorCodeEnum;
import com.duiba.activity.accessweb.enums.YesOrNoEnum;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.mq.producer.RocketMQMsgProducer;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.AppInfoService;
import com.duiba.activity.accessweb.service.activity.LotterySquareService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.tool.AppIdConstant;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareAccountDetailVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareCashDrawsInfoVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareRecordPopVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2018/12/17
 */
@Service
public class LotterySquareServiceImpl implements LotterySquareService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LotterySquareServiceImpl.class);

    @Autowired
    private RemoteNewConsumerShareCodeService remoteNewConsumerShareCodeService;
    @Autowired
    private RemoteLotterySquareBonusConfigService remoteLotterySquareBonusConfigService;
    @Autowired
    private RemoteLotterySquareGradientRewardService remoteLotterySquareGradientRewardService;
    @Autowired
    private RemoteLotterySquareRecordService remoteLotterySquareRecordService;
    @Autowired
    private RemoteConsumerAccountService remoteConsumerAccountService;
    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RemoteCreditsService remoteCreditsService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private AppInfoService appInfoService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private RemoteHbConsisHashKvService remoteHbConsisHashKvService;
    @Autowired
    private RocketMqMessageTopic rocketMqMessageTopic;
    @Autowired
    private RemoteConsumerActivityRelationService remoteConsumerActivityRelationService;
    @Autowired
    private RemoteUserShareCodeService remoteUserareCodeService;
    @Autowired
    private RemoteSimpleShareCodeService remoteSimpleShareCodeService;
    @Autowired
    private DeveloperCacheService developerCacheService;
    @Autowired
    private RemoteCashDrawService remoteCashDrawService;
    @Autowired
    private RemoteRemainingMoneyService remoteRemainingMoneyService;
    @Autowired
    private RocketMQMsgProducer rocketMQMsgProducer;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private RiskService riskService;
    @Resource
    private CommConfig commConfig;


    //点击分享链接
    private static final Integer ACTION_CLICK_LINK = 0;
    //输入分享码
    private static final Integer ACTION_INPUT_SHARECODE = 1;
    //新用户extra key
    private static final String NEW_USER_EXTRA_KEY = "newUser";
    //100
    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    @Override
    public void shareLinkClick(String uid, Long optId, String shareCode) {
        try {
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(optId);
            //校验活动状态
            if (null == operatingActivityDto || !Objects.equals(OperatingActivityDto.StatusIntOpen, operatingActivityDto.getStatus())) {
                LOGGER.warn("入库活动不存在或已关闭，入库id：{}", optId);
                return;
            }
            LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(operatingActivityDto.getActivityId());
            Date now = new Date();
            //校验开始时间和活动状态
            if (null == lotterySquareConfigDto
                    || now.before(lotterySquareConfigDto.getStartTime())
                    || now.after(lotterySquareConfigDto.getEndTime())) {
                LOGGER.warn("红包广场活动不存在或不在活动时间内，入库id：{}", optId);
                return;
            }
            Long consumerId = remoteNewConsumerShareCodeService.getConsumerIdByShareCode(operatingActivityDto.getAppId(), optId, shareCode);
            if (null == consumerId) {
                LOGGER.warn("无效的邀请码：{}，活动id:{}", shareCode, optId);
                return;
            }

            //风控
//            StormEngineResultDto riskResult = riskService.assistRisk(operatingActivityDto.getId()
//                    ,operatingActivityDto.getActivityId()
//                    , ActivityUniformityTypeEnum.RedPacketSquare.getCode(),consumerId);
//            if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
//                LOGGER.warn("红包广场--命中风控："+riskResult.getCopy());
//                return;
//            }
            addShareBonus(lotterySquareConfigDto, ACTION_CLICK_LINK, consumerId, null, null);
        } catch (Exception e) {
            LOGGER.error("有效分享奖励失败，邀请码：{}", shareCode, e);
        }
    }

    private String getNewPublicUserHbaseKey(String partnerUserId, Long optId){
        return ActAccessWebHBaseKeyEnum.K043 + optId.toString() + "_" + partnerUserId;
    }

    @Override
    public void newPublicUserNotify(String partnerUserId, Long opId) {
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
        if (null == operatingActivityDto){
            return;
        }
        LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(operatingActivityDto.getActivityId());
        if (null == lotterySquareConfigDto || !ActThrowChannelEnum.CHNNEL_PUBLIC.equals(lotterySquareConfigDto.getActivityAppType())){
           return;
        }
        remoteHbConsisHashKvService.increaseByKey(getNewPublicUserHbaseKey(partnerUserId,opId),1L);
    }

    @Override
    public void firstShare(Long actId, Long consumerId) {
        LotterySquareConfigDto configDto = activityCacheService.getLsConfigById(actId);
        //每天 or 永久  kv表存储
        remoteHbConsisHashKvService.increaseByKey(getFirstShareHBaseKey(actId, consumerId, configDto.getShareLimitScope()), 1);
    }

    /**
     * 返回当前用户今日是否已分享
     * @param actId
     * @param consumerId
     */
    @Override
    public boolean isUserFirstShared(Long actId, Long consumerId, LimitScopeEnum limitScopeEnum) {
        String hBaseKey = getFirstShareHBaseKey(actId, consumerId, limitScopeEnum);
        Long shareCount = null;
        try {
            shareCount = remoteHbConsisHashKvService.getLongByKey(hBaseKey);
        } catch (Exception e) {
            LOGGER.warn("红包广场活动查询Hbase失败 actId:{},consumerId:{}", actId, consumerId);
        }
        if (null == shareCount) {
            return false;
        } else {
            return shareCount > 0;
        }
    }

    /**
     * 获取首次分享HBaseKey
     * @param actId
     * @param consumerId
     * @param limitScope
     * @return
     */
    private String getFirstShareHBaseKey(Long actId, Long consumerId, LimitScopeEnum limitScope) {
        String hbaseKey = ActAccessWebHBaseKeyEnum.K036.toString() + actId + "_" + consumerId;
        if (null != limitScope && limitScope.equals(LimitScopeEnum.EVERY_DAY)) {
            hbaseKey = hbaseKey + "_" + DateUtils.getDayStr(new Date());
        }
        return hbaseKey;
    }

    @Override
    public List<LotterySquareRecordPopVo> getUnReadBonusAndUpdate(Long activityId, Long consumerId, LSBonusTypeEnum bonusType,AppSimpleDto app) {
        //查询未读梯度拉新奖励
        List<LotterySquareBonusRecordDto> records = remoteLotterySquareRecordService.selectUnReadBonusRecord(activityId, consumerId);
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        Map<LSPrizeTypeEnum, List<LotterySquareBonusRecordDto>> recordMap = records.stream()
                .collect(Collectors.groupingBy(LotterySquareBonusRecordDto::getPrizeType));

        List<LotterySquareBonusRecordDto> rewards = recordMap.get(LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE);
        List<LotterySquareBonusRecordDto> shareBonus = recordMap.get(LSPrizeTypeEnum.PRIZE_TYPE_SHARE);

        List<LotterySquareRecordPopVo> popVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rewards)) {
            LotterySquareRecordPopVo rewardPopVo = getLotterySquareRecordPopVo(bonusType, rewards, LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE,app);
            popVos.add(rewardPopVo);
        }
        if (CollectionUtils.isNotEmpty(shareBonus)) {
            LotterySquareRecordPopVo shareBonusPopVo = getLotterySquareRecordPopVo(bonusType, shareBonus, LSPrizeTypeEnum.PRIZE_TYPE_SHARE,app);
            popVos.add(shareBonusPopVo);
        }

        List<Long> ids = records.stream()
                .map(LotterySquareBonusRecordDto::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            //批量更新数据
            remoteLotterySquareRecordService.batchUpdateRead(ids);
        }
        return popVos;
    }

    @NotNull
    private LotterySquareRecordPopVo getLotterySquareRecordPopVo(LSBonusTypeEnum bonusType, List<LotterySquareBonusRecordDto> records,LSPrizeTypeEnum prizeType,AppSimpleDto app) {
        if(CollectionUtils.isEmpty(records)){
            return new LotterySquareRecordPopVo();
        }
        LotterySquareRecordPopVo lotterySquareRecordPopVo = new LotterySquareRecordPopVo();
        Integer sum = records.stream()
                .mapToInt(LotterySquareBonusRecordDto::getBonus)
                .sum();
        if (Objects.equals(LSBonusTypeEnum.BONUS_TYPE_MONEY, bonusType)) {
            lotterySquareRecordPopVo.setBonus(new BigDecimal(sum).divide(new BigDecimal(100)).toString());
        } else {
            //开启人民币模式 - 汇率转换
            if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                lotterySquareRecordPopVo.setBonus(new BigDecimal(sum).divide(new BigDecimal(app.getCreditsRate()), 2, RoundingMode.HALF_UP).toString());
            } else {
                lotterySquareRecordPopVo.setBonus(sum.toString());
            }
        }
        lotterySquareRecordPopVo.setPrizeType(prizeType.getCode());
        return lotterySquareRecordPopVo;
    }


    @Override
    public Integer bindShareCode(LotterySquareConfigDto configDto,String shareCode, Long opId, ConsumerDto consumer,String transfer,String ip) throws BizException {
        //是否为新用户
        if (!checkNewUser(configDto, opId, consumer.getId(), consumer.getPartnerUserId())) {
            throw new BizException(null).withCode(String.valueOf(ResultCode.C101007.getCode()));
        }
        ConsumerShareCodeInfoDto dto;
        try (RedisLock lock = redisAtomicClient.getLock(buildUserBindKey(consumer.getId()), 2)) {
            if (Objects.isNull(lock)) {
                LOGGER.error("红包广场绑定邀请码 ,获取分布式锁失败 consumerId:{}", consumer.getId());
                throw new BizException(null).withCode(String.valueOf(ResultCode.C101009.getCode()));
            }
            //判断用户是否已绑定邀请码
            if (remoteNewConsumerShareCodeService.isUserBind(consumer.getId(), opId, ShareCodeActivityTypeEnum.HDTOOL)) {
                throw new BizException(null).withCode(String.valueOf(ResultCode.C101004.getCode()));
            }

            try {
                dto = remoteNewConsumerShareCodeService
                        .getInfoByShareCode(RequestLocal.getAppId(), opId, shareCode);
            } catch (Exception e) {
                throw new BizException(null).withCode(String.valueOf(HelpShareErrorCodeEnum.E1000011.getErrorCode()));
            }
            //分享码无效
            if (dto == null) {
                throw new BizException(null).withCode(String.valueOf(ResultCode.C101006.getCode()));
            }
            try (RedisLock inviteLock = redisAtomicClient.getLock(buildInviterUserBindKey(dto.getConsumerId()), 2)) {
                if (Objects.isNull(inviteLock)) {
                    LOGGER.error("红包广场绑定邀请码 ,获取分布式锁失败 consumerId:{}", consumer.getId());
                    throw new BizException(null).withCode(String.valueOf(ResultCode.C101009.getCode()));
                }
                //不能绑定自己的邀请码
                if (Objects.equals(consumer.getId(), dto.getConsumerId())) {
                    throw new BizException(null).withCode(String.valueOf(ResultCode.C101003.getCode()));
                }
                //绑定邀请码接口
                UserInviteParam param = new UserInviteParam();
                param.setActivityId(opId);
                param.setInviteConsumerId(consumer.getId());
                param.setAppId(consumer.getAppId());
                param.setShareCode(shareCode);
                param.setNoBonus(true);
                param.setShareCodeActivityTypeEnum(ShareCodeActivityTypeEnum.HDTOOL);
                InviteResponseDto inviteResponseDto = remoteUserareCodeService.userInvite(param);
                if (inviteResponseDto == null) {
                    throw new BizException("助力失败");
                }
            } catch (BizException e) {
                throw e;
            } catch (Exception e) {
                LOGGER.warn("红包广场，绑定邀请码失败", e);
                throw new BizException(null).withCode(String.valueOf(ResultCode.C101009.getCode()));
            }
            //结算新用户奖励
            Integer bonus = addNewConsumerBonus(configDto, consumer, transfer, ip);
            //异步处理邀请人的奖励
            executorService.submit(() -> {
                //梯度拉新奖励
                addInviteAccumulateBonus(configDto, shareCode, dto.getConsumerId(), opId, transfer, ip);
                //发放有效回流奖励
                addShareBonus(configDto, ACTION_INPUT_SHARECODE, dto.getConsumerId(), transfer, ip);
            });
            return bonus;
        }catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.warn("红包广场，绑定邀请码失败", e);
            throw new BizException(null).withCode(String.valueOf(ResultCode.C101009.getCode()));
        }
    }

    @Override
    public  LotterySquareCashDrawsInfoVo cashIndexInfo(Long actId, Long consumerId, Long developerId){
        LotterySquareCashDrawsInfoVo vo = new LotterySquareCashDrawsInfoVo();
        LotterySquareConfigDto configDto = activityCacheService.getLsConfigById(actId);
        if (null == configDto || null == configDto.getWithdrawConfig()) {
            LOGGER.warn("该活动不支持提现,actId:{}", actId);
            vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_EXCEPTION);
            return vo;
        }
        //活动截止时间
        vo.setEndTime(configDto.getEndTime());
        LotterySquareWithdrawConfigDto withdrawConfigDto = configDto.getWithdrawConfig();
        //可提现总次数
        vo.setTotalCashTimes(null == withdrawConfigDto.getDrawTimeLimit() ? 0 : withdrawConfigDto.getDrawTimeLimit());
        //账户余额
        ConsumerAccountUniquenessKeyDto uniquenessKey = new ConsumerAccountUniquenessKeyDto();
        uniquenessKey.setAccountType(AccountTypeEnum.LOTTERY_SQUARE);
        uniquenessKey.setConsumerId(consumerId);
        uniquenessKey.setRelId(actId);
        //账户余额
        Long accountBalance = 0L;
        try {
            ConsumerAccountsDto account = remoteConsumerAccountService.findByUniquenessKey(uniquenessKey);
            if (account != null && account.getBalanceAmount() > 0) {
                accountBalance = account.getBalanceAmount();
            }
        } catch (Exception e) {
            LOGGER.warn("账户信息查询失败，用户ID:{}", consumerId);
        }
        //可提现金额
        vo.setAccountBalance(accountBalance);
        Long cashMoney = accountBalance;
        if (PerAmountLimitTypeEnum.TYPE_FIXED_AMOUNT.equals(withdrawConfigDto.getPerAmountLimitType())) {
            vo.setFixCashMoney(withdrawConfigDto.getPerLimitAmount());
            cashMoney = withdrawConfigDto.getPerLimitAmount();
        }

        //可提现次数校验
        if (! cashTimesVerify(vo,actId,consumerId,withdrawConfigDto)){
            return vo;
        }
        if (cashMoney > accountBalance || accountBalance == 0) {
            LOGGER.info("余额不足,用户ID:{}", consumerId);
            vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_OUT_MONEY);
            return vo;
        }
        //校验活动预算
        if (!checkCashDrawBudgetControl(configDto, cashMoney, true)) {
            LOGGER.warn("活动奖金已派完,活动ID:{}", actId);
            vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_FINISH);
            return vo;
        }
        //校验开发者账户余额
        if (getDeveloperAccountBlance(developerId) < cashMoney) {
            LOGGER.warn("今日奖金已派完,developerId:{}", developerId);
            vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_DAY_FINISH);
            return vo;
        }
        vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_ENABLE);
        return vo;
    }

    /**
     * 提现次数校验
     * @param vo
     * @param actId
     * @param consumerId
     * @param withdrawConfigDto
     * @return
     */
    private boolean cashTimesVerify(LotterySquareCashDrawsInfoVo vo,Long actId, Long consumerId,LotterySquareWithdrawConfigDto withdrawConfigDto){
        //剩余可提现次数
        String hbaseKey = ActAccessWebHBaseKeyEnum.K035.toString() + actId + "_" + consumerId;
        if (isDrawTimeLimit(withdrawConfigDto)) {
            Long cashedTimes = 0L;
            try {
                if (null != remoteHbConsisHashKvService.getLongByKey(hbaseKey)){
                    cashedTimes = remoteHbConsisHashKvService.getLongByKey(hbaseKey);
                }
            } catch (Exception e){
                LOGGER.warn("活动期间提现次数查询失败,consumerId:{}", consumerId, e);
                vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_OUT_LIMIT);
                return false;
            }
            Integer leftTimes = Math.max(withdrawConfigDto.getDrawTimeLimit() - cashedTimes.intValue(), 0);
            vo.setLeftCashTimes(leftTimes);
            if (leftTimes <= 0) {
                LOGGER.info("活动期间提现次数已达上限,consumerId:{}", consumerId);
                vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_OUT_LIMIT);
                return false;
            }
        } else {
            vo.setLeftCashTimes(-1);
        }
        return true;
    }


    @Override
    public void cashDraws(AccountCashDrawsRequest request) throws BizException {
        LotterySquareConfigDto configDto = activityCacheService.getLsConfigById(request.getRelId());
        if (null == configDto || null == configDto.getWithdrawConfig()) {
            throw new BizException("该活动不支持提现");
        }
        LotterySquareWithdrawConfigDto withdrawConfigDto = configDto.getWithdrawConfig();
        if (PerAmountLimitTypeEnum.TYPE_FIXED_AMOUNT.equals(withdrawConfigDto.getPerAmountLimitType())) {
            request.setChangeMoney(withdrawConfigDto.getPerLimitAmount());
        }
        //校验活动预算
        if (! checkCashDrawBudgetControl(configDto,request.getChangeMoney(),false)){
            throw new BizException("活动奖金已派完");
        }
        //校验开发者账户余额
        if (getDeveloperAccountBlance(request.getDeveloperId()) < request.getChangeMoney()){
            throw new BizException("今日奖金已派完");
        }
        ConsumerAccountUniquenessKeyDto uniquenessKey = new ConsumerAccountUniquenessKeyDto();
        uniquenessKey.setAccountType(AccountTypeEnum.LOTTERY_SQUARE);
        uniquenessKey.setConsumerId(request.getConsumerId());
        uniquenessKey.setRelId(request.getRelId());
        ConsumerAccountsDto account = remoteConsumerAccountService.findByUniquenessKey(uniquenessKey);
        if (account == null || account.getBalanceAmount() < request.getChangeMoney()) {
            throw new BizException("余额不足");
        }
        Date now = new Date();
        if (now.after(configDto.getEndTime())){
            throw new BizException("该活动已结束");
        }
        //如果有提现次数限制
        String hbaseKey = ActAccessWebHBaseKeyEnum.K035.toString() + request.getRelId() + "_" + request.getConsumerId();
        if (isDrawTimeLimit(withdrawConfigDto) && remoteHbConsisHashKvService.increaseByKey(hbaseKey, 1L) > withdrawConfigDto.getDrawTimeLimit()) {
            throw new BizException("活动期间提现次数已达上限");
        }
        //校验此支付宝账号是否被其他用户绑定
        String alipayBondHbaseKey = ActAccessWebHBaseKeyEnum.K049.toString() + request.getRelId() + "_" + request.getChannelAccount().toLowerCase();
        String bondConsumerId = remoteHbConsisHashKvService.getStringByKey(alipayBondHbaseKey);
        if (StringUtils.isNotBlank(bondConsumerId) && !StringUtils.equals(bondConsumerId, request.getConsumerId().toString())){
            throw new BizException("该支付宝账号已被绑定，请更换");
        }
        //如果没有绑定，先绑定
        boolean firstBond = false; //是否是首次绑定
        if (StringUtils.isBlank(bondConsumerId)){
            firstBond = true;
            remoteHbConsisHashKvService.upsertKStrV(alipayBondHbaseKey, request.getConsumerId().toString());
        }
        //风控
        if(commConfig.getRiskAppId().contains(request.getAppId())) {
            StormEngineResultDto riskResult = riskService.invokeRisk(request.getChannelAccount(), request.getUsername(), request.getAppId(), request.getConsumerId(), configDto.getId(),ActivityUniformityTypeEnum.RedPacketSquare.getCode(), configDto.getId());
            LOGGER.info("领奖-风控结果={}", JSONObject.toJSONString(riskResult));
            if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
                LOGGER.warn("命中风控：appid = {} ,cid = {} ,account={}",request.getAppId(), request.getConsumerId(), request.getChannelAccount());
                throw new BizException("疑似风险账户，命中风控");
            }
        }

        AccountCashDrawsResponse response = remoteCashDrawService.cashDraws(request);
        if (response == null) {
            rollBackDrawTimes(withdrawConfigDto,hbaseKey);
            rollBackDrawAccount(request.getRelId(),request.getChangeMoney());
            rollBackAlipayBond(firstBond, alipayBondHbaseKey);
            throw new BizException("提现请求失败，稍后再试");
        }
        if (!response.isSuccess()) {
            LOGGER.warn("提现失败,consumerId:{},msg:{}", request.getConsumerId(),response.getErrorMsg());
            rollBackDrawTimes(withdrawConfigDto,hbaseKey);
            rollBackDrawAccount(request.getRelId(),request.getChangeMoney());
            rollBackAlipayBond(firstBond, alipayBondHbaseKey);
            throw new BizException("提现请求失败，稍后再试");
        }

    }

    /**
     * 获取 开发者余额
     * */
    private int getDeveloperAccountBlance(Long developId){
        try {
            DubboResult<RemainingMoneyDto> remainingMoneyDto = remoteRemainingMoneyService.getRemainingMoneyByDeveloperId(developId);
            if (remainingMoneyDto == null || !remainingMoneyDto.isSuccess() || remainingMoneyDto.getResult().getMoney() == null) {
                LOGGER.warn("开发者余额查询失败，developerId:{}", developId);
                return 0;
            }
            return remainingMoneyDto.getResult().getMoney();
        }catch (Exception e){
            LOGGER.warn("开发者余额查询失败，developerId:{}", developId, e);
            return 0;
        }
    }


    @Override
    public List<LotterySquareAccountDetailVo> queryAccountDetail(Long actId, Long consumerId, Integer currentPage, Integer pageSize) throws BizException {
        String accountId = consumerId + "_" + AccountTypeEnum.LOTTERY_SQUARE.getCode() + "_" + actId;

        List<LotterySquareAccountDetailVo> voList = Lists.newArrayList();
        List<ConsumerAccountsLogDto> dtoList = remoteConsumerAccountService.walletDetail(accountId, (currentPage - 1) * pageSize, pageSize);
        if (CollectionUtils.isEmpty(dtoList)){
            return voList;
        }
        for(ConsumerAccountsLogDto dto:dtoList) {
            LotterySquareAccountDetailVo vo = new LotterySquareAccountDetailVo();
            vo.setMoney(new BigDecimal(dto.getChangeMoney()).divide(ONE_HUNDRED, 2, BigDecimal.ROUND_DOWN));
            vo.setActionType(dto.getActionType());
            vo.setGmtCreate(dto.getGmtCreate());
            vo.setDescription(dto.getBizDescription());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 校验是否有提现次数限制
     * @param dto
     * @return
     */
    private boolean isDrawTimeLimit(LotterySquareWithdrawConfigDto dto){
        return dto.getDrawTimeLimit() != null && dto.getDrawTimeLimit() > 0;
    }


    /**
     * 回滚hbase存储的提现次数
     * @param dto
     * @param hbaseKey
     */
    private void rollBackDrawTimes(LotterySquareWithdrawConfigDto dto,String hbaseKey){
        if (isDrawTimeLimit(dto)) {
            remoteHbConsisHashKvService.increaseByKey(hbaseKey, -1L);
        }
    }

    /**
     * 回滚redis存储的提现额度
     * @param firstBond
     * @param alipayBondHbaseKey
     */
    private void rollBackAlipayBond(boolean firstBond, String  alipayBondHbaseKey){
        if (firstBond){
            remoteHbConsisHashKvService.deleteByKey(alipayBondHbaseKey);
        }
    }

    /**
     * 回滚redis存储的提现额度
     * @param actId
     * @param money
     */
    private void rollBackDrawAccount(Long actId, Long money){
        stringRedisTemplate.opsForValue().increment(buildCashDrawBudgetRedisKey(actId),-money);
    }

    /**
     * 判定当前用户是否为新用户
     *
     * @param consumerId
     */
    @Override
    public boolean checkNewUser(LotterySquareConfigDto configDto, Long optId, Long consumerId, String partnerUserId) {
        //app维度新用户校验
        if (ActThrowChannelEnum.CHNNEL_APP.equals(configDto.getActivityAppType())){
            DubboResult<String> dubboResult = remoteConsumerService.findExtra(consumerId, NEW_USER_EXTRA_KEY);
            if (null == dubboResult || !dubboResult.isSuccess()) {
                return false;
            }
            if (!Objects.equals(dubboResult.getResult(), YesOrNoEnum.YES.getCode().toString())) {
                return false;
            }
        }
        //公众号维度新用户校验
        if (ActThrowChannelEnum.CHNNEL_PUBLIC.equals(configDto.getActivityAppType())) {
            Long notFocusFlag = null;
            try {
                notFocusFlag = remoteHbConsisHashKvService.getLongByKey(getNewPublicUserHbaseKey(partnerUserId, optId));
            } catch (Exception e) {
                LOGGER.warn("hbase 查询失败", e);
            }
            if (null == notFocusFlag || 0 == notFocusFlag){
                return false;
            }
        }

        //校验是否参与过活动
        ConsumerActivityQueryParams params = new ConsumerActivityQueryParams();
        params.setCid(consumerId);
        params.setConsumerBizJoinEnum(ConsumerBizJoinEnum.LOTTERYSQUARE);
        //查询红包广场参与记录
        try {
            return remoteConsumerActivityRelationService.selectConsumerActivityRelationCount(params) == 0;
        } catch (BizException e){
            return false;
        }
    }

    /**
     * 发放新用户奖励
     */
    private Integer addNewConsumerBonus(LotterySquareConfigDto configDto,ConsumerDto consumer, String transfer, String ip) {
        try {
            //根据配置生成新用户奖励
            List<LotterySquareBonusConfigDto> bonusConfigs = remoteLotterySquareBonusConfigService.selectBonusConfigByActIdAndConsumerType(configDto.getId(), LSPrizeTypeEnum.PRIZE_TYPE_NEWUSER);
            Integer bonus = randomBonus(bonusConfigs);
            if (sendBonus(configDto, consumer, bonus, LSPrizeTypeEnum.PRIZE_TYPE_NEWUSER, null, transfer, ip)) {
                return bonus;
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.warn("新人奖励发放失败，consumerid：{}", consumer.getId(), e);
            return null;
        }
    }

    /**
     * 奖励发放-分享奖励
     *
     * @param configDto
     * @param action     : 1-点击分享链接 2-输入邀请码
     * @param inviteUser : 邀请用户
     */
    private void addShareBonus(LotterySquareConfigDto configDto, Integer action, Long inviteUser,String transfer,String ip) {
        try {
            if (!configDto.getFirstShareBonus()) {
                LOGGER.info("首次分享奖励未开启,actId:{}", configDto.getId());
                return;
            }
            //分享奖励-有效分享
            if (ACTION_CLICK_LINK == action && !LSShareEffectiveConditonEnum.EFFECTIVE_SHARE.equals(configDto.getShareBonusRequire())) {
                return;
            }

            //分享奖励-有效回流
            if (ACTION_INPUT_SHARECODE == action && !LSShareEffectiveConditonEnum.EFFECTIVE_RETURN.equals(configDto.getShareBonusRequire())) {
                return;
            }
            //每天 or 永久  kv表存储
            String hbaseKey = ActAccessWebHBaseKeyEnum.K034.toString() + configDto.getId() + "_" + inviteUser;
            LimitScopeEnum limitScope = configDto.getShareLimitScope();
            //先查分享记录
            String shareRecordKey = getFirstShareHBaseKey(configDto.getId(),inviteUser,limitScope);
            Long todayShareTimes = remoteHbConsisHashKvService.getLongByKey(shareRecordKey);
            if (todayShareTimes == null || todayShareTimes <= 0){
                LOGGER.warn("没有分享记录，key:{}", shareRecordKey);
                return;
            }
            if (LimitScopeEnum.EVERY_DAY.equals(limitScope)) {
                hbaseKey = hbaseKey + "_" + DateUtils.getDayStr(new Date());
            }
            if (remoteHbConsisHashKvService.increaseByKey(hbaseKey, 1) > 1) {
                LOGGER.warn("奖励已领取，用户ID:{}", inviteUser);
                return;
            }
            List<LotterySquareBonusConfigDto> allBonusConfigDtos = remoteLotterySquareBonusConfigService.selectBonusConfigByActId(configDto.getId());
            List<LotterySquareBonusConfigDto> bonusConfigDtos = allBonusConfigDtos.stream().filter(item->LSPrizeTypeEnum.PRIZE_TYPE_SHARE.equals(item.getConsumerType())).collect(Collectors.toList());
            Integer rewardCount = randomBonus(bonusConfigDtos);
            if (randomBonus(bonusConfigDtos) > 0) {
                ConsumerDto consumer = remoteConsumerService.find(inviteUser);
                boolean sendFlag = sendBonus(configDto, consumer, rewardCount, LSPrizeTypeEnum.PRIZE_TYPE_SHARE,null,transfer,ip);
                if (!sendFlag) {
                    remoteHbConsisHashKvService.increaseByKey(hbaseKey, -1);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("分享奖励发放失败，分享人：{}", inviteUser, e);
        }
    }

    /**
     * 老用户奖励发放-邀请成功梯度奖励
     */
    private void addInviteAccumulateBonus(LotterySquareConfigDto configDto,String shareCode, Long inviteUser, Long optId,String transfer,String ip) {
        try {

            Integer inviteCount = remoteSimpleShareCodeService.countInvitedUser(shareCode,ShareCodeActivityTypeEnum.HDTOOL,optId);
            if (inviteCount <= 0) {
                return;
            }
            LotterySquareGradientRewardDto rewardDto = remoteLotterySquareGradientRewardService.findByInviteCount(configDto.getId(), inviteCount);
            if (null == rewardDto) {
                LOGGER.info("该等级奖励不存在,actId:{},inviteCount:{}", configDto.getId(), inviteCount);
                return;
            }
            LotterySquareRecordQueryParam recordQueryParam = new LotterySquareRecordQueryParam();
            recordQueryParam.setActivityId(configDto.getId());
            recordQueryParam.setConsumerId(inviteUser);
            recordQueryParam.setPrizeType(LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE);
            recordQueryParam.setPrizeLevel(rewardDto.getPrizeLevel());
            //此级别奖励次数
            Integer rewardCount = remoteLotterySquareRecordService.countBonusRecordByParam(recordQueryParam);
            if (rewardCount > 0) {
                LOGGER.info("该等级奖励已领取,consumerId:{},prizeLevel:{}", inviteUser, rewardDto.getPrizeLevel());
                return;
            }
            //发放奖励
            ConsumerDto consumer = remoteConsumerService.find(inviteUser);
            sendBonus(configDto, consumer, rewardDto.getBonusAmount(), LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE,rewardDto.getPrizeLevel(),transfer,ip);
        } catch (Exception e) {
            LOGGER.warn("邀请梯度奖励发放失败，邀请人:{}", inviteUser, e);
        }
    }


    /**
     * 1、扣除预算
     * 2、生成订单
     * 3、发放奖励
     * 4、更新订单
     * 5、生成用户参与记录（用户新用户判断）
     *
     * @param lotterySquareConfig
     * @param consumer
     * @param bonus
     * @param prizeType
     * @param prizeLevel
     * @return
     */
    private boolean sendBonus(LotterySquareConfigDto lotterySquareConfig, ConsumerDto consumer, Integer bonus, LSPrizeTypeEnum prizeType, Integer prizeLevel, String transfer, String ip) {
        //新增活动子订单及红包广场记录
        String orderNum = insertActivityOrder(consumer, lotterySquareConfig, bonus, ip);
        Long recordId = insertBonusRecord(bonus, consumer, orderNum, lotterySquareConfig, prizeType, prizeLevel);
        if (!checkBudgetControl(lotterySquareConfig, bonus)) {
            //标记订单为预算不足发放失败/插件订单设置为失败
            remoteActivityOrderService.exchangeStatusToFail(orderNum);
            remoteLotterySquareRecordService.updateExchangeStatus(recordId, LSExchangeStatusEnum.EXCHANGE_BONUS_RUN_OUT);
            LOGGER.warn("当前红包广场活动预算不足，不发放当前奖励， activityId:{}", lotterySquareConfig.getId());
            return false;
        }
        Boolean sendResult;
        if (LSBonusTypeEnum.BONUS_TYPE_MONEY == lotterySquareConfig.getBonusType()) {
            sendResult = addMoney(lotterySquareConfig.getId(), consumer, recordId, bonus);
        } else {
            sendResult = addCredits(consumer, recordId, bonus.longValue(), transfer, ip, lotterySquareConfig.getTitle());
        }
        if (!sendResult) {
            //发钱失败-更新订单为领奖失败
            remoteActivityOrderService.exchangeStatusToFail(orderNum);
            remoteLotterySquareRecordService.updateExchangeStatus(recordId, LSExchangeStatusEnum.EXCHANGE_STATUS_FAIL);
            //回滚预算
            stringRedisTemplate.opsForValue().increment(buildBudgetRedisKey(lotterySquareConfig.getId()), -bonus);
            return false;
        }
        //新增用户参与记录
        insertConsumerRelation(lotterySquareConfig.getId(), consumer.getId());
        return true;
    }

    /**
     * 新增用户参与记录
     *
     * @param actId
     * @param consumerId
     */
    private void insertConsumerRelation(Long actId, Long consumerId) {
        //成功则更新用户参与记录
        ConsumerActivityRelationDto relationDto = new ConsumerActivityRelationDto();
        relationDto.setCid(consumerId);
        relationDto.setBizId(actId.toString());
        relationDto.setBizType(ConsumerBizJoinEnum.LOTTERYSQUARE);
        try {
            remoteConsumerActivityRelationService.insert(relationDto);
        }catch (Exception e){
            LOGGER.warn("用户参与记录插入失败，用户ID:{}",consumerId,e);
        }
    }

    /**
     * 奖励发放预警
     * @param lotterySquareConfig
     * @param bonus
     * @return
     */
    private Boolean checkBudgetControl(LotterySquareConfigDto lotterySquareConfig, Integer bonus){
        if (!lotterySquareConfig.getBudgetControl()) {
            //无预算控制
            return true;
        }
        Long budgetNow;
        try {
            budgetNow = stringRedisTemplate.opsForValue().increment(buildBudgetRedisKey(lotterySquareConfig.getId()), bonus);
        } catch (Exception e) {
            LOGGER.warn("redis获取活动总金额失败", e);
            budgetNow = remoteLotterySquareRecordService.getTotalSendBonus(lotterySquareConfig.getId()) + bonus;
            stringRedisTemplate.opsForValue().set(buildBudgetRedisKey(lotterySquareConfig.getId()), budgetNow.toString());
        }
        //是否超过预算
        if (budgetNow >= lotterySquareConfig.getTotalBudget()) {
            //预算回滚
            budgetNow = stringRedisTemplate.opsForValue().increment(buildBudgetRedisKey(lotterySquareConfig.getId()), -bonus);
            LOGGER.info("红包广场，超过预算！activityId:{},活动已发奖励：{}，当前要发奖励：{}，总预算：{}", lotterySquareConfig.getId(), budgetNow, bonus, lotterySquareConfig.getTotalBudget());
            if (NumberUtils.parseLong(stringRedisTemplate.opsForValue().get(budgetRanOutKey(lotterySquareConfig.getId())), 0) == 0L) {
                AppSimpleDto appSimpleDto = developerCacheService.getById(lotterySquareConfig.getAppId());
                //发送预警短信
                String moneyWithUnit = getSendMoney(lotterySquareConfig.getBonusType(), lotterySquareConfig.getTotalBudget() - budgetNow);
                LotterySquareBudgetOutAlarmSmsParam lotterySquareBudgetOutAlarmSmsParam = new LotterySquareBudgetOutAlarmSmsParam(lotterySquareConfig.getBudgetWarnTel(), appSimpleDto.getName(),lotterySquareConfig.getTitle(),moneyWithUnit);
                rocketMQMsgProducer.sendMsg(rocketMqMessageTopic.getAlarmSmsTopic(), RocketMqTagEnum.SMS_LS_BUDGET_RAN_OUT_ALARM.getTag(), null, JSONObject.toJSONString(lotterySquareBudgetOutAlarmSmsParam));
                stringRedisTemplate.opsForValue().set(budgetRanOutKey(lotterySquareConfig.getId()), "1", 1, TimeUnit.DAYS);
            }
            return false;
        }
        //是否超过预算预警
        if (budgetNow >= lotterySquareConfig.getBudgetWarnAmount()
                && NumberUtils.parseLong(stringRedisTemplate.opsForValue().get(budgetWarnKey(lotterySquareConfig.getId())), 0) == 0L
        ) {
            AppSimpleDto appSimpleDto = developerCacheService.getById(lotterySquareConfig.getAppId());
            String moneyWithUnit = getSendMoney(lotterySquareConfig.getBonusType(), lotterySquareConfig.getTotalBudget() - budgetNow);
            LotterySquareBudgetWarnAlarmSmsParam lotterySquareBudgetWarnAlarmSmsParam = new LotterySquareBudgetWarnAlarmSmsParam(lotterySquareConfig.getBudgetWarnTel(), appSimpleDto.getName(),lotterySquareConfig.getTitle(),moneyWithUnit);
            rocketMQMsgProducer.sendMsg(rocketMqMessageTopic.getAlarmSmsTopic(), RocketMqTagEnum.SMS_LS_BUDGET_WARN_ALARM.getTag(), null, JSONObject.toJSONString(lotterySquareBudgetWarnAlarmSmsParam));
            stringRedisTemplate.opsForValue().set(budgetWarnKey(lotterySquareConfig.getId()), "1", 1, TimeUnit.DAYS);
        }
        return true;
    }

    private String getSendMoney(LSBonusTypeEnum bonusTypeEnum, Long money) {
        //红包则除以100展示
        double restMoney = money.doubleValue();
        String unitName = "积分";
        if (LSBonusTypeEnum.BONUS_TYPE_MONEY.equals(bonusTypeEnum)) {
            restMoney = new BigDecimal(money).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
            unitName = "元";
        }
        return restMoney + unitName;
    }

    private Integer randomBonus(List<LotterySquareBonusConfigDto> bonusConfigs) throws BizException {
        if (CollectionUtils.isEmpty(bonusConfigs)) {
            throw new BizException("红包广场管理端配置错误");
        }
        if (bonusConfigs.size() == 1 && LSBonusStyleEnum.BONUS_FIXED_AMOUNT == bonusConfigs.get(0).getBonusStyle()) {
            return bonusConfigs.get(0).getBonusAmount();
        } else {
            int random = RandomUtils.nextInt(0, 100);
            int totalRate = 0;
            for (LotterySquareBonusConfigDto bonusConfigDto : bonusConfigs) {
                totalRate += bonusConfigDto.getRate();
                if (random <= totalRate) {
                    return RandomUtils.nextInt(bonusConfigDto.getRangeFrom(), bonusConfigDto.getRangeTo());
                }
            }
        }
        return 0;
    }

    /**
     * 添加红包余额
     * @param activityId - 红包广场活动id
     * @param consumer
     * @param recordId
     * @param money
     * @return
     */
    private Boolean addMoney(Long activityId, ConsumerDto consumer, Long recordId, Integer money) {
        AccountAmountModifyRequest request = new AccountAmountModifyRequest();
        request.setAppId(consumer.getAppId());
        request.setConsumerId(consumer.getId());
        request.setBizId(recordId + "_" + consumer.getId());
        request.setBizType(AccountBizTypeEnum.LOTTERY_SQUARE);
        request.setAccActType(AccountActionTypeEnum.ACTION_IN);
        request.setAccountType(AccountTypeEnum.LOTTERY_SQUARE);
        request.setRelId(activityId);
        request.setBizDescription("红包广场奖励");
        request.setPartnerUserId(consumer.getPartnerUserId());
        request.setSubType(AccountSubTypeEnum.SUB_ACTIVITY_INCOME);
        request.setChangeMoney(money.longValue());
        request.setNeedLog(Boolean.TRUE);
        try {
            AccountModifyResponse response = remoteConsumerAccountService.accountModify(request);
            if (!response.isSuccess()) {
                LOGGER.error("红包广场发放红包奖励失败 {} recordId:{}", response.getErrorMsg(), recordId);
                return false;
            }
            return true;
        } catch (BizException e) {
            LOGGER.error("红包广场发放红包奖励失败 recordId:{}", recordId, e);
            return false;
        }
    }


    /**
     * 奖励添加积分
     *
     * @param recordId 奖励记录ID
     * @param credits  加积分数
     * @param consumer
     * @param transfer
     * @param ip
     */
    private boolean addCredits(ConsumerDto consumer, Long recordId, Long credits, String transfer, String ip, String title) {
        try {
            CreditsMessage request = combineAddCreditsMessage(consumer, credits, recordId, transfer, ip, title);
            DubboResult<Boolean> result = remoteCreditsService.submitAddCredits(request, rocketMqMessageTopic.getActivityAddCreditsCallback(), RocketMqMessageTopic.LOTTERY_SQUARE_ADDCREDITS_TAG, recordId.toString());
            return (null != result && result.getResult());
        } catch (Exception e) {
            LOGGER.error("红包广场加积分请求失败，订单号：{}", recordId);
            return false;
        }
    }

    private Long insertBonusRecord(Integer bonus, ConsumerDto consumerDto, String orderNum, LotterySquareConfigDto lotterySquareConfig, LSPrizeTypeEnum prizeType, Integer prizeLevel) {
        LotterySquareBonusRecordDto lotterySquareBonusRecordDto = new LotterySquareBonusRecordDto();
        lotterySquareBonusRecordDto.setActivityId(lotterySquareConfig.getId());
        lotterySquareBonusRecordDto.setAppId(consumerDto.getAppId());
        lotterySquareBonusRecordDto.setConsumerId(consumerDto.getId());
        lotterySquareBonusRecordDto.setPartnerUserId(consumerDto.getPartnerUserId());
        lotterySquareBonusRecordDto.setPrizeType(prizeType);
        if (LSPrizeTypeEnum.PRIZE_TYPE_ACCMULATE == prizeType) {
            lotterySquareBonusRecordDto.setPrizeLevel(prizeLevel);
        }
        lotterySquareBonusRecordDto.setBonusType(lotterySquareConfig.getBonusType());
        lotterySquareBonusRecordDto.setBonus(bonus);
        //状态直接置为成功 - 如果发奖失败再进行更改
        if(lotterySquareConfig.getBonusType() == LSBonusTypeEnum.BONUS_TYPE_CREDITS){
            lotterySquareBonusRecordDto.setExchangeStatus(LSExchangeStatusEnum.EXCHANGE_STATUS_WATTING);
        }else{
            lotterySquareBonusRecordDto.setExchangeStatus(LSExchangeStatusEnum.EXCHANGE_STATUS_SUCCESS);
        }
        lotterySquareBonusRecordDto.setOrderNum(orderNum);
        lotterySquareBonusRecordDto.setReadStatus(false);
        return remoteLotterySquareRecordService.insert(lotterySquareBonusRecordDto);
    }

    private String insertActivityOrder(ConsumerDto consumer, LotterySquareConfigDto lotterySquareConfig, Integer bonus,String ip) {
        ActivityOrderDto activityOrderDto = new ActivityOrderDto();

        activityOrderDto.setConsumerId(consumer.getId());
        activityOrderDto.setPartnerUserId(consumer.getPartnerUserId());
        activityOrderDto.setAppId(consumer.getAppId());
        activityOrderDto.setDuibaActivityId(lotterySquareConfig.getId());
        activityOrderDto.setAppActivityId(lotterySquareConfig.getId());
        activityOrderDto.setExchangeStatus(ActivityOrderDto.ExchangeInit);
        //奖励金额填入addCredits字段
        activityOrderDto.setAddCredits(bonus.longValue());
        activityOrderDto.setActivityType(ActivityOrderDto.TypeLotterySquare);
        activityOrderDto.setConsumeCredits(0L);
        //红包广场参与均不需扣积分，扣积分状态直接置为成功
        activityOrderDto.setConsumeCreditsStatus(ActivityOrderDto.ConsumeCreditsSuccess);
        activityOrderDto.setIp(ip);
        return remoteActivityOrderService.createOrder(activityOrderDto).getResult();
    }

    /**
     * 封装加积分请求消息
     *
     * @param
     * @param
     * @return
     */
    private CreditsMessage combineAddCreditsMessage(ConsumerDto consumer, Long credits, Long orderId, String transfer, String ip, String title) {
        AppSimpleDto app = appInfoService.findAppByAppId(consumer.getAppId());
        if (app == null) {
            throw new AccessActivityRuntimeException("查询不到APP信息，appId:" + consumer.getAppId());
        }
        String addCreditsUrl = appInfoService.findAppExtra(app.getId()).getAddCreditsUrl();
        if (StringUtils.isEmpty(addCreditsUrl)) {
            throw new AccessActivityRuntimeException("app尚不支持加积分请求,appId:" + app.getId());
        }

        AddCreditsParams p = new AddCreditsParams();
        p.setAppKey(app.getAppKey());
        p.setCredits(credits);
        p.setOrderNum(CommonConstants.AAW_STR + AddCreditsType.LOTTERY_SQUARE.getCode() + "-" + orderId);
        p.setUid(consumer.getPartnerUserId());
        p.setTimestamp(new Date());
        p.setType(AddCreditsOuterType.HDTOOL.getCode());
        if (AppIdConstant.isOppoApp(app.getId()) && StringUtils.isNotBlank(title)) {
            p.setDescription(title);
        } else {
            p.setDescription("红包广场积分奖励");
        }
        p.setIp(ip);
        if (StringUtils.isNotBlank(transfer)) {
            p.setTransfer(transfer);
        }
        Map<String, String> paramsMap = p.toRequestMap(app.getAppSecret());
        CreditsMessage creditsMessage = new CreditsMessage();
        creditsMessage.setTimestamp(System.currentTimeMillis());
        creditsMessage.setAppId(String.valueOf(app.getId()));
        creditsMessage.setConsumerId(String.valueOf(consumer.getId()));
        creditsMessage.setRelationId(orderId.toString());
        creditsMessage.setRelationType(AddCreditsType.LOTTERY_SQUARE.getCode());
        Map<String, String> params = Maps.newHashMap();
        params.put(AddCreditsResultMsg.USER_IP, ip);
        params.put(AddCreditsResultMsg.TRANSFER, transfer);
        params.put(AddCreditsResultMsg.USER_ID, consumer.getPartnerUserId());
        params.put(CreditsMessageDto.MESSAGE_CHANNEL_TYPE_KEY, CallbackChannelTypeEnum.ROCKETMQ.getType());
        creditsMessage.setParams(params);
        creditsMessage.setHttpUrl(AssembleTool.assembleUrl(addCreditsUrl, paramsMap));
        creditsMessage.setHttpType(CreditsMessage.HTTP_GET);
        creditsMessage.setAuthParams(paramsMap);
        return creditsMessage;
    }

    /**
     * 体现预算校验
     * @param lotterySquareConfig
     * @param bonus
     * @param justQuery : 只查询余额是否充足，不做扣减
     * @return
     */
    private Boolean checkCashDrawBudgetControl(LotterySquareConfigDto lotterySquareConfig, Long bonus, boolean justQuery){
        if (!lotterySquareConfig.getBudgetControl()) {
            //无预算控制
            return true;
        }

        Long budgetNow;
        try {
            if (justQuery){
                return NumberUtils.parseLong(stringRedisTemplate.opsForValue().get(buildCashDrawBudgetRedisKey(lotterySquareConfig.getId())),0) + bonus <=  lotterySquareConfig.getTotalBudget();
            }
            budgetNow = stringRedisTemplate.opsForValue().increment(buildCashDrawBudgetRedisKey(lotterySquareConfig.getId()), bonus);
        } catch (Exception e) {
            LOGGER.warn("redis获取活动总提现金额失败", e);
            return false;
        }
        //是否超过预算
        if (budgetNow > lotterySquareConfig.getTotalBudget()) {
            //预算回滚
            rollBackDrawAccount(lotterySquareConfig.getId(),bonus);
            LOGGER.warn("红包广场，超过预算！activityId:{},活动已提现金额：{}，当前提现金额：{}，总预算：{}", lotterySquareConfig.getId(), budgetNow, bonus, lotterySquareConfig.getTotalBudget());
            return false;
        }

        return true;
    }

    /**
     * 发送短信预警（一次）
     * @param activityId
     * @return
     */
    private String budgetWarnKey(Long activityId) {
        return RedisKeyFactory.K921.toString() + activityId;
    }

    /**
     * 发送短信预警(预算发完)（一次）
     * @param activityId
     * @return
     */
    private String budgetRanOutKey(Long activityId) {
        return RedisKeyFactory.K922.toString() + activityId;
    }

    /**
     * 提现预算key
     * @param activityId
     * @return
     */
    private String buildCashDrawBudgetRedisKey(Long activityId) {
        return RedisKeyFactory.K923.toString() + "_" + activityId;
    }

    /**
     * 预算key
     * @param activityId
     * @return
     */
    private String buildBudgetRedisKey(Long activityId) {
        return RedisKeySpace.K150.toString() + "_" + activityId;
    }

    /**
     * 用户绑定邀请码锁
     * @param consumerId
     * @return
     */
    private String buildUserBindKey(Long consumerId) {
        return RedisKeyFactory.K925.toString() + "_" + consumerId;
    }

    /**
     * 用户绑定邀请码锁
     * @param consumerId
     * @return
     */
    private String buildInviterUserBindKey(Long consumerId) {
        return RedisKeyFactory.K926.toString() + "_" + consumerId;
    }



}
