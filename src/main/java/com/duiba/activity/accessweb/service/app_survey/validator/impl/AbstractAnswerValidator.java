package com.duiba.activity.accessweb.service.app_survey.validator.impl;

import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyExtInfoDto;
import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyOptionDto;
import cn.com.duiba.activity.center.api.dto.app_survey.AppSurveyQuestionDto;
import cn.com.duiba.boot.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.config.HsbcConfig;
import com.duiba.activity.accessweb.controller.app_survey.request.QuestionAndUserAnswer;
import com.duiba.activity.accessweb.controller.app_survey.request.UserAnswer;
import com.duiba.activity.accessweb.service.app_survey.AppSurveyUserSubmitContext;
import com.duiba.activity.accessweb.service.app_survey.validator.AnswerValidator;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class AbstractAnswerValidator implements AnswerValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractAnswerValidator.class);

    @Resource
    protected HsbcConfig hsbcConfig;

    /**
     * 校验答案选项
     *
     * @param userSubmitContext     上下文
     * @param questionAndUserAnswer 用户提交题目答案
     * @throws BizException S.H.I.T happens
     */
    public void checkAnswerOption(AppSurveyUserSubmitContext userSubmitContext, QuestionAndUserAnswer questionAndUserAnswer) throws BizException {
        Map<Long, AppSurveyQuestionDto> questId2ConfigMap = userSubmitContext.getQuestId2ConfigMap();
        AppSurveyQuestionDto appSurveyQuestionDto = questId2ConfigMap.get(questionAndUserAnswer.getId());
        //题目配置
        if (Objects.isNull(appSurveyQuestionDto)) {
            throw new BizException(String.format("找不到提交答案的题目配置，questionId = %s", questionAndUserAnswer.getId()));
        }

        //所有选项配置id
        List<Long> optionIdList = appSurveyQuestionDto.getOptionConfigList().stream().map(AppSurveyOptionDto::getId).collect(Collectors.toList());


        //不存在的选项配置
        boolean isOptionError = questionAndUserAnswer.getAnswerContent().stream()
                .anyMatch(userAnswer -> Objects.isNull(userAnswer) || Objects.isNull(userAnswer.getOptionId()) || !optionIdList.contains(userAnswer.getOptionId()));

        if (isOptionError) {
            throw new BizException(String.format("提交选项异常，questionAndUserAnswer = %s", JSON.toJSONString(questionAndUserAnswer)));
        }
    }

    /**
     * 校验单、多选择题选项用户填写
     * @param userSubmitContext
     * @param questionAndUserAnswer
     * @throws BizException
     */
    protected void checkSelectQuestionOptionFill(AppSurveyUserSubmitContext userSubmitContext, QuestionAndUserAnswer questionAndUserAnswer) throws BizException {
        Map<Long, AppSurveyQuestionDto> questId2ConfigMap = userSubmitContext.getQuestId2ConfigMap();
        AppSurveyQuestionDto appSurveyQuestionDto = questId2ConfigMap.get(questionAndUserAnswer.getId());
        //题目配置
        if (Objects.isNull(appSurveyQuestionDto)) {
            LOGGER.info("提交异常 无该题目，questionId = {}", questionAndUserAnswer.getId());
            throw new BizException("提交异常，无该题目");
        }
        //选项map
        Map<Long, AppSurveyOptionDto> optionMap = appSurveyQuestionDto.getOptionConfigList().stream().collect(Collectors.toMap(AppSurveyOptionDto::getId, Function.identity(), (k1, k2) -> k1));
        for (UserAnswer answer :questionAndUserAnswer.getAnswerContent()){
            AppSurveyOptionDto appSurveyOptionDto = optionMap.get(answer.getOptionId());
            if(Objects.isNull(appSurveyOptionDto)){
                LOGGER.info("提交异常 无该选项，questionAndUserAnswer = {}", JSON.toJSONString(questionAndUserAnswer));
                throw new BizException("提交异常 无该选项");
            }
            final String extraJson = appSurveyOptionDto.getExtraJson();
            if (StringUtils.isBlank(extraJson)) {
                continue;
            }
            // 是否用户填写
            Boolean isUserFill = JSONObject.parseObject(extraJson).getBoolean(AppSurveyExtInfoDto.IS_USER_FILL);
            if (Objects.isNull(isUserFill) || !isUserFill) {
                continue;
            }
            if (StringUtils.isBlank(answer.getUserFillContent())) {
                LOGGER.info("提交异常 用户填写必填，questionAndUserAnswer = {}", JSON.toJSONString(questionAndUserAnswer));
                throw new BizException("还有未填项未填写");
            }
        }
    }
}
