package com.duiba.activity.accessweb.service.activity.factory.prize.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityOptionsDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolOptionsDto;
import cn.com.duiba.activity.center.api.dto.prize.ActivityPrizeOptionDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityOptionsService;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteDuibaHdtoolServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.prize.RemoteActivityPrizeOptionService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.wolf.dubbo.DubboResult;

import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.vo.OrdersVO;

/** 
 * ClassName:ActivityPrizeCommonService.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年8月1日 上午9:31:50 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Component
public class ActivityPrizeCommonService {
	@Autowired
	private RemoteDuibaHdtoolServiceNew remoteDuibaHdtoolServiceNew;
	@Autowired
	private RemoteOperatingActivityOptionsService remoteOperatingActivityOptionsService;
	@Autowired
	private RemoteActivityPrizeOptionService remoteActivityPrizeOptionService;
	@Autowired
	private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
	@Autowired
	private CommonService commonService;

	
	public String getOptionImgUrl(OrdersVO orderDO, int lotteryType) {
		if(lotteryType == ConsumerExchangeRecordDto.TypeHdtoolLottery){
			return hdtoolOptionImgurl(orderDO.getPrizeId(),orderDO.getIsDuibaActivity());
		}else if(lotteryType == ConsumerExchangeRecordDto.TypePluginLottery){
			//集卡规则奖品时
			if(ActivityOrderDto.TypeCollectRule.equals(orderDO.getActivityType())){
				return itemOptionImgurl(orderDO.getItemId(),orderDO.getAppId());
			}else{
				return activityOptionImgurl(orderDO.getActivityOptionId());
			}
		}
		return null;
	}
	private String activityOptionImgurl(Long prizeId) {
		//查询奖品信息
		ActivityPrizeOptionDto optionResult = remoteActivityPrizeOptionService.find(prizeId).getResult();
		return optionResult != null ? optionResult.getLogo() : null;
	}
	private String itemOptionImgurl(Long itemId, Long appId) {
		//查询商品信息
		ItemKeyDto itemKey = commonService.findItemKeyDto(null, itemId, appId);
		return itemKey!=null && itemKey.getItem() != null ? itemKey.getItem().getSmallImage() : null;
	}
	private String hdtoolOptionImgurl(Long prizeId, Boolean isDuiba) {
		//奖品名称
		if (isDuiba) {
			DuibaHdtoolOptionsDto option = remoteDuibaHdtoolServiceNew.findOptionById(prizeId);
			if(option !=null){
				return option.getLogo();
			}
		} else {
			//自有活动
			OperatingActivityOptionsDto option = remoteOperatingActivityOptionsService.findOptionById(prizeId);
			if(option !=null){
				return option.getLogo();
			}
		}
		return null;
	}
	public ConsumerExchangeRecordDto recordInfo(OrdersVO orderDO,int lotteryType) {
		
		Map<String, Object> maprecord = new HashMap<>();
		if(lotteryType == ConsumerExchangeRecordDto.TypePluginLottery){
			maprecord.put("relationId", orderDO.getOrderNum());
		}else{
			maprecord.put("relationId", orderDO.getId());
		}
		maprecord.put("type", lotteryType);
		maprecord.put("consumerId", orderDO.getConsumerId());
		DubboResult<ConsumerExchangeRecordDto> resRecord = remoteConsumerExchangeRecordService.selectOneByMapCondition(maprecord);
		return resRecord.getResult();
	}
	

}
