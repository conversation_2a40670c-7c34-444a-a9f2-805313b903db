package com.duiba.activity.accessweb.service.plugin.impl;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.activity_order.ActivityOrderDto;
import cn.com.duiba.activity.center.api.dto.chaos.ActPreStockDto;
import cn.com.duiba.activity.center.api.dto.direct.ActivityBlackList4DeveloperDto;
import cn.com.duiba.activity.center.api.dto.direct.DuibaActivityAppSpecifyNewDto;
import cn.com.duiba.activity.center.api.dto.happy_code.HappyCodeOrderDto;
import cn.com.duiba.activity.center.api.dto.plugin.ActivityPluginDto;
import cn.com.duiba.activity.center.api.dto.plugin.triggertime.TriggerTimeDayDto;
import cn.com.duiba.activity.center.api.dto.plugin.triggertime.TriggerTimeHourDto;
import cn.com.duiba.activity.center.api.dto.prize.ActivityPrizeOptionDto;
import cn.com.duiba.activity.center.api.dto.sign.SignConfigDto;
import cn.com.duiba.activity.center.api.enums.LimitScopeEnum;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteActivityConsumerLimitService;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.chaos.RemoteActPreStockService;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteActivityBlackList4DeveloperService;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteDuibaActivityAppSpecifyNewService;
import cn.com.duiba.activity.center.api.remoteservice.happycode.RemoteHappyCodeOrderService;
import cn.com.duiba.activity.center.api.remoteservice.plugin.RemoteActivityPluginService;
import cn.com.duiba.activity.center.api.remoteservice.prize.RemoteActivityPrizeOptionService;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountBizTypeEnum;
import cn.com.duiba.api.bo.subcredits.SubCreditsMsgDto;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.api.enums.subcredits.SubCreditsOuterType;
import cn.com.duiba.api.enums.subcredits.SubCreditsType;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.credits.sdk.CreditConsumeParams;
import cn.com.duiba.developer.center.api.domain.dto.AppNewExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppNewExtraService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.CreditsMessage;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.thirdparty.dto.CreditsMessageDto;
import cn.com.duiba.thirdparty.enums.CallbackChannelTypeEnum;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.ActivityCommCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.HtdoolConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.core.event.ActivityOrdersEvent;
import com.duiba.activity.accessweb.core.event.ActivityOrdersEvent.ActivityOrdersEventType;
import com.duiba.activity.accessweb.core.event.DuibaEventsDispatcher;
import com.duiba.activity.accessweb.exception.AccessActivityException;
import com.duiba.activity.accessweb.exception.BusinessException;
import com.duiba.activity.accessweb.log.JsonActivityAccessLog;
import com.duiba.activity.accessweb.mq.OnsMessageTopic;
import com.duiba.activity.accessweb.mq.RocketMqMessageTopic;
import com.duiba.activity.accessweb.mq.producer.RocketMQMsgProducer;
import com.duiba.activity.accessweb.pjv.rsp.plugin.AllPluginOptionRsp;
import com.duiba.activity.accessweb.service.ActivityAddVistTimeService;
import com.duiba.activity.accessweb.service.ActivitySignService;
import com.duiba.activity.accessweb.service.globalreward.GlobalRewardService;
import com.duiba.activity.accessweb.service.luckbug.AppSlotCacheService;
import com.duiba.activity.accessweb.service.plugin.ActivityPluginFlowInnerService;
import com.duiba.activity.accessweb.service.plugin.ActivityPluginRandomService;
import com.duiba.activity.accessweb.service.plugin.ActivityPluginService;
import com.duiba.activity.accessweb.service.plugin.joincheck.impl.UnlockingRecordCheckHandler;
import com.duiba.activity.accessweb.service.sign.SignActivityCacheService;
import com.duiba.activity.accessweb.service.token.FormTokenService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.AppIdConstant;
import com.duiba.activity.accessweb.tool.AssembleTool;
import com.duiba.activity.accessweb.tool.DpmInfo;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.ActivityPluginDrawVo;
import com.duiba.activity.accessweb.vo.plugin.PluginOptionVO;
import com.duiba.activity.accessweb.vo.plugin.PluginOptionsVO;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimaps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName:ActivityPluginServiceImpl.java <br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 创建时间：2016年10月19日 上午10:39:30
 * @parameter
 * @since JDK 1.6
 */
@Service
public class ActivityPluginServiceImpl implements ActivityPluginService {
    private static Logger log = LoggerFactory.getLogger(ActivityPluginServiceImpl.class);

    @Autowired
    private ActivityPluginFlowInnerService activityPluginFlowInnerService;
    @Autowired
    private RemoteActivityPluginService remoteActivityPluginService;
    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;
    @Autowired
    private ActivityPluginRandomService activityPluginRandomService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private RemoteActivityPrizeOptionService remoteActivityPrizeOptionService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private RemoteActivityBlackList4DeveloperService remoteActivityBlackList4DeveloperService;
    @Autowired
    private RemoteDuibaActivityAppSpecifyNewService remoteDuibaActivityAppSpecifyNewService;
    @Autowired
    private RemoteActivityConsumerLimitService remoteActivityConsumerLimitService;
    @Autowired
    private FormTokenService formTokenService;
    @Autowired
    private ActivityAddVistTimeService activityAddVistTimeService;
    @Autowired
    private DomainService domainService;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
    @Autowired
    private RemoteAppNewExtraService remoteAppNewExtraService;
    @Autowired
    private ActivitySignService activitySignService;

    @Autowired
    private RocketMqMessageTopic rocketMqMessageTopic;
    @Autowired
    private RemoteHappyCodeOrderService remoteHappyCodeOrderService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private ActivityCommCacheService activityCommCacheService;
    @Autowired
    private RemoteActPreStockService remoteActPreStockService;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private SignActivityCacheService signActivityCacheService;
    @Autowired
    private GlobalRewardService globalRewardService;
    @Autowired
    private AppSlotCacheService appSlotCacheService;

    @Autowired
    private RocketMQMsgProducer rocketMQMsgProducer;




    // 楼层来源类型
    public static final String TYPE_HOME_WEB = "chome";

    /**
     * 扣积分类型
     */
    private static final String CREDITS_MESSAGE_PLUGIN = "plugin";


    @Override
    public ActivityPluginDto[] filterActivityPluginDto(List<ActivityPluginDto> list, ConsumerDto consumerDO, AppSimpleDto app, String url, Date joinTime, boolean signOpen,boolean preview) {
        if (CollectionUtils.isEmpty(list)) {
            return new ActivityPluginDto[0];
        }

        List<ActivityBlackList4DeveloperDto> activityBlackList4DeveloperDtos = new ArrayList<>();
        ActivityBlackList4DeveloperDto activityBlackList4DeveloperDto;
        List<Long> activityIds = new ArrayList<>();
        for (ActivityPluginDto dto : list) {
            activityBlackList4DeveloperDto = new ActivityBlackList4DeveloperDto();
            activityBlackList4DeveloperDto.setActivityId(dto.getId());
            activityBlackList4DeveloperDto.setActivityType(OperatingActivityDto.TypePlugin);
            activityBlackList4DeveloperDto.setDeveloperId(app.getDeveloperId());
            activityBlackList4DeveloperDtos.add(activityBlackList4DeveloperDto);

            activityIds.add(dto.getId());
        }

        Set<Long> baclistIds = findBacklist(activityBlackList4DeveloperDtos);
        Set<Long> appSpecifyIds = findAppSpecify(activityIds, app.getId(), DuibaActivityAppSpecifyNewDto.ACTIVITY_PLUGIN);

        boolean isHomeSource = TYPE_HOME_WEB.equals(url);
        boolean login = !consumerDO.isNotLoginUser() && !ConsumerDto.PREVIEWUSERID.equals(consumerDO.getPartnerUserId());

        //过滤不满足条件的数据
        ActivityPluginDto[] pluginDtos = new ActivityPluginDto[]{null,null,null};
        for (ActivityPluginDto dto : list) {
            if (!filterByBasicRule(dto, baclistIds, appSpecifyIds)) {
                continue;
            }
            if(hiddenPreview(dto,preview)){
                continue;
            }
            pluginDtos[2] = dto;
            if (!filterByPluginRule(signOpen, pluginDtos, consumerDO, url, joinTime, isHomeSource, login)) {
                continue;
            }

        }
        if (pluginDtos[0] == null && pluginDtos[1] == null) {
            return new ActivityPluginDto[0];
        }
        return new ActivityPluginDto[]{pluginDtos[0], pluginDtos[1]};
    }

    /**
     * 是否预览不展示
     * @return
     */
    private boolean hiddenPreview(ActivityPluginDto dto,boolean preview){
        return dto.getHiddenForDeveloper()!=null&&dto.getHiddenForDeveloper()&&preview;
    }

    private Set<Long> findBacklist(List<ActivityBlackList4DeveloperDto> activityBlackList4DeveloperDtos) {
        DubboResult<List<Long>> dubboResult = remoteActivityBlackList4DeveloperService.getActivityBlackList(activityBlackList4DeveloperDtos);
        if (!dubboResult.isSuccess()) {
            log.warn("getActivityBlackList failed, msg={}", dubboResult.getMsg());
            return Collections.emptySet();
        }
        if (dubboResult.getResult() == null) {
            return Collections.emptySet();
        }
        return new HashSet<>(dubboResult.getResult());
    }

    private Set<Long> findAppSpecify(List<Long> activityIds, Long appId, String type) {
        DubboResult<List<Long>> dubboResult = remoteDuibaActivityAppSpecifyNewService.inappSpecifyActivitys(activityIds, appId, type);
        if (!dubboResult.isSuccess()) {
            log.warn("inappSpecifyActivitys failed, msg={}", dubboResult.getMsg());
            return Collections.emptySet();
        }
        if (dubboResult.getResult() == null) {
            return Collections.emptySet();
        }
        return new HashSet<>(dubboResult.getResult());
    }

    private boolean filterByBasicRule(ActivityPluginDto dto, Set<Long> baclistIds, Set<Long> appSpecifyIds) {
        //判断活动是否下架
        if(!isOffTheShelf(dto)){
            return false;
        }
        //过滤 不满足触发 事件的活动
        if (dto.getStatus() != ActivityPluginDto.STATUS_OPEN) {
            return false;
        }
        // 开启了黑名单，并且在黑名单中，过滤掉
        if (dto.isOpenSwitch(ActivityPluginDto.SWITCHES_DEV_BLACKLIST) && baclistIds.contains(dto.getId())) {
            return false;
        }

        // 开启了定向，但是不在定向范围内
        if (dto.isOpenSwitch(ActivityPluginDto.SWITCHES_DIRECT) && !appSpecifyIds.contains(dto.getId())) {
            return false;
        }
        return true;
    }
    private boolean isOffTheShelf(ActivityPluginDto dto){
        //关联活动的开关是否打开
        if(!dto.isOpenSwitch(ActivityPluginDto.SWITCHES_DUIBA_RELATE)){
            return true;
        }
        //判断活动是否下架
        JSONObject extJson = JSONObject.parseObject(dto.getExtJson());
        if(extJson == null){
            return true;
        }
        Long duibaActivityId = extJson.getLong("duibaActivityId");
        Integer deuibaActivityRelate = extJson.getInteger("duibaActivityRelate");
        if(duibaActivityId == null || deuibaActivityRelate == null){
            return true;
        }
        Long appId = RequestLocal.getAppId();
        OperatingActivityDto operatingActivityDto;
        //活动工具
        if(deuibaActivityRelate == 1 || deuibaActivityRelate == 2){
            operatingActivityDto = remoteOperatingActivityServiceNew.findByAppIdAndDuibaHdtoolIdAndDeleted(appId,duibaActivityId,false);
        }else if(deuibaActivityRelate == 3){
                operatingActivityDto = remoteOperatingActivityServiceNew.findByAppIdAndDuibaNgameActivityIdAndDeleted(appId,duibaActivityId,false);
        }else{
            return true;
        }
        return operatingActivityDto == null || operatingActivityDto.getStatus() == 2;
    }

    private boolean filterByPluginRule(boolean signOpen, ActivityPluginDto[] pluginDtos, ConsumerDto consumerDO, String url, Date joinTime, boolean isHomeSource, boolean login) {
        // 过来触发场景及触发时间不满足条件数据
        // 签到和插件活动互斥
        ActivityPluginDto dto = pluginDtos[2];
        // 新人福利插件活动优先级最高，只要存在新人福利活动，优先弹它
        if (!signOpen
                && (pluginDtos[0] == null || (null != dto.getSignType() && ActivityPluginDto.SIGN_TYPE_NEW_USER_WELFARE == dto.getSignType()))
                && activityPluginFlowInnerService.filertTriggerTime(dto, consumerDO, url)) {
            pluginDtos[0] = dto;
            if (!isHomeSource) {
                return false;
            }
        }

        if (login && isHomeSource && pluginDtos[1] == null && activityPluginFlowInnerService.filterExitPlugin(dto, joinTime)) {
            pluginDtos[1] = dto;
            if (signOpen) {
                return false;
            }
        }
        if (pluginDtos[0] != null && pluginDtos[1] != null) {
            return false;
        }
        return true;
    }

    @Override
    public String dojoin(ActivityPluginDto pluDto, ConsumerDto consumerDO, String url,AppSimpleDto app, boolean deductCredits) {
        // 需要扣取的积分
        Integer credits = null;
        // 判断参与次数限制次数
        if (ActivityPluginDto.PLUGDRAW.equals(pluDto.getSubType())) {
            credits = checkCredits(pluDto,consumerDO,app,deductCredits);
        }
        //部分定制id需要发起扣积分请求
        if(credits == null && AppIdConstant.FREE_SUB_CREIDS.contains(app.getId())){
            credits = 0;
        }
        Long needCredits = getNeedCredits(credits, app.getCreditsRate());
        //生成自订单
        ActivityOrderDto order = activityPluginFlowInnerService.createOrder(consumerDO, pluDto.getId(),ActivityOrderDto.TypePlugin,
                needCredits == null? 0: needCredits.intValue());
        //用于统计
        DuibaEventsDispatcher.get().dispatchEvent(new ActivityOrdersEvent(ActivityOrdersEventType.OnOrderSuccess, order));
        final RequestParams requestParams = RequestParams.parse(RequestLocal.getRequest());

        if (needCredits != null) {
            //构造消息体
//            CreditsMessage mq = getCreditsMessage(consumerDO, app, order, pluDto, requestParams, needCredits);
//            DubboResult<Boolean> result = remoteCreditsService.submitSubCredits(mq, rocketMqMessageTopic.getActivitySubCreditsCallback()
//            , OnsMessageTopic.TAG_ACTIVITY_PLUGIN_SUB_CREDITS, ActivityOrderDto.TypePlugin + "-" + order.getOrderNum());
            SubCreditsMsgDto subCreditsMsgDto=buildSubCreditsMsgDto(consumerDO, app, order, pluDto
                    , requestParams, needCredits);
            String msgId=rocketMQMsgProducer.sendMsgWithGzip(rocketMqMessageTopic.getActivitySubCreditsNew()
                    ,OnsMessageTopic.TAG_ACTIVITY_PLUGIN_SUB_CREDITS
                    , ActivityOrderDto.TypePlugin + "-" + order.getOrderNum()
                    ,JSON.toJSONString(subCreditsMsgDto));

            if(StringUtils.isBlank(msgId)){
                log.warn("扣积分请求失败, appId={}, activityId={}, consumerId={}, orderNum={}", app.getId()
                        , order.getDuibaActivityId(), consumerDO.getId(),order.getOrderNum());
            }
        } else {
            //异步 根据概率出奖
            executorService.submit(() -> activityPluginFlowInnerService.asyncConsumerPlug(order, pluDto, app, requestParams));
        }
        return order.getOrderNum();
    }

    private Integer checkCredits(ActivityPluginDto pluDto, ConsumerDto consumerDO, AppSimpleDto app, boolean deductCredits) {
        // 判断是否开启，通过开启时间判断，目前只对插件工具进行判断
        if (!checkStatusByTriggerTime(pluDto.getTriggerTime())) {
            log.info("插件{}未到开启时间", pluDto.getId());
            throw new BusinessException("活动未到开启时间");
        }

        // 获取需要扣取的积分：null，免费使用，不用调扣积分逻辑；不为null，扣相应积分
        if (deductCredits) {
            // 强制按扣积分逻辑处理
            return getNeedCredits(pluDto, consumerDO, app);
        } else {
            Integer credits = checkLimitCountAndReturnNeedCredits(pluDto, consumerDO, app);
            // 如果是免费使用，增加一次免费参与次数
            if (credits == null) {
                incrNum(pluDto.getLimitScope(), consumerDO.getId(), pluDto.getId());
            }
            return credits;

        }
    }

    private void incrNum(Integer limitScope, Long consumerId, Long activityId){
        if (LimitScopeEnum.EVERY_DAY.getId().equals(limitScope)) {
            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNumEveryDay(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.EVERY_WEEK.getId().equals(limitScope)) {
            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNumEveryWeek(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.EVERY_MONTH.getId().equals(limitScope)) {
            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNumEveryMonth(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.FOREVER.getId().equals(limitScope)) {
            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNum(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        }
    }

    @Override
    public Long getNeedCredits(Integer credits, Integer creditsRate){
        if(null == credits){
            return null;
        }
        if(credits.equals(0)){
            return 0L;
        }
        Integer rate = creditsRate == null ? 100 : creditsRate; // 如果app没配积分倍率，默认按100倍算
        Long needCredits = (long) Math.ceil(credits * (long) rate / 100.0);
        return needCredits < 1 ? 1 : needCredits;
    }

    /**
     * 构建扣积分消息
     * @param consumerDO
     * @param app
     * @param order
     * @param activityPluginDto
     * @param requestParams
     * @return
     */
    @Deprecated
    private CreditsMessage getCreditsMessage(ConsumerDto consumerDO, AppSimpleDto app, ActivityOrderDto order, ActivityPluginDto activityPluginDto,
            RequestParams requestParams, Long credits){
        CreditConsumeParams p = new CreditConsumeParams();
        p.setAppKey(app.getAppKey());
        p.setOrderNum(CREDITS_MESSAGE_PLUGIN + "-" + order.getOrderNum());
        p.setUid(consumerDO.getPartnerUserId());
        p.setType(SubCreditsOuterType.HDTOOL.getCode());
        p.setIp(order.getIp());
        p.setFacePrice(0);
        p.setActualPrice(0);
        p.setTimestamp(new Date());
        String transfer = requestParams.getTransfer();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(transfer)) {
            p.setTransfer(transfer);
        }
        p.setDescription(activityPluginDto.getTitle());
        p.setCredits(credits);
        Map<String,String> map = p.toRequestMap(app.getAppSecret());
        CreditsMessage mq = new CreditsMessage();
        mq.setTimestamp(System.currentTimeMillis());
        mq.setAppId(String.valueOf(app.getId()));
        mq.setConsumerId(String.valueOf(order.getConsumerId()));
        mq.setRelationType(CREDITS_MESSAGE_PLUGIN);
        mq.setRelationId(order.getOrderNum());

        Map<String, String> params = Maps.newHashMap();
        String req = JSONObject.toJSONString(requestParams);
        params.put("requestParams", req);
        params.put("credits", credits == null ? "0": credits.toString());
        params.put(CreditsMessageDto.MESSAGE_CHANNEL_TYPE_KEY,CallbackChannelTypeEnum.ROCKETMQ.getType());
        mq.setParams(params);
        String url = AssembleTool.assembleUrl(app.getCreditsConsumeRequestUrl(), map);
        mq.setHttpUrl(url);
        mq.setHttpType(CreditsMessage.HTTP_GET);

        // 必须在组装url之后 AssembleTool.assembleUrl
        map.put("appSecret", app.getAppSecret());
        mq.setAuthParams(map);
        return mq;
    }


    private SubCreditsMsgDto buildSubCreditsMsgDto(ConsumerDto consumerDO, AppSimpleDto app, ActivityOrderDto order
            , ActivityPluginDto activityPluginDto, RequestParams requestParams, Long credits){
        CreditConsumeParams p = new CreditConsumeParams();
        p.setAppKey(app.getAppKey());
        p.setOrderNum(CREDITS_MESSAGE_PLUGIN + "-" + order.getOrderNum());
        p.setUid(consumerDO.getPartnerUserId());
        p.setType(SubCreditsOuterType.HDTOOL.getCode());
        p.setIp(order.getIp());
        p.setFacePrice(0);
        p.setActualPrice(0);
        p.setTimestamp(new Date());
        String transfer = requestParams.getTransfer();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(transfer)) {
            p.setTransfer(transfer);
        }
        p.setDescription(activityPluginDto.getTitle());
        p.setCredits(credits);
        Map<String,String> map = p.toRequestMap(app.getAppSecret());
        SubCreditsMsgDto mq = new SubCreditsMsgDto();
        mq.setTimestamp(System.currentTimeMillis());
        mq.setAppId(app.getId());
        mq.setConsumerId(order.getConsumerId());
        mq.setRelationType(SubCreditsType.PLUGIN);
        mq.setRelationId(order.getOrderNum());

        Map<String, String> params = Maps.newHashMap();
        String req = JSONObject.toJSONString(requestParams);
        params.put("requestParams", req);
        params.put("credits", credits == null ? "0": credits.toString());
        params.put(CreditsMessageDto.MESSAGE_CHANNEL_TYPE_KEY,CallbackChannelTypeEnum.ROCKETMQ.getType());
        mq.setParams(params);
//        String url = AssembleTool.assembleUrl(app.getCreditsConsumeRequestUrl(), map);
//        mq.setHttpUrl(url);
        mq.setHttpType(SubCreditsMsgDto.HTTP_GET);

        // 必须在组装url之后 AssembleTool.assembleUrl
        map.put("appSecret", app.getAppSecret());
        mq.setAuthParams(map);

        mq.setCreditsConsumeRequestUrl(app.getCreditsConsumeRequestUrl());

        mq.setCreditConsumeParams(p);
        mq.setAppSecret(app.getAppSecret());
        mq.setCallbackTopic(rocketMqMessageTopic.getActivitySubCreditsCallbackNew());
        mq.setCallbackTag(OnsMessageTopic.TAG_ACTIVITY_PLUGIN_SUB_CREDITS);
        mq.setCallbackKey(ActivityOrderDto.TypePlugin + "-" + order.getOrderNum());

        return mq;
    }

    @Override
    public Integer getLimitCount(Long activityId, Long consumerId, final ActivityPluginDto pluDto) {
        if (activityId == null || consumerId == null) {
            return 0;
        }
        Integer limintCount = 0;
        if(null == pluDto.getLimitScope()){
            return limintCount;
        }
        if (LimitScopeEnum.EVERY_DAY.getId().equals(pluDto.getLimitScope())) {
            limintCount = remoteActivityConsumerLimitService.findConsumerJoinPlugdrawNumEveryDay(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.EVERY_WEEK.getId().equals(pluDto.getLimitScope())) {
            limintCount = remoteActivityConsumerLimitService.findConsumerJoinPlugdrawNumEveryWeek(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.EVERY_MONTH.getId().equals(pluDto.getLimitScope())) {
            limintCount = remoteActivityConsumerLimitService.findConsumerJoinPlugdrawNumEveryMonth(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
        } else if(LimitScopeEnum.FOREVER.getId().equals(pluDto.getLimitScope())) {
            DubboResult<Integer> ret = remoteActivityConsumerLimitService.findConsumerJoinPlugdrawNum(consumerId, ActivityPluginDto.PLUGDRAW, activityId);
            if (ret.isSuccess() && ret.getResult() != null) {
                limintCount = ret.getResult();
            }
        }
        return limintCount == null ? 0 : limintCount;
    }

    @Override
    public JSONObject getOrderStatus(String orderId, Long consumerId, HttpServletRequest request) throws AccessActivityException {
        JSONObject result = new JSONObject();

        String orderIdTrimed = StringUtils.trimToNull(orderId);

        if(orderIdTrimed == null){
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.MESSAGE_KEY, "参数不正确");
            return result;
        }
        //获取订单信息
        DubboResult<ActivityOrderDto> orderResult = remoteActivityOrderService.findByOrderNum(orderIdTrimed);

        ActivityOrderDto orderDO = orderResult.getResult();

        JSONObject checkResult = statusCheck(orderResult, orderDO, consumerId);
        if (checkResult != null) {
            return checkResult;
        }

        result.put(CommonConstants.SUCCESS_KEY, true);
        result.put(CommonConstants.LOTTERYCODE, 1);

        //查询奖品信息
        DubboResult<ActivityPrizeOptionDto> optionResult = remoteActivityPrizeOptionService.find(orderDO.getActivityOptionId());
        if (!optionResult.isSuccess()) {
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.LOTTERYCODE, -1);
            result.put(CommonConstants.MESSAGE_KEY, "奖品信息 不存在");
            return result;
        }
        //中集卡商品时
        if (ItemDto.TypeCollectGoods.equals(orderDO.getActivityOptionType())) {
            return collectGoods(result, optionResult, orderDO);
        }
        //中开心码时
        if (Objects.equals(ItemDto.TYPE_HAPPY_CODE, orderDO.getActivityOptionType())) {
            return happyCode(result, orderDO);
        }

        //查询兑换记录新
        Map<String, Object> maprecord = Maps.newHashMap();
        maprecord.put("relationId", orderIdTrimed);
        maprecord.put("consumerId", consumerId);
        maprecord.put("type", ConsumerExchangeRecordDto.TypePluginLottery);
        ConsumerExchangeRecordDto record = remoteConsumerExchangeRecordService.selectOneByMapCondition(maprecord).getResult();
        if (record == null) {
            result.put(CommonConstants.SUCCESS_KEY, true);
            result.put(CommonConstants.LOTTERYCODE, 0);
            result.put(CommonConstants.MESSAGE_KEY, "成功-等待");
            return result;
        } else if (!record.getConsumerId().equals(consumerId)) {
            throw new AccessActivityException("无权访问");
        }
        Map<String, Object> lottery = Maps.newHashMap();
        //图片
        if(optionResult.getResult()!=null) {
            lottery.put("imgurl", optionResult.getResult().getLogo());
        }
        //查看使用方法地址
        String actDomain = domainService.getSystemDomain(orderDO.getAppId()).getActivityDomain();
        lottery.put("link", actDomain + "/activity/takePrizeNew?recordId=" + record.getId() + "&dbnewopen");
        lottery.put("type", orderDO.getActivityOptionType());
        lottery.put("id", orderDO.getActivityOptionId());
        lottery.put(CommonConstants.TITLE, orderDO.getActivityOptionName());
        if (ItemDto.TypeCoupon.equals(orderDO.getActivityOptionType()) || ItemDto.LuckyBag.equals(orderDO.getActivityOptionType())) {
            if (ItemDto.TypeCoupon.equals(orderDO.getActivityOptionType()) && record.getOrderId() == null) {
                result.put(CommonConstants.SUCCESS_KEY, true);
                result.put(CommonConstants.LOTTERYCODE, 0);
                result.put(CommonConstants.MESSAGE_KEY, "成功-等待");
                return result;
            }
            activityPluginRandomService.getOrderForOther(lottery, orderDO, request, record);
        }
        if(orderDO.getActivityOptionId()==null){
            lottery.put("isAppLucky", true);
        }
        result.put("lottery", lottery);
        result.put(CommonConstants.TAKESUCCESS, false);

        return result;
    }

    private JSONObject happyCode(JSONObject result, ActivityOrderDto pluginOrder) {
        String orderId = pluginOrder.getFacePrice();
        HappyCodeOrderDto order = remoteHappyCodeOrderService.findOrderById(Long.valueOf(orderId));
        if (order == null) {
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.LOTTERYCODE, -1);
            result.put(CommonConstants.MESSAGE_KEY, "开心码订单 不存在");
            return result;
        }
        Map<String, Object> lottery = Maps.newHashMap();
        //图片
        lottery.put("happyCode", order.getHappyCode());
        lottery.put("type", ItemDto.TYPE_HAPPY_CODE);
        lottery.put("id", pluginOrder.getActivityOptionId());
        lottery.put("phaseNumber", order.getPhaseNumber());
        lottery.put(CommonConstants.TITLE, pluginOrder.getActivityOptionName());
        result.put("lottery", lottery);
        result.put(CommonConstants.TAKESUCCESS, false);
        return result;
    }

    private JSONObject collectGoods(JSONObject result, DubboResult<ActivityPrizeOptionDto> optionResult, ActivityOrderDto orderDO) {
        Map<String, Object> lottery = Maps.newHashMap();
        //图片
        lottery.put("imgurl", optionResult.getResult().getLogo());
        lottery.put("type", orderDO.getActivityOptionType());
        lottery.put("id", orderDO.getActivityOptionId());
        lottery.put(CommonConstants.TITLE, orderDO.getActivityOptionName());
        //集卡红包金额
        Long bonus = globalRewardService.getPrizePrice(String.valueOf(orderDO.getOrderNum()), AccountBizTypeEnum.BIZ_PLUGIN_ACTIVITY, orderDO.getItemId());
        if (bonus != null && bonus > 0) {
            lottery.put("bonus", bonus);
        }
        result.put("lottery", lottery);
        result.put(CommonConstants.TAKESUCCESS, false);
        return result;
    }


    private JSONObject statusCheck(DubboResult<?> dubboResult, ActivityOrderDto activityOrderDto, Long cid) {
        JSONObject result = new JSONObject();
        //订单校验
        if (!dubboResult.isSuccess() || activityOrderDto == null || !Objects.equals(activityOrderDto.getConsumerId(), cid)) {
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.LOTTERYCODE, -1);
            result.put(CommonConstants.MESSAGE_KEY, "无权查看");
            return result;
        }

        //订单状态校验
        if (Objects.equals(ActivityOrderDto.ConsumeCreditsSuccess, activityOrderDto.getConsumeCreditsStatus())) {
            if (ActivityPrizeOptionDto.Prize_Type_Thanks.equals(activityOrderDto.getActivityOptionType())) {
                result.put(CommonConstants.SUCCESS_KEY, true);
                result.put(CommonConstants.LOTTERYCODE, 2);
                result.put("type", activityOrderDto.getActivityOptionType());
                return result;
            }
        } else if (Objects.equals(ActivityOrderDto.ConsumeCreditsFail, activityOrderDto.getConsumeCreditsStatus())) {
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.LOTTERYCODE, -1);
            result.put(CommonConstants.MESSAGE_KEY, activityOrderDto.getError4admin());
            return result;
        } else {
            result.put(CommonConstants.SUCCESS_KEY, true);
            result.put(CommonConstants.LOTTERYCODE, 0);
            result.put(CommonConstants.MESSAGE_KEY, "成功-等待");
            return result;
        }
        return null;
    }


    @Override
    public JSONObject plugDrawInfo(Long consumerId, Long activityId, AppSimpleDto app, HttpServletRequest request) {
        JSONObject jsonObject = new JSONObject();
        DubboResult<ActivityPluginDto> result = remoteActivityPluginService.findById(activityId);
        if (result.isSuccess() && result.getResult() != null) {
            ActivityPluginDto plugin = result.getResult();
            if (ActivityPluginDto.STATUS_OPEN != plugin.getStatus()) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "该活动没有开启");
                return jsonObject;
            }

            List<Long> activityIds = Lists.newArrayList();
            activityIds.add(plugin.getId());
            Set<Long> appSpecifyIds = findAppSpecify(activityIds, app.getId(), DuibaActivityAppSpecifyNewDto.ACTIVITY_PLUGIN);
            // 开启了定向，但是不在定向范围内
            if (plugin.isOpenSwitch(ActivityPluginDto.SWITCHES_DIRECT) && !appSpecifyIds.contains(plugin.getId())) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "该app不支持此活动");
                return jsonObject;
            }

            List<ActivityBlackList4DeveloperDto> activityBlackList4DeveloperDtos = new ArrayList<>();
            ActivityBlackList4DeveloperDto activityBlackList4DeveloperDto;
            activityBlackList4DeveloperDto = new ActivityBlackList4DeveloperDto();
            activityBlackList4DeveloperDto.setActivityId(plugin.getId());
            activityBlackList4DeveloperDto.setActivityType(OperatingActivityDto.TypePlugin);
            activityBlackList4DeveloperDto.setDeveloperId(app.getDeveloperId());
            activityBlackList4DeveloperDtos.add(activityBlackList4DeveloperDto);
            Set<Long> baclistIds = findBacklist(activityBlackList4DeveloperDtos);
            // 开启了黑名单，并且在黑名单中，过滤掉
            if (plugin.isOpenSwitch(ActivityPluginDto.SWITCHES_DEV_BLACKLIST) && baclistIds.contains(plugin.getId())) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "该app不支持此活动");
                return jsonObject;
            }
            long addTime = activityAddVistTimeService.getAddTime(consumerId, activityId, app);
            Integer limitCount = getLimitCount(activityId, consumerId, plugin);
            ActivityPluginDrawVo pluginVo = BeanUtils.copy(plugin, ActivityPluginDrawVo.class);
            if (pluginVo.getLimitCount() != null && pluginVo.getLimitCount() > 0 && pluginVo.getLimitCount() + addTime >= limitCount) {
                pluginVo.setLimitCount(pluginVo.getLimitCount() + (int) addTime - limitCount);
            }
            jsonObject.put("plugin", pluginVo);
            String dpm = app.getId() + ".3.1.0";
            jsonObject.put("st_info_dpm", dpm);
            jsonObject.put("st_info_dpm_again", app.getId() + ".3.5.0");
            jsonObject.put("token", formTokenService.getConsumerToken(RequestLocal.getCid()));
            jsonObject.put(CommonConstants.SUCCESS_KEY, true);
            //pv,uv
            JsonActivityAccessLog.logPlugin(RequestTool.getIpAddr(request), app.getId(),
                    consumerId, activityId);
            // suc=1表示正常进入首页
            AccessLogFilter.putExPair("suc", "1");
            AccessLogFilter.putExPair("id", Objects.toString(activityId, ""));
            AccessLogFilter.putExPair("type", ActivityPluginDto.PLUGDRAW);
            // 广告位id
            AccessLogFilter.putExPair("duibaSlotId", Objects.toString(appSlotCacheService.getSlotIdWithDefaultType(app.getId(), plugin.getChannelType()),""));
        } else {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "活动不存在");
            return jsonObject;
        }
        return jsonObject;
    }

    @Override
    public void getElement(JSONObject jsonObject, Long activityId, Long consumerId, AppSimpleDto app) {
        long addTime = activityAddVistTimeService.getAddTime(consumerId, activityId, app);
        DubboResult<ActivityPluginDto> result = remoteActivityPluginService.findById(activityId);
        if (!(result.isSuccess() && result.getResult() != null)) {
            return;
        }
        ActivityPluginDto plugin = result.getResult();
        Integer limitCount = getLimitCount(activityId, consumerId, plugin);
        int remain = plugin.getLimitCount() + (int) addTime - limitCount;
        jsonObject.put("limitCount", remain > 0 ? remain : 0);
        jsonObject.put("limitScope", plugin.getLimitScope());
        if(plugin.isOpenSwitch(ActivityPluginDto.SWITCHES_DEDUCTION_CREDITS)) {
            if(null == plugin.getCreditsPrice()){
                log.info("插件'{}'积分价值设置有误，支持扣积分，但积分价值为{}了", plugin.getId(),plugin.getCreditsPrice());
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "积分价值设置有误");
            }
            jsonObject.put("creditsPrice", getNeedCredits(plugin.getCreditsPrice(), app.getCreditsRate())); // 积分价值
        }
    }

    @Override
    public int getPrizeTimes(Long activityId, Long consumerId, AppSimpleDto app) {
        long addTime = activityAddVistTimeService.getAddTime(consumerId, activityId, app);
        DubboResult<ActivityPluginDto> result = remoteActivityPluginService.findById(activityId);
        if (!(result.isSuccess() && result.getResult() != null)) {
            return 0;
        }
        ActivityPluginDto plugin = result.getResult();
        Integer limitCount = getLimitCount(activityId, consumerId, plugin);
        int remain = plugin.getLimitCount() + (int) addTime - limitCount;
        return remain > 0 ? remain : 0;
    }

    @Override
    public boolean checkLimitCount(ActivityPluginDto pluDto, Integer limintCount, ConsumerDto consumer, JSONObject jsonObject, AppSimpleDto app) {
        long addTime = activityAddVistTimeService.getAddTime(consumer.getId(), pluDto.getId(), app);
        if (pluDto.getLimitCount() != null && pluDto.getLimitCount() > 0 && (pluDto.getLimitCount() + addTime) <= limintCount) {
            if (jsonObject != null) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                if (consumer.isNotLoginUser()) {
                    jsonObject.put(CommonConstants.STATUS, 1);
                    jsonObject.put(CommonConstants.MESSAGE_KEY, "请先登录");
                    //参与类型（0：每天，1：永久）
                } else if (pluDto.getLimitScope() == 0) {
                    jsonObject.put(CommonConstants.STATUS, HtdoolConstants.STATUS_EVERYDAY);
                    jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_JRCSYYW);
                } else {
                    jsonObject.put(CommonConstants.STATUS, HtdoolConstants.STATUS_FOREVER);
                    jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_YJCSYYW);
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 校验是否可以继续使用插件活动，并返回需要扣取的积分
     * @param pluDto
     * @param consumerDto
     * @param app
     * @return
     *  返回为null：免费使用，不用调扣积分逻辑；
     *  返回不为null：扣相应积分
     */
    private Integer checkLimitCountAndReturnNeedCredits(ActivityPluginDto pluDto, ConsumerDto consumerDto, AppSimpleDto app) {
        Integer limitCount = getLimitCount(pluDto.getId(), consumerDto.getId(), pluDto);
        long addTime = activityAddVistTimeService.getAddTime(consumerDto.getId(), pluDto.getId(), app);
        // 免费次数为空或为0，无限制使用;免费次数还没用完，可以继续使用
        if(pluDto.getLimitCount() == null || pluDto.getLimitCount().equals(0) || (pluDto.getLimitCount() + addTime) > limitCount){
            return null;
        }
        // 免费次数已用完，需要走扣积分逻辑
        return getNeedCredits(pluDto, consumerDto, app);
    }

    // 免费次数已用完，需要走扣积分逻辑，获取需要扣减的积分
    private Integer getNeedCredits(ActivityPluginDto pluDto, ConsumerDto consumerDto, AppSimpleDto app){
        if(app.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType) || (!pluDto.isOpenSwitch(ActivityPluginDto.SWITCHES_DEDUCTION_CREDITS))){
            // 如果不支持扣积分，不能使用
            checkLimitScopeThrow(consumerDto, pluDto.getLimitScope());
            return null;
        }else{
            // 有设积分价值，扣相应积分
            if(null == consumerDto.getCredits()){
                log.info("用户'{}'积分异常，积分为null，用户信息：{}", consumerDto.getId(), JSON.toJSONString(consumerDto));
                throw new BusinessException("积分异常");
            }
            if(null == pluDto.getCreditsPrice() || pluDto.getCreditsPrice().compareTo(0) < 0){
                log.info("插件'{}'积分价值设置有误，支持扣积分，但积分价值为{}。插件对象：{}", pluDto.getId(), pluDto.getCreditsPrice(), JSON.toJSONString(pluDto));
                throw new BusinessException("积分价值设置有误");
            }
            if(consumerDto.getCredits().compareTo(Long.valueOf(pluDto.getCreditsPrice())) < 0){
                log.info("抱歉，积分不足。用户信息：{}，活动信息：{}", JSON.toJSONString(consumerDto), JSON.toJSONString(pluDto));
                throw new BusinessException("抱歉，积分不足");
            }
            return pluDto.getCreditsPrice();
        }
    }

    // 如果不支持扣积分，不能使用
    private void checkLimitScopeThrow(ConsumerDto consumerDto, Integer limitScope){
        if(consumerDto.isNotLoginUser()){
            log.info("请先登录");
            throw new BusinessException("请先登录");
        }else if(limitScope.equals(LimitScopeEnum.EVERY_DAY.getId())){
            //参与类型-每天
            log.info(CommonConstants.MSG_ERROR_JRCSYYW);
            throw new BusinessException(CommonConstants.MSG_ERROR_JRCSYYW);
        }else if(limitScope.equals(LimitScopeEnum.EVERY_WEEK.getId())){
            //参与类型-每周
            log.info(CommonConstants.MSG_ERROR_MZCSYYW);
            throw new BusinessException(CommonConstants.MSG_ERROR_MZCSYYW);
        }else if(limitScope.equals(LimitScopeEnum.EVERY_MONTH.getId())){
            //参与类型-每月
            log.info(CommonConstants.MSG_ERROR_MYCSYYW);
            throw new BusinessException(CommonConstants.MSG_ERROR_MYCSYYW);
        }else {
            //参与类型-永久
            log.info(CommonConstants.MSG_ERROR_YJCSYYW);
            throw new BusinessException(CommonConstants.MSG_ERROR_YJCSYYW);
        }
    }

    @Override
    public boolean isOpenFlowRule(Long appId) {
        return advancedCacheClient.getWithCacheLoader(RedisKeyFactory.K003.toString() + appId, 3, TimeUnit.MINUTES, () -> {
            AppNewExtraDto appNewExtraDto = remoteAppNewExtraService.findByAppId(appId).getResult();
            if (appNewExtraDto == null) {
                return false;
            }
            return appNewExtraDto.getFlowRule() != null && appNewExtraDto.getFlowRule();
        });
    }

    @Override
    public JSONObject buildPluginSign(HttpServletRequest request, ConsumerDto consumerDto, String url) {
        Long appId = consumerDto.getAppId();
        SignConfigDto sign = signActivityCacheService.findSignConfigByAppId(appId);
        JSONObject retJson = new JSONObject();
        // 如果没有插件活动则要显示签到弹层
        if (!activitySignService.isTodayNotSign(consumerDto, sign, url)) {
            retJson.put("signOpen", false);
            return retJson;
        }
        String skinWidth =request.getParameter("skinWidth");
        String brickStr = activitySignService.findSysSign(skinWidth);
        if (StringUtils.isBlank(brickStr)) {
            retJson.put("signOpen", false);
            return retJson;
        }
        Long signId = sign.getId();
        Long cid = consumerDto.getId();
        //埋点数据
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(appId);
        JSONObject defaultJson = new JSONObject();
        defaultJson.put(CommonConstants.ST_INFO_DPM_IMG, DpmInfo.setStInfo(appId, cid, appId + ".11." + signId + ".0", "212." + signId + ".0.0", domainConfigDto.getEmbedDomain()));
        defaultJson.put("brick", brickStr);
        defaultJson.put("ruleDescription", sign.getRuleDescription());
        defaultJson.put("rateDescription", sign.getRateDescription());
        defaultJson.put("activityId", sign.getId());
        defaultJson.put("type", "sign");
        retJson.put("default", defaultJson);
        // pv,uv统计数据
        JsonActivityAccessLog.logSign(RequestTool.getIpAddr(request), request.getHeader("User-Agent"), appId, cid, sign.getId());

        AccessLogExUtil.putAccessLogExPair(true, 500, 1, PageBizTypeEnum.ACT_INDEX, sign.getId(), consumerDto.getAppId(),
                ActivityUniformityTypeEnum.BarrageSign, consumerDto.isNotLoginUser(),
                consumerDto.getCredits(), null, null, null, null);

        retJson.put("signOpen", true);
        return retJson;
    }

//    @Override
//    public String dojoinPlugdraw(ActivityPluginDto pluDto, ConsumerDto consumerDO,AppSimpleDto app) throws StatusException {
//
//            // 参与次数+1
//            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNumEvery(consumerDO.getId(), ActivityPluginDto.PLUGDRAW, pluDto.getId());
//            //限制次数+1
//            remoteActivityConsumerLimitService.incrConsumerJoinPlugdrawNum(consumerDO.getId(), ActivityPluginDto.PLUGDRAW, pluDto.getId());
//
//        //生成自订单
//        ActivityOrderDto order = activityPluginFlowInnerService.createOrder(consumerDO, pluDto.getId(),ActivityOrderDto.TypePlugin);
//        //用于统计
//        DuibaEventsDispatcher.get().dispatchEvent(new ActivityOrdersEvent(ActivityOrdersEventType.OnOrderSuccess, order));
//        final RequestParams requestParams = RequestParams.parse(RequestLocal.getRequest());
//        //部分定制id需要发起扣积分请求
//        if (AppIdConstant.FREE_SUB_CREIDS.contains(app.getId())) {
//            //构造消息体
//            CreditsMessage mq = getCreditsMessage(consumerDO, app, order, pluDto, requestParams);
//            DubboResult<Boolean> result = remoteCreditsService.submitSubCredits(mq, onsMessageTopic.getActivitySubCreditsCallback()
//            , OnsMessageTopic.TAG_ACTIVITY_PLUGIN_SUB_CREDITS, ActivityOrderDto.TypePlugin + "-" + order.getOrderNum());
//            if(!result.isSuccess() || !result.getResult()){
//                log.warn("扣积分请求失败, appId={}, activityId={}, consumerId={}, msg={}", app.getId(), order.getDuibaActivityId(), consumerDO.getId(), result.getMsg());
//            }
//        } else {
//            //异步 根据概率出奖
//            executorService.submit(() -> activityPluginFlowInnerService.asyncConsumerPlug(order, pluDto, app, requestParams));
//        }
//        return order.getOrderNum();
//    }

    @Override
    public Map<Long,String> findBrickByIds(List<Long> ids) {
        return activityCommCacheService.findPluginBrickByIds(ids);
    }

    @Override
    public ActivityPluginDto findById(Long activityId) {
        return activityCacheService.findPluginById(activityId);
    }

    @Override
    public DubboResult<List<ActivityPluginDto>> findActivityPluginInfoList() {
        List<ActivityPluginDto> plugins = advancedCacheClient.get(RedisKeyFactory.K061.toString());
        if (null != plugins) {
            return DubboResult.successResult(plugins);
        }
        DubboResult<List<ActivityPluginDto>> result = remoteActivityPluginService.findActivityPluginInfoList();
        if (null == result || !result.isSuccess() || null == result.getResult()) {
            return result;
        }
        plugins = result.getResult();
        advancedCacheClient.set(RedisKeyFactory.K061.toString(), plugins, 1, TimeUnit.MINUTES);
        return DubboResult.successResult(plugins);
    }

    @Override
    public Integer unlocking(Long id, Long consumerId) {
        ActivityPluginDto plugin = activityCacheService.findPluginById(id);
        if (plugin == null) {
            throw new BusinessException("插件活动不存在");
        }
        if (Objects.equals(plugin.getDeleted(), 1)) {
            throw new BusinessException("插件活动已删除");
        }
        Date now = new Date();
        //校验当前时间是否在解锁区间内
        if (!checkUnlockingDateArea(now, plugin.getExtJson())) {
            //未到解锁时间
            return UnlockingRecordCheckHandler.NOT_UNLOCKING_TIME;
        }
        String key = RedisKeyFactory.K061.toString() + id + "_" + consumerId;
        Date oldDate = advancedCacheClient.get(key);
        if (oldDate != null && checkUnlockingDateArea(oldDate, plugin.getExtJson())) {
            //提醒前端用户重复解锁了
            return UnlockingRecordCheckHandler.UNLOCKED;
        }
        advancedCacheClient.set(key, now, 30, TimeUnit.DAYS);
        //解锁成功
        return UnlockingRecordCheckHandler.UNLOCKING;
    }

    @Override
    public boolean checkUnlockingRecord(Long id, String extJson, Long consumerId) {
        String key = RedisKeyFactory.K061.toString() + id + "_" + consumerId;
        Date unlockingDate = advancedCacheClient.get(key);
        return checkUnlockingDateArea(unlockingDate, extJson);
    }

    @Override
    public AllPluginOptionRsp findAllOptionInfo(String idList) {
        AllPluginOptionRsp rsp = new AllPluginOptionRsp();
        List<Long> ids = Splitter.on(",").omitEmptyStrings().splitToList(idList).stream().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            rsp.setList(Collections.emptyList());
            return rsp;
        }
        DubboResult<List<ActivityPrizeOptionDto>> result = remoteActivityPrizeOptionService.queryActivityOptionsByConfigIds(ids, ActivityPrizeOptionDto.Activity_Type_Plugin);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getResult())) {
            rsp.setList(Collections.emptyList());
            return rsp;
        }
        List<ActivityPrizeOptionDto> allOption = result.getResult();
        List<Long> prizeIds = Lists.transform(allOption, ActivityPrizeOptionDto::getId);
        List<ActPreStockDto> stocks = remoteActPreStockService.findPreStockListByPrizeIds(prizeIds, ActivityPrizeOptionDto.Activity_Type_Plugin, 0L);
        Map<Long, ActPreStockDto> stockMap = Maps.uniqueIndex(stocks, ActPreStockDto::getRelationPrizeId);
        ImmutableListMultimap<Long, ActivityPrizeOptionDto> prizeMap = Multimaps.index(allOption, ActivityPrizeOptionDto::getActivityId);
        rsp.setList(prizeMap.keySet().stream()
                .filter(key -> null != prizeMap.get(key))
                .map(key -> {
                    PluginOptionsVO pluginOptionsVO = new PluginOptionsVO();
                    List<ActivityPrizeOptionDto> prizes = prizeMap.get(key);
                    pluginOptionsVO.setPluginId(key);
                    pluginOptionsVO.setOptions(prizes.stream()
                            .filter(p -> !Objects.equals(p.getPrizeType(), ActivityPrizeOptionDto.PrizeTypeLuckBag))
                            .filter(p -> !Objects.equals(p.getPrizeType(), ActivityPrizeOptionDto.Prize_Type_Thanks))
                            .filter(p -> stockMap.get(p.getId()) != null)
                            .map(p -> {
                                PluginOptionVO pluginOptionVO = new PluginOptionVO();
                                pluginOptionVO.setCount(stockMap.get(p.getId()).getPrizeQuantity());
                                pluginOptionVO.setId(p.getId());
                                pluginOptionVO.setImg(p.getLogo());
                                pluginOptionVO.setName(p.getDescription());
                                pluginOptionVO.setType(p.getPrizeType());
                                return pluginOptionVO;
                            })
                            .collect(Collectors.toList()));
                    return pluginOptionsVO;
                })
                .collect(Collectors.toList()));
        return rsp;
    }

    /**
     * 校验设置的时间是否在配置的解锁时间区间内
     * @param now
     * @param extJson
     * @return
     */
    private boolean checkUnlockingDateArea(Date now, String extJson) {
        if (now == null || StringUtils.isBlank(extJson)) {
            return false;
        }
        JSONObject json = JSONObject.parseObject(extJson);
        Date start = DateUtils.getMinuteDate(json.getString(ActivityPluginDto.UNLOCKING_RECORD_START_TIME));
        Date end = DateUtils.getMinuteDate(json.getString(ActivityPluginDto.UNLOCKING_RECORD_END_TIME));
        return now.equals(start) || now.equals(end) || now.after(start) && now.before(end);
    }

    /**
     * 根据触发时间判断插件是否可触发
     * @param triggerTime
     * @return
     *
     * triggerTime格式：
     * [
        {
            "startDate":"2018-04-27",
            "endDate":"2018-04-29",
            "times":
            [
                {
                    "startHour":15,
                    "startMinute":30,
                    "endHour":16,
                    "endMinute":0
                },
                {
                    "startHour":17,
                    "startMinute":30,
                    "endHour":18,
                    "endMinute":0
                }
            ]
        }
       ]
     */
    private boolean checkStatusByTriggerTime(String triggerTime){
        List<TriggerTimeDayDto> triggerTimeDayDtoList = JSON.parseArray(triggerTime, TriggerTimeDayDto.class);
        if(CollectionUtils.isEmpty(triggerTimeDayDtoList)){
            return true;
        }
        DateTime now = DateTime.now();
        for(TriggerTimeDayDto triggerTimeDayDto: triggerTimeDayDtoList){
            DateTime startDate = DateTime.parse(triggerTimeDayDto.getStartDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
            if(now.isBefore(startDate)){
                // 小于触发日期，返回
                continue;
            }
            DateTime endDate = DateTime.parse(triggerTimeDayDto.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd")).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59);
            if(now.isAfter(endDate)){
                // 大于结束日期，返回
                continue;
            }
            if(CollectionUtils.isEmpty(triggerTimeDayDto.getTimes())){
                // 没有设置小时维度，全天可触发
                return true;
            }
            // 校验每日的触发时间
            Optional<Boolean> optional = checkStatusByTriggrtTimeOfHour(triggerTimeDayDto.getTimes());
            if(optional.isPresent() && optional.get()){
                return true;
            }
        }
        return false;
    }

    // 校验每日的触发时间
    private Optional<Boolean> checkStatusByTriggrtTimeOfHour(List<TriggerTimeHourDto> triggerTimeHourDtoList){
        DateTime now = DateTime.now();
        for(TriggerTimeHourDto triggerTimeHourDto: triggerTimeHourDtoList){
            DateTime startHour = now.withHourOfDay(triggerTimeHourDto.getStartHour()).withMinuteOfHour(triggerTimeHourDto.getStartMinute()).withSecondOfMinute(0);
            if(now.isBefore(startHour)){
                continue;
            }
            DateTime endHour = now.withHourOfDay(triggerTimeHourDto.getEndHour()).withMinuteOfHour(triggerTimeHourDto.getEndMinute()).withSecondOfMinute(59);
            if(now.isAfter(endHour)){
                continue;
            }
            return Optional.of(true);
        }
        return Optional.empty();
    }

}
