package com.duiba.activity.accessweb.service.activity.sharecode;

import cn.com.duiba.activity.common.center.api.dto.sharecode.InviteBonusRecordDto;
import cn.com.duiba.activity.common.center.api.params.UserInviteParam;
import cn.com.duiba.activity.common.center.api.rsp.sharecode.InviteResponseDto;
import cn.com.duiba.boot.exception.BizException;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.rsp.sharecode.InviedUserList;
import com.duiba.activity.accessweb.vo.activity.sharecode.HelpResultVO;

/**
 * <AUTHOR>
 * @date 2018/04/11
 */
public interface ShareCodeService {

    /**
     * 助力接口
     * @param checkParameter 助力相关参数
     * @return Result<Long>
     */
    Result<HelpResultVO> doHelpNew(CheckParameter checkParameter);

    /**
     * 助力接口
     * @param userInviteParam 助力相关参数
     * @param operatingActivityId 入库活动id
     * @return Result<Long>
     */
    Result<HelpResultVO> doHelp(UserInviteParam userInviteParam, Long operatingActivityId);

    /**
     * 获取奖励记录
     * @param bizId bizId
     * @return Result<InviteBonusRecordDto>
     */
    Result<InviteBonusRecordDto> findByBizId(Long bizId);

    Result<Long> doHelp2(UserInviteParam userInviteParam, Long operatingActivityId);

    /**
     * 校验分享码
     * @param userInviteParam
     * @return
     */
    Result checkShareCode(UserInviteParam userInviteParam);

    /**
     * 查询助力人列表
     * @param shareCode 分享码
     * @param activityId 活动id
     * @param activityType 活动类型
     * @param count 查询数量
     * @param reversed 是否倒序
     * @return
     */
    Result<InviedUserList> findInvitedConsumers(String shareCode, Long activityId, String activityType, int count, boolean reversed);

    /**
     * 建立用户邀请关系（还未发放奖励）
     * @param checkParameter
     * @return
     * @throws BizException
     */
    InviteResponseDto startHelpNew(CheckParameter checkParameter) throws BizException;

    /**
     * 助力接口   定制  针对 发放集卡 扣除 卡片库存
     * @param checkParameter 助力相关参数
     * @return Result<Long>
     */
	Result<HelpResultVO> doHelpCustom(CheckParameter checkParameter);
}
