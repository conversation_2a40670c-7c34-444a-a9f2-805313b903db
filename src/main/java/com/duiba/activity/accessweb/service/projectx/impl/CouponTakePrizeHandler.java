package com.duiba.activity.accessweb.service.projectx.impl;

import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.prize.center.api.dto.projectx.AwardRequest;
import cn.com.duiba.prize.center.api.dto.projectx.PrizeRecordDto;
import cn.com.duiba.prize.center.api.enums.AwardSourceEnums;
import cn.com.duiba.prize.center.api.remoteservice.projectx.RemoteProjectXService;
import com.duiba.activity.accessweb.service.projectx.TakePrizeHandler;
import com.duiba.activity.accessweb.vo.projectx.TakePrizeDto;
import com.duiba.activity.accessweb.vo.projectx.TakePrizeRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/07/05
 */
@Service
public class CouponTakePrizeHandler implements TakePrizeHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(VirtualTakePrizeHandler.class);

    @Resource
    private RemoteProjectXService remoteProjectXService;

    @Override
    public String getItemType() {
        return ItemDto.TypeCoupon;
    }

    @Override
    public Result<Long> takePrize(PrizeRecordDto prizeRecordDto, TakePrizeRequest takePrizeRequest, TakePrizeDto takePrizeDto) {
        AwardRequest awardRequest = new AwardRequest();

        awardRequest.setPrizeRecordId(prizeRecordDto.getId());
        awardRequest.setProjectOrderNo(prizeRecordDto.getProjectOrderNo());
        if(takePrizeDto.getItemKeyDto().getAppItem() != null) {
            awardRequest.setAppItemId(prizeRecordDto.getItemId());
        } else {
            awardRequest.setItemId(prizeRecordDto.getItemId());
        }
        awardRequest.setItemType(prizeRecordDto.getItemType());
        awardRequest.setDeveloperBizId(prizeRecordDto.getDeveloperBizId());
        awardRequest.setUserAgent(takePrizeDto.getUserAgent());
        awardRequest.setProjectId(prizeRecordDto.getProjectId());
        awardRequest.setIp(takePrizeDto.getIp());
        awardRequest.setTransfer(takePrizeDto.getCookies().get("transfer"));
        awardRequest.setConsumerId(prizeRecordDto.getConsumerId());
        awardRequest.setAppId(prizeRecordDto.getAppId());
        awardRequest.setDegree(prizeRecordDto.getDegree());
        try {
            return ResultBuilder.success(remoteProjectXService.takePrize(awardRequest));
        } catch (Exception e) {
            LOGGER.warn("优惠券领奖失败:{}", prizeRecordDto.getId(), e);
            return ResultBuilder.fail("领奖失败，请稍后再试");
        }
    }
}
