package com.duiba.activity.accessweb.enums.wechatRed;

public enum WeChatRedStatusEnum {

    /**
     * 状态
     */
    WAIT_DRAW(0, "待领取"),
    DRAW_SUCCESS(1, "领取成功"),
    DRAW_FAIL(2, "领取失败"),
    DRAW_LIMIT(3, "不可领取"),
    ;


    private Integer status;

    private String desc;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    WeChatRedStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
