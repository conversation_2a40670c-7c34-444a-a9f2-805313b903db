package com.duiba.activity.accessweb.enums;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2018/12/28 15:26
 * @description: 书旗春节组队pk-开奖状态
 */
public enum ShuQiTeamBattlePrizeStatusEnum {

    CAN_NOT_OPEN_PRIZE(0,"不可开奖，没有胜利，页面不需要有按钮"),
    CAN_OPEN_PRIZE(1,"可开奖"),
    HAS_OPEN_PRIZE(2,"已开奖");

    private Integer status;

    private String desc;

    ShuQiTeamBattlePrizeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
