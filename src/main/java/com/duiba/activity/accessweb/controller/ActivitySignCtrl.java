package com.duiba.activity.accessweb.controller;

import cn.com.duiba.activity.center.api.dto.sign.SignConfigDto;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.wolf.perf.timeprofile.DBTimeProfile;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.service.ActivitySignService;
import com.duiba.activity.accessweb.service.sign.SignActivityCacheService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.InnerLogUtil;
import com.duiba.activity.accessweb.tool.PluginDomainCrossUtils;
import com.duiba.activity.accessweb.tool.RequestParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/** 
 * ClassName:ActivityPluginCtrl.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年11月04日 上午10:48:09 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Controller
@RequestMapping("/activitySign")
public class ActivitySignCtrl {

    private static Logger log = LoggerFactory.getLogger(ActivitySignCtrl.class);

	@Autowired
	private ActivitySignService activitySignService;
	@Autowired
    private PluginDomainCrossUtils pluginDomainCrossUtils;
    @Autowired
    private SignActivityCacheService signActivityCacheService;
	
	/**
	 * 下单流程
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
    @RequestMapping(value="/doJoin",method = {RequestMethod.GET,RequestMethod.POST})
    public String doJoin(HttpServletRequest request,HttpServletResponse response) {
	    response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_ORIGIN, pluginDomainCrossUtils.allowDomain(request,RequestLocal.getAppId()));
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_METHODS, "POST,GET");
        response.setHeader(CommonConstants.ACCESS_CONTROL_MAX_AGE, "3600");
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_HEADERS, "x-requested-with");
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        
        JSONObject jsonObject = new JSONObject();
        try {
            DBTimeProfile.enter("doJion begin");
            //查询客户信息。
            ConsumerDto consumerDO = RequestLocal.getConsumerDO();
            if(consumerDO == null){
                log.info("根据客户id未查找到对应客户信息");
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "根据客户id未查找到对应客户信息");
                return jsonObject.toJSONString();
            }
            if(consumerDO.isNotLoginUser()){
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "请先登陆");
                return jsonObject.toJSONString();
            }
            jsonObject.put(CommonConstants.SUCCESS_KEY, true);
            SignConfigDto dto = signActivityCacheService.findSignConfigByAppId(consumerDO.getAppId());
            if(dto != null){
                //调用活动中心接口查询满足条件的 签到活动
                JSONObject retJson =activitySignService.dojoin(dto.getId(), consumerDO.getAppId(), consumerDO.getId(), RequestTool.getIpAddr(request), request.getHeader("User-Agent"), RequestParams.getTransfer(request), RequestTool.getOSNew(RequestLocal.getRequest()));
                jsonObject.put("orderNum", retJson.get("orderNum"));
                jsonObject.put("addCredits", retJson.get("addCredits"));
                jsonObject.put("signDays", retJson.get("signDays"));

                AccessLogExUtil.putAccessLogExPair(true, 500, 2, PageBizTypeEnum.ACT_JOIN, dto.getId(), null,
                        ActivityUniformityTypeEnum.BarrageSign, consumerDO.isNotLoginUser(), consumerDO.getCredits(),
                        null, null, null, null);
                InnerLogUtil.joinInnerLog (ActivityUniformityTypeEnum.BarrageSign.getCode(), dto.getId (), null);

                return jsonObject.toJSONString();
            }else{
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY,"活动不存在");
                return jsonObject.toJSONString();
            }
        } catch (Exception e) {
            log.warn("doJion 服务器异常", e);
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY,e.getMessage());
            return jsonObject.toJSONString();
        } finally {
            DBTimeProfile.release();
        }
    }
	
	/**
	 * 获取订单状态
	 * @param orderId
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
    @RequestMapping(value="/getOrderStatus",method = {RequestMethod.GET,RequestMethod.POST})
    public String getOrderStatus(String orderId,HttpServletRequest request,HttpServletResponse response){
	    response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_ORIGIN, pluginDomainCrossUtils.allowDomain(request,RequestLocal.getAppId()));
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_METHODS, "POST,GET");
        response.setHeader(CommonConstants.ACCESS_CONTROL_MAX_AGE, "3600");
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_HEADERS, "x-requested-with");
        response.setHeader(CommonConstants.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        
        try {
            DBTimeProfile.enter("activitySign.getOrderStatus");
            JSONObject result = activitySignService.getOrderStatus(orderId, RequestLocal.getCid(),request);
            return result.toJSONString();
        } catch (Exception e) {
            log.warn("getOrderStatus error", e);
            JSONObject result = new JSONObject();
            result.put(CommonConstants.SUCCESS_KEY, false);
            result.put(CommonConstants.MESSAGE_KEY, "服务器异常 请请稍后");
            return result.toJSONString();
        } finally {
            DBTimeProfile.release();
        }
    }
}
