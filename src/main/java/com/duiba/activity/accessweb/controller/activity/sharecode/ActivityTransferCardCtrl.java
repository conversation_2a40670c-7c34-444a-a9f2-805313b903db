package com.duiba.activity.accessweb.controller.activity.sharecode;

import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.activity.sharecode.ActivityTransferCardService;
import com.duiba.activity.accessweb.service.activity.sharecode.ActivtyShareCodeService;
import com.duiba.activity.accessweb.tool.result.ResultUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date created in 2019/1/17
 * @description: 集卡转赠
 */
@RestController
@RequestMapping("/aaw/activityTransferCard")
public class ActivityTransferCardCtrl {

    private Logger logger = LoggerFactory.getLogger(ActivityTransferCardCtrl.class);

    @Autowired
    private ActivityTransferCardService activityTransferCardService;
    @Autowired
    private ActivtyShareCodeService activtyShareCodeService;

    /**
     * 集卡分享赠送
     * @param cardId
     * @return
     */
    @PostMapping("/shareCard")
    public Result shareCard(Long cardId){
        if (null == cardId){
            return ResultUtil.fail(ErrorCode.E0100007);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()){
            return ResultUtil.fail(ErrorCode.E0100000);
        }
        //记录用户的赠与请求动作
        try {
            activityTransferCardService.recordShareCard(consumer.getId(), cardId);
        } catch (BizException e){
            return ResultUtil.fail(e.getCode(),e.getMessage());
        } catch (Exception e){
            logger.warn("集卡分享异常，cardId:{},consumerId:{}", cardId, consumer.getId(),e);
            return ResultUtil.failWithDesc("服务器开小差了，请稍后重试");
        }
        return ResultUtil.success();
    }

    /**
     *
     * @param inviteUser
     * @param cardId
     * @return
     */
    @PostMapping("/acceptCard")
    public Result acceptCard(String inviteUser, Long cardId){
        if (StringUtils.isBlank(inviteUser) || null == cardId){
            return ResultUtil.fail(ErrorCode.E0100007);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()){
            return ResultUtil.fail(ErrorCode.E0100000);
        }
        if (StringUtils.equals(inviteUser,consumer.getPartnerUserId())){
            return ResultUtil.fail(ErrorCode.E0800001);
        }
        try {
            activityTransferCardService.acceptCard(consumer.getAppId(), consumer.getId(), inviteUser, cardId);
        } catch (BizException e){
            return ResultUtil.fail(e.getCode(), e.getMessage());
        } catch (Exception e){
            logger.warn("集卡收取异常，cardId:{},consumerId:{}", cardId, consumer.getId(),e);
            return ResultUtil.failWithDesc("服务器开小差了，请稍后再试");
        }
        return ResultUtil.success();
    }

    /**
     * 接受赠卡
     * @param cardId 卡片ID
     * @param shareCode 分享码
     * @param activityId 活动入库ID
     * @return
     */
    @PostMapping("/acceptCardWithShareCode")
    public cn.com.duiba.api.bo.reqresult.Result acceptCard(@RequestParam Long cardId, @RequestParam String shareCode, @RequestParam Long activityId) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.C00000001.getCode(), ResultCode.C00000001.getDescription());
        }
        Long inviteConsumerId;
        try {
            inviteConsumerId = activtyShareCodeService.getConsumerIdByShareCode(consumer.getAppId(), activityId, shareCode);
        } catch (BizException e) {
            logger.error("根据分享码获取用户信息失败");
            return ResultBuilder.fail(ResultCode.C100100.getCode(), ResultCode.C100100.getDescription());
        }
        if (inviteConsumerId.equals(consumer.getId())) {
            return ResultBuilder.fail(ErrorCode.E0800001.getErrorCode(), ErrorCode.E0800001.getDesc());
        }
        try {
            activityTransferCardService.acceptCard(consumer, inviteConsumerId, cardId);
        } catch (BizException e) {
            return ResultBuilder.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.warn("集卡收取异常，cardId:{},consumerId:{}", cardId, consumer.getId(), e);
            return ResultBuilder.fail("服务器开小差了，请稍后再试");
        }
        return ResultBuilder.success();
    }

    @PostMapping("/askCard")
    public cn.com.duiba.api.bo.reqresult.Result<String> askCardRecord(Long cardId) {
        if (null == cardId){
            return ResultBuilder.fail(ErrorCode.E0100007.getErrorCode(), ErrorCode.E0100007.getDesc());
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()){
            return ResultBuilder.fail(ErrorCode.E0100000.getErrorCode(), ErrorCode.E0100000.getDesc());
        }
        //记录用户的赠与请求动作
        try {
            String token = activityTransferCardService.recordAskCard(consumer.getId(), cardId);
            return ResultBuilder.success(token);
        } catch (Exception e){
            logger.warn("集卡分享异常，cardId:{},consumerId:{}", cardId, consumer.getId(),e);
            return ResultBuilder.fail("服务器开小差了，请稍后重试");
        }
    }
}
