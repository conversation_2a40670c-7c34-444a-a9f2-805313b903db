package com.duiba.activity.accessweb.controller;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.quizz.DuibaQuizzDto;
import cn.com.duiba.activity.center.api.dto.quizz.QuizzOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteDuibaQuizzServiceInner;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.enums.saas.SaasFuncTypeEnum;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.utils.SaasGrantUtil;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.HtdoolConstants;
import com.duiba.activity.accessweb.service.CommonService;
import com.duiba.activity.accessweb.service.WeixinShareContentService;
import com.duiba.activity.accessweb.service.common.ActivityCommonService;
import com.duiba.activity.accessweb.service.common.ActivitySelfOrderService;
import com.duiba.activity.accessweb.service.quizz.QuizzService;
import com.duiba.activity.accessweb.tool.ActivityBusinessTool;
import com.duiba.activity.accessweb.tool.CreditsCalculateService;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.DuibaActivityVo;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.duiba.activity.accessweb.vo.questionanswer.QuestionOptionsVO;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.javatuples.Pair;
import org.javatuples.Triplet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/** 
 * ClassName:QuizzCtrl.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2017年3月28日 上午11:45:33 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
@Controller
@RequestMapping("/quizz")
public class QuizzCtrl extends BaseNewCtrl{
	
	private static final Logger logger = LoggerFactory.getLogger(QuizzCtrl.class);
	@Autowired
	private QuizzService quizzService;
	@Autowired
	private RemoteDuibaQuizzServiceInner remoteDuibaQuizzServiceInner;
	@Autowired
	private ActivityCommonService activityCommonService;
	@Autowired
	private CreditsCalculateService creditsCalculateService;
	@Autowired
	private ActivitySelfOrderService activitySelfOrderService;
	@Autowired
	private RemoteAppService remoteAppServiceForDevelop;
	@Autowired
	private WeixinShareContentService weixinShareContentService;
	@Autowired
	private CommonService commonService;

	@Autowired
	private DomainService domainService;

	/**
	 * 
	 * @param duibaId
	 * @return
	 */
	@RequestMapping("/duiba")
	public ModelAndView duiba(@RequestParam(name = "id") Long duibaId, HttpServletRequest request){
		//判断活动id 是否为null
		if(duibaId == null){
			logger.warn("QuizzCtrl duiba duibaId is null");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		Pair<OperatingActivityDto, Boolean> pairOp = super.findByAppIdAndDuiba(consumerDto.getAppId(), duibaId, "quizz");
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}

		OperatingActivityDto opt = pairOp.getValue0();
		if(opt.getActivityId() != null && !SaasGrantUtil.checkGrant(appSimpleDto.getDeveloperId(), appSimpleDto.getId(), SaasFuncTypeEnum.ACTIVITY, opt.getType(), opt.getActivityId(), true)){
			return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
		}

		try {
			return quizzService.tupleModelAndView(pairOp.getValue0(),consumerDto,appSimpleDto,CommonConstants.DUIBA,false,request);
		} catch (Exception e) {
			logger.error("QuizzCtrl duiba error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}
	
	/**
	 * 
	 * @param id 开发者运营活动表主键
	 * @return
	 */
	@RequestMapping("/index")
	public ModelAndView index(@RequestParam(name = "id") Long id, HttpServletRequest request){
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();

		//查询活动信息
		Pair<OperatingActivityDto, Boolean> pairOp = super.findOperatingActivityById(id,consumerDto.getAppId());
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}

		OperatingActivityDto opt = pairOp.getValue0();
		if(opt.getActivityId() != null && !SaasGrantUtil.checkGrant(appSimpleDto.getDeveloperId(), appSimpleDto.getId(), SaasFuncTypeEnum.ACTIVITY, opt.getType(), opt.getActivityId(), true)){
			return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
		}

		try {
			ModelAndView modelAndView = quizzService.tupleModelAndView(pairOp.getValue0(),consumerDto,appSimpleDto,CommonConstants.DUIBA,false,request);
			modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request, appSimpleDto.getAppKey()));
			return modelAndView;
		} catch (Exception e) {
			logger.error("QuizzCtrl index error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}
	
	/**
	 * 
	 * @param id 开发者运营活动表主键
	 * @return
	 */
	@RequestMapping("/share")
	public ModelAndView mater(@RequestParam(name = "id") Long id,@RequestParam(name = "appId") Long appId, HttpServletRequest request){
		//查询app信息
		AppSimpleDto app = remoteAppServiceForDevelop.getSimpleApp(appId).getResult();
		if (app == null) {
			logger.warn("appId is empty.");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//查询活动信息
		Pair<OperatingActivityDto, Boolean> pairOp = super.findOperatingActivityById(id,appId);
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		try {
			return quizzService.materTupleModelAndView(pairOp.getValue0(),app,request);
		} catch (Exception e) {
			logger.error("QuizzCtrl index error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}
	
	@ResponseBody
	@RequestMapping(value = "/shareElement", produces = "application/json")
	public String ajaxElement(@RequestParam(name = "actId") Long actId, @RequestParam(name = "hdType") String type, HttpServletRequest request){
		JSONObject json = new JSONObject();
		try {
			Pair<OperatingActivityDto, DuibaQuizzDto>  pair = getActivityInfo(type,actId,null,false);
			DuibaQuizzDto duibaQuizzDO = pair.getValue1();
			OperatingActivityDto operatingActivityDto = pair.getValue0();
			AppSimpleDto appSimpleDto = remoteAppServiceForDevelop.getSimpleApp(operatingActivityDto.getAppId()).getResult();
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDto, duibaQuizzDO==null?null:duibaQuizzDO.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			//组装element信息
			Map<String,Object> elementMap = Maps.newHashMap();
			elementMap.put(CommonConstants.STATUS, HtdoolConstants.STATUS_FOREVER);
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			json.put(CommonConstants.ELEMENT, elementMap);
			json.put("rule", duibaQuizzDO ==null ? "":duibaQuizzDO.getRule());
			// 获取活动奖项
			json.put("options", quizzService.findDisplayOptions(operatingActivityDto.getActivityId(),appSimpleDto));
			json.put(CommonConstants.SUCCESS_KEY, true);
			return json.toJSONString();
		} catch (Exception e) {
			logger.error("QuestionCtrl ajaxElement error", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toString();
		}
	}

	/**
	 * 
	 * @param id 开发者运营活动表主键
	 * @return
	 */
	@RequestMapping("/preview")
	public ModelAndView preview(@RequestParam(name = "id") Long id, @RequestParam(name = "type") String type, HttpServletRequest request){
		if(id == null || StringUtils.isBlank(type)){
			logger.error("QuestionCtrl preview id is null or type is null");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		try {
			Pair<OperatingActivityDto, DuibaQuizzDto> pair = getActivityInfo(type,id,id,true);
			OperatingActivityDto operatingActivityDO = pair.getValue0();
			return quizzService.tupleModelAndView(operatingActivityDO,consumerDto,appSimpleDto,type,true,request);
		} catch (Exception e) {
			logger.error("QuizzCtrl preview error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}

	@ResponseBody
	@RequestMapping(value = "/ajaxElement", produces = "application/json")
	public String ajaxElement(@RequestParam(name = "preview") String preview, @RequestParam(name = "actId") Long actId, @RequestParam(name = "hdType") String type, HttpServletRequest request){
		JSONObject json = new JSONObject();
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return json.toString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		try {
			Pair<OperatingActivityDto, DuibaQuizzDto>  pair = getActivityInfo(type,actId,Long.parseLong(request.getParameter("hdToolId")),Boolean.valueOf(preview));
			DuibaQuizzDto duibaQuizzDO = pair.getValue1();
			OperatingActivityDto operatingActivityDto = pair.getValue0();
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDto, duibaQuizzDO==null?null:duibaQuizzDO.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			//组装element信息
			DuibaActivityVo activity = new DuibaActivityVo();
			if(duibaQuizzDO != null){
				activity.setFreeLimit(duibaQuizzDO.getFreeLimit());
				activity.setFreeScope(duibaQuizzDO.getFreeScope());
				activity.setLimitCount(duibaQuizzDO.getLimitCount());
				activity.setLimitScope(duibaQuizzDO.getLimitScope());
			}
			activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
			activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDto.getCreditsType()));
			Map<String,Object> elementMap =activityCommonService.getElement(Boolean.valueOf(preview),operatingActivityDto, consumerDto.getId(), activity);
			elementMap.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(), appSimpleDto));
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			json.put(CommonConstants.ELEMENT, elementMap);
			json.put("rule", duibaQuizzDO ==null ? "":duibaQuizzDO.getRule());
			// 获取活动奖项
			json.put("options", quizzService.findDisplayOptions(operatingActivityDto.getActivityId(),appSimpleDto));
			json.put(CommonConstants.SUCCESS_KEY, true);
			return json.toJSONString();
		} catch (Exception e) {
			logger.error("QuestionCtrl ajaxElement error", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toString();
		}
	}

	@ResponseBody
	@RequestMapping(value = "/doJoin", produces = "application/json")
	public String doJoin(@RequestParam(name = "activityId") Long operationAcitvityId,
			@RequestParam(name = "token") String token, 
			@RequestParam(name = "quizzData") String quizzData,
			HttpServletRequest request,HttpServletResponse response){
		JSONObject json = new JSONObject();

		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return json.toString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		Pair<OperatingActivityDto, DuibaQuizzDto>  pair = getActivityInfo("",operationAcitvityId,null,false);
		OperatingActivityDto operatingActivityDto = pair.getValue0();
		DuibaQuizzDto quizzDto = pair.getValue1();
		//活动判断
		if(quizzDto == null || operatingActivityDto == null){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_EXTEXT_HDBCZ);
			return json.toString();
		}
		operatingActivityDto.setCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType)? DuibaHdtoolDto.CreditsTypeNo:DuibaHdtoolDto.CreditsTypeYes);
		//是否可以抽奖校验
		DuibaActivityVo activity = new DuibaActivityVo();
		activity.setFreeLimit(quizzDto.getFreeLimit());
		activity.setFreeScope(quizzDto.getFreeScope());
		activity.setLimitCount(quizzDto.getLimitCount());
		activity.setLimitScope(quizzDto.getLimitScope());
		activity.setAutoOffDate(quizzDto.getAutoOffDate());
		activity.setStatus(quizzDto.getStatus());
		activity.setBlackCheck(quizzDto.isOpenSwitch(DuibaQuizzDto.SWITCHES_DEV_BLACKLIST));
		activity.setSpecify(quizzDto.isOpenSwitch(DuibaQuizzDto.SWITCHES_DIRECT));
		activity.setCreditsPrice(quizzDto.getCreditsPrice());
		//活动判断
		activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
		activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDto.getCreditsType()));
		//用户及 活动校验
		if(super.checkActivityAndUserInfo(activity,operatingActivityDto,appSimpleDto,consumerDto,json,false,token)){
			json.put(CommonConstants.SUCCESS_KEY, false);
			return json.toString();
		}
		try{
			RequestParams parms = RequestParams.parse(request);
			//记录当前用户参加活动的id
			ActivityBusinessTool.setConsumerJoinActivityIdCookie(operationAcitvityId, request, response);
			Long needCredits = json.get(CommonConstants.NEEDCREDITS)== null ? 0L:json.getLong(CommonConstants.NEEDCREDITS);
			//生成自订单
			QuizzOrdersDto qOrder = quizzService.createOrder(consumerDto, appSimpleDto, operatingActivityDto, parms,needCredits,StringEscapeUtils.unescapeHtml4(quizzData));
			json.put(CommonConstants.SUCCESS_KEY, true);
			json.put("orderId", qOrder.getId());

			// suc=1表示成功
			AccessLogFilter.putExPair("suc", "1");
			AccessLogFilter.putExPair("oaId", Objects.toString(operatingActivityDto.getId(),""));
			AccessLogFilter.putExPair("id", Objects.toString(operatingActivityDto.getActivityId()==null?null:operatingActivityDto.getActivityId(),""));

			return json.toString();
		} catch (Exception e) {
			logger.warn("测试题异常：appId="+consumerDto.getAppId()+",consumerId="+consumerDto.getId()+",ip="+RequestTool.getIpAddr(request)+","+e.getMessage(), e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, e.getMessage());
			return json.toString();
		}

	}

	@ResponseBody
	@RequestMapping(value = "/getOrderStatus", produces = "application/json")
	public String getOrderStatus(@RequestParam(name = "orderid") Long orderId, HttpServletRequest request,HttpServletResponse response) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(CommonConstants.SUCCESS_KEY, true);
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			jsonObject.put(CommonConstants.SUCCESS_KEY, false);
			jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return jsonObject.toString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		OrdersVO orderDO = activitySelfOrderService.findQuizzOrder(consumerDto.getId(),orderId,jsonObject);
		if(!jsonObject.getBooleanValue(CommonConstants.SUCCESS_KEY)){
			if(jsonObject.getString("result")!=null){
				jsonObject.put(CommonConstants.SUCCESS_KEY, true);
			}
			return jsonObject.toJSONString();
		}
		try {
			String slotId = RequestLocal.getSlotId();
			//查询活动信息
			Pair<OperatingActivityDto, DuibaQuizzDto>  pair = getActivityInfo("",orderDO.getOperatingActivityId(),null,false);
			OperatingActivityDto operatingActivityDO = pair.getValue0();
			DuibaQuizzDto quizzDto = pair.getValue1();

			//返回code
			Integer lotteryCode = HtdoolConstants.LOTTERY_CODE_0;
			QuestionOptionsVO vo = null;
			if(orderDO.getPrizeId() != null ){
				vo = quizzService.findDisplayOption(orderDO.getPrizeId());
			}
			//谢谢参与或者扣积分失败
			if ( !HdtoolOrdersDto.PrizeTypeThanks.equals(orderDO.getPrizeType())) {
				lotteryCode = super.getOrderForOther(operatingActivityDO, BeanUtils.copy(orderDO,OrdersVO.class), jsonObject, consumerDto, slotId, ConsumerExchangeRecordDto.TypeQuizz,vo);
			}
			//组装element信息
			DuibaActivityVo activity = new DuibaActivityVo();
			activity.setFreeLimit(quizzDto.getFreeLimit());
			activity.setFreeScope(quizzDto.getFreeScope());
			activity.setLimitCount(quizzDto.getLimitCount());
			activity.setLimitScope(quizzDto.getLimitScope());
			activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
			activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDO.getCreditsType()));
			Map<String,Object> elementMap =activityCommonService.getElement(false,operatingActivityDO, consumerDto.getId(), activity);
			elementMap.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(), appSimpleDto));
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDO, quizzDto.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			jsonObject.put("result", lotteryCode);
			jsonObject.put(CommonConstants.ELEMENT, JSONArray.toJSON(elementMap));
			if(vo != null){
				jsonObject.put("html", vo.getQuizzResult());
			}
			jsonObject.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(),appSimpleDto));
			return jsonObject.toJSONString();
		} catch (Exception e) {
			logger.error("QuizzCtrl getOrderStatus error", e);
			JSONObject json = new JSONObject();
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toString();
		}
	}

	private Pair<OperatingActivityDto, DuibaQuizzDto> getActivityInfo(String type, Long opId, Long duibaId,boolean prew) {
		OperatingActivityDto operatingActivityDO;
		DuibaQuizzDto duibaQuizzDO = null;
		if (prew && CommonConstants.DUIBA.equals(type)) {
			duibaQuizzDO = remoteDuibaQuizzServiceInner.find(duibaId).getResult();
			operatingActivityDO = new OperatingActivityDto();
			operatingActivityDO.setId(duibaId);
			operatingActivityDO.setType(OperatingActivityDto.TypeDuibaQuizz);
			operatingActivityDO.setActivityId(duibaId);
			operatingActivityDO.setStatus(OperatingActivityDto.StatusIntOpen);
			operatingActivityDO.setTitle(duibaQuizzDO.getTitle());
		} else {
			operatingActivityDO = super.findOperatingActivityDto(opId);
			if(activityCommonService.isDuibaActivity(operatingActivityDO)){
				duibaQuizzDO = remoteDuibaQuizzServiceInner.find(operatingActivityDO.getActivityId()).getResult();
			}
		}
		return Pair.with(operatingActivityDO, duibaQuizzDO);
	}
}
