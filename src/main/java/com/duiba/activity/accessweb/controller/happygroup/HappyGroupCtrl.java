package com.duiba.activity.accessweb.controller.happygroup;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.controller.BaseNewCtrl;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.WeixinShareContentService;
import com.duiba.activity.accessweb.service.happygroup.HappyGroupService;
import com.duiba.activity.accessweb.vo.happygroup.BaseInfoVO;
import com.duiba.activity.accessweb.vo.happygroup.ItemGroupVO;
import com.duiba.activity.accessweb.vo.happygroup.JoinResultVO;
import com.duiba.activity.accessweb.vo.happygroup.MainPageVO;
import com.duiba.activity.accessweb.vo.happygroup.RecordInfoVO;
import com.duiba.activity.accessweb.vo.happygroup.WinnerVO;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Quartet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * @author: zhengjianhao
 * @date: 2019/2/26 15:29
 * @description: 良品铺子拼拼乐拼团活动
 */
@RestController
@RequestMapping("/aaw/happyGroup")
public class HappyGroupCtrl extends BaseNewCtrl {

	private static final Logger LOGGER = LoggerFactory.getLogger(HappyGroupCtrl.class);

	@Resource
	private HappyGroupService happyGroupService;
	@Resource(name = "redisTemplate")
	private RedisAtomicClient redisAtomicClient;
	@Autowired
	private ActivityCacheService activityCacheService;
	@Autowired
	private WeixinShareContentService weixinShareContentService;

	/**
	 * 页面-首页
	 */
	@GetMapping("/index")
	public ModelAndView index(Long activityId, boolean preview, HttpServletRequest request) {
		//组装 页面展示信息
		ModelAndView modelAndView = new ModelAndView("/happygroup/index");
		modelAndView.addObject("activityId", activityId);
		modelAndView.addObject("preview", preview);
		modelAndView.addObject("winner", getWinner(activityId));

		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
		if(null != operatingActivityDto){
			modelAndView.addObject("appId", operatingActivityDto.getAppId());
			modelAndView.addObject("title", operatingActivityDto.getTitle());
		}
		//设置分享信息
		modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));
		return modelAndView;
	}

	private String getWinner(Long activityId){
		// 获取中奖轮播数据
		String winner = happyGroupService.getWinner(activityId);
		return StringUtils.isBlank(winner) ? "[]" : winner;
	}

	/**
	 * 页面-拼团详情
	 */
	@GetMapping("/groupDetail")
	public ModelAndView groupDetail(Long activityId, HttpServletRequest request) {
		//组装 页面展示信息
		ModelAndView modelAndView = new ModelAndView("/happygroup/groupDetail");
		modelAndView.addObject("activityId", activityId);
		modelAndView.addObject("winner", getWinner(activityId));

		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
		if(null != operatingActivityDto){
			modelAndView.addObject("appId", operatingActivityDto.getAppId());
			modelAndView.addObject("title", operatingActivityDto.getTitle());
		}
		//设置分享信息
		modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));
		return modelAndView;
	}

	/**
	 * 页面-拼团记录
	 */
	@GetMapping("/groupRecord")
	public ModelAndView groupRecord(Long activityId, HttpServletRequest request) {
		//组装 页面展示信息
		ModelAndView modelAndView = new ModelAndView("/happygroup/groupRecord");
		modelAndView.addObject("activityId", activityId);

		OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
		if(null != operatingActivityDto){
			modelAndView.addObject("title", operatingActivityDto.getTitle());
		}
		//设置分享信息
		modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));
		return modelAndView;
	}

	/**
	 * 增加中奖轮播数据
	 */
	@PostMapping("/addWinner")
	public Result addWinner(@RequestBody WinnerVO winnerVO){
		try {
			return ResultBuilder.success(happyGroupService.addWinner(winnerVO.getActivityId(), winnerVO.getWinnerList()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 获取所有中奖轮播数据
	 */
	@GetMapping("/getWinners")
	public Result getWinners(Long activityId){
		if(null == activityId){
			return ResultBuilder.fail("入库活动id没传");
		}
		return ResultBuilder.success(happyGroupService.getWinner(activityId));
	}

	/**
	 * 更新覆盖中奖轮播数据
	 */
	@PostMapping("/coverWinner")
	public Result coverWinner(@RequestBody WinnerVO winnerVO){
		try {
			return ResultBuilder.success(happyGroupService.coverWinner(winnerVO.getActivityId(), winnerVO.getWinnerList()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 活动基本配置查询
	 */
	@GetMapping("/getBaseInfo")
	public Result<BaseInfoVO> getBaseInfo(@RequestParam Long activityId){
		try {
			return ResultBuilder.success(happyGroupService.getBaseInfo(activityId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 首页查询
	 */
	@GetMapping("/getMainInfo")
	public Result<MainPageVO> getMainInfo(@RequestParam Long activityId, Long itemId){
		try {
			return ResultBuilder.success(happyGroupService.getMainInfo(activityId, itemId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 开团
	 */
	@GetMapping("/openGroup")
	public Result openGroup(@RequestParam Long activityId, @RequestParam Long groupItemId){
		try {
			return ResultBuilder.success(happyGroupService.openGroup(activityId, groupItemId));
		}catch (BizException e){
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 获取团详情
	 */
	@GetMapping("/getGroupDetail")
	public Result<ItemGroupVO> getGroupDetail(@RequestParam Long activityId, @RequestParam Long groupId){
		try {
			return ResultBuilder.success(happyGroupService.getGroupDetail(activityId, groupId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 参团
	 */
	@GetMapping("/joinGroup")
	public Result<JoinResultVO> joinGroup(@RequestParam Long activityId, @RequestParam Long groupId){
		try {
			return ResultBuilder.success(happyGroupService.joinGroup(activityId, groupId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 领奖
	 */
	@GetMapping("/openPrize")
	public Result openPrize(@RequestParam Long groupId, HttpServletRequest request){
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3().getCode(), quartet.getValue3().getDescription());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		String key = RedisKeyFactory.K194.toString() + groupId + "_" + quartet.getValue0().getId();
		try (RedisLock lock = redisAtomicClient.getLock(key, 5)){
			if (Objects.isNull(lock)) {
				return ResultBuilder.fail(ResultCode.C100804.getDescription());
			}
			return ResultBuilder.success(happyGroupService.openPrize(quartet.getValue1().getId(), quartet.getValue0(), groupId, request));
		}catch (BizException e){
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}catch (Exception e){
			LOGGER.warn("良品铺子拼拼乐活动-领奖异常", e);
			return ResultBuilder.fail(ResultCode.C100804.getDescription());
		}
	}

	/**
	 * 拼团记录
	 */
	@GetMapping("/getRecordList")
	public Result<List<RecordInfoVO>> getRecordList(@RequestParam Long activityId,
													@RequestParam Integer groupStatus,
													@RequestParam Integer pageNo,
													@RequestParam Integer pageSize){
		try {
			return ResultBuilder.success(happyGroupService.getRecordList(activityId, groupStatus, pageNo, pageSize));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}
}
