package com.duiba.activity.accessweb.controller.huaxing;

import cn.com.duiba.credits.sdk.CreditTool;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duibabiz.component.domain.DomainService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.annotations.NoLoginCanAccess;
import com.duiba.activity.accessweb.constant.huaxing.HuaXingConstants;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.tool.AssembleTool;
import com.duiba.activity.accessweb.tool.HttpUtil;
import com.duiba.activity.accessweb.util.huaxing.HuaXingCodeEnum;
import com.duiba.activity.accessweb.util.huaxing.HuaXingParseUtil;
import com.duiba.activity.accessweb.vo.huaxing.GdhxChargeRequest;
import com.duiba.activity.accessweb.vo.huaxing.GdhxWapRefundChargeRequest;
import com.duiba.activity.accessweb.vo.huaxing.AutoLoginRedirectUrlVO;
import com.duiba.activity.accessweb.vo.huaxing.CommonVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 华兴银行免登接口
 */
@RestController
@RequestMapping("/aaw/huaxingbank")
public class HuaXingController {
    private static final Logger LOGGER = LoggerFactory.getLogger(HuaXingController.class);

    @Resource
    private HuaXingConstants huaXingConstants;
    @Resource
    private DomainService domainService;
    @Autowired
    private RemoteAppService remoteAppService;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;

    private static final SimpleDateFormat MILLI_SECOND = getFormat("yyyyMMddHHmmss");

    private static SimpleDateFormat getFormat(String format) {
        return new SimpleDateFormat(format);
    }

    public static Date getMilliDbDate(String str) {
        synchronized (MILLI_SECOND) {
            return getDate(str, MILLI_SECOND);
        }
    }

    private static Date getDate(String dateStr, SimpleDateFormat format) {
        if ("".equals(dateStr) || dateStr == null) {
            return null;
        }
        try {
            return format.parse(dateStr);
        } catch (ParseException e) {
            LOGGER.error("华兴format yyyy-MM-dd HH:mm:ss error:", e);
        }
        return null;
    }

    @PostConstruct
    public void initSecretKey() {
        try {
            loadOldSecretKey();
            getSecretKeyForCache();
        } catch (Exception e) {
            LOGGER.warn("huaxing 初始化 失败", e);
        }
    }

    @GetMapping("/loadSecretKey")
    public String loadSecretKey(@RequestParam String reload) {
        if (StringUtils.equals(reload, "true")) {
            JSONObject resBody = doLoadSecretKey();
            if (MapUtils.isEmpty(resBody)) {
                // 重试获取
                resBody = doLoadSecretKey();
            }
            if (MapUtils.isEmpty(resBody)) {
                return null;
            }
            String applyTime = resBody.getString("applyTime");
            int expTimeSeconds = HuaXingParseUtil.getRedisExpTimeSeconds(applyTime);

            String appSecret1 = resBody.getString("appSecret");
            advancedCacheClient.set(HuaXingParseUtil.getNewRedisKey(), appSecret1, expTimeSeconds, TimeUnit.SECONDS);
            return appSecret1;
        }
        return getSecretKeyForCache();
    }

    /**
     * 获取授权码跳转链接
     *
     * @return
     */
    @PostMapping("/getAutoLoginUrl")
    public String getAutoLoginUrl(@RequestBody JSONObject param) throws UnsupportedEncodingException {
        LOGGER.info("广州华兴免登参数：{}", param);
        String secretKey = getSecretKeyForCache();

        //app获取
        // AppSimpleDto app = appInfoCacheService.getAppByAppId(huaXingConstants.getDuibaAppId());
        AppSimpleDto app = getAppByAppId(huaXingConstants.getDuibaAppId());
        if (app == null) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.APP_NOT_EXIST);
        }
        JSONObject response = HuaXingParseUtil.parseGHBResponseMsg(
                huaXingConstants.getPublicString(),
                huaXingConstants.getUserId(),
                secretKey, param);

        if (param.getBooleanValue("replaceSecretKey")) {
            replaceOldSecretKey(secretKey);
        }

        String uid = Optional.ofNullable(response)
                .map(v -> v.getString("uid"))
                .orElse(null);
        if (StringUtils.isBlank(uid)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.UID_NOT_EXIST);
        }
        String nickname = Optional.ofNullable(response)
                .map(v -> v.getString("nickname"))
                .orElse(null);
        String transfer = Optional.ofNullable(response)
                .map(v -> v.getString("transfer"))
                .orElse(null);
        String activityID = Optional.ofNullable(response)
                .map(v -> v.getString("activityID"))
                .orElse(null);
        String uname = Optional.ofNullable(response)
                .map(v -> v.getString("uname"))
                .orElse(null);
        LOGGER.info("test: getAutoLoginUrl--- {}{}{}{}", nickname, transfer, activityID, uname);
        LOGGER.info("huaxing response={}", JSON.toJSON(response));
        return getResponseMsg(secretKey, param, getAutoLoginUrl(app, uid, nickname, transfer, activityID, uname));
    }

    /**
     * 获取免登URL
     *
     * @param app
     * @param uid
     * @return
     */
    private String getAutoLoginUrl(AppSimpleDto app, String uid, String nickname, String transfer, String activityID, String uname) throws UnsupportedEncodingException {
        Map<String, String> params = Maps.newHashMap();
        params.put("uid", uid);
        params.put("credits", "0");
        params.put("redirect", assembleAutoLoginRedirectUrl(transfer, activityID));
        boolean unamex = StringUtils.isNoneBlank(uname);
        boolean nicknamex = StringUtils.isNotBlank(nickname);
        if (unamex && nicknamex) {
            params.put("dcustom", URLDecoder.decode("nickname=" + nickname + "&uname=" + uname, "utf-8"));
        } else if (!unamex && nicknamex) {
            params.put("dcustom", URLDecoder.decode("nickname=" + nickname, "utf-8"));
        } else if (unamex && !nicknamex) {
            params.put("dcustom", URLDecoder.decode("uname=" + uname, "utf-8"));
        }
        String url = domainService.getSystemDomain(app.getId()).getHomeDomain() + "/autoLogin/autologin?";
        String autoLoginUrl = new CreditTool(app.getAppKey(), app.getAppSecret()).buildUrlWithSign(url, params);
        return StringUtils.startsWith(autoLoginUrl, "//") ? "https:" + autoLoginUrl : autoLoginUrl;
    }

    /**
     * 组装免登跳转地址
     *
     * @param transfer
     * @return
     */
    private String assembleAutoLoginRedirectUrl(String transfer, String activityID) {
        String url = null;
        AutoLoginRedirectUrlVO autoLoginRedirectUrlVO = huaXingConstants.getAutoLogin().stream()
                .filter(x -> x.getActivityId().equals(activityID))
                .findFirst()
                .orElse(null);
        url = autoLoginRedirectUrlVO != null ?
                autoLoginRedirectUrlVO.getActUrl() :
                huaXingConstants.getAutoLoginRedirectUrl();
        if (StringUtils.isBlank(transfer)) {
            return url;
        }
        if (url.endsWith("?")) {
            return url + "transfer=" + transfer;
        } else if (url.contains("?")) {
            return url + "&transfer=" + transfer;
        } else {
            return url + "?transfer=" + transfer;
        }
    }

    private String getResponseMsg(String secretKey, JSONObject reqParam, HuaXingCodeEnum errorEnum) {
        JSONObject reqHeader = reqParam.getJSONObject("header");

        JSONObject header = new JSONObject();
        header.put("errorCode", errorEnum.getCode());
        header.put("errorMsg", errorEnum.getMsg());
        header.put("requestId", reqHeader.getString("requestId"));
        header.put("appId", huaXingConstants.getHuaXingAppId());

        JSONObject param = new JSONObject()
                .fluentPut("header", header)
                .fluentPut("body", StringUtils.EMPTY);
        LOGGER.info("{} 响应数据 {} {}", HuaXingParseUtil.PROJECT_NAME, reqParam.toJSONString(), param.toJSONString());

        return HuaXingParseUtil.getGHBRequestMsg(
                huaXingConstants.getPrivateString(),
                huaXingConstants.getUserId(),
                secretKey, param
        ).toJSONString();
    }

    private String getResponseMsgNew(String secretKey, JSONObject reqParam, HuaXingCodeEnum errorEnum) {
        JSONObject reqHeader = reqParam.getJSONObject("header");

        JSONObject header = new JSONObject();
        header.put("errorCode", errorEnum.getCode());
        header.put("errorMsg", errorEnum.getMsg());
        header.put("SRV_RESP_CD", errorEnum.getCode());
        header.put("SRV_RESP_INFO", errorEnum.getMsg());
        header.put("requestId", reqHeader.getString("requestId"));
        header.put("appId", huaXingConstants.getHuaXingAppId());

        JSONObject body = new JSONObject();
        body.put("SRV_RESP_CD", errorEnum.getCode());
        body.put("SRV_RESP_INFO", errorEnum.getMsg());

        JSONObject param = new JSONObject()
                .fluentPut("header", header)
                .fluentPut("body", body);
        LOGGER.info("{} 产品推送响应数据 {} {}", HuaXingParseUtil.PROJECT_NAME, reqParam.toJSONString(), param.toJSONString());

        return HuaXingParseUtil.getGHBRequestMsgNew(
                huaXingConstants.getPrivateString(),
                huaXingConstants.getUserId(),
                secretKey, param
        ).toJSONString();
    }

    private String getResponseMsg(String secretKey, JSONObject reqParam, String redirectUrl) {
        JSONObject reqHeader = reqParam.getJSONObject("header");

        JSONObject header = new JSONObject();
        header.put("errorCode", HuaXingCodeEnum.SUCCEED.getCode());
        header.put("errorMsg", HuaXingCodeEnum.SUCCEED.getMsg());
        header.put("requestId", reqHeader.getString("requestId"));
        header.put("appId", huaXingConstants.getHuaXingAppId());

        JSONObject body = new JSONObject();
        body.put("redirectUrl", redirectUrl);

        JSONObject param = new JSONObject().fluentPut("header", header).fluentPut("body", body);
        LOGGER.info("{} 响应数据 {} {}", HuaXingParseUtil.PROJECT_NAME, reqParam.toJSONString(), param.toJSONString());

        return HuaXingParseUtil.getGHBRequestMsg(
                huaXingConstants.getPrivateString(),
                huaXingConstants.getUserId(),
                secretKey, param
        ).toJSONString();
    }

    private String getSecretKeyForCache() {
        String key = HuaXingParseUtil.getNewRedisKey();
        String appSecret = advancedCacheClient.get(key);
        if (StringUtils.isNotBlank(appSecret)) {
            return appSecret;
        }
        synchronized (this) {
            if (StringUtils.isNotBlank(appSecret)) {
                return appSecret;
            }
            JSONObject resBody = doLoadSecretKey();
            if (MapUtils.isEmpty(resBody)) {
                // 重试获取
                resBody = doLoadSecretKey();
            }
            if (MapUtils.isEmpty(resBody)) {
                return null;
            }
            String applyTime = resBody.getString("applyTime");
            int expTimeSeconds = HuaXingParseUtil.getRedisExpTimeSeconds(applyTime);

            String appSecret1 = resBody.getString("appSecret");
            advancedCacheClient.set(key, appSecret1, expTimeSeconds, TimeUnit.SECONDS);
            return appSecret1;
        }
    }

    private JSONObject doLoadSecretKey() {
        String url = huaXingConstants.getApiHost() + "/api/security/crypto/aesKeyGenerator/V2";
        String requestTime = HuaXingParseUtil.getResponseTime();
        JSONObject header = new JSONObject();
        header.put("requestId", HuaXingParseUtil.getRequestId(requestTime));
        header.put("requestTime", requestTime);
        header.put("appId", huaXingConstants.getHuaXingAppId());
        header.put("charset", HuaXingParseUtil.ENCODE_UTF_8);

        JSONObject body = new JSONObject();
        body.put("appSecret", StringUtils.EMPTY);

        JSONObject param = new JSONObject().fluentPut("header", header).fluentPut("body", body);
        LOGGER.info("{} 获取密钥 请求 {} {}", HuaXingParseUtil.PROJECT_NAME, url, param.toJSONString());

        param = HuaXingParseUtil.getSecretKeyRequestMsg(
                huaXingConstants.getPublicString(),
                huaXingConstants.getPrivateString(),
                huaXingConstants.getUserId(), param);

        String response = doPost(url, param.toJSONString());
        if (StringUtils.isBlank(response)) {
            LOGGER.info("{} 获取密钥 响应失败 {} {}", HuaXingParseUtil.PROJECT_NAME, url, param.toJSONString());
            return null;
        }
        try {
            LOGGER.info("{} 获取密钥 响应原始报文 {} {} {}", HuaXingParseUtil.PROJECT_NAME, url, param.toJSONString(), response);
            JSONObject resBody = HuaXingParseUtil.parseSecretKeyRequestMsg(
                    huaXingConstants.getPublicString(),
                    huaXingConstants.getPrivateString(),
                    huaXingConstants.getUserId(),
                    JSONObject.parseObject(response));
            LOGGER.info("{} 获取密钥 响应解析报文 {} {} {}", HuaXingParseUtil.PROJECT_NAME, url, param.toJSONString(), resBody);
            if (MapUtils.isEmpty(resBody)) {
                return null;
            }
            String appSecret = resBody.getString("appSecret");
            String applyTime = resBody.getString("applyTime");
            if (StringUtils.isBlank(appSecret) || StringUtils.isBlank(applyTime)) {
                return null;
            }
            return resBody;
        } catch (Exception e) {
            LOGGER.info("{} 获取密钥 解析错误 {} {} {}", HuaXingParseUtil.PROJECT_NAME, url, param, response, e);
            return null;
        }
    }

    private void loadOldSecretKey() {
        if (null == HuaXingParseUtil.getSecretKeyOld()) {
            String oldSecretKey = advancedCacheClient.get(HuaXingParseUtil.getOldRedisKey());
            HuaXingParseUtil.setSecretKeyOld((null == oldSecretKey) ? StringUtils.EMPTY : oldSecretKey);
        }
    }

    private void replaceOldSecretKey(String secretKey) {
        HuaXingParseUtil.setSecretKeyOld(secretKey);
        advancedCacheClient.set(HuaXingParseUtil.getOldRedisKey(), secretKey, 6, TimeUnit.HOURS);
    }

    private AppSimpleDto getAppByAppId(Long appId) {
        try {
            return remoteAppService.getSimpleApp(appId).getResult();
        } catch (Exception e) {
            LOGGER.error("{} 获取app失败, appId={}", HuaXingParseUtil.PROJECT_NAME, appId, e);
            return null;
        }
    }

    private String doPost(String url, String json) {
        CloseableHttpClient httpclient = null;
        CloseableHttpResponse response = null;
        try {
            StringEntity stringEntity = new StringEntity(json, HuaXingParseUtil.ENCODE_UTF_8);
            stringEntity.setContentEncoding(HuaXingParseUtil.ENCODE_UTF_8);
            stringEntity.setContentType("application/json");
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(stringEntity);
            HttpUtil.resetTimeOut(httpPost);
            httpclient = HttpClients.createDefault();
            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity, HuaXingParseUtil.ENCODE_UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(response);
            IOUtils.closeQuietly(httpclient);
        }
    }

    // public static void main(String[] args) {
    // String requestTime = HuaXingParseUtil.getResponseTime();
    // JSONObject header = new JSONObject();
    // header.put("requestId", HuaXingParseUtil.getRequestId(requestTime));
    // header.put("requestTime", requestTime);
    // header.put("appId", "DuiBa001");
    // header.put("charset", HuaXingParseUtil.ENCODE_UTF_8);
    //
    // JSONObject body = new JSONObject();
    // body.put("appSecret", StringUtils.EMPTY);
    //
    // JSONObject param = new JSONObject().fluentPut("header", header).fluentPut("body", body);
    // param = HuaXingParseUtil.getSecretKeyRequestMsg(
    //         "9CDDF60536D570F0C2122C2ED266C6CD02B141003E3FB2718F46B5E4985C0D5FBE14B562A26EF3C50FB36F3C92432F505DD14AF47162F955E181937971F887BE",
    //         "0092E880DECB22B5391996BA8136261C68A12A66488B8A5E8B87F0736711DC9F6D",
    //         "1234567812345678", param);
    // System.err.println(param.toJSONString());
    //
    // String response = "{\n" +
    //         "    \"body\": \"BEWGnQHgswICWvvP/1KrAeMEUrl1AXWj/MbeWuML3xw/mj8fiu6CLjInxAQyydx35A8c0TabB5MJb5/OdNHHGD4ZQV9fZycifoMzsUz832nfcjcDclvlaO1+wtg910hmRLYrnkpLLN9SCuGy71D4C+nZNyceGdWysu9lk/FrFTd0sAaBVHOHn7XsdCYLb7/Wl5DhIImPVceMWR7Ahvyb8Gt/C6HjM/w0SPQ3QRBtOgWc\",\n" +
    //         "    \"header\": {\n" +
    //         "        \"charset\": \"UTF-8\",\n" +
    //         "        \"requestId\": \"20210730122317858804594\",\n" +
    //         "        \"appId\": \"DuiBa001\",\n" +
    //         "        \"reserve\": \"\",\n" +
    //         "        \"responseId\": \"OPB01202107301223344368\",\n" +
    //         "        \"signData\": \"RTUxQkI2NUM1RjAwQzA3OTY1NjM5RDkwQTlCNkMwMzAzQjc2MDAxN0EzNzBGNzc2QURCNjI3QjMxNTQyOUFGNzdGREUwOEExMjI3M0VENkJFMTI0ODI4Qzg3MEVFOEU5MjM3OUVEMUU5NjNBNzBENUMxODRERTM1MDMyODI0QTQ=\",\n" +
    //         "        \"errorCode\": \"0000\",\n" +
    //         "        \"errorMsg\": \"请求成功!\",\n" +
    //         "        \"subCode\": \"0000\",\n" +
    //         "        \"subMsg\": \"\",\n" +
    //         "        \"responseTime\": \"20210730122334641\"\n" +
    //         "    }\n" +
    //         "}";
    //
    // JSONObject resBody = HuaXingParseUtil.parseSecretKeyRequestMsg(
    //         "9CDDF60536D570F0C2122C2ED266C6CD02B141003E3FB2718F46B5E4985C0D5FBE14B562A26EF3C50FB36F3C92432F505DD14AF47162F955E181937971F887BE",
    //         "0092E880DECB22B5391996BA8136261C68A12A66488B8A5E8B87F0736711DC9F6D",
    //         "1234567812345678",
    //         JSONObject.parseObject(response));
    //
    // System.err.println(resBody.toJSONString());
    // }


    @PostMapping("/pushData")
    @NoLoginCanAccess
    public String pushData(@RequestBody JSONObject param) throws UnsupportedEncodingException {
        String secretKey = getSecretKeyForCache();

        JSONObject response = HuaXingParseUtil.parseGHBResponseMsg(
                huaXingConstants.getPublicString(),
                huaXingConstants.getUserId(),
                secretKey, param);
        LOGGER.info("huaxing-产品推送 请求入参param={}", param);
        LOGGER.info("huaxing-产品推送 解析response={}", JSON.toJSON(response));

        if (param.getBooleanValue("replaceSecretKey")) {
            replaceOldSecretKey(secretKey);
        }

        String uid = Optional.ofNullable(response)
                .map(v -> v.getString("UID"))
                .orElse(null);
        if (StringUtils.isBlank(uid)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.UID_NOT_EXIST);
        }
        String productId = Optional.ofNullable(response)
                .map(v -> v.getString("PRODUCT_ID"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 productId={}", JSON.toJSON(productId));
        if (StringUtils.isBlank(productId)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.PRODUCT_ID_NOT_EXIST);
        }

        String productName = Optional.ofNullable(response)
                .map(v -> v.getString("PRODUCT_NAME"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 productName={}", JSON.toJSON(productName));

        String status = Optional.ofNullable(response)
                .map(v -> v.getString("STATUS"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 status={}", JSON.toJSON(status));
        if (StringUtils.isBlank(status)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.STATUS_NOT_EXIST);
        }

        String submitTime = Optional.ofNullable(response)
                .map(v -> v.getString("SUBMITTIME"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 submitTime={}", submitTime);
        if (StringUtils.isBlank(submitTime)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.SUBMITTIME_NOT_EXIST);
        }

        String productType = Optional.ofNullable(response)
                .map(v -> v.getString("PRODUCT_TYPE"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 productType={}", productType);
        if (StringUtils.isBlank(productType)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.PRODUCT_TYPE_NOT_EXIST);
        }

        String orderNo = Optional.ofNullable(response)
                .map(v -> v.getString("OrderNo"))
                .orElse(null);
        LOGGER.info("huaxing-产品推送 orderNo={}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.ORDER_NO_NOT_EXIST);
        }

        String activityID = Optional.ofNullable(response)
                .map(v -> v.getString("activityID"))
                .orElse(null);

        // 调用projectx-2021
        pushCoop(uid, productId, status, submitTime, productType, orderNo, activityID);
        return getResponseMsgNew(secretKey, param, HuaXingCodeEnum.SUCCEED);
    }

    public void pushCoop(String uid, String productId, String status, String submitTime, String productType, String orderNo, String activityID) {
        String urlx = null;
        CommonVO commonVO = huaXingConstants.getPushUrlList().stream()
                .filter(x -> x.getActivityId().equals(activityID))
                .findFirst()
                .orElse(null);
        urlx = commonVO != null ?
                commonVO.getUrl() :
                huaXingConstants.getPushUrl();
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("uid", uid);
        paramsMap.put("productId", productId);
        paramsMap.put("status", status);
        paramsMap.put("submitTime", submitTime);
        paramsMap.put("productType", productType);
        paramsMap.put("orderNo", orderNo);
        paramsMap.put("token", huaXingConstants.getProjectxToken());
        String url = AssembleTool.assembleUrl(urlx, paramsMap);
        String responseStr = HttpUtil.sendGet(url);
        LOGGER.info("{}推送到projext-2021,url={},responseStr={}", HuaXingParseUtil.PROJECT_NAME, url, responseStr);
        if (StringUtils.isBlank(responseStr) || !"ok".equals(JSONObject.parseObject(responseStr).getString("data"))) {
            LOGGER.warn("{}推送到projext-2021失败,url={},responseStr={}", HuaXingParseUtil.PROJECT_NAME, url, responseStr);
        }

    }

    @GetMapping("/queryGeneralIntegral")
    @NoLoginCanAccess
    public Result<Integer> queryGeneralIntegral(
            @RequestParam String uid,
            HttpServletRequest request
    ) {
        AppSimpleDto app = getAppByAppId(huaXingConstants.getDuibaAppId());
        Result failResult = validate(request, app);
        if (failResult != null) {
            return failResult;
        }
        JSONObject response = queryGeneralIntegralApi(uid);
        if (response == null) {
            LOGGER.info("{} 查询可用积分失败，uid=[{}]", HuaXingParseUtil.PROJECT_NAME, uid);
            return ResultBuilder.success(0);
        } else {
            return ResultBuilder.success(response.getInteger("CURR_POINT_SMALL_SUM"));
        }
    }

    @GetMapping("/getPayCreditsBody")
    @NoLoginCanAccess
    public Result<String> getPayCreditsBody(
            @RequestParam String uid,
            @RequestParam String orderNo,
            @RequestParam int quantity,
            HttpServletRequest request
    ) {
        AppSimpleDto app = getAppByAppId(huaXingConstants.getDuibaAppId());
        Result failResult = validate(request, app);
        if (failResult != null) {
            return failResult;
        }
        String body = HuaXingParseUtil.generatePayCreditsBody(
                uid, orderNo, quantity, huaXingConstants.getPayMerchNum(),
                huaXingConstants.getPaySecret(), huaXingConstants.getPayUserId(), huaXingConstants.getPayPrivateString()
        );
        LOGGER.info("{} 生成支付积分body，uid=[{}], orderNo=[{}], quantity=[{}], body=[{}]",
                HuaXingParseUtil.PROJECT_NAME, uid, orderNo, quantity, body);
        return ResultBuilder.success(body);
    }

    @PostMapping("/payCreditsSuccessCallback")
    @NoLoginCanAccess
    public String payCreditsSuccessCallback(@RequestBody JSONObject param) {
        String secretKey = getSecretKeyForCache();
        JSONObject response = HuaXingParseUtil.parseGHBResponseMsg(
                huaXingConstants.getPublicString(),
                huaXingConstants.getUserId(),
                secretKey,
                param
        );
        String paramJson = JSONObject.toJSONString(param);
        String responseJson = JSONObject.toJSONString(response);
        LOGGER.info("{} 支付积分成功异步通知，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
        if (response == null) {
            LOGGER.warn("{} 支付积分成功异步通知，响应结果为空，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.PAY_SUCCESS_CALLBACK_NONE);
        }
        if (Boolean.TRUE.equals(param.getBoolean("replaceSecretKey"))) {
            replaceOldSecretKey(secretKey);
        }
        String outTradeNo = response.getString("out_trade_no");
        String account = response.getString("account");
        String totalFee = response.getString("total_fee");
        String tradeNo = response.getString("trade_no");
        String notifyTime = response.getString("notify_time");
        if (StringUtils.isAnyBlank(outTradeNo, totalFee)) {
            LOGGER.warn("{} 支付积分成功异步通知，缺少必传字段，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.PAY_SUCCESS_CALLBACK_LACK_FIELD);
        }
        if (StringUtils.isNotBlank(getPayNotifyUrl(outTradeNo))){
            // 回调星速台
            LOGGER.info("{}支付成功异步通知，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            paySuccessCallback(outTradeNo, account, totalFee, tradeNo, notifyTime);
        }else {
            // 回调星速台
            payCreditsSuccessCallback(outTradeNo, account, totalFee, tradeNo, notifyTime);
        }

        return getResponseMsgNew(secretKey, param, HuaXingCodeEnum.SUCCEED);
    }

    @GetMapping("/getOrderMessage")
    @NoLoginCanAccess
    public Result<String> getOrderMessage(
            @RequestParam String uid,
            @RequestParam String orderNo,
            HttpServletRequest request
    ) {
        AppSimpleDto app = getAppByAppId(huaXingConstants.getDuibaAppId());
        Result failResult = validate(request, app);
        if (failResult != null) {
            return failResult;
        }
        JSONObject response = getOrderMessageApi(uid, orderNo);
        if (response == null) {
            LOGGER.info("{} 查询订单状态失败，uid=[{}], orderNo=[{}]", HuaXingParseUtil.PROJECT_NAME, uid, orderNo);
            return ResultBuilder.success("");
        } else {
            return ResultBuilder.success(response.getString("TXN_STATUS"));
        }
    }

    @PostMapping("/updateInfoCallback")
    @NoLoginCanAccess
    public String updateInfoCallback(@RequestBody JSONObject param) {
        String secretKey = getSecretKeyForCache();
        JSONObject response = HuaXingParseUtil.parseGHBResponseMsg(
                huaXingConstants.getPublicString(),
                huaXingConstants.getUserId(),
                secretKey,
                param
        );
        String paramJson = JSONObject.toJSONString(param);
        String responseJson = JSONObject.toJSONString(response);
        LOGGER.info("{} 信息补录异步通知，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
        if (response == null) {
            LOGGER.warn("{} 信息补录异步通知，响应结果为空，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.UPDATE_INFO_CALLBACK_NONE);
        }
        if (Boolean.TRUE.equals(param.getBoolean("replaceSecretKey"))) {
            replaceOldSecretKey(secretKey);
        }
        String uid = response.getString("UID");
        String orderNo = response.getString("OrderNo");
        String status = response.getString("STATUS");
        String activityID = response.getString("activityID");
        if (StringUtils.isAnyBlank(uid, orderNo, status)) {
            LOGGER.warn("{} 信息补录异步通知，缺少必传字段，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.UPDATE_INFO_CALLBACK_LACK_FIELD);
        }
        if (!Lists.newArrayList("0", "1").contains(status)) {
            LOGGER.warn("{} 信息补录异步通知，状态字段非法，param=[{}], response=[{}]", HuaXingParseUtil.PROJECT_NAME, paramJson, responseJson);
            return getResponseMsg(secretKey, param, HuaXingCodeEnum.UPDATE_INFO_CALLBACK_INVALID_STATUS_FIELD);
        }
        // 回调星速台
        updateInfoCallback(uid, orderNo, status, activityID);
        return getResponseMsgNew(secretKey, param, HuaXingCodeEnum.SUCCEED);
    }

    /**
     * 基础校验
     *
     * @param request
     * @param app
     * @return
     */
    private Result validate(HttpServletRequest request, AppSimpleDto app) { // NOSONAR
        String timestamp = request.getParameter("timestamp");
        String appKey = request.getParameter("appKey");
        String sign = request.getParameter("sign");
        if (StringUtils.isAnyBlank(timestamp, appKey, sign)) {
            return ResultBuilder.fail(ResultCode.E00003);
        }
        if (!Objects.equals(appKey, app.getAppKey()) || !SignTool.signVerify(app.getAppSecret(), request)) {
            return ResultBuilder.fail(ResultCode.E00001);
        }
        if (!NumberUtils.isDigits(timestamp) || Math.abs(System.currentTimeMillis() - Long.parseLong(timestamp)) > TimeUnit.MINUTES.toMillis(5L)) {
            return ResultBuilder.fail(ResultCode.E00002);
        }
        return null;
    }

    /**
     * 查询积分api
     *
     * @param uid
     * @return
     */
    private JSONObject queryGeneralIntegralApi(String uid) {
        String url = huaXingConstants.getApiHost() + "/api/custodian/integralShop/queryGeneralIntegral/V1";
        JSONObject body = new JSONObject();
        body.put("PTY_ID", uid);
        String bodyJson = body.toJSONString();
        String secretKey = getSecretKeyForCache();
        JSONObject param = HuaXingParseUtil.assembleGHBBusinessRequest(
                bodyJson,
                secretKey,
                huaXingConstants.getHuaXingAppId(),
                huaXingConstants.getUserId(),
                huaXingConstants.getPrivateString()
        );
        String paramJson = param.toJSONString();
        String responseBodyJson = doPost(url, paramJson);
        if (StringUtils.isBlank(responseBodyJson)) {
            LOGGER.warn("{} 查询积分，响应结果为空，url=[{}], body=[{}], param=[{}]", HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson);
            return null;
        }
        try {
            JSONObject response = HuaXingParseUtil.parseGHBBusinessResponse(
                    JSONObject.parseObject(responseBodyJson),
                    secretKey,
                    huaXingConstants.getUserId(),
                    huaXingConstants.getPublicString()
            );
            if (MapUtils.isEmpty(response)) {
                LOGGER.warn("{} 查询积分，解析结果为空，url=[{}], body=[{}], param=[{}], responseBody=[{}]",
                        HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson);
                return null;
            }
            if (Boolean.TRUE.equals(response.getBoolean("replaceSecretKey"))) {
                replaceOldSecretKey(secretKey);
            }
            String responseJson = response.toJSONString();
            LOGGER.info("{} 查询积分，解析结果正常，url=[{}], body=[{}], param=[{}], responseBody=[{}], response=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, responseJson);
            return response;
        } catch (Exception e) {
            LOGGER.warn("{} 查询积分，解析结果异常，url=[{}], body=[{}], param=[{}], responseBody=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, e);
            return null;
        }
    }

    /**
     * 支付积分成功回调星速台
     *
     * @param outTradeNo
     * @param account
     * @param totalFee
     * @param tradeNo
     * @param notifyTime
     */
    private void payCreditsSuccessCallback(String outTradeNo, String account, String totalFee, String tradeNo, String notifyTime) {
        Set<String> urlList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(huaXingConstants.getPayCallBackList())) {
            urlList.addAll(huaXingConstants.getPayCallBackList());
        }
        if (StringUtils.isNotBlank(huaXingConstants.getPayCreditsSuccessCallbackUrl())) {
            urlList.add(huaXingConstants.getPayCreditsSuccessCallbackUrl());
        }
        for (String urlConfig : urlList) {
            try {
                Map<String, String> paramsMap = Maps.newHashMap();
                paramsMap.put("outTradeNo", outTradeNo);
                paramsMap.put("account", account);
                paramsMap.put("totalFee", totalFee);
                paramsMap.put("tradeNo", tradeNo);
                paramsMap.put("notifyTime", notifyTime);
                paramsMap.put("token", huaXingConstants.getProjectxToken());
                String url = AssembleTool.assembleUrl(urlConfig, paramsMap);
                String responseString = HttpUtil.sendGet(url);
                if (StringUtils.isNotBlank(responseString) && Boolean.TRUE.equals(JSONObject.parseObject(responseString).getBoolean("success"))) {
                    LOGGER.info("{}-支付积分成功回调星速台，成功，url={}, responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
                } else {
                    LOGGER.warn("{}-支付积分成功回调星速台，失败，url={}, responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
                }
            } catch (Exception e) {
                LOGGER.warn("{}-支付积分成功回调星速台，失败，url={}", HuaXingParseUtil.PROJECT_NAME, urlConfig, e);
            }
        }
    }

    /**
     * 支付成功回调星速台
     *
     * @param outTradeNo
     * @param account
     * @param totalFee
     * @param tradeNo
     * @param notifyTime
     */
    private void paySuccessCallback(String outTradeNo, String account, String totalFee, String tradeNo, String notifyTime) {
        Map<String, String> paramsMap = Maps.newHashMap();
        notifyTime = String.valueOf(getMilliDbDate(notifyTime).getTime());
        paramsMap.put("outTradeNo", outTradeNo);
        paramsMap.put("account", account);
        paramsMap.put("totalFee", totalFee);
        paramsMap.put("tradeNo", tradeNo);
        paramsMap.put("notifyTime", notifyTime);
        paramsMap.put("sign", DigestUtils.md5Hex(outTradeNo+notifyTime));


        String url = AssembleTool.assembleUrl(getPayNotifyUrl(outTradeNo), paramsMap);
        String responseString = HttpUtil.sendGet(url);
        if (StringUtils.isNotBlank(responseString) && Boolean.TRUE.equals(JSONObject.parseObject(responseString).getBoolean("success"))) {
            LOGGER.info("{}支付成功回调星速台,成功,url={},responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
        } else {
            LOGGER.warn("{}支付成功回调星速台,失败,url={},responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
        }
    }

    /**
     * 根据订单号获取支付回调地址
     *
     * @param outTradeNo
     * @return
     */
    private String getPayNotifyUrl(String outTradeNo) {
        String[] s = outTradeNo.split("_");
        if (s.length < 2) {
            return null;
        }
        String actId = s[1];

        //解析订单号,拿到不同项目id，对应不同的回调地址
        JSONArray array = JSONArray.parseArray(huaXingConstants.getActIdToPayNotifyConfig());
        String payNotifyUrl = null;
        for (int i = 0; i < array.size(); i++) {
            if (array.getJSONObject(i).getString("actId").equals(actId)) {
                payNotifyUrl = array.getJSONObject(i).getString("payNotifyUrl");
                break;
            }
        }

        return payNotifyUrl;

    }


    /**
     * 查询订单状态api
     *
     * @param uid
     * @param orderNo
     * @return
     */
    private JSONObject getOrderMessageApi(String uid, String orderNo) {
        String url = huaXingConstants.getApiHost() + "/api/custodian/integralShop/getOrderMessage/V1";
        JSONObject body = new JSONObject();
        body.put("PTY_ID", uid);
        body.put("MERCH_NUM", huaXingConstants.getQueryMerchNum());
        body.put("ORDER_NUM", orderNo);
        String bodyJson = body.toJSONString();
        String secretKey = getSecretKeyForCache();
        JSONObject param = HuaXingParseUtil.assembleGHBBusinessRequest(
                bodyJson,
                secretKey,
                huaXingConstants.getHuaXingAppId(),
                huaXingConstants.getUserId(),
                huaXingConstants.getPrivateString()
        );
        String paramJson = param.toJSONString();
        String responseBodyJson = doPost(url, paramJson);
        if (StringUtils.isBlank(responseBodyJson)) {
            LOGGER.warn("{} 查询订单状态，响应结果为空，url=[{}], body=[{}], param=[{}]", HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson);
            return null;
        }
        try {
            JSONObject response = HuaXingParseUtil.parseGHBBusinessResponse(
                    JSONObject.parseObject(responseBodyJson),
                    secretKey,
                    huaXingConstants.getUserId(),
                    huaXingConstants.getPublicString()
            );
            if (MapUtils.isEmpty(response)) {
                LOGGER.warn("{} 查询订单状态，解析结果为空，url=[{}], body=[{}], param=[{}], responseBody=[{}]",
                        HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson);
                return null;
            }
            if (Boolean.TRUE.equals(response.getBoolean("replaceSecretKey"))) {
                replaceOldSecretKey(secretKey);
            }
            String responseJson = response.toJSONString();
            LOGGER.info("{} 查询订单状态，解析结果正常，url=[{}], body=[{}], param=[{}], responseBody=[{}], response=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, responseJson);
            return response;
        } catch (Exception e) {
            LOGGER.warn("{} 查询订单状态，解析结果异常，url=[{}], body=[{}], param=[{}], responseBody=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, e);
            return null;
        }
    }

    /**
     * 信息补录回调星速台
     *
     * @param uid
     * @param orderNo
     * @param status
     */
    private void updateInfoCallback(String uid, String orderNo, String status, String activityID) {
        String urlx = null;
        CommonVO commonVO = huaXingConstants.getUpdateInfoCallbackList().stream()
                .filter(x -> x.getActivityId().equals(activityID))
                .findFirst()
                .orElse(null);
        urlx = commonVO != null ?
                commonVO.getUrl() :
                huaXingConstants.getUpdateInfoCallbackUrl();
        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("uid", uid);
        paramsMap.put("orderNo", orderNo);
        paramsMap.put("status", status);
        paramsMap.put("token", huaXingConstants.getProjectxToken());
        String url = AssembleTool.assembleUrl(urlx, paramsMap);
        String responseString = HttpUtil.sendGet(url);
        if (StringUtils.isNotBlank(responseString) && Boolean.TRUE.equals(JSONObject.parseObject(responseString).getBoolean("success"))) {
            LOGGER.info("{} 信息补录回调星速台，成功，url={}, responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
        } else {
            LOGGER.warn("{} 信息补录回调星速台，失败，url={}, responseString={}", HuaXingParseUtil.PROJECT_NAME, url, responseString);
        }
    }

    /**
     * 发起支付
     * 其实就是把支付请求参数进行一个加密，由前端调用native方法拉起支付
     *
     * @param request
     * @return
     */
    @PostMapping("/createCharge")
    @NoLoginCanAccess
    public String createCharge(@RequestBody GdhxChargeRequest request) {
        return HuaXingParseUtil.generatePayBody(
                request,
                huaXingConstants.getPayMerchNum(),
                huaXingConstants.getPaySecret(),
                huaXingConstants.getPayUserId(),
                huaXingConstants.getPayPrivateString(),
                huaXingConstants.getMrchdTyp()
        );
    }

    @PostMapping("/refundCharge")
    @NoLoginCanAccess
    public String refundCharge(@RequestBody GdhxWapRefundChargeRequest gdhxWapRefundChargeRequest,HttpServletRequest request) {

        JSONObject response = refundApi(gdhxWapRefundChargeRequest);
        if (response == null) {
            LOGGER.warn("{}发起退款失败，参数=[{}]", HuaXingParseUtil.PROJECT_NAME, JSONObject.toJSONString(gdhxWapRefundChargeRequest));
            return null;
        } else {
            return response.getString("TXN_STATUS");
        }
    }

    /**
     * 退款api
     *
     * @param request
     * @return
     */
    private JSONObject refundApi(GdhxWapRefundChargeRequest request) {
        String url = huaXingConstants.getApiHost() + "/api/custodian/integralShop/refunds/V1";
        JSONObject body = new JSONObject();
        body.put("ORIG_ORDER_NUM", request.getOutOrderNo());
        body.put("RFUND_ORDER_NUM", request.getRefundNo());
        body.put("PTY_ID", request.getuId());
        body.put("MERCH_NUM", request.getMerchantNo());
        //新增字段
        body.put("MRCHD_STATI_INFO", HuaXingParseUtil.getRefundInfo(request.getInfos()));
        body.put("MRCHD_INFO_SET",HuaXingParseUtil.getRefundGoodsInfo(request.getInfos()));
        LOGGER.info("{}发起退款,body=[{}]", HuaXingParseUtil.PROJECT_NAME, JSONObject.toJSONString(body));

        String bodyJson = body.toJSONString();
        String secretKey = getSecretKeyForCache();
        JSONObject param = HuaXingParseUtil.assembleGHBBusinessRequest(
                bodyJson,
                secretKey,
                huaXingConstants.getHuaXingAppId(),
                huaXingConstants.getUserId(),
                huaXingConstants.getPrivateString()
        );
        String paramJson = param.toJSONString();
        String responseBodyJson = doPost(url, paramJson);
        if (StringUtils.isBlank(responseBodyJson)) {
            LOGGER.warn("{}发起退款,响应结果为空,url=[{}],body=[{}],param=[{}]", HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson);
            return null;
        }
        try {
            JSONObject response = HuaXingParseUtil.parseGHBBusinessResponse(
                    JSONObject.parseObject(responseBodyJson),
                    secretKey,
                    huaXingConstants.getUserId(),
                    huaXingConstants.getPublicString()
            );
            if (MapUtils.isEmpty(response)) {
                LOGGER.warn("{}发起退款,解析结果为空,url=[{}],body=[{}],param=[{}],responseBody=[{}]",
                        HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson);
                return null;
            }
            if (Boolean.TRUE.equals(response.getBoolean("replaceSecretKey"))) {
                replaceOldSecretKey(secretKey);
            }
            String responseJson = response.toJSONString();
            LOGGER.info("{}发起退款,解析结果正常,url=[{}],body=[{}],param=[{}],responseBody=[{}],response=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, responseJson);
            return response;
        } catch (Exception e) {
            LOGGER.warn("{}发起退款,解析结果异常,url=[{}],body=[{}],param=[{}],responseBody=[{}]",
                    HuaXingParseUtil.PROJECT_NAME, url, bodyJson, paramJson, responseBodyJson, e);
            return null;
        }
    }


}
