package com.duiba.activity.accessweb.controller.kaiyuanzq;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.prize.center.api.dto.projectx.PrizeRecordDto;
import cn.com.duiba.prize.center.api.remoteservice.projectx.RemoteProjectXService;
import com.duiba.activity.accessweb.annotations.NoLoginCanAccess;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Desc 开源证券
 * <AUTHOR>
 */
@RestController
@RequestMapping("/aaw/kaiyuanBond")
public class KaiyuanBondCtrl {

    private static final Logger LOGGER = LoggerFactory.getLogger(KaiyuanBondCtrl.class);


    @Resource
    private RemoteProjectXService projectXService;


    @GetMapping("getMainOrderNumByProjectxOrderNo")
    public Result<String> getMainOrderNumByProjectxOrderNo(@RequestParam String projectxOrderNo) {
        if (StringUtils.isBlank(projectxOrderNo)) {
            return ResultBuilder.fail("没有对应订单号");
        }
        projectxOrderNo = getProjectOrderNo(projectxOrderNo);
        PrizeRecordDto prizeRecordDto = projectXService.findPrizeRecordByProjectOrderNo(projectxOrderNo);
        if (prizeRecordDto == null) {
            return ResultBuilder.fail("没有对应订单号");
        }
        if (!RequestLocal.getPartnerUserId().equals(prizeRecordDto.getPartnerUserId())) {
            return ResultBuilder.fail("无权查看");
        }
        return ResultBuilder.success(prizeRecordDto.getOrderNo().replace("C", ""));
    }

    /**
     * 兼容项目id前缀的订单号
     */
    private String getProjectOrderNo(String projectxOrderNo) {
        try {
            if (projectxOrderNo.startsWith("p")) {
                return projectxOrderNo.substring(9);
            }
            return projectxOrderNo;
        } catch (Exception e) {
            LOGGER.warn("星速台订单号解析错误 projectxOrderNo={}", projectxOrderNo, e);
            return projectxOrderNo;
        }
    }
}
