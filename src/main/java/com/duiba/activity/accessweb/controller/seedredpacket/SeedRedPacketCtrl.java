package com.duiba.activity.accessweb.controller.seedredpacket;

import cn.com.duiba.activity.center.api.dto.seedredpacket.LandInfoDto;
import cn.com.duiba.activity.center.api.dto.seedredpacket.SeedRedPacketDto;
import cn.com.duiba.activity.center.api.remoteservice.seedredpacket.RemoteAppRelateSeedActService;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.kvtable.service.api.dto.DuibaKvtableDto;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.controller.BaseNewCtrl;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.seedredpacket.SeedRedPacketService;
import com.duiba.activity.accessweb.service.token.FormTokenService;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.Environment;
import com.duiba.activity.accessweb.tool.PluginDomainCrossUtils;
import com.duiba.activity.accessweb.vo.seedredpacket.SeedRedPacketIndexVO;
import com.duiba.activity.accessweb.vo.seedredpacket.StealLandInfoVO;
import com.duiba.activity.accessweb.vo.seedredpacket.StealRedPacketVO;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Quartet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * @author: zhengjianhao
 * @date: 18/7/5 15:52
 * @description: 种红包
 */
@Controller
@RequestMapping("/seedRedPacket")
public class SeedRedPacketCtrl extends BaseNewCtrl {

	private static Logger log = LoggerFactory.getLogger(SeedRedPacketCtrl.class);

	@Resource
	private PluginDomainCrossUtils pluginDomainCrossUtils;
	@Resource
	private SeedRedPacketService seedRedPacketService;
	@Resource
	private FormTokenService formTokenService;
	@Resource(name = "stringRedisTemplate")
	private StringRedisTemplate stringRedisTemplate;

	@Resource(name = "redisTemplate")
	private RedisTemplate<String, Object> redisTemplate;

	@Resource(name="stringRedisTemplate")
	private RedisAtomicClient redisAtomicClient;
	@Resource
	private RemoteAppRelateSeedActService remoteAppRelateSeedActService;

	private static final String OPERATE_FREQUENTLY = "操作过于频繁，请稍后尝试";

	/**
	 * 获取土地列表
	 */
	@ResponseBody
	@RequestMapping(value = "/getLandList", method = { RequestMethod.GET })
	public Result getLandList(@RequestParam Long floorId, HttpServletRequest request, HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		if (isUseNewVersion(appSimpleDto.getId())) {
			return ResultBuilder.fail("当前应用已开启组件化种红包活动");
		}
		try{
			return ResultBuilder.success(seedRedPacketService.getLandList(consumerDO, appSimpleDto, floorId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 获取土地列表
	 */
	@ResponseBody
	@RequestMapping(value = "/new/getLandList", method = { RequestMethod.GET })
	public Result getLandListNew(Long pageId, HttpServletRequest request, HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		try{
			return ResultBuilder.success(seedRedPacketService.getLandListNew(consumerDO, appSimpleDto, pageId));
		} catch (BizException e) {
			log.info("种红包-查询土地信息失败 msg={}", e.getMessage());
			return ResultBuilder.fail(e.getMessage());
		} catch (Exception e) {
			log.info("种红包-查询土地信息失败", e);
			return ResultBuilder.fail("系统异常");
		}
	}

	@ResponseBody
	@RequestMapping(value = "/new/index", method = { RequestMethod.GET })
	public Result<SeedRedPacketIndexVO> getIndex(HttpServletRequest request, HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		AppSimpleDto app = RequestLocal.getConsumerAppDO();
		if (app == null || app.getId() == null) {
			return ResultBuilder.fail("非法访问");
		}
		try{
			return ResultBuilder.success(seedRedPacketService.getIndex(app));
		} catch (BizException e) {
			log.info("种红包-查询活动基础信息失败 msg={}", e.getMessage());
			return ResultBuilder.fail(e.getMessage());
		} catch (Exception e) {
			log.info("种红包-查询活动基础信息失败", e);
			return ResultBuilder.fail("系统异常");
		}

	}


	/**
	 * 解锁土地
	 */
	@ResponseBody
	@RequestMapping(value = "/unlockLand", method = { RequestMethod.GET })
	public Result unlockLand(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam Long landId,
			@RequestParam Long floorId,
			@RequestParam String dpm,
			@RequestParam String dcm) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		if (isUseNewVersion(appSimpleDto.getId())) {
			return ResultBuilder.fail("当前应用已开启组件化种红包活动");
		}
		try {
			AccessLogFilter.putExPair("status", 3);
			AccessLogFilter.putExPair("dpm", Objects.toString(dpm, ""));
			AccessLogFilter.putExPair("dcm", Objects.toString(dcm, ""));
			AccessLogFilter.putExPair("url_path", "/seedRedPacket/unlockLand");

			seedRedPacketService.unlockLand(landId, consumerDO.getId(), appSimpleDto.getId(), floorId, dpm, dcm);
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
		return ResultBuilder.success();
	}

	@ResponseBody
	@RequestMapping(value = "/new/unlockLand", method = { RequestMethod.GET })
	public Result unlockLandNew(HttpServletRequest request,
								HttpServletResponse response,
								@RequestParam Long landId,
								Long pageId,
								String dpm,
								String dcm) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();

		try {
			seedRedPacketService.unlockLandNew(landId, consumerDO, appSimpleDto.getId(), pageId, dpm, dcm);
		} catch (BizException e) {
			log.info("种红包-解锁土地", e);
			return ResultBuilder.fail(e.getMessage());
		} catch (Exception e) {
			log.info("种红包-解锁土地", e);
			return ResultBuilder.fail("系统异常");
		}
		return ResultBuilder.success();
	}

	/**
	 * 种红包
	 */
	@ResponseBody
	@RequestMapping(value = "/seed", method = { RequestMethod.GET })
	public Result seed(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam Long landId,
			@RequestParam Long floorId,
			@RequestParam String dpm,
			@RequestParam String dcm) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		if (isUseNewVersion(appSimpleDto.getId())) {
			return ResultBuilder.fail("当前应用已开启组件化种红包活动");
		}
		// 防重复校验
		String redisKey = RedisKeyFactory.K148.toString() + appSimpleDto.getId() + "_" + consumerDO.getId() + "_" + landId;
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail(OPERATE_FREQUENTLY);
		}

		try{
			AccessLogFilter.putExPair("status", 1);
			AccessLogFilter.putExPair("dpm", Objects.toString(dpm, ""));
			AccessLogFilter.putExPair("dcm", Objects.toString(dcm, ""));
			AccessLogFilter.putExPair("url_path", "/seedRedPacket/seed");
			return ResultBuilder.success(seedRedPacketService.seed(landId, floorId, consumerDO, appSimpleDto, dpm, dcm));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 种红包
	 */
	@ResponseBody
	@RequestMapping(value = "/new/seed", method = { RequestMethod.GET })
	public Result seedNew(HttpServletRequest request,
						  HttpServletResponse response,
						  Long landId,
						  Long pageId,
						  String dpm,
						  String dcm) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 防重复校验
		String redisKey = RedisKeyFactory.K148.toString() + appSimpleDto.getId() + "_" + consumerDO.getId() + "_" + landId;
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail(OPERATE_FREQUENTLY);
		}
		try{
			return ResultBuilder.success(seedRedPacketService.seedNew(landId, pageId, consumerDO, appSimpleDto, dpm, dcm));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 获取子订单状态
	 */
	@ResponseBody
	@RequestMapping(value = {"/getOrderStatus", "/new/getOrderStatus","/h5/getOrderStatus"}, method = { RequestMethod.GET })
	public Result getOrderStatus(@RequestParam String orderId, HttpServletRequest request, HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);

		ConsumerDto consumerDO = RequestLocal.getConsumerDO();
		if(consumerDO == null){
			return ResultBuilder.fail(CommonConstants.MSG_INDEX_EXCEPTION_USER);
		}
		try{
			return ResultBuilder.success(seedRedPacketService.getOrderStatus(orderId, consumerDO.getId()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 收割红包
	 */
	@ResponseBody
	@RequestMapping(value = "/reap", method = { RequestMethod.GET })
	public Result reap(
			@RequestParam String redPacketId,
			@RequestParam String dpm,
			@RequestParam String dcm,
			HttpServletRequest request,
			HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		if (isUseNewVersion(appSimpleDto.getId())) {
			return ResultBuilder.fail("当前应用已开启组件化种红包活动");
		}
		// 防重复校验
		String redisKey = RedisKeyFactory.K149.toString() + "_" + redPacketId;
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail(OPERATE_FREQUENTLY);
		}
		try{
			AccessLogFilter.putExPair("status", 2);
			AccessLogFilter.putExPair("dpm", Objects.toString(dpm, ""));
			AccessLogFilter.putExPair("dcm", Objects.toString(dcm, ""));
			AccessLogFilter.putExPair("url_path", "/seedRedPacket/reap");
			return ResultBuilder.success(seedRedPacketService.reap(redPacketId, consumerDO, appSimpleDto.getId(), dpm, dcm));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 收割红包
	 */
	@ResponseBody
	@RequestMapping(value = "/new/reap", method = { RequestMethod.GET })
	public Result reapNew(
			@RequestParam String redPacketId,
			String dpm,
			String dcm,
			Long pageId,
			HttpServletRequest request,
			HttpServletResponse response) {
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 防重复校验
		String redisKey = RedisKeyFactory.K149.toString() + "_" + redPacketId;
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail(OPERATE_FREQUENTLY);
		}
		try{
			return ResultBuilder.success(seedRedPacketService.reapNew(redPacketId, consumerDO, appSimpleDto.getId(), dpm, dcm, pageId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}


	@ResponseBody
	@RequestMapping(value = "/setKey", method = { RequestMethod.GET })
	public Result setKey(String key, String value){
		if(!Environment.isDaily()){
			return null;
		}
		value = StringEscapeUtils.unescapeHtml(value);
		Random random = new Random();
		Integer expireTime = DateUtil.getToTomorrowSeconds() + random.nextInt(300);
		stringRedisTemplate.opsForValue().set(key,
				value, expireTime, TimeUnit.SECONDS);
		return ResultBuilder.success(stringRedisTemplate.opsForValue().get(key));
	}

	@ResponseBody
	@RequestMapping(value = "/getKey", method = { RequestMethod.GET })
	public Result getKey(String key){
		if(!Environment.isDaily()){
			return null;
		}
		return ResultBuilder.success(stringRedisTemplate.opsForValue().get(key));
	}

	@ResponseBody
	@RequestMapping(value = "/getKeyTTL", method = { RequestMethod.GET })
	public Result getKeyTTL(String key){
		if(!Environment.isDaily()){
			return null;
		}
		return ResultBuilder.success(stringRedisTemplate.getExpire(key));
	}

	@ResponseBody
	@RequestMapping(value = "/getKey2", method = { RequestMethod.GET })
	public Result getKey2(String key){
		if(!Environment.isDaily()){
			return null;
		}
		return ResultBuilder.success(redisAtomicClient.getLong(key));
	}

	@ResponseBody
	@RequestMapping(value = "/clearKey", method = { RequestMethod.GET })
	public Boolean clearKey(String key) {
		if(!Environment.isDaily()){
			return false;
		}
		stringRedisTemplate.delete(key);
		return true;
	}

	@ResponseBody
	@RequestMapping(value = "/getObjectKey", method = { RequestMethod.GET })
	public Result getObjectKey(String key){
		if(!Environment.isDaily()){
			return null;
		}
		Object obj = redisTemplate.opsForValue().get(key);
		log.warn("getObjectKey: {}", obj);
		if(obj == null){
			return null;
		}
		return ResultBuilder.success(JSONObject.toJSONString(obj));
	}

	@ResponseBody
	@RequestMapping(value = "/getHashKey", method = { RequestMethod.GET })
	public Result getObjectKey(String key, String redPacketId){
		if(!Environment.isDaily()){
			return null;
		}
		Object obj = redisTemplate.opsForHash().get(key, redPacketId);
		if(obj == null){
			return null;
		}
		return ResultBuilder.success(JSONObject.toJSONString(obj));
	}

	@ResponseBody
	@RequestMapping(value = "/clearObjectKey", method = { RequestMethod.GET })
	public Boolean clearObjectKey(String key) {
		if(!Environment.isDaily()){
			return false;
		}
		redisTemplate.delete(key);
		return true;
	}

	@ResponseBody
	@RequestMapping(value = "/getTtlByKey", method = { RequestMethod.GET })
	public Result getTtlByKey(String key){
		if(!Environment.isDaily()){
			return null;
		}
		Long ttl = redisTemplate.getExpire(key,TimeUnit.SECONDS);
		return ResultBuilder.success(ttl);
	}

	/**
	 * 修改用户土地信息后门
	 */
	@ResponseBody
	@RequestMapping(value = "/updateUserLand", method = { RequestMethod.GET })
	public Result<LandInfoDto> updateUserLand(
			Long appId,
			Long activityId,
			Long landId,
			Long consumerId,
			Integer landStatus,
			String redPacketId){
		try{
			LandInfoDto landInfoDto = seedRedPacketService.updateUserLand(appId, activityId, landId, consumerId, landStatus, redPacketId);
			return ResultBuilder.success(landInfoDto);
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 查询用户土地信息后门
	 */
	@ResponseBody
	@RequestMapping(value = "/getUserLand", method = { RequestMethod.GET })
	public Result<List<LandInfoDto>> getUserLand(Long appId, Long activityId, Long consumerId){
		return ResultBuilder.success(seedRedPacketService.getUserLand(appId, activityId, consumerId));
	}

	/**
	 * 根据vkey查询hbase信息后门
	 */
	@ResponseBody
	@RequestMapping(value = "/findHBaseInfoByVKey", method = { RequestMethod.GET })
	public Result<DuibaKvtableDto> findHBaseInfoByVKey(String vkey, Long consumerId){
		DuibaKvtableDto duibaKvtableDto = seedRedPacketService.findHBaseInfoByVKey(vkey, consumerId);
		return ResultBuilder.success(duibaKvtableDto);
	}

	/**
	 * 查询广告投放位置以及活动规则
	 */
	@ResponseBody
	@RequestMapping("/getExtConfig")
	public Result<SeedRedPacketDto> getExtConfig(HttpServletRequest request, HttpServletResponse response){
        //设置跨域信息
        pluginDomainCrossUtils.allowCrossDomain(request, response);
        Long appId = RequestLocal.getAppId();
		String ip = RequestTool.getIpAddr(request);
//		boolean proxy = RequestTool.isProxy(request)
		SeedRedPacketDto seedRedPacketDto;
		try {
			seedRedPacketDto = seedRedPacketService.selectSeedRedPacketDto(appId, ip);
			return ResultBuilder.success(seedRedPacketDto);
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 获取所有可解锁的土地列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getAllHasUnlockLand", method = { RequestMethod.GET })
	public Result<List<LandInfoDto>> getAllHasUnlockLand(Long landId, HttpServletRequest request, HttpServletResponse response){
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
        Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> appAndConsumerInfo = findAppAndConsumerInfoNew();
        if(StringUtils.isNotBlank(appAndConsumerInfo.getValue2())){
            return ResultBuilder.fail(appAndConsumerInfo.getValue2());
        }
        if(appAndConsumerInfo.getValue3() != null){
            return ResultBuilder.fail(appAndConsumerInfo.getValue3());
        }
        ConsumerDto consumerDto = appAndConsumerInfo.getValue0();
        AppSimpleDto appSimpleDto = appAndConsumerInfo.getValue1();
        try {
            List<LandInfoDto> allHasUnlockLand = seedRedPacketService.getAllHasUnlockLand(landId, consumerDto, appSimpleDto);
            return ResultBuilder.success(allHasUnlockLand);
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
	}

	/**
	 * 获取所有可解锁的土地列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/new/getAllHasUnlockLand", method = { RequestMethod.GET })
	public Result<List<LandInfoDto>> getAllHasUnlockLandNew(Long landId, HttpServletRequest request, HttpServletResponse response){
		//设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> appAndConsumerInfo = findAppAndConsumerInfoNew();
		if (StringUtils.isNotBlank(appAndConsumerInfo.getValue2())) {
			return ResultBuilder.fail(appAndConsumerInfo.getValue2());
		}
		if (appAndConsumerInfo.getValue3() != null) {
			return ResultBuilder.fail(appAndConsumerInfo.getValue3());
		}
		ConsumerDto consumerDto = appAndConsumerInfo.getValue0();
		AppSimpleDto appSimpleDto = appAndConsumerInfo.getValue1();
		try {
			List<LandInfoDto> allHasUnlockLand = seedRedPacketService.getAllHasUnlockLandNew(landId, consumerDto, appSimpleDto);
			return ResultBuilder.success(allHasUnlockLand);
		} catch (BizException e) {
			log.info("种红包-查询可解锁土地信息失败 msg={}", e.getMessage());
			return ResultBuilder.fail(e.getMessage());
		} catch (Exception e) {
			log.info("种红包-查询可解锁土地信息失败", e);
			return ResultBuilder.fail("系统异常");
		}
	}

	/**
	 * 查询app映射的活动，没有则新增，慎用！
	 */
	@ResponseBody
	@RequestMapping(value = "/getActIdByAppIdWithAdd", method = { RequestMethod.GET })
	public Result<Long> getActIdByAppIdWithAdd(Long appId, Long activityId){
		if (Environment.isOnline()) {
			return ResultBuilder.fail(ErrorCode.********.getDesc());
		}
		if(null == appId){
			ResultBuilder.fail("appId不能为空");
		}
		return ResultBuilder.success(remoteAppRelateSeedActService.getActIdByAppIdWithAdd(appId, activityId));
	}

	/**
	 * 删除app与活动的映射关系，慎用！！！
	 */
	@ResponseBody
	@RequestMapping(value = "/removeAppRelateAct", method = { RequestMethod.GET })
	public Result<Integer> removeAppRelateAct(Long appId){
		if (Environment.isOnline()) {
			return ResultBuilder.fail(ErrorCode.********.getDesc());
		}
		return ResultBuilder.success(seedRedPacketService.removeAppRelateAct(appId));
	}

	/**
	 * 获取偷红包土地列表
	 */
	@ResponseBody
	@RequestMapping(value = "/getStealLandList", method = {RequestMethod.GET})
	public Result<StealLandInfoVO> getStealLandList(HttpServletRequest request, HttpServletResponse response){
		// 设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> appAndConsumerInfo = findAppAndConsumerInfoNew();
		if(StringUtils.isNotBlank(appAndConsumerInfo.getValue2())){
			return ResultBuilder.fail(appAndConsumerInfo.getValue2());
		}
		if(appAndConsumerInfo.getValue3() != null){
			return ResultBuilder.fail(appAndConsumerInfo.getValue3());
		}
		ConsumerDto consumerDto = appAndConsumerInfo.getValue0();
		AppSimpleDto appSimpleDto = appAndConsumerInfo.getValue1();
		// 统一访问日志
		try {
			return ResultBuilder.success(seedRedPacketService.getStealLandList(consumerDto, appSimpleDto));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 偷红包
	 */
	@ResponseBody
	@RequestMapping(value = "/stealRedPacket", method = {RequestMethod.GET})
	public Result<StealRedPacketVO> stealRedPacket(Long landId, String redPacketId, HttpServletRequest request, HttpServletResponse response){
		// 设置跨域信息
		pluginDomainCrossUtils.allowCrossDomain(request, response);
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> appAndConsumerInfo = findAppAndConsumerInfoNew();
		if(StringUtils.isNotBlank(appAndConsumerInfo.getValue2())){
			return ResultBuilder.fail(appAndConsumerInfo.getValue2());
		}
		if(appAndConsumerInfo.getValue3() != null){
			return ResultBuilder.fail(appAndConsumerInfo.getValue3());
		}
		ConsumerDto consumerDto = appAndConsumerInfo.getValue0();
		AppSimpleDto appSimpleDto = appAndConsumerInfo.getValue1();
		// 防重复校验
		String redisKey = RedisKeyFactory.K159.toString() + "_" + redPacketId;
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail(OPERATE_FREQUENTLY);
		}
		try {
			return ResultBuilder.success(seedRedPacketService.stealRedPacket(consumerDto, appSimpleDto, landId, redPacketId));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	private boolean isUseNewVersion(Long appId) {
		return seedRedPacketService.checkUseVersion(appId);
	}
}
