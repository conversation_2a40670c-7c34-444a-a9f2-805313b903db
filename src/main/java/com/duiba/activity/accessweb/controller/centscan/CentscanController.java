package com.duiba.activity.accessweb.controller.centscan;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.centscan.CentscanConfigDto;
import cn.com.duiba.activity.center.api.dto.centscan.CentscanPrizeConfigDto;
import cn.com.duiba.activity.center.api.enums.centscan.BankTypeEnum;
import cn.com.duiba.activity.center.api.enums.centscan.CardTypeEnum;
import cn.com.duiba.activity.center.api.enums.centscan.RedirectPageEnum;
import cn.com.duiba.activity.center.api.remoteservice.centscan.RemoteCentscanConfigService;
import cn.com.duiba.activity.center.api.remoteservice.centscan.RemoteCentscanPrizeConfigService;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.constant.CentscanConstants;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.service.market.MarketTempService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by sunyan on 2019/10/8.
 * 一分扫活动
 */
@RestController
@RequestMapping("/aaw/centscan")
public class CentscanController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CentscanController.class);

    private static final String DEFAULT_ERROR_VIEW = "/error";
    private static final String DEFAULT_CLOSE_VIEW = "/close";

    @Autowired
    private ActivityCacheService activityCacheService;

    @Autowired
    private RemoteCentscanConfigService remoteCentscanConfigService;

    @Autowired
    private RemoteCentscanPrizeConfigService remoteCentscanPrizeConfigService;

    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;

    @Autowired
    private DomainService domainService;
    @Autowired
    private DeveloperCacheService developerCacheService;
    @Autowired
    private MarketTempService marketTempService;
    @Autowired
    private CentscanConstants centscanConstants;
    /**
     * 加载vm模版
     * @param opId
     * @return
     */
    @GetMapping("/index")
    public ModelAndView index(Long opId,Boolean preview, HttpServletRequest request) {
        Long appId = RequestLocal.getAppId();
        AppSimpleDto app = developerCacheService.getById(appId);
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        OperatingActivityDto operatingActivityDto;
        try {
            operatingActivityDto = checkActivity(opId, appId);
        } catch (BizException e) {
            if (e.getCode().equals(ErrorCode.E0100017.getErrorCode())) {
                return new ModelAndView(DEFAULT_CLOSE_VIEW);
            } else {
                return new ModelAndView(DEFAULT_ERROR_VIEW);

            }
        }
        Long centId = operatingActivityDto.getActivityId();
        CentscanConfigDto centscanConfigDto = remoteCentscanConfigService.find(centId);
        if (centscanConfigDto == null) {
            return new ModelAndView(DEFAULT_CLOSE_VIEW);
        }
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/centscan/index");
        try {
            JSONObject cfg = new JSONObject();
            cfg.put("preview", preview);
            cfg.put("id", centId);
            cfg.put("opId", opId);
            cfg.put("appId", appId);
            cfg.put("centscanName", centscanConfigDto.getCentscanName());
            cfg.put("centscanRule", centscanConfigDto.getCentscanRule());
            cfg.put("startTime", centscanConfigDto.getStartTime());
            cfg.put("endTime", centscanConfigDto.getEndTime());
            cfg.put("openBind", centscanConfigDto.getOpenBind());
            cfg.put("openBindregion", centscanConfigDto.getOpenBindregion());
            cfg.put("bindUrl", centscanConfigDto.getBindUrl());
            cfg.put("customRedirect", centscanConstants.getRedirectMap().get(opId));
            cfg.put("openClassify", centscanConfigDto.getOpenClassify());
            cfg.put("classifyId", centscanConfigDto.getClassifyId());
            cfg.put("interfaceJson", centscanConfigDto.getInterfaceJson());
            cfg.put("appItemId", getAppItemId(centscanConfigDto));
            cfg.put("channelType", centscanConfigDto.getChannelType());
            cfg.put("activityMode", centscanConfigDto.getActivityMode());
            cfg.put("prizeClassifyId", centscanConfigDto.getPrizeClassifyId());
            String cardName="";
            String centscanCard = centscanConfigDto.getCentscanCard();
            String[] centscanCards = centscanCard.split(",");
            if(centscanCards.length==1){
                String[] card = centscanCards[0].split("_");
                cardName = BankTypeEnum.getByCode(card[0]).getDesc()+ CardTypeEnum.getByCode(card[1]).getDesc();
            }else if(centscanCards.length==2){
                String[] card = centscanCards[0].split("_");
                cardName = BankTypeEnum.getByCode(card[0]).getDesc()+ "卡";
            }
            cfg.put("cardName", cardName);
            long currentTime = System.currentTimeMillis();
            if(centscanConfigDto.getStartTime().getTime() > currentTime) {
                cfg.put("status",0);//未开始
            } else if(centscanConfigDto.getEndTime().getTime() <= currentTime){
                cfg.put("status",2);//已结束
            } else{
                cfg.put("status",1);//进行中
            }
            doDingzhi(request, appId, cfg);
            modelAndView.addObject("cfg", cfg);
            modelAndView.addObject("title", centscanConfigDto.getCentscanName());
            //营销落地页活动代码
            modelAndView.addObject("activityCode", marketTempService.getMarketTempActivityCode(ActivityUniformityTypeEnum.Centscan));
            //唤起登陆相关参数
            String loginProgram = developerCacheService.getLoginCode(app);
            modelAndView.addObject("loginProgram", loginProgram);
            modelAndView.addObject("loginFlag", consumer.isNotLoginUser());
            modelAndView.addObject("openLogin", app.isAppSwitch(AppSimpleDto.SwitchUseLoginCode) && org.apache.commons.lang.StringUtils.isNotBlank(loginProgram));
        } catch (Exception e) {
            LOGGER.warn("一分扫活动加载vm模版异常，opId={}", opId, e);
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        //访问日志
        doAccesslog(opId, consumer, centId);
        return modelAndView;
    }

    private String getAppItemId(CentscanConfigDto centscanConfigDto) {
        if (CentscanConfigDto.MODE_LIST != centscanConfigDto.getActivityMode()) {
            return centscanConfigDto.getAppItemId().toString();
        }
        List<Long> appItemIds =
                remoteCentscanPrizeConfigService.findByConfigId(centscanConfigDto.getId())
                        .stream().map(CentscanPrizeConfigDto::getAppItemId).collect(Collectors.toList());

        if (appItemIds.isEmpty()) {
            return StringUtils.EMPTY;
        }

        return StringUtils.join(appItemIds, ',');
    }

    private void doDingzhi(HttpServletRequest request, Long appId, JSONObject cfg) {
        //定制口红机记录id
        if(centscanConstants.getLipstickIds().contains(appId)){
            DomainConfigDto domainConfigDto = domainService.getSystemDomain(appId);
            String lipstickRecordId = request.getParameter("lipstickRecordId");
            cfg.put("lipstickRecordId", lipstickRecordId);
            cfg.put("lipstickUrl", "https:" + domainConfigDto.getActivityDomain() + centscanConstants.getLipstickUrl());
        }

        String payOrderId = request.getParameter("payOrderId");
        if (NumberUtils.isNumeric(payOrderId)) {
            DubboResult<OrdersDto> ordersResult = remoteConsumerOrderSimpleService.findById(Long.valueOf(payOrderId));
            OrdersDto ordersDto = ordersResult.getResult();
            //如果已经支付则不唤起支付框
            if (ordersDto!=null&& Objects.equals(ordersDto.getConsumerPayStatus(), OrdersDto.ConsumerPayStatusWaitPay)){
                cfg.put("payOrderId", payOrderId);
                String code = request.getParameter("code");
                cfg.put("wxAuthCode", code);
            }
        }
    }

    private void doAccesslog(Long opId, ConsumerDto consumer, Long centId) {
        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("pageBizId", 157);
        AccessLogFilter.putExPair("id", centId);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", ActivityUniformityTypeEnum.Centscan.getCode());
        if (consumer != null && !consumer.isNotLoginUser() && !consumer.getPartnerUserId().startsWith("gen_")) {
            AccessLogFilter.putExPair("loginStatus", 1);
            AccessLogFilter.putExPair("userCredits", consumer.getCredits());
            AccessLogFilter.putExPair("firstSignDay", consumer.getGmtCreate());
        } else {
            AccessLogFilter.putExPair("loginStatus", 2);
            AccessLogFilter.putExPair("userCredits", 0);
            AccessLogFilter.putExPair("firstSignDay", "");
        }
    }

    private OperatingActivityDto checkActivity(Long opId, Long appId) throws BizException{
        if (opId == null) {
            throw new BizException(ErrorCode.E0100007.getDesc()).withCode(ErrorCode.E0100007.getErrorCode());
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
        if (null == operatingActivityDto) {
            throw new BizException(ErrorCode.E0100015.getDesc()).withCode(ErrorCode.E0100015.getErrorCode());
        }
        if (!Objects.equals(operatingActivityDto.getType(), ActivityUniformityTypeEnum.Centscan.getCode())) {
            LOGGER.info("入库活动类型非一分扫活动,activityId={}", opId);
            throw new BizException(ErrorCode.E0100003.getDesc()).withCode(ErrorCode.E0100003.getErrorCode());
        }
        if (!Objects.equals(operatingActivityDto.getAppId(), appId)) {
            LOGGER.info("入库活动appId不一致,activityId={},appId={}", opId,appId);
            throw new BizException(ErrorCode.E0100003.getDesc()).withCode(ErrorCode.E0100003.getErrorCode());
        }
        if (!Objects.equals(operatingActivityDto.getStatus(), OperatingActivityDto.StatusIntOpen)) {
            LOGGER.info("活动已关闭,activityId={}", opId);
            throw new BizException(ErrorCode.E0100017.getDesc()).withCode(ErrorCode.E0100017.getErrorCode());
        }
        return operatingActivityDto;
    }


    /**
     * 加载vm模版
     * @param pageCode
     * @return
     */
    @GetMapping("/redirect")
    public ModelAndView redirect(Integer pageCode,Long orderId) {
        DubboResult<OrdersDto> ordersResult = remoteConsumerOrderSimpleService.findById(orderId);
        OrdersDto ordersDto = ordersResult.getResult();
        CentscanConfigDto centscanConfigDto = remoteCentscanConfigService.find(ordersDto.getRelationId());
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/centscan/redirect");
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(ordersDto.getAppId());
        String redirectUrl = "https:" + domainConfigDto.getTradeDomain() + RedirectPageEnum.getByCode(pageCode).getUrl()+orderId;
        modelAndView.addObject("callappUrl", centscanConfigDto.getCallappUrl());
        modelAndView.addObject("redirectUrl", redirectUrl);
        modelAndView.addObject("channelType", centscanConfigDto.getChannelType());
        modelAndView.addObject("isLogin", RequestLocal.getConsumerDO() != null && !RequestLocal.getConsumerDO().isNotLoginUser());
        return modelAndView;
    }

    /**
     * 获取用户银行卡号
     * @return
     */
    @GetMapping("/getCardList")
    public Result<List> getCardList(Long id) {
        CentscanConfigDto centscanConfigDto = remoteCentscanConfigService.find(id);
        if(centscanConfigDto==null||centscanConfigDto.getCardUrl()==null){
            return ResultBuilder.success(new ArrayList<>());
        }
        //// TODO: 2019/10/10 httpclient调用第三方接口
        return ResultBuilder.success(new ArrayList<>());
    }
}
