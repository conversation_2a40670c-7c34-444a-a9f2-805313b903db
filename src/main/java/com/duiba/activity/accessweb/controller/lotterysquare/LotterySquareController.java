package com.duiba.activity.accessweb.controller.lotterysquare;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareBonusConfigDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareBonusRecordDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareConfigDto;
import cn.com.duiba.activity.center.api.dto.lotterysquare.LotterySquareGradientRewardDto;
import cn.com.duiba.activity.center.api.enums.ActThrowChannelEnum;
import cn.com.duiba.activity.center.api.enums.LSBonusStyleEnum;
import cn.com.duiba.activity.center.api.enums.LSBonusTypeEnum;
import cn.com.duiba.activity.center.api.enums.LSExchangeStatusEnum;
import cn.com.duiba.activity.center.api.enums.LSPrizeTypeEnum;
import cn.com.duiba.activity.center.api.params.LotterySquareRecordQueryParam;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareBonusConfigService;
import cn.com.duiba.activity.center.api.remoteservice.lotterysquare.RemoteLotterySquareRecordService;
import cn.com.duiba.activity.common.center.api.enums.ShareCodeActivityTypeEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountCashDrawChannelEnum;
import cn.com.duiba.activity.common.center.api.enums.consumeraccounts.AccountTypeEnum;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteNewConsumerShareCodeService;
import cn.com.duiba.activity.common.center.api.remoteservice.sharecode.RemoteSimpleShareCodeService;
import cn.com.duiba.activity.common.center.api.req.consumeraccounts.AccountCashDrawsRequest;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppNewExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppNewExtraService;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.kvtable.service.api.enums.ActCmsWebHBaseKeyEnum;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbApiKvNoHotspotService;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibabiz.component.filters.bloom.url.UrlSerialAccessLocal;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.duiba.activity.accessweb.annotations.NoLoginCanAccess;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.constant.BlackUserConstant;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.constant.RefreshConstant;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.enums.LSActivityStatusEnum;
import com.duiba.activity.accessweb.enums.LSBonusListDescEnum;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.WeixinShareContentService;
import com.duiba.activity.accessweb.service.activity.LotterySquareService;
import com.duiba.activity.accessweb.service.activity.sharecode.ActivtyShareCodeService;
import com.duiba.activity.accessweb.service.market.MarketTempService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.share.ShareConfigService;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.activity.ActivityShareCodeResultVO;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareAccountDetailPageVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareAccountDetailVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareBonusListVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareCashDrawsInfoVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareConfigVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareGradientRewardVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareRecordPopVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareRecordVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySquareShareInfoVo;
import com.duiba.activity.accessweb.vo.lotterysquare.LotterySuqareBindResultVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2018/12/13
 */
@RestController
@RequestMapping("/aaw/lotterySquare")
public class LotterySquareController {

    private static final Logger log = LoggerFactory.getLogger(LotterySquareController.class);

    private static final String DEFAULT_ERROR_VIEW = "/error";

    private static final String PAGE_BIZ_ID = "pageBizId";
    private static final String USER_CREDITS = "userCredits";
    private static final String SUB_TYPE = "sub_type";
    private static final String ACTIVITY_ID = "activityId";

    private static final String MESSAGE_TEXT = "发放失败，请联系客服处理";

    @Autowired
    private RemoteLotterySquareBonusConfigService remoteLotterySquareBonusConfigService;
    @Autowired
    private RemoteNewConsumerShareCodeService remoteNewConsumerShareCodeService;
    @Autowired
    private RemoteLotterySquareRecordService remoteLotterySquareRecordService;
    @Autowired
    private RemoteHbApiKvNoHotspotService remoteHbApiKvNoHotspotService;
    @Autowired
    private RemoteSimpleShareCodeService remoteSimpleShareCodeService;
    @Autowired
    private WeixinShareContentService weixinShareContentService;
    @Autowired
    private RemoteAppNewExtraService remoteAppNewExtraService;
    @Autowired
    private ActivtyShareCodeService activtyShareCodeService;
    @Autowired
    private DeveloperCacheService developerCacheService;
    @Autowired
    private LotterySquareService lotterySquareService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private ShareConfigService shareConfigService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private RefreshConstant refreshConstant;
    @Autowired
    private MarketTempService marketTempService;
    @Autowired
    private RiskService riskService;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private BlackUserConstant blackUserConstant;

    @GetMapping("/index")
    public ModelAndView index(Long opId, boolean share, HttpServletRequest request, String shareCode, boolean preview) {
        if (null == opId) {
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
        if (null == operatingActivityDto) {
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        if (!Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypeLotterySquare)) {
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        String shareCodeField = "";
        if(!share){
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            if (app == null) {
                return new ModelAndView(DEFAULT_ERROR_VIEW);
            }
            if (!Objects.equals(operatingActivityDto.getAppId(), RequestLocal.getAppId())) {
                return new ModelAndView(DEFAULT_ERROR_VIEW);
            }
            //查找分享代码块
            shareCodeField = shareConfigService.getAppShareConfCode(app,request);
        }
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/lotterySquare/index");
        modelAndView.addObject("appId", operatingActivityDto.getAppId());
        modelAndView.addObject("opId", opId);
        modelAndView.addObject(ACTIVITY_ID, operatingActivityDto.getActivityId());
        modelAndView.addObject("pageType", share ? "share":"activity");
        modelAndView.addObject("title", operatingActivityDto.getTitle());
        modelAndView.addObject("inviterShareCode", shareCode);
        modelAndView.addObject("appShareConfCode", shareCodeField);
        modelAndView.addObject("preview", preview);
        if(null != RequestLocal.getConsumerDO()){
            //微信公众号用
            modelAndView.addObject("appKey", RequestLocal.getConsumerAppDO().getAppKey());
            modelAndView.addObject("partnerUserId", RequestLocal.getConsumerDO().getPartnerUserId());
        }
        //设置分享信息
        modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));
        //活动代码 by lufeng
        modelAndView.addObject("activityCode", marketTempService.getMarketTempActivityCode(ActivityUniformityTypeEnum.RedPacketSquare));

        //打印访问日志
        printAccessLog(opId,operatingActivityDto.getActivityId(), share);
        riskService.visitRiskLog(operatingActivityDto);
        return modelAndView;
    }

    @GetMapping("/cashIndex")
    public ModelAndView cashIndex(Long activityId){
        ModelAndView modelAndView = new ModelAndView("/lotterySquare/cashIndex");
        modelAndView.addObject(ACTIVITY_ID, activityId);
        return modelAndView;
    }

    @GetMapping("/cashConfirm")
    public ModelAndView cashConfirm(Long activityId){
        ModelAndView modelAndView = new ModelAndView("/lotterySquare/cashConfirm");
        modelAndView.addObject(ACTIVITY_ID, activityId);
        return modelAndView;
    }

    @GetMapping("/cashDetail")
    public ModelAndView cashDetail(Long activityId){
        ModelAndView modelAndView = new ModelAndView("/lotterySquare/cashDetail");
        modelAndView.addObject(ACTIVITY_ID, activityId);
        return modelAndView;
    }

    private void printAccessLog(Long opId,Long activityId, boolean share) {
        int loginStatus;
        if (null == RequestLocal.getConsumerDO()) {
            loginStatus = 2;
        } else {
            loginStatus = RequestLocal.getConsumerDO().isNotLoginUser() ? 2 : 1;
        }

        if (share) {
            //打印访问日志 - 分享页
            AccessLogFilter.putExPair(SUB_TYPE, "1");
            AccessLogFilter.putExPair(PAGE_BIZ_ID, "153");
        } else {
            //打印访问日志 - 主页
            AccessLogFilter.putExPair(SUB_TYPE, "2");
            AccessLogFilter.putExPair(PAGE_BIZ_ID, "207");
        }
        AccessLogFilter.putExPair("suc", "1");
        AccessLogFilter.putExPair("type", "300");
        AccessLogFilter.putExPair("id", activityId);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", "54");
        AccessLogFilter.putExPair("loginStatus", loginStatus);
        if (loginStatus == 1) {
            AccessLogFilter.putExPair(USER_CREDITS, RequestLocal.getConsumerDO().getCredits());
        } else {
            AccessLogFilter.putExPair(USER_CREDITS, 0);
        }
    }

    @GetMapping("/data")
    public Result<LotterySquareConfigVo> data(Long opId, boolean preview) {
        try {
            if (opId == null) {
                return ResultBuilder.fail(ResultCode.C100002);
            }
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
            if (operatingActivityDto == null) {
                return ResultBuilder.fail(ResultCode.C100015);
            }
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            if (preview){
                app = developerCacheService.getById(operatingActivityDto.getAppId());
            }
            if (app == null) {
                return ResultBuilder.fail(ResultCode.C100006);
            }
            if (!Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypeLotterySquare)) {
                return ResultBuilder.fail(ResultCode.C101001);
            }
            Long activityId = operatingActivityDto.getActivityId();
            LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(activityId);
            if (lotterySquareConfigDto == null) {
                return ResultBuilder.fail(ResultCode.C100015);
            }

            LotterySquareConfigVo lotterySquareConfigVo = BeanUtils.copy(lotterySquareConfigDto, LotterySquareConfigVo.class);
            lotterySquareConfigVo.setShareBonusRequire(lotterySquareConfigDto.getShareBonusRequire().getCode());
            lotterySquareConfigVo.setActivityAppType(lotterySquareConfigDto.getActivityAppType().getCode());
            lotterySquareConfigVo.setBonusType(lotterySquareConfigDto.getBonusType().getCode());
            lotterySquareConfigVo.setShareCodeText(getShareCodeText(app.getId()));

            //返回活动链接
            DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
            lotterySquareConfigVo.setActivityUrl(domainConfigDto.getActivityDomain() + "/aaw/lotterySquare/index?opId=" + opId);

            //如果类型是微信，返回带免登接口的活动地址
            lotterySquareConfigVo.setAutoLoginActivityUrl(getAutoLoginActivityUrl(app, lotterySquareConfigVo));

            //梯度奖励类型转换-当奖励类型为红包时会做转换
            List<LotterySquareGradientRewardDto> gradientRewards = activityCacheService.getLsGradientRewardById(activityId);
            List<LotterySquareGradientRewardVo> gradientRewardVos = new ArrayList<>();
            AppSimpleDto appSimpleDto = app;
            gradientRewards.stream().forEach(o -> gradientRewardVos.add(transferGradientRewardVo(appSimpleDto,o, lotterySquareConfigDto.getBonusType())));

            lotterySquareConfigVo.setGradientRewards(gradientRewardVos);
            //获取活动状态
            lotterySquareConfigVo.setActivityStatus(checkActivityStatus(lotterySquareConfigDto, operatingActivityDto));
            //获取开发者积分单位（考虑开启人民币模式）
            if (Objects.equals(LSBonusTypeEnum.BONUS_TYPE_MONEY, lotterySquareConfigDto.getBonusType())
                    || app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                lotterySquareConfigVo.setUnitName("元");
            } else {
                lotterySquareConfigVo.setUnitName(app.getUnitName());
            }

            //首次分享奖励
            List<LotterySquareBonusConfigDto> bonusConfigs = remoteLotterySquareBonusConfigService.selectBonusConfigByActId(activityId);

            //获取分享最高奖励-当奖励类型为红包时会做转换
            if(lotterySquareConfigDto.getFirstShareBonus()){
                lotterySquareConfigVo.setFirstShareLimitScope(lotterySquareConfigDto.getShareLimitScope().getId());
                //分享奖励配置信息
                setBonusConfig(app, lotterySquareConfigDto, lotterySquareConfigVo, bonusConfigs, LSPrizeTypeEnum.PRIZE_TYPE_SHARE);
                //新用奖励信息
                setBonusConfig(app, lotterySquareConfigDto, lotterySquareConfigVo, bonusConfigs, LSPrizeTypeEnum.PRIZE_TYPE_NEWUSER);
            }
            //返回登陆用户相关信息
            ConsumerDto consumer = RequestLocal.getConsumerDO();
            if (consumer == null || consumer.isNotLoginUser() || preview || ConsumerDto.PREVIEWUSERID.equals(consumer.getPartnerUserId())) {
                lotterySquareConfigVo.setLogin(false);
                return ResultBuilder.success(lotterySquareConfigVo);
            }
            //获取用户分享码
            ActivityShareCodeResultVO myShareCode = activtyShareCodeService.getShareCode(consumer, opId, ShareCodeActivityTypeEnum.HDTOOL.getCode());
            if (myShareCode != null) {
                lotterySquareConfigVo.setMyShareCode(myShareCode.getShareCode());
                lotterySquareConfigVo.setInviteCount(remoteSimpleShareCodeService.countInvitedUser(myShareCode.getShareCode(), ShareCodeActivityTypeEnum.HDTOOL, opId));
            }
            if (lotterySquareConfigDto.getFirstShareBonus()) {
                //当前用户今日是否已分享
                lotterySquareConfigVo.setShowFirstShare(!lotterySquareService.isUserFirstShared(activityId, consumer.getId(), lotterySquareConfigDto.getShareLimitScope()));
            }
            //未绑定&&新用户
            Boolean showBindShareCode = (!remoteNewConsumerShareCodeService.isUserBind(consumer.getId(), opId, ShareCodeActivityTypeEnum.HDTOOL));
            lotterySquareConfigVo.setShowBindShareCode(showBindShareCode);
            lotterySquareConfigVo.setNewUser(lotterySquareService.checkNewUser(lotterySquareConfigDto,opId, consumer.getId(), consumer.getPartnerUserId()));
             return ResultBuilder.success(lotterySquareConfigVo);
        } catch (Exception e) {
            log.warn("获取红包广场首页数据失败 opId{}", opId, e);
            return ResultBuilder.fail(ErrorCode.E9999999.getDesc());
        }
    }

    private String getShareCodeText(Long appId){
        String shareCodeText = "邀请码";
        if (refreshConstant.getJcnetAppId().equals(appId)){
            shareCodeText = "红包码";
        }
        return shareCodeText;
    }

    @GetMapping("/popInfo")
    public Result<List<LotterySquareRecordPopVo>> popInfo(Long opId) {
        if (opId == null) {
            return ResultBuilder.fail(ResultCode.C100002);
        }
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return ResultBuilder.fail(ResultCode.C100006);
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
        if (operatingActivityDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        if (!Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypeLotterySquare)) {
            return ResultBuilder.fail(ResultCode.C101001);
        }
        Long activityId = operatingActivityDto.getActivityId();
        LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(activityId);
        if (lotterySquareConfigDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        //返回登陆用户相关信息
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.C00000001);
        }
        //是否添加缓存？
        //弹窗信息-当奖励类型为红包时会做转换/人民币模式汇率转换
        List<LotterySquareRecordPopVo> list = lotterySquareService.getUnReadBonusAndUpdate(activityId, consumer.getId(), lotterySquareConfigDto.getBonusType(),app);
        return ResultBuilder.success(list);
    }

    private String getAutoLoginActivityUrl(AppSimpleDto app, LotterySquareConfigVo lotterySquareConfigVo) throws UnsupportedEncodingException {
        if (!Objects.equals(lotterySquareConfigVo.getActivityAppType(), ActThrowChannelEnum.CHNNEL_PUBLIC.getCode())) {
            return null;
        }
        String client = app.getCreditsRemainQueryUrl();
        client += client.contains("?") ? "&" : "?";
        //查询透传参数(默认值：dbredirect)
        String redirectParam = "dbredirect";
        AppNewExtraDto appNewExtraDto = remoteAppNewExtraService.findByAppId(app.getId()).getResult();
        if (StringUtils.isNotBlank(appNewExtraDto.getRedirectParameterName())) {
            redirectParam = appNewExtraDto.getRedirectParameterName();
        }
        return client + redirectParam + "=" + URLEncoder.encode(lotterySquareConfigVo.getActivityUrl(), "utf-8");
    }

    @GetMapping("/shareInfo")
    private Result<LotterySquareShareInfoVo> getShareInfo(Long opId){
        try {
            if (opId == null) {
                return ResultBuilder.fail(ResultCode.C100002);
            }
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
            if (operatingActivityDto == null) {
                return ResultBuilder.fail(ResultCode.C100015);
            }
            if (!Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypeLotterySquare)) {
                return ResultBuilder.fail(ResultCode.C101001);
            }
            LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(operatingActivityDto.getActivityId());
            if (lotterySquareConfigDto == null) {
                return ResultBuilder.fail(ResultCode.C100015);
            }
            LotterySquareShareInfoVo shareInfoVo = BeanUtils.copy(lotterySquareConfigDto, LotterySquareShareInfoVo.class);
            shareInfoVo.setActivityAppType(lotterySquareConfigDto.getActivityAppType().getCode());
            shareInfoVo.setBonusType(lotterySquareConfigDto.getBonusType().getCode());
            shareInfoVo.setShareCodeText(getShareCodeText(operatingActivityDto.getAppId()));

            AppSimpleDto app = developerCacheService.getById(lotterySquareConfigDto.getAppId());
            //获取开发者积分单位（考虑开启人民币模式）
            if (Objects.equals(LSBonusTypeEnum.BONUS_TYPE_MONEY, lotterySquareConfigDto.getBonusType())
                    || app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                shareInfoVo.setUnitName("元");
            } else {
                shareInfoVo.setUnitName(app.getUnitName());
            }
            //返回活动链接
            DomainConfigDto domainConfigDto = domainService.getSystemDomain(lotterySquareConfigDto.getAppId());
            shareInfoVo.setActivityUrl(domainConfigDto.getActivityDomain() + "/aaw/lotterySquare/index?opId=" + opId);

            //首次分享奖励
            List<LotterySquareBonusConfigDto> bonusConfigs = remoteLotterySquareBonusConfigService.selectBonusConfigByActId(lotterySquareConfigDto.getId());

            List<LotterySquareBonusConfigDto> newConsumerBonus = bonusConfigs.stream()
                    .filter(b -> b.getConsumerType().equals(LSPrizeTypeEnum.PRIZE_TYPE_NEWUSER))
                    .collect(Collectors.toList());
            if (newConsumerBonus.size() == 1 && LSBonusStyleEnum.BONUS_FIXED_AMOUNT == newConsumerBonus.get(0).getBonusStyle()) {
                shareInfoVo.setNewConsumerBonusStyle(LSBonusStyleEnum.BONUS_FIXED_AMOUNT.getCode());
                shareInfoVo.setMaxNewConsumerBonus(getMoneyByBonusType(app, lotterySquareConfigDto.getBonusType(), newConsumerBonus.get(0).getBonusAmount()));
            } else {
                newConsumerBonus = newConsumerBonus.stream()
                        .sorted(Comparator.comparing(LotterySquareBonusConfigDto::getRangeTo).reversed())
                        .collect(Collectors.toList());
                shareInfoVo.setNewConsumerBonusStyle(LSBonusStyleEnum.BONUS_RANDOM.getCode());
                shareInfoVo.setMaxNewConsumerBonus(getMoneyByBonusType(app, lotterySquareConfigDto.getBonusType(), newConsumerBonus.get(0).getRangeTo()));
            }
            return ResultBuilder.success(shareInfoVo);
        } catch (Exception e) {
            log.error("查询红包广场分享信息失败 opId:{}", opId, e);
            return ResultBuilder.fail(ResultCode.C999999);
        }
    }



    /**
     * 获取皮肤基本信息
     * @param actId
     * @return
     */
    @GetMapping("/getBaseInfo")
    public Result<String> getBaseInfo(Long actId) {
        if(null == actId){
            return ResultBuilder.fail(ErrorCode.E0200021.getDesc());
        }
        LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(actId);
        if (lotterySquareConfigDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        String key = ActCmsWebHBaseKeyEnum.K001.toString() + lotterySquareConfigDto.getAppId() + "_" + actId;
        String dataJson = remoteHbApiKvNoHotspotService.getStringByKey(key);
        return ResultBuilder.success(dataJson);
    }

    /**
     * 分享链接点击
     * @param uid : 用户partnerUserId (可为空)
     * @param opId : 入库活动id
     * @param shareCode ： 分享码
     * @return
     */
    @NoLoginCanAccess
    @PostMapping("/shareLinkClick")
    public Result shareLinkClick(String uid, Long opId, String shareCode){
        if(null == opId || StringUtils.isBlank(shareCode)){
            log.warn("参数不可为空");
            return ResultBuilder.fail("入参非法");
        }
        // 接口顺序访问检查
        if (!UrlSerialAccessLocal.hasRecord()) {
            log.info("urlSerialAccessService,拦截");
            return ResultBuilder.fail("请重新进入活动");
        }

        lotterySquareService.shareLinkClick(uid,opId,shareCode);
        return ResultBuilder.success();
    }

    /**
     * 微信公众号新用户同步
     * @param opId : 入库活动id
     * @return
     */
    @PostMapping("/newUserNotify")
    public Result newPublicUserNotify(Long opId){
        lotterySquareService.newPublicUserNotify(RequestLocal.getPartnerUserId(),opId);
        return ResultBuilder.success();
    }

    /**
     * 分享按钮点击
     * @param actId : 活动id
     * @return
     */
    @PostMapping("/firstShare")
    public Result firstShare(Long actId){
        lotterySquareService.firstShare(actId,RequestLocal.getCid());
        return ResultBuilder.success();
    }


    /**
     * 提现主页信息返回
     * @param actId
     * @return
     */
    @GetMapping("/cashIndexInfo")
    public Result<LotterySquareCashDrawsInfoVo> cashIndexInfo(Long actId){
        LotterySquareCashDrawsInfoVo vo = new LotterySquareCashDrawsInfoVo();
        vo.setButtonStatus(LotterySquareCashDrawsInfoVo.CASH_STATUS_EXCEPTION);
        if (null == actId || actId <= 0) {
            return ResultBuilder.success(vo);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || ConsumerDto.isNotLoginUser(consumer.getPartnerUserId())) {
            return ResultBuilder.success(vo);
        }
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return ResultBuilder.success(vo);
        }
        vo = lotterySquareService.cashIndexInfo(actId,consumer.getId(),app.getDeveloperId());
        return ResultBuilder.success(vo);
    }

    /**
     * 提现请求
     * @param username 提现支付宝真实姓名
     * @param account  提现支付宝账号
     * @param money 提现金额（元）
     * @return
     */
    @ResponseBody
    @PostMapping("/cashDraws")
    public Result cashDraws(Long actId,String username, String account, String money) {
        if (null == actId || actId <= 0) {
            return ResultBuilder.fail("非法的活动ID");
        }
        if (StringUtils.isBlank(username)) {
            return ResultBuilder.fail("用户真实姓名不能为空");
        }
        if (StringUtils.isBlank(account)) {
            return ResultBuilder.fail("支付宝账号不能为空");
        }
        if (! mailCheck(account)){
            return ResultBuilder.fail("系统异常,请稍后再试");
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || ConsumerDto.isNotLoginUser(consumer.getPartnerUserId())) {
            return ResultBuilder.fail("用户未登录");
        }
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return ResultBuilder.fail("用户未登录");
        }
        if (StringUtils.isBlank(money) || new BigDecimal(money).compareTo(BigDecimal.ZERO) <= 0) {
            return ResultBuilder.fail("非法的提现金额");
        }

        AccountCashDrawsRequest request = new AccountCashDrawsRequest();
        request.setAccountType(AccountTypeEnum.LOTTERY_SQUARE);
        request.setAppId(RequestLocal.getAppId());
        request.setAppName(app.getName());
        request.setChannelType(AccountCashDrawChannelEnum.ALI_PAY);
        request.setClientIp(RequestLocal.getIp());
        request.setClientUa(RequestLocal.getUserAgent());
        request.setConsumerId(consumer.getId());
        request.setDeveloperId(app.getDeveloperId());
        request.setDrawsDescription("红包广场提现操作");
        request.setChannelAccount(account);
        //扣除开发者余额
        request.setDeductDeveloperAmount(true);
        request.setUsername(username);
        request.setRelId(actId);
        try {
            if (StringUtils.isNotBlank(money)) {
                BigDecimal changeMoney = new BigDecimal(money).multiply(new BigDecimal(100));
                request.setChangeMoney(changeMoney.longValue());
            }
        } catch (Exception e) {
            return ResultBuilder.fail("非法的提现金额");
        }
        try (RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K924.toString() + consumer.getId(), 3)){
             if (null == lock){
                 return ResultBuilder.fail("操作频繁,请稍后再试");
             }
            lotterySquareService.cashDraws(request);
        } catch (BizException e) {
            log.warn("提现请求失败", e);
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提现请求失败", e);
            return ResultBuilder.fail("系统异常,请稍后再试");
        }
        return ResultBuilder.success();
    }

    /**
     * 非主流邮箱校验
     * @param account
     * @return
     */
    private boolean mailCheck(String account){
        if (! StringUtils.contains(account,"@")){
            return true;
        }
        String[] accountStrs = account.split("@");
        if (accountStrs.length < 2){
            return false;
        }
        Set<String> normalMails = blackUserConstant.getBlackMail();
        if (CollectionUtils.isEmpty(normalMails)){
            return true;
        }
        return normalMails.contains(accountStrs[1]);
    }

    @GetMapping("/accountDetail")
    public Result<LotterySquareAccountDetailPageVo> accountDetail(Long actId, Integer pageNum, Integer pageSize){
        LotterySquareAccountDetailPageVo pageVo = new LotterySquareAccountDetailPageVo();
        List<LotterySquareAccountDetailVo> voList = Lists.newArrayList();
        try {
            voList = lotterySquareService.queryAccountDetail(actId, RequestLocal.getCid(), pageNum, pageSize);
        } catch (Exception e){
            log.warn("资金明细查询失败，consumerId:{},actId:{}",RequestLocal.getCid(),actId);
        }
        pageVo.setList(voList);
        pageVo.setHasMore(voList.size() >= pageSize);
        return ResultBuilder.success(pageVo);
    }

    @PostMapping("/bind")
    public Result<LotterySuqareBindResultVo> bind(String shareCode, Long actId, Long opId) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return ResultBuilder.fail(ResultCode.C100006);
        }
        if (StringUtils.isBlank(shareCode)) {
            return ResultBuilder.fail(ResultCode.C101005);
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(opId);
        if (operatingActivityDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        if (!Objects.equals(operatingActivityDto.getType(), OperatingActivityDto.TypeLotterySquare)) {
            return ResultBuilder.fail(ResultCode.C101001);
        }
        LotterySquareConfigDto lotterySquareConfigDto = activityCacheService.getLsConfigById(operatingActivityDto.getActivityId());
        if (lotterySquareConfigDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        //判断活动状态
        if (checkActivityStatus(lotterySquareConfigDto, operatingActivityDto) != LSActivityStatusEnum.PROCESS.getCode()) {
            return ResultBuilder.fail(ResultCode.C101002);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || consumer.isNotLoginUser() || StringUtils.startsWith(consumer.getPartnerUserId(), "gen_")) {
            return ResultBuilder.fail(ResultCode.C00000001);
        }
        if (! consumer.getAppId().equals(operatingActivityDto.getAppId())){
            return ResultBuilder.fail(ResultCode.C100067);
        }
        // 接口顺序访问检查
        if (!UrlSerialAccessLocal.hasRecord()) {
            log.info("urlSerialAccessService,拦截");
            return ResultBuilder.fail("请重新进入活动");
        }

        //风控
        StormEngineResultDto riskResult = riskService.joinRisk(opId, actId, operatingActivityDto.getType());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultBuilder.fail(riskResult.getCopy());
        }
        try {
            RequestParams requestParams = RequestParams.parse(RequestLocal.getRequest());
            Integer bonus = lotterySquareService.bindShareCode(lotterySquareConfigDto, shareCode, opId, consumer, requestParams.getTransfer(), RequestLocal.getIp());

            if (bonus != null) {
                LotterySuqareBindResultVo lotterySuqareBindResultVo = new LotterySuqareBindResultVo();
                lotterySuqareBindResultVo.setBonus(getMoneyByBonusType(RequestLocal.getConsumerAppDO(),lotterySquareConfigDto.getBonusType(),bonus));

                //打印访问日志
                AccessLogFilter.putExPair("type", "300");
                AccessLogFilter.putExPair(SUB_TYPE, "3");
                AccessLogFilter.putExPair("suc", "1");
                AccessLogFilter.putExPair("id", operatingActivityDto.getActivityId());
                AccessLogFilter.putExPair("activityid", opId);
                AccessLogFilter.putExPair("activitytype", "54");
                AccessLogFilter.putExPair("loginStatus", 1);
                AccessLogFilter.putExPair(USER_CREDITS, Objects.toString(RequestLocal.getConsumerDO().getCredits(),"0"));
                AccessLogFilter.putExPair(PAGE_BIZ_ID, "236");
                return ResultBuilder.success(lotterySuqareBindResultVo);
            } else {
                return ResultBuilder.fail(ResultCode.C101008);
            }
        } catch (BizException e) {
            return ResultBuilder.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("绑定邀请码失败 opId:{},activityId:{}", opId, actId, e);
            return ResultBuilder.fail(ResultCode.E00005);
        }

    }

    @GetMapping("/myBonusList")
    public Result<LotterySquareBonusListVo> myBonusList(Long actId,
                                                        @RequestParam(defaultValue = "1") Integer pageNo,
                                                        @RequestParam(defaultValue = "30") Integer pageSize) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return ResultBuilder.fail(ResultCode.C100006);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.C00000001);
        }
        if(actId == null){
            return ResultBuilder.fail(ResultCode.C100015);
        }

        LotterySquareConfigDto config = activityCacheService.getLsConfigById(actId);
        LotterySquareRecordQueryParam param = new LotterySquareRecordQueryParam();
        param.setActivityId(actId);
        param.setConsumerId(consumer.getId());
        param.setPageNo(pageNo);
        param.setPageSize(pageSize);
        Page<LotterySquareBonusRecordDto> records = remoteLotterySquareRecordService.selectBonusListRecordByPage(param);
        Long totalBonus = remoteLotterySquareRecordService.getConsumerTotalBonus(actId,consumer.getId());

        //描述修改
        List<LotterySquareRecordVo> recordVos = new ArrayList<>();
        //类型转换
        records.getList().stream().forEach(o -> recordVos.add(transferRecordDto2Vo(app,o, config.getBonusType())));
        LotterySquareBonusListVo result = new LotterySquareBonusListVo();
        result.setTotalCount(records.getTotalCount());
        result.setBonusList(recordVos);
        result.setHasMore(recordVos.size() >= pageSize);
        result.setBonusType(config.getBonusType().getCode());
        if (LSBonusTypeEnum.BONUS_TYPE_CREDITS.equals(config.getBonusType())) {
            result.setMyTotalBonus(totalBonus.toString());
            result.setUnitName(RequestLocal.getConsumerAppDO().getUnitName());
            //开启人民币模式积分汇率转换
            if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                result.setMyTotalBonus(new BigDecimal(totalBonus).divide(new BigDecimal(app.getCreditsRate()), 2, RoundingMode.HALF_UP).toString());
            }
        } else {
            result.setMyTotalBonus(divideOneHundred(totalBonus).toString());
            result.setUnitName("元");
        }

        return ResultBuilder.success(result);
    }

    /**
     * 设置分享、新用户奖励配置信息
     * @param app
     * @param lotterySquareConfigDto
     * @param lotterySquareConfigVo
     * @param bonusConfigs
     * @param prizeType
     */
    private void setBonusConfig(AppSimpleDto app, LotterySquareConfigDto lotterySquareConfigDto, LotterySquareConfigVo lotterySquareConfigVo, List<LotterySquareBonusConfigDto> bonusConfigs ,LSPrizeTypeEnum prizeType) {
        List<LotterySquareBonusConfigDto> bonusConfigDtos = bonusConfigs.stream()
                .filter(b -> b.getConsumerType().equals(prizeType))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bonusConfigDtos)) {
            return;
        }
        String  maxBonus ;
        Integer bonusStyle;
        if (bonusConfigDtos.size() == 1 && LSBonusStyleEnum.BONUS_FIXED_AMOUNT == bonusConfigDtos.get(0).getBonusStyle()) {
            maxBonus = getMoneyByBonusType(RequestLocal.getConsumerAppDO(), lotterySquareConfigDto.getBonusType(), bonusConfigDtos.get(0).getBonusAmount());
            bonusStyle = LSBonusStyleEnum.BONUS_FIXED_AMOUNT.getCode();
        } else {
            bonusConfigDtos = bonusConfigDtos.stream()
                    .sorted(Comparator.comparing(LotterySquareBonusConfigDto::getRangeTo).reversed())
                    .collect(Collectors.toList());
            maxBonus = getMoneyByBonusType(app, lotterySquareConfigDto.getBonusType(), bonusConfigDtos.get(0).getRangeTo());
            bonusStyle = LSBonusStyleEnum.BONUS_RANDOM.getCode();
        }
        if (LSPrizeTypeEnum.PRIZE_TYPE_SHARE.equals(prizeType)){
            lotterySquareConfigVo.setMaxShareBonus(maxBonus);
            lotterySquareConfigVo.setFirstShareBonusStyle(bonusStyle);
        } else if (LSPrizeTypeEnum.PRIZE_TYPE_NEWUSER.equals(prizeType)){
            lotterySquareConfigVo.setMaxNewConsumerBonus(maxBonus);
            lotterySquareConfigVo.setNewConsumerBonusStyle(bonusStyle);
        }
    }

    private LotterySquareRecordVo transferRecordDto2Vo(AppSimpleDto app, LotterySquareBonusRecordDto bonusRecordDto, LSBonusTypeEnum bonusType) {
        LotterySquareRecordVo recordVo = BeanUtils.copy(bonusRecordDto, LotterySquareRecordVo.class);
        recordVo.setPrizeType(bonusRecordDto.getPrizeType().getCode());
        recordVo.setBonusType(bonusRecordDto.getBonusType().getCode());
        recordVo.setBonus(getMoneyByBonusType(app, bonusType, bonusRecordDto.getBonus()));
        if (Objects.equals(bonusRecordDto.getExchangeStatus(), LSExchangeStatusEnum.EXCHANGE_BONUS_RUN_OUT)
                || Objects.equals(bonusRecordDto.getExchangeStatus(), LSExchangeStatusEnum.EXCHANGE_STATUS_FAIL)
        ) {
            recordVo.setSubDesc(MESSAGE_TEXT);
        }
        recordVo.setExchangeStatus(bonusRecordDto.getExchangeStatus().getCode());
        recordVo.setDesc(LSBonusListDescEnum.getByDescCode(recordVo.getPrizeType()));
        return recordVo;
    }

    private LotterySquareGradientRewardVo transferGradientRewardVo(AppSimpleDto app, LotterySquareGradientRewardDto gradientRewardDto, LSBonusTypeEnum bonusType) {
        LotterySquareGradientRewardVo gradientRewardVo = BeanUtils.copy(gradientRewardDto, LotterySquareGradientRewardVo.class);
        gradientRewardVo.setBonusAmount(getMoneyByBonusType(app, bonusType, gradientRewardDto.getBonusAmount()));
        return gradientRewardVo;
    }

    /**
     * 收益->分到人民币模式换算
     * @param bonus
     * @return
     */
    private BigDecimal divideOneHundred(Long bonus) {
        if (null == bonus) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(bonus).divide(new BigDecimal(100));

    }

    private String getMoneyByBonusType(AppSimpleDto app, LSBonusTypeEnum bonusTypeEnum, Integer money) {
        if (Objects.equals(bonusTypeEnum, LSBonusTypeEnum.BONUS_TYPE_CREDITS)) {
            //开启人民币模式 - 汇率转换
            if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                return new BigDecimal(money).divide(new BigDecimal(app.getCreditsRate()),2, RoundingMode.HALF_UP).toString();
            }
            return money.toString();
        }
        return divideOneHundred(money.longValue()).toString();
    }

    private Integer checkActivityStatus(LotterySquareConfigDto lotterySquareConfigDto, OperatingActivityDto operatingActivityDto) {
        Date now = new Date();
        //如果不是开启状态，则为关闭
        if (!Objects.equals(operatingActivityDto.getStatus(), OperatingActivityDto.StatusIntOpen)) {
            //活动关闭-但在进行中-返回CLOSED_NOT_END状态
            if(now.before(lotterySquareConfigDto.getEndTime())){
                return LSActivityStatusEnum.CLOSED_NOT_END.getCode();
            }
            return LSActivityStatusEnum.CLOSED.getCode();
        }

        //校验开始时间和活动状态
        if (now.before(lotterySquareConfigDto.getStartTime())) {
            return LSActivityStatusEnum.NOT_START.getCode();
        }
        if (now.after(lotterySquareConfigDto.getEndTime())) {
            return LSActivityStatusEnum.END.getCode();
        }
        return LSActivityStatusEnum.PROCESS.getCode();
    }

}
