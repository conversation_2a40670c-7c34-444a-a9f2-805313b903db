package com.duiba.activity.accessweb.controller.zjunionpay;

import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.tool.ItemKeyUtils;
import cn.com.duiba.order.center.api.dto.OrderPromotionDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrderPromotionService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duibabiz.component.domain.DomainService;
import com.duiba.activity.accessweb.vo.projectx.SeckillOrderListVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhangyongjie on 2021/11/4 7:37 下午
 */

@RestController
@RequestMapping("/aaw/zjUnionpay")
public class ZjUnionProjectxController {

    private static final Logger logger = LoggerFactory.getLogger(ZjUnionProjectxController.class);
    @Autowired
    private RemoteOrderPromotionService remoteOrderPromotionService;

    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;

    @Autowired
    private RemoteItemKeyService remoteItemKeyService;

    @Autowired
    private DomainService domainService;

    public static final String ORDER_DETAIL_URL = "/crecord/recordDetailNew?after=1&orderId=";


    @GetMapping("/querySeckillOrder")
    @ResponseBody
    public Result<?> querySeckillOrderList(){
        Long cid = RequestLocal.getCid();
        logger.info("【浙江银联】星速台查询秒杀订单，进入");
        try{
            List<OrderPromotionDto> orderPromotionDtos = remoteOrderPromotionService.queryByTypeAndConsumerId(3, cid);
            if(CollectionUtils.isEmpty(orderPromotionDtos)){
                return ResultBuilder.success(Collections.emptyList());
            }
            List<Long> orderIdList = orderPromotionDtos.stream().map(OrderPromotionDto::getOrderId).collect(Collectors.toList());

            DubboResult<List<OrdersDto>> ordersResult = remoteConsumerOrderSimpleService.findByIds(orderIdList, cid);
            List<OrdersDto> ordersDtoList = ordersResult.getResult();

            List<Long> appItemIds = ordersDtoList.stream().map(OrdersDto::getAppItemId).collect(Collectors.toList());


            Map<Long, ItemKeyDto> itemMap = CollectionUtils.isEmpty(appItemIds) ? Maps.newHashMap() :
                    remoteItemKeyService.getBatchItemKeyByAppItemIdsIncludeDeleted(appItemIds).getResult()
                            .stream().collect(Collectors.toMap(o -> o.getAppItem().getId(), Function.identity()));

            DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
            String activityDomain = domainConfigDto.getActivityDomain();
            List<SeckillOrderListVO> resultList
                    = ordersDtoList.stream().filter(order -> !"fail".equals(order.getStatus())).map(order -> {
                SeckillOrderListVO resultVO = new SeckillOrderListVO();
                resultVO.setOrderId(order.getId());
                ItemKeyDto itemKeyDto = itemMap.get(order.getAppItemId());
                if(Objects.nonNull(itemKeyDto)){
                    resultVO.setAppItemName(ItemKeyUtils.getTitle(itemKeyDto));
                    resultVO.setImageUrl(ItemKeyUtils.getSmallImage(itemKeyDto));
                }
                resultVO.setOrderDetailPageUrl(activityDomain + ORDER_DETAIL_URL + order.getId());
                return resultVO;
            }).sorted(Comparator.comparing(SeckillOrderListVO::getOrderId)).collect(Collectors.toList());
            return ResultBuilder.success(resultList);

        }catch (Exception e){
            logger.warn("【星速台】查询秒杀订单失败",e);
            return ResultBuilder.fail("网络异常");
        }
    }
}