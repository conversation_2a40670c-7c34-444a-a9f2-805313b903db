package com.duiba.activity.accessweb.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import cn.com.duibabiz.component.domain.DomainService;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;

@Controller
@RequestMapping("/newtools")
public class ActivityHdtoolCtrl extends BaseNewCtrl {
	private static Logger log = LoggerFactory.getLogger(ActivityHdtoolCtrl.class);
	@Autowired
	private DomainService domainService;

	/**
	 * 活动工具首页初始化
	 * <AUTHOR>
	 * @param request
	 * @param id
	 * @return
	 * @since JDK 1.6
	 */
	@RequestMapping("/index")
	public void index(@RequestParam Long id, HttpServletRequest request,HttpServletResponse response){
		String redirectUrl = "/hdtool/index?"+ request.getQueryString();
		String appDomain = domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain();
		try {
			log.info("/hdtool/index:{},appId:{} ,Referer:{}",id,RequestLocal.getAppId(),request.getHeader("Referer"));
			response.sendRedirect(appDomain + redirectUrl);
		} catch (Exception e) {
			//
		}
	}

}
