package com.duiba.activity.accessweb.controller.shuqi;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.constant.ShuqiConstant;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.shuqi.ShuqiLikeActivityV2Service;
import com.duiba.activity.accessweb.vo.shuqi.LikeOpenPrizeResultVO;
import com.duiba.activity.accessweb.vo.shuqi.LikeRankInfoVO;
import com.duiba.activity.accessweb.vo.shuqi.LikeRankListInfoVO;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * Created by HePeng on 2019/08/26 13:52.
 */
@RestController
@RequestMapping("/aaw/shuqi/like/v2")
public class ShuqiLikeActivityV2Ctrl {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShuqiLikeActivityV2Ctrl.class);

    @Autowired
    private ShuqiConstant shuqiConstant;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private ShuqiLikeActivityV2Service shuqiLikeActivityV2Service;

    @GetMapping("/result")
    public Result<LikeOpenPrizeResultVO> result(Long activityId) {
        // 校验
        Pair<ResultCode, OperatingActivityDto> pair = preCheck(activityId, true);
        if(pair.getLeft() != null) {
            return ResultBuilder.fail(pair.getLeft());
        }
        // 处理
        try {
            return ResultBuilder.success(shuqiLikeActivityV2Service.openPrizeResult(pair.getRight(), RequestLocal.getConsumerDO()));
        } catch (Exception e1) {
            LOGGER.warn("书旗拉新点赞活动二期开奖结果接口异常", e1);
            return ResultBuilder.fail(ResultCode.C999999);
        }
    }

    @GetMapping("/rankList")
    public Result<LikeRankListInfoVO> rankList(Long activityId) {
        // 校验
        Pair<ResultCode, OperatingActivityDto> pair = preCheck(activityId, false);
        if(pair.getLeft() != null) {
            return ResultBuilder.fail(pair.getLeft());
        }
        // 处理
        try {
            return ResultBuilder.success(shuqiLikeActivityV2Service.rankList(pair.getRight(), RequestLocal.getConsumerDO()));
        } catch (Exception e1) {
            LOGGER.warn("书旗拉新点赞活动二期排行榜接口异常", e1);
            return ResultBuilder.fail(ResultCode.C999999);
        }
    }

    @GetMapping("/openPrize")
    public Result<List<LikeRankInfoVO>> openPrize(Long activityId) {
        // 权限校验
        if(SpringEnvironmentUtils.isProdEnv()
                || !Objects.equals(1L, RequestLocal.getAppId())
                || !Objects.equals("1", RequestLocal.getPartnerUserId())) {
            return ResultBuilder.success();
        }
        // 活动id检查
        if(!shuqiConstant.getShuqiLikeActivityId().equals(activityId)) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        // 查询活动
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
        if(operatingActivityDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        // 处理
        try {
            return ResultBuilder.success(shuqiLikeActivityV2Service.openPrize(operatingActivityDto));
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e1) {
            LOGGER.warn("书旗拉新点赞活动二期开奖接口异常", e1);
            return ResultBuilder.fail(ResultCode.C999999);
        }
    }

    /**
     * 前置校验
     * @param activityId 入库活动id
     * @param canAccessAfterOpenPrize 开奖之后仍能访问
     * @return
     */
    private Pair<ResultCode, OperatingActivityDto> preCheck(Long activityId, boolean canAccessAfterOpenPrize) {
        // 活动id检查
        if(!shuqiConstant.getShuqiLikeActivityId().equals(activityId)) {
            return Pair.of(ResultCode.C100015, null);
        }
        // 查询活动
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
        if(operatingActivityDto == null) {
            return Pair.of(ResultCode.C100015, null);
        }
        // 是否有权限访问活动
        if(!Objects.equals(operatingActivityDto.getAppId(), RequestLocal.getAppId())) {
            return Pair.of(ResultCode.C100014, operatingActivityDto);
        }
        // 活动是否已结束
        if(!Objects.equals(Boolean.FALSE, operatingActivityDto.getDeleted())
                || !Objects.equals(OperatingActivityDto.StatusIntOpen, operatingActivityDto.getStatus())) {
            return Pair.of(ResultCode.C100063, operatingActivityDto);
        }
        // 活动是否已开奖
        if(!canAccessAfterOpenPrize && shuqiLikeActivityV2Service.openPrizeFlag(operatingActivityDto.getId())) {
            return Pair.of(ResultCode.C100063, operatingActivityDto);
        }
        return Pair.of(null, operatingActivityDto);
    }
}
