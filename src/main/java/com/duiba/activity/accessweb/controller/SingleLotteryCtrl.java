/**
 * Project Name:activity-access-web
 * File Name:SingleLotteryCtrl.java
 * Package Name:com.duiba.activity.accessweb.controller
 * Date:2017年1月16日下午1:49:31
 * Copyright (c) 2017, duiba.com.cn All Rights Reserved.
 */

package com.duiba.activity.accessweb.controller;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.direct.ActivityBlackList4DeveloperDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.DuibaSingleLotteryDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryAppSpecifyDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.SingleLotteryOrderDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.direct.RemoteActivityBlackList4DeveloperService;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteDuibaSingleLotteryServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryOrderService;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.dcommons.enums.GoodsTypeEnum;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.domain.enums.saas.SaasFuncTypeEnum;
import cn.com.duiba.developer.center.api.utils.AppIdConstant;
import cn.com.duiba.developer.center.api.utils.SaasGrantUtil;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.goods.center.api.remoteservice.RemoteGoodsAppItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsCouponDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemBaseDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.thirdparty.dto.CreditsMessageDto;
import cn.com.duiba.thirdparty.enums.CallbackChannelTypeEnum;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibabiz.component.filters.bloom.url.UrlSerialAccessLocal;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.config.HsbcConfig;
import com.duiba.activity.accessweb.config.XyFamilyConfig;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.exception.NotLoginRunTimeException;
import com.duiba.activity.accessweb.exception.StatusException;
import com.duiba.activity.accessweb.service.CouponService;
import com.duiba.activity.accessweb.service.GoodsService;
import com.duiba.activity.accessweb.service.HsbcService;
import com.duiba.activity.accessweb.service.hdtool.service.ItemService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.singlelottery.SingleLotteryCreatorService;
import com.duiba.activity.accessweb.service.singlelottery.SingleLotteryService;
import com.duiba.activity.accessweb.service.singlelottery.VipLimitService;
import com.duiba.activity.accessweb.service.singlelottery.impl.SingleLotteryServiceImpl;
import com.duiba.activity.accessweb.service.token.FormTokenService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.CreditsCalculateService;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.PlantformHelper;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.ItemKeyVO;
import com.duiba.activity.accessweb.vo.SingleLotteryVO;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * ClassName:SingleLotteryCtrl <br/>
 * Date:     2017年1月16日 下午1:49:31 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.6
 */
@Controller
@RequestMapping("/singleLottery")
public class SingleLotteryCtrl extends BaseCtrl {

    private static final Logger log = LoggerFactory.getLogger(SingleLotteryCtrl.class);

    @Autowired
    private RemoteDuibaSingleLotteryServiceNew remoteDuibaSingleLotteryServiceNew;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private SingleLotteryService singleLotteryService;
    @Autowired
    private VipLimitService vipLimitService;
    @Autowired
    private RemoteActivityBlackList4DeveloperService remoteActivityBlackList4DeveloperService;
    @Autowired
    private FormTokenService formTokenService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private SingleLotteryCreatorService singleLotteryCreatorService;
    @Autowired
    private RemoteSingleLotteryOrderService remoteSingleLotteryOrderService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private CreditsCalculateService creditsCalculateService;
    @Autowired
    private RemoteGoodsAppItemExtraService remoteAppItemExtraService;
    @Autowired
    private RemoteItemExtraService remoteItemExtraService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private XyFamilyConfig xyFamilyConfig;
    @Resource
    private HsbcService hsbcService;
    @Autowired
    private HsbcConfig hsbcConfig;
    @Resource
    private RemoteAppItemGoodsService remoteAppItemGoodsService;
    @Resource
    private RemoteAppItemSkuService remoteAppItemSkuService;
    /**
     * 香溢家虚拟商品前缀
     */
    private final static String XY_FAMILY_JF = "JF";
    /**
     * 单品抽奖中奖类型：大奖、小奖
     */
    private static final ImmutableSet<Integer> IN_PRIZE_TYPE = ImmutableSet.of(SingleLotteryOrderDto.OptionTypeBig, SingleLotteryOrderDto.OptionTypeSmall);


    @RequestMapping("/duiba")
    public ModelAndView duiba(Long id, HttpServletRequest request) {
        DuibaSingleLotteryDto duibaSingleLottery = remoteDuibaSingleLotteryServiceNew.find(id);
        Long appId = RequestLocal.getAppId();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (duibaSingleLottery == null || appId == null) {
            return new ModelAndView("/error");
        }
        OperatingActivityDto oa = remoteOperatingActivityServiceNew.findByAppIdAndDuibaSingleLotteryIdAndDeleted(appId, id, false);
        if (oa == null || oa.getDeleted()) {
            return new ModelAndView("/error");
        }

        if(oa.getActivityId() != null && !SaasGrantUtil.checkGrant(app.getDeveloperId(), app.getId(), SaasFuncTypeEnum.ACTIVITY, oa.getType(), oa.getActivityId(), true)){
            return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
        }
        return doIndex(oa.getId(), request);
    }

    @ResponseBody
    @RequestMapping(value = "/init", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject init(Long id, HttpServletRequest request) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        JSONObject json = new JSONObject();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            return json;
        }
        return singleLotteryService.init(id, consumer, app, request);
    }

    @RequestMapping("/index")
    public ModelAndView index(HttpServletRequest request) {
        String idStr = StringUtils.trim(request.getParameter("id"));
        if (StringUtils.isEmpty(idStr)) {
            log.warn("id param is invalid!");
            return new ModelAndView("/error");
        }

        Long id;
        if (NumberUtils.isNumeric(idStr)) {
            id = Long.valueOf(idStr);
        } else {
            id = strToLong(idStr);
        }
        if (id==null) {
            log.warn("id param is invalid! idStr={}", idStr);
            return new ModelAndView("/error");
        }
        return doIndex(id, request);
    }


    private ModelAndView doIndex(Long id, HttpServletRequest request){
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer == null || consumer.getAppId() == null) {
            log.debug("consumerDto or appId is null");
            throw new NotLoginRunTimeException();
        }
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            return new ModelAndView("/error");
        }
        // 此判断为专题主推单品的逻辑
        Long idTmp = singleLotteryService.getSpecialId(id, request.getParameter("inlet"), app.getId());
        if (idTmp == null) {
            return new ModelAndView("/error");
        }
        OperatingActivityDto oa = remoteOperatingActivityServiceNew.find(id);
        if(oa.getActivityId() != null && !SaasGrantUtil.checkGrant(app.getDeveloperId(), app.getId(), SaasFuncTypeEnum.ACTIVITY, oa.getType(), oa.getActivityId(), true)){
            return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
        }

        AccessLogExUtil.putAccessLogExPair(true, 500, 1, PageBizTypeEnum.ACT_INDEX, oa.getActivityId(),
                oa.getId(), ActivityUniformityTypeEnum.getByCode(oa.getType()), consumer.isNotLoginUser(),
                consumer.getCredits(), null, null, null, null);
        riskService.visitRiskLog(oa);
        return singleLotteryService.getIndexModel(idTmp, request, consumer, app, oa);
    }



    private Long strToLong(String str) {
        StringBuilder sb = new StringBuilder();
        for (char c:str.toCharArray()) {
            if (CharUtils.isAsciiNumeric(c)) {
                sb.append(c);
            }
        }
        String strNum = sb.toString();
        return NumberUtils.isNumeric(strNum)? Long.valueOf(strNum):null;
    }

    @ResponseBody
    @RequestMapping(value = "/doJoin", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject doJoin(HttpServletRequest request) {
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isBlank(request.getParameter("operationAcitvityId"))) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "无权查看");
            return jsonObject;
        }

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        Long operationAcitvityId = Long.parseLong(request.getParameter("operationAcitvityId"));
        OperatingActivityDto operatingActivity = remoteOperatingActivityServiceNew.find(operationAcitvityId);

        SingleLotteryVO singleLotteryVO = singleLotteryService.findSingleLotteryVOByAcitivity(operatingActivity);
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "无权查看");
            return jsonObject;
        }

        //校验积分
        long credits = checkAndSetCredits(operatingActivity, singleLotteryVO, app);
        //校验入库活动是否可用
        if (checkOperatingActivity(jsonObject, operatingActivity)) {
            return jsonObject;
        }
        //校验是否到活动开始时间
        if(Objects.nonNull(operatingActivity.getStartTime()) && new Date().before(operatingActivity.getStartTime())){
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, String.format(CommonConstants.ACTIVITY_NOT_START,DateUtil.getMinuteStr(operatingActivity.getStartTime())));
            return jsonObject;
        }
        //校验用户是否能正常参加
        if (checkConsumer(jsonObject, consumer, operatingActivity, singleLotteryVO, app, credits)) {
            return jsonObject;
        }
        //校验黑名单和定向
        if (checkDirectAndBlack(jsonObject, operatingActivity, singleLotteryVO, app)) {
            return jsonObject;
        }
        //校验用户限额
        if (checkConsumerLimit(jsonObject, consumer, operatingActivity, singleLotteryVO, app)) {
            return jsonObject;
        }
        Long cid = RequestLocal.getCid();
        // 接口顺序访问检查
        if (!UrlSerialAccessLocal.hasRecord()) {
            log.info("urlSerialAccessService,拦截");
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "请重新进入活动");
            return jsonObject;
        }
        if (!formTokenService.checkAndInvalidConsumerToken(cid, request.getParameter("token"))) {
            log.warn("doJoin token check fail ,cid={},ip={}", cid, RequestTool.getIpAddr(request));
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "未中奖(" + StatusException.CodeFormTokenError + ")");
            jsonObject.put("token", getConsumerFormTokenJS());
            return jsonObject;
        }
        //风控
        StormEngineResultDto riskResult = riskService.joinRisk(operatingActivity.getId(),operatingActivity.getActivityId(),operatingActivity.getType());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, riskResult.getCopy());
            jsonObject.put("token", getConsumerFormTokenJS());
            return jsonObject;
        }

        //抽奖流程
        SingleLotteryOrderDto singleLotteryOrderDto = null;
        try {
            //抽奖订单创建方法
            singleLotteryOrderDto = singleLotteryCreatorService.createOrder(consumer, operationAcitvityId, RequestTool.getIpAddr(request), request);
        } catch (Exception e) {
            boolean isFree = singleLotteryService.isFreeLimit(singleLotteryVO, operatingActivity, consumer);
            consumer = remoteConsumerService.find(consumer.getId());
            Integer lotteryButtonStatus = singleLotteryService.retButtonContent(isFree, singleLotteryVO, credits, operatingActivity, consumer, app);
            log.error("单品抽奖创建抽奖订单失败", e);
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, e.getMessage());
            jsonObject.put("lotteryButtonStatus", lotteryButtonStatus);
            jsonObject.put("token", getConsumerFormTokenJS());
            return jsonObject;
        }

        AccessLogExUtil.putAccessLogExPair(true, 500, 2, PageBizTypeEnum.ACT_JOIN, operatingActivity.getActivityId(),
                operatingActivity.getId(), ActivityUniformityTypeEnum.getByCode(operatingActivity.getType()), consumer.isNotLoginUser(),
                consumer.getCredits(), null, null, null, null);


        // 异步扣积分
        Map<String, String> map = Maps.newHashMap();
        RequestParams requestParams = RequestParams.parse(request);
        map.put("ip", RequestTool.getIpAddr(request));
        map.put("deap", requestParams.getCookies("deap"));
        map.put("userAgent", requestParams.getUserAgent());
        map.put("wdata3", RequestLocal.getTokenId());
        map.put("transfer", requestParams.getTransfer());
        map.put("os", requestParams.getOs());
        map.put("proxy", String.valueOf(requestParams.isProxy()));
        map.put("slotId", requestParams.getSlotId());
        Long operatingActivityId = operatingActivity != null ? operatingActivity.getId() : null;
        if(null != operatingActivityId){
            map.put("opId", String.valueOf(operatingActivityId));
        }
        //params里增加这个，调用扣积分接口回调就是mq
        map.put(CreditsMessageDto.MESSAGE_CHANNEL_TYPE_KEY,CallbackChannelTypeEnum.ROCKETMQ.getType());
        singleLotteryCreatorService.asyncConsumerCredits(consumer, app, singleLotteryOrderDto, map);

        jsonObject.put(CommonConstants.SUCCESS_KEY, true);
        jsonObject.put("singleLotteryOrderId", singleLotteryOrderDto.getId());
        jsonObject.put("token", getConsumerFormTokenJS());
        return jsonObject;
    }


    /**
     * 校验用户限额
     *
     * @param jsonObject
     * @param consumer
     * @param operatingActivity
     * @param singleLotteryVO
     * @param app
     * @return
     */
    private boolean checkConsumerLimit(JSONObject jsonObject, ConsumerDto consumer, OperatingActivityDto operatingActivity, SingleLotteryVO singleLotteryVO, AppSimpleDto app) {
        boolean isNotDrawLimit = singleLotteryService.isNotDrawLimit(singleLotteryVO, operatingActivity, consumer);
        if (!isNotDrawLimit) {
            if (singleLotteryVO.getConsumerDrawLimitScope().equalsIgnoreCase(SingleLotteryVO.CONSUMER_DRAW_LIMIT_EVERYDAY)) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "今日已抽完");
                jsonObject.put("lotteryButtonStatus", SingleLotteryServiceImpl.STATUS_LOTTERY_TODAY_JOIN);
                return true;
            } else {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "已抽完");
                jsonObject.put("lotteryButtonStatus", SingleLotteryServiceImpl.STATUS_LOTTERY_HAS);
                return true;
            }
        }
        //此次兑换不是免费而且开启无积分体系
        if (!singleLotteryService.isFreeLimit(singleLotteryVO, operatingActivity, consumer) && app.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType)) {
            if (singleLotteryVO.getFreeLimitScope() == null || singleLotteryVO.getFreeLimitScope().equalsIgnoreCase(SingleLotteryVO.FREE_LIMIT_EVERYDAY)) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "今日已抽完");
                jsonObject.put("lotteryButtonStatus", SingleLotteryServiceImpl.STATUS_LOTTERY_TODAY_JOIN);
                return true;
            } else {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "已抽完");
                jsonObject.put("lotteryButtonStatus", SingleLotteryServiceImpl.STATUS_LOTTERY_HAS);
                return true;
            }
        }
        return false;
    }

    /**
     * 校验并设置积分
     *
     * @param operatingActivity
     * @param singleLotteryVO
     * @param app
     * @return
     */
    private long checkAndSetCredits(OperatingActivityDto operatingActivity, SingleLotteryVO singleLotteryVO, AppSimpleDto app) {
        long credits = 0L;
        if (SingleLotteryVO.TYPE_DUIBA.equals(singleLotteryVO.getType())) {
            //Start 开发者可编辑兑吧活动的抽奖积分
            if (operatingActivity.getCustomCredits() > 0L) {
                credits = operatingActivity.getCustomCredits();
            } else {
                credits = singleLotteryService.facePrizeToCredits(singleLotteryVO.getCreditsPrice(), app.getCreditsRate());
            }
            //End 开发者可编辑兑吧活动的抽奖积分
        } else if (SingleLotteryVO.TYPE_APP.equals(singleLotteryVO.getType()) && singleLotteryVO.getCredits() != null) {
            credits = singleLotteryVO.getCredits();
        }
        return credits;
    }

    /**
     * 校验入库活动是否可用
     *
     * @param jsonObject
     * @param operatingActivity
     * @return
     */
    private boolean checkOperatingActivity(JSONObject jsonObject, OperatingActivityDto operatingActivity) {
        if (null != operatingActivity.getParentActivityId()) {
            OperatingActivityDto parentActivity = remoteOperatingActivityServiceNew.find(operatingActivity.getParentActivityId());
            if (parentActivity.getDeleted() || parentActivity.getStatus() != OperatingActivityDto.StatusIntOpen) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "活动已结束");
                return true;
            }
        }
        if (operatingActivity.getType() != OperatingActivityDto.TypeDuibaSingleLottery && operatingActivity.getType() != OperatingActivityDto.TypeAppSingleLottery) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "非法活动");
            return true;
        }
        return false;
    }

    /**
     * 校验用户是否能正常参加
     *
     * @param jsonObject
     * @param consumer
     * @param operatingActivity
     * @param singleLotteryVO
     * @param app
     * @param credits
     * @return
     */
    private boolean checkConsumer(JSONObject jsonObject, ConsumerDto consumer, OperatingActivityDto operatingActivity, SingleLotteryVO singleLotteryVO, AppSimpleDto app, long credits) {
        if (!operatingActivity.getAppId().equals(consumer.getAppId())) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "无权访问");
            return true;
        }

        if (null == operatingActivity.getStatus() || operatingActivity.getStatus() != OperatingActivityDto.StatusIntOpen) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "活动已结束");
            return true;
        }
        if (!app.getId().equals(consumer.getAppId())) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "非法活动");
            return true;
        }
        if (!singleLotteryService.isFreeLimit(singleLotteryVO, operatingActivity, consumer)) {
            Long consumeCredits = consumer.getCredits();
            if (credits > consumeCredits) {
                jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                jsonObject.put(CommonConstants.MESSAGE_KEY, "积分不足");
                return true;
            }
        }

        if (singleLotteryVO.getAppSingleLotteryDto() != null && app.isAppSwitch(AppSimpleDto.SwitchOpenVipLimit) && !vipLimitService.isCanExchange(singleLotteryVO.getAppSingleLotteryDto(), consumer.getVipLevel())) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "您的会员等级不符");
            return true;
        }
        return false;
    }

    /**
     * 校验黑名单和定向
     *
     * @param jsonObject
     * @param operatingActivity
     * @param singleLotteryVO
     * @param app
     * @return
     */
    private boolean checkDirectAndBlack(JSONObject jsonObject, OperatingActivityDto operatingActivity, SingleLotteryVO singleLotteryVO, AppSimpleDto app) {
        //兑吧单品抽奖黑名单定向验证
        if (operatingActivity.getType() == OperatingActivityDto.TypeDuibaSingleLottery) {
            DuibaSingleLotteryDto duibaSinglelottery = remoteDuibaSingleLotteryServiceNew.find(singleLotteryVO.getId());
            if (duibaSinglelottery.isOpenSwitch(DuibaSingleLotteryDto.SWITCHES_DEV_BLACKLIST)) {
                //活动的新黑名单
                Boolean isBlack = remoteActivityBlackList4DeveloperService.isExistBlackByActivityIdAndActivityTypeAndDeveloperId(duibaSinglelottery.getId(), ActivityBlackList4DeveloperDto.TypeDuibaSingleLottery, app.getDeveloperId()).getResult();
                if (isBlack) {
                    jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                    jsonObject.put(CommonConstants.MESSAGE_KEY, "无法参与");
                    return true;
                }
                //活动的新黑名单
            }
            if (duibaSinglelottery.isOpenSwitch(DuibaSingleLotteryDto.SWITCHES_DIRECT)) {
                SingleLotteryAppSpecifyDto sla = remoteDuibaSingleLotteryServiceNew.findSpecifyByDuibaSingleLotteryAndApp(singleLotteryVO.getId(), app.getId());
                if (null == sla) {
                    jsonObject.put(CommonConstants.SUCCESS_KEY, false);
                    jsonObject.put(CommonConstants.MESSAGE_KEY, "无参与资格");
                    return true;
                }
            }
            //单品活动专用可以抽奖,此活动已经属于该app，此处不强验单品二级归属
        }
        return false;
    }

    /**
     * 查询订单状态
     *
     * @param id
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @ResponseBody
    @RequestMapping(value = "/getOrderStatus", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject getOrderStatus(@RequestParam("id") Long id, HttpServletRequest request) throws UnsupportedEncodingException {
        JSONObject preResult = preCheck(id);
        Boolean success = preResult.getBoolean(CommonConstants.SUCCESS_KEY);
        if (success != null) {
            return preResult;
        }
        ConsumerDto consumer = preResult.getObject("consumer", ConsumerDto.class);
        AppSimpleDto app = preResult.getObject("app", AppSimpleDto.class);
        SingleLotteryOrderDto singleLotteryOrder = preResult.getObject("singleLotteryOrder", SingleLotteryOrderDto.class);
        OperatingActivityDto operatingActivity = preResult.getObject("operatingActivity", OperatingActivityDto.class);

        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        SingleLotteryVO singleLotteryVO = singleLotteryService.findSingleLotteryVOByAcitivity(operatingActivity);

        long credits = getCredits(singleLotteryVO, app.getCreditsRate());

        int result = 0;//未中奖
        String url = "";
        Long recordId = null;
        ConsumerExchangeRecordDto record = null;
        JSONObject json = new JSONObject();
        if (!Objects.equals(SingleLotteryOrderDto.OptionTypeNo, singleLotteryOrder.getOptionType())) {//中奖
            //查询兑换记录
            Map<String, Object> maprecord = new HashMap<>();
            maprecord.put("relationId", singleLotteryOrder.getId());
            maprecord.put("consumerId", consumer.getId());
            maprecord.put("type", ConsumerExchangeRecordDto.TypeSingleLottery);
            record = remoteConsumerExchangeRecordService.selectOneByMapCondition(maprecord).getResult();
            // 此时，可能是扣积分已经成功，但是兑换记录还未生成的时候，此时，返回处理中，然客户端再查一次
            if (null == record) {
                log.warn("crecord is null, singleLottery orderId={}, cid={}", singleLotteryOrder.getId(), consumer.getId());
                json.put(CommonConstants.SUCCESS_KEY, true);
                json.put("status", singleLotteryOrder.getStatus());
                json.put(CommonConstants.MESSAGE_KEY, "处理中");
                json.put("result", -2);
                return json;
            }
            result = singleLotteryOrder.getOptionType();
            url = "/activity/takePrizeNew?recordId=" + record.getId() + "&dbnewopen";
            recordId = record.getId();
        }
        boolean isFree = singleLotteryService.isFreeLimit(singleLotteryVO, operatingActivity, consumer);
        Integer lotteryButtonStatus = singleLotteryService.retButtonContent(isFree, singleLotteryVO, credits, operatingActivity, consumer, app);
        ItemDto item = null;
        if (singleLotteryOrder.getItemId() != null) {
            item = remoteDuibaItemGoodsService.find(singleLotteryOrder.getItemId()).getResult();
        }

        ItemKeyDto itemKey = null;
        ItemKeyVO itemKeyVO = null;
        if (IN_PRIZE_TYPE.contains(result)) {
            JSONObject paramsJson = handleNormalPrize(json, singleLotteryOrder, app.getId(), recordId, result, domainConfigDto.getTradeDomain(), record.getOrderId(), url);
            json = paramsJson.getJSONObject("json");
            if (!paramsJson.getBoolean("needContinue")) {
                return json;
            }
            url = paramsJson.getString("url");
            itemKey = paramsJson.getObject("itemKey", ItemKeyDto.class);
            itemKeyVO = paramsJson.getObject("itemKeyVO", ItemKeyVO.class);
        }

        //优惠券
        if (itemKey != null && ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())) {
            handleCouponPrize(json, itemKeyVO, itemKey, request, singleLotteryOrder, app.getId(), url);
        }

        result = handleLuckyPirze(json, result, app.getId(), consumer.getId(), operatingActivity.getId(), operatingActivity.getActivityId(), singleLotteryOrder.getId(), recordId);

        json.put(CommonConstants.SUCCESS_KEY, true);
        json.put("status", SingleLotteryOrderDto.StatusSuccess);
        json.put("result", result);
        json.put(CommonConstants.MESSAGE_KEY, "成功");
        json.put("url", url);
        json.put("isFree", isFree);
        json.put("lotteryButtonStatus", lotteryButtonStatus);
        json.put("credits", creditsCalculateService.convertCreditsUnit4Consumer(credits, app));
        json.put("consumeCredits", consumer.getCredits());
        json.put("descrption", getDesc(item, itemKey));

        // 香溢家定制
        this.xyFamilyCheckGoods(consumer,json,operatingActivity,record,itemKey);
        if (IN_PRIZE_TYPE.contains(result)) {
            json.put("lotteryType", itemKey.getItemDtoType());
        }
        return json;
    }

    private void xyFamilyCheckGoods(ConsumerDto consumer, JSONObject json, OperatingActivityDto operatingActivity, ConsumerExchangeRecordDto record, ItemKeyDto itemKey) {
        json.put("xyFamily",false);
        if (!xyFamilyConfig.getAppIds().contains(consumer.getAppId())) {
            return;
        }
        if (!xyFamilyConfig.getActivityType().contains(operatingActivity.getType())) {
            return;
        }
        if (itemKey == null || itemKey.getAppItem() == null) {
            return;
        }
        AppItemDto appItemDto = itemKey.getAppItem();
        // 不是虚拟商品 则直接返回
        if (appItemDto == null || !Objects.equals(appItemDto.getType(),ItemDto.TypeVirtual)) {
            return;
        }
        List<AppItemSkuDto> skuList = remoteAppItemSkuService.findSkuListByAppItemId(appItemDto.getId());
        if (CollUtil.isEmpty(skuList)) {
            return;
        }
        // 商品编码
        String merchantCoding = skuList.get(0).getMerchantCoding();
        if (!merchantCoding.contains(XY_FAMILY_JF)) {
            json.put("xyFamily",true);
        }
    }

    private int handleLuckyPirze(JSONObject json, int result, Long appId, Long consumerId, Long oaId, Long activityId, Long subOrderId, Long recordId) {
        // 福袋
        if (Objects.equals(result, SingleLotteryOrderDto.OptionTypeLucky)) {
            Map<String, Object> lottery = new HashMap<>();
            try {
                goodsService.immediatelyButtonForLuck(lottery, appId, consumerId, oaId, activityId, SingleLotteryOrderDto.PREFIX_ORDER + subOrderId, null, recordId);
            } catch (Exception e) {
                log.error("", e);
            }

            if (MapUtils.isNotEmpty(lottery)) {
                json.put("lottery", lottery);
                json.put("lotteryType", HdtoolOrdersDto.PrizeTypeLuckBag);
                json.put("isAppLucky", true); // 标识谢谢参与转福袋
            } else {
                result = SingleLotteryOrderDto.OptionTypeNo; // 置为未中奖
            }
        }
        return result;
    }

    private String getDesc(ItemDto item, ItemKeyDto itemKey) {
        String descrption = null;
        if (null != item && StringUtils.isNotBlank(item.getName())) {
            descrption = item.getName();
        } else if (itemKey != null && null != itemKey.getAppItem() && StringUtils.isNotBlank(itemKey.getAppItem().getTitle())) {
            descrption = itemKey.getAppItem().getTitle();
        }
        return descrption;
    }

    private JSONObject preCheck(Long id) {
        JSONObject json = new JSONObject();
        if (null == id) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            json.put(CommonConstants.MESSAGE_KEY, "抽奖失败，请稍后再试");
            return json;
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app == null) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            json.put(CommonConstants.MESSAGE_KEY, "无权查看");
            return json;
        }

        SingleLotteryOrderDto singleLotteryOrder = remoteSingleLotteryOrderService.find(id);
        if (null == singleLotteryOrder) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            json.put(CommonConstants.MESSAGE_KEY, "抽奖失败，请稍后再试");
            return json;
        }

        if (!singleLotteryOrder.getConsumerId().equals(consumer.getId())) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            json.put(CommonConstants.MESSAGE_KEY, "无权访问");
            return json;
        }

        if (SingleLotteryOrderDto.StatusCreate == singleLotteryOrder.getStatus()) {
            json.put(CommonConstants.SUCCESS_KEY, true);
            json.put(CommonConstants.MESSAGE_KEY, "处理中");
            json.put("result", -2);
            json.put("status", SingleLotteryOrderDto.StatusCreate);
            return json;
        }

        if (SingleLotteryOrderDto.StatusFail == singleLotteryOrder.getStatus()) {
            json.put(CommonConstants.SUCCESS_KEY, true);
            json.put(CommonConstants.MESSAGE_KEY, "抽奖失败，请稍后再试");
            json.put("status", SingleLotteryOrderDto.StatusFail);
            return json;
        }

        OperatingActivityDto operatingActivity = remoteOperatingActivityServiceNew.find(singleLotteryOrder.getOperatingActivityId());
        if (operatingActivity == null) {
            json.put(CommonConstants.SUCCESS_KEY, false);
            json.put(CommonConstants.MESSAGE_KEY, "抽奖失败，请稍后再试");
            return json;
        }
        JSONObject paramJson = new JSONObject();
        paramJson.put("result", json);
        paramJson.put("consumer", consumer);
        paramJson.put("app", app);
        paramJson.put("singleLotteryOrder", singleLotteryOrder);
        paramJson.put("operatingActivity", operatingActivity);
        return paramJson;
    }

    private long getCredits(SingleLotteryVO singleLotteryVO, Integer creditsRate) {
        long credits = 0;
        if (SingleLotteryVO.TYPE_DUIBA.equals(singleLotteryVO.getType())) {
            credits = singleLotteryService.facePrizeToCredits(singleLotteryVO.getCreditsPrice(), creditsRate);
        } else if (SingleLotteryVO.TYPE_APP.equals(singleLotteryVO.getType()) && singleLotteryVO.getCredits() != null) {
            credits = singleLotteryVO.getCredits();
        }
        return credits;
    }

    private JSONObject handleNormalPrize(JSONObject json, SingleLotteryOrderDto singleLotteryOrder, Long appId, Long recordId, int result,
                                         String tradeDomain, Long mainOrderId, String url) {
        JSONObject paramsJson = new JSONObject();
        ItemKeyDto itemKey = remoteItemKeyService.findItemKey(singleLotteryOrder.getAppItemId(), singleLotteryOrder.getItemId(), appId).getResult();
        if (ItemDto.TypeVirtual.equals(itemKey.getItemDtoType()) || ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())) {
            //如果虚拟商品需要输入用户名，则直接跳到领奖页面
            if (isNeedAccount(recordId, itemKey)) {
                String degree = null;
                try {
                    degree = URLEncoder.encode(singleLotteryOrder.getPrizeDegree(), "utf-8");
                } catch (UnsupportedEncodingException e) {
                    log.error("", e);
                }
                return getParamJson(json, recordId, result, paramsJson, itemKey, degree);
            }
            if (mainOrderId == null) {
                json.put(CommonConstants.SUCCESS_KEY, true);
                json.put(CommonConstants.MESSAGE_KEY, "处理中");
                json.put("result", -2);
                json.put("status", singleLotteryOrder.getStatus());

                paramsJson.put("json", json);
                paramsJson.put("needContinue", false);
                return paramsJson;
            }
            url = tradeDomain + "/crecord/recordDetailNew?orderId=" + mainOrderId + "&after=1&dbnewopen";
            json.put("takeSuccess", true);
        } else if (ItemDto.TypeObject.equals(itemKey.getItemDtoType())) {
            url = "/activity/takePrizeNew?recordId=" + recordId + "&dbnewopen";
        }
        ItemKeyVO itemKeyVO = new ItemKeyVO(itemKey);
        json.put("img", itemKeyVO.getSmallImage());
        json.put("appItemId", itemKey.getAppItem() != null ? itemKey.getAppItem().getId() : null);
        json.put("itemId", itemKey.getItem() != null ? itemKey.getItem().getId() : null);

        paramsJson.put("json", json);
        paramsJson.put("url", url);
        paramsJson.put("needContinue", true);
        paramsJson.put("itemKeyVO", itemKeyVO);
        paramsJson.put("itemKey", itemKey);
        return paramsJson;
    }

    @NotNull
    private JSONObject getParamJson(JSONObject json, Long recordId, int result, JSONObject paramsJson, ItemKeyDto itemKey, String degree) {
        StringBuilder sb = new StringBuilder();
        Long itemId = itemKey.getAppItem() == null ? itemKey.getItem().getId() : itemKey.getAppItem().getId();
        String itemIdStr = itemKey.getAppItem() == null ? "&itemId=":"&appItemId=";
        sb.append("/activity/virtualInputUserName?recordId=").append(recordId);
        sb.append(itemIdStr).append(itemId);
        sb.append("&degree=").append(degree).append("&dbnewopen");
        json.put("status", SingleLotteryOrderDto.StatusSuccess);
        json.put("url", sb.toString());
        json.put("result", result);
        json.put(CommonConstants.SUCCESS_KEY, true);
        json.put("takeSuccess", true);
        json.put("needAccount", true);

        paramsJson.put("json", json);
        paramsJson.put("needContinue", false);
        return paramsJson;
    }

    private boolean isNeedAccount(Long recordId, ItemKeyDto itemKey) {
        return ItemDto.TypeVirtual.equals(itemKey.getItemDtoType()) &&
                        (itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeNeedUserName)) ||
                        (itemKey.getItem()!= null && itemKey.getItem().isOpTypeItem(ItemBaseDto.OpTypeNeedUserName))
                                && recordId != null;
    }

    private void handleCouponPrize(JSONObject json, ItemKeyVO itemKeyVO, ItemKeyDto itemKey, HttpServletRequest request,
                                   SingleLotteryOrderDto singleLotteryOrder, Long appId, String url) {
        int linkTo = ItemDto.LinkToBussiness;
        //Start 淘券需求 修改埋点数据
        fillImg(json, itemKeyVO);

        if (itemKey.getItem() != null && itemKey.getItem().getId() != null) {
            linkTo = itemService.findImmediatelyButton(itemKey.getItem().getId());//按钮跳转目标
        }

        json.put("confirm", needConfirm(itemKey, request, linkTo));

        String btnText = "";
        String btnToDetail = "查看使用方法";
        String btnToBussiness = null;

        AppItemExtraDto appItemExtraDO = remoteAppItemExtraService.findByAppItemId(singleLotteryOrder.getAppItemId()).getResult();
        String tip = "";
        if (null == appItemExtraDO) {
            if(singleLotteryOrder.getItemId()!=null){
                ItemExtraDto itemExtraDO = remoteItemExtraService.findByItemId(singleLotteryOrder.getItemId()).getResult();
                tip = itemExtraDO.getUsePrompt();
                btnToBussiness = itemExtraDO.getBtnText();
            }
        } else {
            tip = appItemExtraDO.getUsePrompt();
            btnToBussiness = appItemExtraDO.getBtnText();
        }
        json.put("tip", tip);
        if (StringUtils.isBlank(btnToBussiness)) {//按钮有配置文案用文案,无配置则固定:马上使用
            btnToBussiness = "马上使用";
        }

        GoodsCouponDto goodsCouponDto = null;
        if (null != singleLotteryOrder.getCouponId()) {
            goodsCouponDto = couponService.findCoupon(itemKey, singleLotteryOrder.getCouponId());
        }

        fillCouponCode(json, itemKey, goodsCouponDto);

        String validEndDate = DateUtil.getDayStr(itemKeyVO.getValidEndDate());
        if (goodsCouponDto != null) {
            validEndDate = DateUtil.getDayStr(goodsCouponDto.getOverDue());
        }
        json.put("validate", validEndDate);

        //应用下载功能  没有开关，根据URL是否配置来判断
        String iosDownloadUrl = "";//IOS下载链接
        String androidDownloadUrl = "";//Android下载链接
        String openUrl = ""; //ios打开连接

        String downUrl;
        boolean showUse = false;//马上使用按钮 是否显示
        json.put("isEmbed", true);

        if (null != itemKey.getItem() && singleLotteryOrder.getStatus().equals(HdtoolOrdersDto.StatusSuccess)) {
            JSONObject paramsJson = handleCouponDetail(request, itemKey, singleLotteryOrder, appId, showUse, btnText, linkTo, btnToBussiness, iosDownloadUrl, openUrl, btnToDetail);
            showUse = paramsJson.getBoolean("showUse");
            btnText = paramsJson.getString("btnText");
            linkTo = paramsJson.getInteger("linkTo");
            iosDownloadUrl = paramsJson.getString("iosDownloadUrl");
            androidDownloadUrl = paramsJson.getString("androidDownloadUrl");
            openUrl = paramsJson.getString("openUrl");
        } else if (isAppCoupon(itemKey, singleLotteryOrder.getCouponId())) {
            showUse = true;
            btnText = btnToBussiness;
            downUrl = goodsCouponDto.getLink();
            iosDownloadUrl = downUrl;
            androidDownloadUrl = downUrl;
        }
        if (ItemDto.LinkToDetail == linkTo) {
            iosDownloadUrl = url;
            androidDownloadUrl = url;
            openUrl = "";
        }
        //二维码品牌券特殊处理
        if (isQRCodeCoupon(goodsCouponDto)) {
            showUse = true;
            btnText = btnToBussiness;
            downUrl = goodsCouponDto.getPassword();
            iosDownloadUrl = downUrl;
            androidDownloadUrl = downUrl;
            json.put("coupon_code", null);
            json.put("coupon_key", null);
        }
        json.put("iosDownloadUrl", iosDownloadUrl);
        json.put("androidDownloadUrl", androidDownloadUrl);
        json.put("openUrl", openUrl);
        if (Objects.equals(appId, 16101L)) {
            showUse = false;
        }
        json.put("showUse", showUse);
        json.put("useBtnText", btnText);
    }

    private void fillImg(JSONObject json, ItemKeyVO itemKeyVO) {
        String multiImage = itemKeyVO.getMultiImage();
        if (StringUtils.isNotBlank(multiImage)) {
            String[] multiImages = multiImage.split(",");
            json.put("img", multiImages[0]);
        } else {
            json.put("img", multiImage);
        }
    }

    /**
     * 链接类型优惠券 不需要弹窗
     * 安卓，并打开了下载链接有弹窗提示
     *
     * @param itemKey
     * @param request
     * @param linkTo
     * @return
     */
    private boolean needConfirm(ItemKeyDto itemKey, HttpServletRequest request, int linkTo) {
        return itemService.isCommonOrRepeatSub(itemKey) && PlantformHelper.isAndroid(request) && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeAndriodAlert) && linkTo == ItemDto.LinkToBussiness;
    }

    private void fillCouponCode(JSONObject json, ItemKeyDto itemKey, GoodsCouponDto goodsCouponDto) {
        if (((itemKey.getItem() != null && itemKey.getItem().getSubType() != ItemDto.SubTypeLink) ||
                (itemKey.isSelfAppItemMode() && itemKey.getAppItem().getSubType() != ItemDto.SubTypeLink))
                && null != goodsCouponDto) {
            json.put("coupon_code", goodsCouponDto.getCode());
            json.put("coupon_key", goodsCouponDto.getPassword());
        }
    }


    /**
     * 是否是二维码链接券
     *
     * @return
     */
    private boolean isQRCodeCoupon(GoodsCouponDto goodsCouponDto) {
        return goodsCouponDto != null && goodsCouponDto.getPassword() != null && (goodsCouponDto.getPassword().startsWith("http://") || goodsCouponDto.getPassword().startsWith("https://"));
    }


    /**
     * 是否为App自有券
     *
     * @param itemKey
     * @param couponId
     * @return
     */
    private boolean isAppCoupon(ItemKeyDto itemKey, Long couponId) {
        return null != itemKey.getAppItem() && null != itemKey.getAppItem().getSubType() && itemKey.getAppItem().getSubType() == ItemDto.SubTypeLink && null != couponId;
    }


    private JSONObject handleCouponDetail(HttpServletRequest request, ItemKeyDto itemKey, SingleLotteryOrderDto singleLotteryOrder, Long appId,
                                          boolean showUse, String btnText, Integer linkTo, String btnToBussiness,
                                          String iosDownloadUrl, String openUrl, String btnToDetail) {
        String userAgent = request.getHeader("user-agent");
        String androidDownloadUrl;
        String downUrl;
        if (Objects.equals(itemKey.getItem().getSubType(), ItemDto.SubTypeLink)) {
            //按钮固定显示马上使用
            showUse = true;
            btnText = btnToBussiness;
            //调getUrlByCoupon接口
            downUrl = this.itemService.getUrlByCoupon(singleLotteryOrder.getCouponId(), itemKey.getItem().getId(), singleLotteryOrder.getId(), singleLotteryOrder.getConsumerId().toString(), null, "1");
            linkTo = -1;//改掉，防止影响后面的
            iosDownloadUrl = downUrl;
            androidDownloadUrl = downUrl;
        } else {
            //调getItem 接口
            downUrl = this.itemService.getUrlByItem(itemKey.getItem().getId(), GoodsTypeEnum.DUIBA, singleLotteryOrder.getId(), userAgent, "1", null, null);
            String[] urls = judgeIosDownloadAndOpenUrl(itemKey, downUrl, iosDownloadUrl, openUrl);
            iosDownloadUrl = urls[0];
            openUrl = urls[1];
            androidDownloadUrl = downUrl;
            String[] showUseAndBtn = judgeShowuseAndBtntext(linkTo, btnToDetail, btnToBussiness, downUrl, itemKey, userAgent, appId, showUse, btnText);
            showUse = Boolean.valueOf(showUseAndBtn[0]);
            btnText = showUseAndBtn[1];
        }
        JSONObject paramsJson = new JSONObject();
        paramsJson.put("showUse", showUse);
        paramsJson.put("btnText", btnText);
        paramsJson.put("linkTo", linkTo);
        paramsJson.put("iosDownloadUrl", iosDownloadUrl);
        paramsJson.put("androidDownloadUrl", androidDownloadUrl);
        paramsJson.put("openUrl", openUrl);
        return paramsJson;
    }

    private String[] judgeIosDownloadAndOpenUrl(ItemKeyDto itemKey, String downUrl, String iosDownloadUrl, String openUrl) {
        //新数据逻辑
        if (itemKey.getItem().getIosOpen() != null && itemKey.getItem().getIosOpen().length() == 2) {
            if ("0".equals(itemKey.getItem().getIosOpen().substring(0, 1))) {
                iosDownloadUrl = downUrl;
            } else {
                openUrl = downUrl;
            }
            //兼容旧数据
        } else {
            if (StringUtils.isNotBlank(itemKey.getItem().getIosDownload())) {
                iosDownloadUrl = downUrl;
            } else if (StringUtils.isNotBlank(itemKey.getItem().getIosOpen())) {
                openUrl = downUrl;
            }
        }
        return new String[]{iosDownloadUrl, openUrl};
    }

    private String[] judgeShowuseAndBtntext(int linkTo, String btnToDetail, String btnToBussiness,
                                            String downUrl, ItemKeyDto itemKey, String userAgent,
                                            Long appId, boolean showUse, String btnText) {
        if (linkTo == ItemDto.LinkToDetail) {
            showUse = true;
            btnText = btnToDetail;
        } else if (linkTo == ItemDto.LinkToBussiness) {//跳商家页
            btnText = btnToBussiness;
            if (StringUtils.isNotBlank(downUrl) && (PlantformHelper.isAndroid(userAgent) || PlantformHelper.isIOS(userAgent))) {//下载链接不为空
                //是安卓或者ios系统
                showUse = true;
                //特定应用
                String showOpenStr = remoteDuibaItemGoodsService.getJsonValue(itemKey.getItem().getId(), ItemDto.Key_Ganji).getResult();
                boolean show = StringUtils.isNotBlank(showOpenStr) ? Boolean.valueOf(showOpenStr) : false;
                if (AppIdConstant.showUseButton.contains(appId) && !show) {
                    showUse = false;
                }
            }
        }
        return new String[]{String.valueOf(showUse), btnText};
    }

    /**
     * 查询中奖记录
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/lotteryRecord", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject lotteryRecord(HttpServletRequest request) {
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isBlank(request.getParameter("operationAcitvityId"))) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "无权查看");
        }
        //查询中奖订单5条
        OperatingActivityDto operatingActivity = remoteOperatingActivityServiceNew.find(
                Long.parseLong(request.getParameter("operationAcitvityId")));
        if (!operatingActivity.getAppId().equals(RequestLocal.getConsumerDO().getAppId())) {
            jsonObject.put(CommonConstants.SUCCESS_KEY, false);
            jsonObject.put(CommonConstants.MESSAGE_KEY, "无权查看");
        }
        String lotteryRecord = singleLotteryService.loadLotteryRecord(operatingActivity);
        jsonObject.put("lotteryRecord", lotteryRecord);
        return jsonObject;
    }
}
