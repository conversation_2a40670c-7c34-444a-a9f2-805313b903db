package com.duiba.activity.accessweb.controller.freegroup;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.utils.ReplaceCdnUtil;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibabiz.component.filters.bloom.url.UrlSerialAccessLocal;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.constant.AilLaiYiConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.controller.BaseNewCtrl;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.WeixinShareContentService;
import com.duiba.activity.accessweb.service.common.ActivityCommonService;
import com.duiba.activity.accessweb.service.freegroup.FreeGroupRecordService;
import com.duiba.activity.accessweb.service.market.MarketTempService;
import com.duiba.activity.accessweb.service.share.ShareConfigService;
import com.duiba.activity.accessweb.tool.AppCustomInfoTool;
import com.duiba.activity.accessweb.tool.ConsumerCheckUtil;
import com.duiba.activity.accessweb.tool.result.ResultUtil;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupBaseConfigVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupGuideGroupVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupItemVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupJoinRecordVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupOrderStatusVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupRecordItemVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupTakePrizeOwnerUserVO;
import com.duiba.activity.accessweb.vo.freegroup.FreeGroupWindowVO;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Quartet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: lufeng
 * @Description: 0元拼团工具 入口
 * @Date: Created in 2019/3/15
 */
@RestController
@RequestMapping("aaw/freeGroup")
public class FreeGroupToolController extends BaseNewCtrl {

    private static final Logger LOGGER = LoggerFactory.getLogger(FreeGroupToolController.class);

    @Autowired
    private FreeGroupRecordService freeGroupRecordService;
    @Autowired
    private MarketTempService marketTempService;
    @Autowired
    private WeixinShareContentService weixinShareContentService;
    @Autowired
    private ShareConfigService shareConfigService;
    @Autowired
    private ActivityCacheService activityCacheService;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    private static final String DCUSTOM_IS_NEW = "newUser";

    private static final String DEFAULT_ERROR_VIEW = "/error";

    @Autowired
    private DomainService domainService;
    @Autowired
    private ActivityCommonService activityCommonService;

    /**
     * 获取拼团活动基本信息
     * @param activityId 活动入库id
     * @return
     */
    @GetMapping("/index")
    public ModelAndView index(HttpServletRequest request, Long activityId, Integer fromType, boolean share) { //NOSONAR
        if (activityId == null) {
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(activityId);
        if (null == operatingActivityDto) {
            LOGGER.info("入库活动不存在,activityId={}", activityId);
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        if (!Objects.equals(operatingActivityDto.getType(), ActivityUniformityTypeEnum.FreeGroupTool.getCode())) {
            LOGGER.info("入库活动类型非拼团活动,activityId={}", activityId);
            return new ModelAndView(DEFAULT_ERROR_VIEW);
        }
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/freeGroup/index");
        String shareCodeField = "";
        if(!share){
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            if (app == null) {
                LOGGER.info("0元拼团app信息为空,activityId={}", activityId);
                return new ModelAndView(DEFAULT_ERROR_VIEW);
            }
            if (!Objects.equals(operatingActivityDto.getAppId(), RequestLocal.getAppId())) {
                LOGGER.info("入库活动appId不一致,activityId={}", activityId);
                return new ModelAndView(DEFAULT_ERROR_VIEW);
            }
            //查找分享代码块
            shareCodeField = shareConfigService.getAppShareConfCode(app,request);
        }
        modelAndView.addObject("appId", operatingActivityDto.getAppId());
        modelAndView.addObject("activityId", activityId);

        modelAndView.addObject("appShareConfCode", shareCodeField);
        //营销落地页活动代码
        modelAndView.addObject("activityCode", marketTempService.getMarketTempActivityCode(ActivityUniformityTypeEnum.FreeGroupTool));
        //设置分享信息
        modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));

        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if(consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())) {
            modelAndView.addObject("login", false);
            modelAndView.addObject("loginFlag", false);
        } else if (AilLaiYiConstants.getALYConsumerIds().contains(consumerDto.getId())){
            //兼容线上有问题的数据，放行有问题的consumerId
            modelAndView.addObject("login", true);
            modelAndView.addObject("loginFlag", true);
        } else {
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            if (app == null || !app.getId().equals(operatingActivityDto.getAppId())) {
                modelAndView.addObject("login", false);
                modelAndView.addObject("loginFlag", false);
            } else {
                modelAndView.addObject("login", true);
                modelAndView.addObject("loginFlag", true);
            }
        }

        Result<FreeGroupBaseConfigVO> result = freeGroupRecordService.getGroupConfig(operatingActivityDto.getAppId(), activityId, RequestLocal.getConsumerDO(), fromType);
        if (result.getSuccess()) {
            modelAndView.addObject("configId", result.getData().getId());
            modelAndView.addObject("title", result.getData().getTitle());
            modelAndView.addObject("rule", result.getData().getRule());
            modelAndView.addObject("logoImage", result.getData().getLogoImage());
            modelAndView.addObject("bannerImage", result.getData().getBannerImage());
            modelAndView.addObject("bulletScreenStatus", result.getData().getBulletScreenStatus());
            modelAndView.addObject("sharePic", result.getData().getSharePic());
            modelAndView.addObject("shareSubTitle", result.getData().getShareSubTitle());
            modelAndView.addObject("shareTitle", result.getData().getShareTitle());
            modelAndView.addObject("interfaceConfig", result.getData().getInterfaceConfig());
            modelAndView.addObject("openStatus", result.getData().getOpenStatus());
            modelAndView.addObject("participateConditionType", result.getData().getParticipateConditionType());
            modelAndView.addObject("logOnType", result.getData().getLogOnType());
            modelAndView.addObject("grouping", result.getData().isGrouping());
            modelAndView.addObject("isEnd", result.getData().isEnd());
            modelAndView.addObject("isClose", result.getData().isClose());
            modelAndView.addObject("callUpRegistH5Code", result.getData().getCallUpRegistH5Code());
            modelAndView.addObject("openLogin", result.getData().isOpenLogin());
            modelAndView.addObject("loginProgram", result.getData().getLoginProgram());
        }
        // 扩展信息
        modelAndView.addObject("uid", RequestLocal.getPartnerUserId());
        modelAndView.addObject("dcustom", activityCommonService.getConsumerExtraField(null));
        return modelAndView;
    }

    /**
     * 获取用户弹窗对象
     * @param activityId
     * @return
     */
    @GetMapping("/window")
    public Result<FreeGroupWindowVO> window(Long activityId) {
        if (activityId == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if(consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())) {
            return ResultUtil.fail(ErrorCode.E0100000);
        }
        Long consumerId = consumerDto.getId();
        Result<FreeGroupWindowVO> freeGroupWindow = freeGroupRecordService.getFreeGroupWindow(activityId, consumerId);
        if (Objects.nonNull(freeGroupWindow)){
            FreeGroupWindowVO data = freeGroupWindow.getData();
            if (Objects.nonNull(data)){
                DomainConfigDto systemDomain = domainService.getSystemDomain(RequestLocal.getAppId());
                data.setItemImage(ReplaceCdnUtil.replaceCdn(systemDomain,data.getItemImage()));
            }

        }
        return freeGroupWindow;
    }

    /**
     * 获取首页拼团商品信息
     * @param activityId
     * @return
     */
    @GetMapping("/listGroupItem")
    public Result<List<FreeGroupItemVO>> listGroupItem(Long activityId) {
        if (activityId == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        Result<List<FreeGroupItemVO>> result = freeGroupRecordService.listGroupItem(activityId, RequestLocal.getCid());
        List<FreeGroupItemVO> data = result.getData();
        DomainConfigDto systemDomain = domainService.getSystemDomain(RequestLocal.getAppId());
        for (FreeGroupItemVO datum : data) {
            datum.setItemImage(ReplaceCdnUtil.replaceCdn(systemDomain,datum.getItemImage()));

        }

        return result;
    }


    /**
     * 0元开团
     * @param activityId
     * @param groupItemId
     * @return
     */
    @PostMapping("/doOpenGroup")
    public Result<Long> doOpenGroup(Long activityId, Long groupItemId, Integer fromType) {
        if (activityId == null || groupItemId == null || fromType == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if(consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())) {
            return ResultUtil.fail(ErrorCode.E0100000);
        }
        // 接口顺序访问检查
        if (!UrlSerialAccessLocal.hasRecord()) {
            LOGGER.info("urlSerialAccessService,拦截");
            return ResultUtil.failWithDesc("请重新进入活动");
        }
        return freeGroupRecordService.doOpenGroup(activityId, groupItemId, consumerDto, fromType);
    }

    /**
     * 拼团
     * @param activityId
     * @param groupRecordId
     * @return
     */
    @PostMapping("/doJoinGroup")
    public Result<Boolean> doJoinGroup(HttpServletRequest request, Long activityId, Long groupItemId, Long groupRecordId, Integer fromType) {
        if (activityId == null || groupRecordId == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if(consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())) {
            return ResultUtil.fail(ErrorCode.E0100000);
        }

        // 接口顺序访问检查
        if (!UrlSerialAccessLocal.hasRecord()) {
            LOGGER.info("urlSerialAccessService,拦截");
            return ResultUtil.failWithDesc("请重新进入活动");
        }
        Map<String, String> dcustomMap = AppCustomInfoTool.parseDcustom(request);
        String isNew = dcustomMap.get(DCUSTOM_IS_NEW);
        if (!StringUtils.equals(isNew, "1")) {
            return ResultUtil.fail(ErrorCode.E0900003);
        }
        return freeGroupRecordService.doJoinGroup(activityId, groupItemId, consumerDto, groupRecordId, fromType);
    }

    /**
     * 获取用户参团记录
     * @param activityId
     * @return
     */
    @GetMapping("/listGroupJoinRecord")
    public Result<List<FreeGroupJoinRecordVO>> listGroupJoinRecord(Long activityId) {
        if (activityId == null) {
             return ResultUtil.fail(ErrorCode.E0100005);
        }
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if(consumerDto == null || ConsumerCheckUtil.isNotLoginUser(consumerDto.getPartnerUserId())) {
            return ResultUtil.successWithData(Collections.emptyList());
        }
        Long consumerId = consumerDto.getId();
        return freeGroupRecordService.listGroupJoinRecord(activityId, consumerId);
    }

    /**
     * 获取商品详情（含用户参团状态）
     * @param activityId
     * @param groupItemId
     * @return
     */
    @GetMapping("/getGroupItem")
    public Result<FreeGroupRecordItemVO> getGroupItem(Long activityId, Long groupItemId, Long groupRecordId) {
        if (activityId == null || groupItemId == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        Long consumerId = RequestLocal.getCid();
        return freeGroupRecordService.getGroupRecordItem(activityId, groupItemId, groupRecordId, consumerId);
    }

    /**
     * 获取排行榜中奖用户信息
     * @param activityId
     * @param groupItemId
     * @return
     */
    @GetMapping("listGroupTakePrizeUser")
    public Result<FreeGroupTakePrizeOwnerUserVO> getGroupTakePrizeOwnerUser(Long activityId, Long groupItemId, Long groupRecordId) {
        if (activityId == null || groupItemId == null) {
            return ResultUtil.fail(ErrorCode.E0100005);
        }
        Long consumerId = RequestLocal.getCid();
        return freeGroupRecordService.getGroupTakePrizeOwnerUser(activityId, groupItemId, groupRecordId, consumerId);
    }

    /**
     * 获取排名引导数据
     */
    @GetMapping("/getRankGuideInfo")
    public Result<List<FreeGroupGuideGroupVO>> getRankGuideInfo(@RequestParam Long groupId) {
        try {
            return ResultBuilder.success(freeGroupRecordService.getRankGuideInfo(groupId));
        }catch (BizException e){
            LOGGER.info("0元拼团工具-获取团{}的排名引导数据异常", groupId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    /**
     * 领奖
     */
    @GetMapping("/takePrize")
    public Result openPrize(@RequestParam Long groupId, HttpServletRequest request){
        //获取 用户信息及app信息
        Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
        if (null != quartet.getValue3()) {
            return ResultBuilder.fail(quartet.getValue3().getCode(), quartet.getValue3().getDescription());
        }
        if (null != quartet.getValue2()) {
            return ResultBuilder.fail(quartet.getValue2());
        }
        if (ConsumerCheckUtil.isNotLoginUser(quartet.getValue0().getPartnerUserId())) {
            return ResultBuilder.fail(ResultCode.C00000001);
        }

        String key = RedisKeyFactory.K195.toString() + groupId + "_" + quartet.getValue0().getId();
        try (RedisLock lock = redisAtomicClient.getLock(key, 5)){
            if (Objects.isNull(lock)) {
                return ResultBuilder.fail(ResultCode.C100804.getDescription());
            }
            return ResultBuilder
                    .success(freeGroupRecordService.takePrize(quartet.getValue1(), quartet.getValue0(), groupId, request));
        }catch (BizException e){
            return ResultBuilder.fail(e.getCode(), e.getMessage());
        }catch (Exception e){
            LOGGER.warn("0元拼团工具-领奖异常", e);
            return ResultBuilder.fail(ResultCode.C100804.getDescription());
        }
    }

    /**
     * 根据插件子订单id获取订单详情页url
     */
    @GetMapping("/getOrderUrl")
    public Result<String> getOrderUrl(@RequestParam String orderId) {
        try {
            return ResultBuilder.success(freeGroupRecordService.getOrderUrl(orderId));
        }catch (BizException e){
            LOGGER.info("0元拼团工具-根据子订单id{}获取订单详情页url异常", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    /**
     * 根据订单id查询订单状态并跳转对应页面
     */
    @GetMapping("/orderStatus")
    public Result<FreeGroupOrderStatusVO> orderStatus(@RequestParam String orderId, @RequestParam Long userRecordId) {
        try {
            return ResultBuilder.success(freeGroupRecordService.orderStatus(orderId, userRecordId));
        }catch (BizException e){
            LOGGER.info("0元拼团工具-根据子订单id{}查询订单状态并跳转对应页面异常", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @GetMapping("/share")
    public Result share(@RequestParam String activityId, Integer fromType) {
        if (!StringUtils.isNumeric(activityId)) {
            return ResultBuilder.fail("activityId必须为数字");
        }
        freeGroupRecordService.share(Long.valueOf(activityId), fromType, RequestLocal.getConsumerDO());
        return ResultBuilder.success();
    }
}
