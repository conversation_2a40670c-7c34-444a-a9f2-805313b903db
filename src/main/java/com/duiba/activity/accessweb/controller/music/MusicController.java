package com.duiba.activity.accessweb.controller.music;


import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import com.duiba.activity.accessweb.exception.AccessActivityRuntimeException;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.music.MusicReportService;
import com.duiba.activity.accessweb.service.music.MusicService;
import com.duiba.activity.accessweb.vo.music.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * Created by fangdong on 2019/07/08
 */
@RestController
@RequestMapping("/aaw/music")
public class MusicController {
    private static final Logger log = LoggerFactory.getLogger(MusicController.class);

    @Resource
    private MusicService musicService;
    @Resource
    private MusicReportService musicReportService;

    @GetMapping("/item")
    public Result<MusicItemVo> getItem(MusicItemQueryVo query) {
        if (query == null || query.getSongListId() == null || query.getItemId() == null) {
            return ResultBuilder.fail(ResultCode.C100002.getCode(), ResultCode.C100002.getDescription());
        }

        try {
            MusicItemVo item = musicService.getItem(query, RequestLocal.getConsumerDO());
            return ResultBuilder.success(item);
        } catch (AccessActivityRuntimeException e) {
            log.warn("音乐歌曲获取异常, query={}", query, e);
            return ResultBuilder.fail(ResultCode.C100000.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("音乐歌曲获取异常, query={}", query, e);
            return ResultBuilder.fail(ResultCode.C999999.getCode(), ResultCode.C999999.getDescription());
        }
    }

    @GetMapping("/category")
    public Result<MusicCategoryVo> getCategory(MusicCategoryQueryVo query) {
        if (query == null || (query.getCategoryID() == null && query.getCategoryCode() == null)) {
            return ResultBuilder.fail(ResultCode.C100002.getCode(), ResultCode.C100002.getDescription());
        }

        try {
            MusicCategoryVo item =  musicService.getCategory(query);
            return ResultBuilder.success(item);
        } catch (AccessActivityRuntimeException e) {
            log.info("音乐分类获取异常, query={}", query, e);
            return ResultBuilder.fail(ResultCode.C100000.getCode(), e.getMessage());
        } catch (Exception e) {
            log.info("音乐分类获取异常, query={}", query, e);
            return ResultBuilder.fail(ResultCode.C999999.getCode(), ResultCode.C999999.getDescription());
        }
    }

    @PostMapping("/report")
    public Result<Void> report(MusicDataReportSaveVo data, HttpServletRequest request) {
        if (data == null) {
            return ResultBuilder.fail(ResultCode.C100002.getCode(), ResultCode.C100002.getDescription());
        }

        try {
            RequestParams requestParams = RequestParams.parse(request);
            musicReportService.save(data, RequestLocal.getConsumerDO(), requestParams);
            return ResultBuilder.success(null);
        } catch (AccessActivityRuntimeException e) {
            log.warn("音乐数据上报异常, data={}", data, e);
            return ResultBuilder.fail(ResultCode.C100000.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("音乐数据上报异常, data={}", data, e);
            return ResultBuilder.fail(ResultCode.C999999.getCode(), ResultCode.C999999.getDescription());
        }
    }

}
