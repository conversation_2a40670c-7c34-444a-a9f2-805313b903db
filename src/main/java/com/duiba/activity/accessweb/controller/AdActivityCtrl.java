/**
 * Project Name:activity-access-web
 * File Name:AdActivityCtrl.java
 * Package Name:com.duiba.activity.accessweb.controller
 * Date:2016年12月8日下午4:39:00
 * Copyright (c) 2016, duiba.com.cn All Rights Reserved.
 *
*/

package com.duiba.activity.accessweb.controller;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.advertising.AdActivityDto;
import cn.com.duiba.activity.center.api.remoteservice.advertising.RemoteAdActivityService;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.UrlUtils;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.service.AdvertService;
import cn.com.duibabiz.component.domain.DomainService;
import com.duiba.activity.accessweb.vo.AdActivityVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * ClassName:AdActivityCtrl <br/>
 * Function: 商业活动所需接口
 * Date:     2016年12月8日 下午4:39:00 <br/>
 * <AUTHOR>
 */
@Controller
@RequestMapping("/adActivity")
public class AdActivityCtrl {
    @Resource
    private AdvertService advertService;
    @Autowired
    private RemoteAdActivityService remoteAdActivityService;
    @Autowired
    private DomainService domainService;

    /**
     * 商业活动中心列表
     *
     * <AUTHOR>
     */
    @ResponseBody
    @RequestMapping("/list")
    public String activityCenterList(HttpServletRequest request){
        JSONObject json = new JSONObject();
        String callback = request.getParameter("callback");
        //埋点
        String adslotId = request.getParameter("adslotId");
        
        if (StringUtils.isBlank(adslotId)) {
            json.put("success", false);
            json.put("message", "广告位id(adslotId) 异常");
            return callback+"("+json.toJSONString()+")";
        }

        List<AdActivityVO> vos = this.advertService.findRandomList(NumberUtils.toLong(adslotId));
        if(CollectionUtils.isNotEmpty(vos)){
            int location = 0;
            for(AdActivityVO vo:vos){
                location++;
                Map<String,String> params = Maps.newHashMap();
                params.put("pre_content", String.valueOf(location));
                vo.setLink(UrlUtils.appendParams(vo.getLink(), params));
            }
        }
        json.put("data", vos);
        json.put("success", true);
        
        return callback+"("+json.toJSONString()+")";
    }
    

    @ResponseBody
    @RequestMapping("/open")
    public void openActivity(Long activityId,int type,HttpServletRequest request,HttpServletResponse response) throws IOException{
        Long appId = RequestLocal.getAppId();
        if(null != appId && OperatingActivityDto.isHdTool(type)){
           String preContent = request.getParameter(CommonConstants.PRECONTENT);
           DubboResult<AdActivityDto> result = this.remoteAdActivityService.addOnAppForDuiba(activityId, type, appId);
           if(result.isSuccess() && null != result.getResult()){
               String slotId = request.getParameter(CommonConstants.ADSLOTID);
               String url = result.getResult().getActivityUrl();
               Map<String,String> params = Maps.newHashMap();
               params.put(CommonConstants.PRECONTENT, preContent);
               params.put(CommonConstants.PRETYPE, "30");
               params.put(CommonConstants.ADSLOTID, slotId);
               url = UrlUtils.appendParams(url,params);
               response.sendRedirect(url);
           }
        }
    }
    
    @RequestMapping("/openActivityCenter")
    public void openActivityCenter(HttpServletRequest request,HttpServletResponse response) throws IOException {
        Map<String,String> params = Maps.newHashMap();
        //活动中心url(子页面地址)推啊活动中心，id=18，兑吧活动中心，id=15
        String activityCenterUrl = domainService.getSystemDomain(RequestLocal.getAppId()).getGoodsDomain() + "/button/activityCategory";
        String slotId = request.getParameter(CommonConstants.ADSLOTID);
        if(StringUtils.isNotBlank(slotId)){
            params.put("id", "18");
            params.put(CommonConstants.ADSLOTID, slotId);
        }else{
            params.put("id", "15");
        }
        params.put(CommonConstants.PRECONTENT, request.getParameter(CommonConstants.PRECONTENT));
        params.put(CommonConstants.PRETYPE, request.getParameter(CommonConstants.PRETYPE));
        params.put(CommonConstants.PREACTIVITYID, request.getParameter(CommonConstants.PREACTIVITYID));
        params.put(CommonConstants.PREACTIVITYTYPE, request.getParameter(CommonConstants.PREACTIVITYTYPE));
        activityCenterUrl = UrlUtils.appendParams(activityCenterUrl, params);
        response.sendRedirect(activityCenterUrl);
    }
}
