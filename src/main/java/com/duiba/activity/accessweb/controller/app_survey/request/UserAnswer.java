package com.duiba.activity.accessweb.controller.app_survey.request;

import javax.validation.constraints.NotNull;

/**
 * 用户答案
 *
 * <AUTHOR>
 */
public class UserAnswer {

    /**
     * 题目id
     */
    @NotNull(message = "题目id不能为空")
    private Long questionId;

    /**
     * 选项id
     */
    private Long optionId;


    /**
     * 用户提交答案
     */
    private String answerContent;

    /**
     * 用户填写答案
     */
    private String userFillContent;

    /**
     *跳题
     */
    private Integer optionSkipQuestionNum;


    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getOptionId() {
        return optionId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    public String getUserFillContent() {
        return userFillContent;
    }

    public void setUserFillContent(String userFillContent) {
        this.userFillContent = userFillContent;
    }

    public Integer getOptionSkipQuestionNum() {
        return optionSkipQuestionNum;
    }

    public void setOptionSkipQuestionNum(Integer optionSkipQuestionNum) {
        this.optionSkipQuestionNum = optionSkipQuestionNum;
    }
}
