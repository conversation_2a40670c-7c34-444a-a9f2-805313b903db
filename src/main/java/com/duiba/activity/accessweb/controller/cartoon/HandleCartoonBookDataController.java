package com.duiba.activity.accessweb.controller.cartoon;

import cn.com.duiba.activity.center.api.dto.BizResultDto;
import cn.com.duiba.activity.center.api.dto.manhua.CartoonBookVoteDto;
import cn.com.duiba.activity.center.api.remoteservice.manhua.RemoteCartoonBookVoteService;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.tool.Environment;
import com.duiba.activity.accessweb.vo.manhua.CartoonBookVoteVO;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 漫漫漫画书籍数据处理controller
 * <p>
 * Created by sunchangji on 2018/7/11.
 */
@RestController
@RequestMapping("cartoon/datas")
public class HandleCartoonBookDataController {
    @Autowired
    private RemoteCartoonBookVoteService remoteCartoonBookVoteService;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;

    /**
     * 导入书籍数据
     *
     * @param books
     * @return
     */
    @PostMapping("export")
    public Result<String> exportBookData(@RequestBody List<CartoonBookVoteVO> books) {
        if (Environment.isOnline()) {
            return ResultBuilder.fail("无权操作");
        }
        List<CartoonBookVoteDto> bookVoteDtos = BeanUtils.copyList(books, CartoonBookVoteDto.class);
        BizResultDto resultDto = remoteCartoonBookVoteService.batchInsertBook(bookVoteDtos);
        removeRedisCache(bookVoteDtos.get(0).getAppId());
        return ResultBuilder.success(resultDto.getMsg());
    }

    /**
     * 查询正在参数投票的书籍,appId为空是所有的书籍,appId不为空则只查询app下的书籍记录
     *
     * @param appId
     * @return
     */
    @GetMapping("list")
    public Result<List<CartoonBookVoteVO>> findBooks(Long appId) {
        if (Environment.isOnline()) {
            return ResultBuilder.fail("无权操作");
        }
        List<CartoonBookVoteDto> cartoonBookVoteDtos = remoteCartoonBookVoteService.findAllByAppId(appId);
        return ResultBuilder.success(BeanUtils.copyList(cartoonBookVoteDtos, CartoonBookVoteVO.class));
    }

    @PostMapping("update")
    public Result<String> updateBook(@RequestBody CartoonBookVoteVO book) {
        if (Environment.isOnline()) {
            return ResultBuilder.fail("无权操作");
        }
        BizResultDto resultDto = remoteCartoonBookVoteService.update(BeanUtils.copy(book, CartoonBookVoteDto.class));
        removeRedisCache(book.getAppId());
        return ResultBuilder.success(resultDto.getMsg());
    }

    @GetMapping("delete")
    public Result<String> deleteByIds(@RequestParam String ids,@RequestParam Long appId) {
        if (Environment.isOnline()) {
            return ResultBuilder.fail("无权操作");
        }
        if (StringUtils.isEmpty(ids)) {
            return ResultBuilder.fail("ids参数不能为空");
        }
        List<String> idsStrs = Splitter.on(",").splitToList(ids);
        List<Long> idsLong = new ArrayList<>();
        for (String idsStr : idsStrs) {
            idsLong.add(Long.valueOf(idsStr));
        }
        boolean isSuccess = remoteCartoonBookVoteService.deleteByIds(idsLong);
        removeRedisCache(appId);
        return isSuccess ? ResultBuilder.success() : ResultBuilder.fail("失败");
    }

    /**
     * 失效redis中书籍列表数据
     *
     * @param appId 兑吧应用id
     */
    private void removeRedisCache(Long appId) {
        String redisKey = RedisKeyFactory.K404.toString() + appId;

        advancedCacheClient.remove(redisKey);
    }
}
