package com.duiba.activity.accessweb.controller.ningbo;

import cn.com.duiba.activity.custom.center.api.dto.identity.IdentityCustomerRelationDto;
import cn.com.duiba.activity.custom.center.api.enums.identity.IdentityRelTypeEnum;
import cn.com.duiba.activity.custom.center.api.remoteservice.identity.RemoteIdentityCustomerRelationSerivce;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.annotations.NoLoginCanAccess;
import com.duiba.activity.accessweb.constant.NbbankConstants;
import com.duiba.activity.accessweb.enums.ErrorCode;
import com.duiba.activity.accessweb.exception.BusinessException;
import com.duiba.activity.accessweb.service.ningbo.NingBoService;
import com.duiba.activity.accessweb.tool.HttpUtil;
import com.duiba.activity.accessweb.vo.nbbank.AddressBook;
import com.duiba.activity.accessweb.vo.ningbobank.params.DeductCreditsParams;
import com.duiba.activity.accessweb.vo.ningbobank.params.OrderCreateMCParams;
import com.duiba.activity.accessweb.vo.ningbobank.params.QueryCreditsParams;
import com.duiba.activity.accessweb.vo.ningbobank.vo.AppNoticeRequestVo;
import com.duiba.activity.accessweb.vo.ningbobank.vo.ConfirmResult;
import com.duiba.activity.accessweb.vo.ningbobank.vo.OpenVo;
import com.nbcb.sdk.OpenSDK;
import com.nbcb.sdk.aes.exception.SDKException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;


/**
 * 〈一句话功能简述〉<br>
 * 〈宁波银行〉
 *
 * <AUTHOR>
 * @create 2020/8/20
 * @since 1.0.0
 */
@RestController
@RequestMapping(value = "/aaw/ningBo")
public class NingBoCtrl {
    private static final Logger logger = LoggerFactory.getLogger(NingBoCtrl.class);

    private static final String PROJECT_NAME = "宁波银行虚拟社区";

    //商户私钥(测试环境)
//    private static String priKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgEEQMz7zloaogtcFzEnYbxeTXoJ5HXEcCrA2qVRy9FPmgCgYIKoEcz1UBgi2hRANCAATVjAoxBZMw3apF0A5HbegpE8q1+CazPP88iNCYP1uG1SSOfR2pvTA37sIZLKw/EV2IJf8R4FOyIbqUDQdubd37";

    //商户私钥(生产环境)
    private static String priKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQga2OcSxRr3FhV70jfznEvd0ZJ5xHxKCf16gYT97f9sMegCgYIKoEcz1UBgi2hRANCAAQfUpuiZYxyY8s3LeY6/cu6SZ1Lt+aPnirgMM+uMeUtpPFykoVoTjiy4lUKtW0VUTweQujz0l2s/bwPS6Ne2xlw";

    @Autowired
    private NingBoService ningBoService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private RemoteIdentityCustomerRelationSerivce identityCustomerRelationSerivce;


    @Autowired
    private NbbankConstants constants;


    private static final String OK = "ok";

    private static final String FAIL = "fail";

    static {
        try {
            OpenSDK.init(new ClassPathResource("ningbobank/config.properties").getInputStream(), priKey);
        } catch (IOException e) {
            logger.error("宁波银行虚拟社区OpenSDK初始化失败",e);
        }
    }

    @PostMapping("/init")
    @NoLoginCanAccess
    public String init() {
        try {
            OpenSDK.init(new ClassPathResource("ningbobank/config.properties").getInputStream(), priKey);
        } catch (Exception e) {
            logger.error("宁波银行虚拟社区OpenSDK初始化失败",e);
            return "fail";
        }
        return "success";
    }

    @PostMapping("/integralExchange")
    @ResponseBody
    public OpenVo integralExchange(@RequestParam String tradeNo, @RequestParam String totalFee, @RequestParam String ipAdr,
                                   @RequestParam String openId, String appId, String key) {
        //未开始限制，或者白名单用户
        if (!constants.isOpenLimit() || constants.getExchangeWhiteUser().contains(openId)) {
            logger.info("宁波银行兑换波豆，uid转换前:{}",openId);
            openId = getOriginalOpenId(openId);
            logger.info("宁波银行兑换波豆，uid转换后:{}",openId);
        }
        return ningBoService.exchange(tradeNo, totalFee, ipAdr, openId, appId, key);
    }



    /**
     * 双端投放时，根据星速台传来的uid获取app端的uid信息
     *
     * @param openId
     * @return
     */
    private String getOriginalOpenId(String openId) {

        //根据openId查询免登进来的uid
        IdentityCustomerRelationDto identityCustomerRelationDto = new IdentityCustomerRelationDto();
        identityCustomerRelationDto.setPartnerid(openId);
        identityCustomerRelationDto.setAppId(constants.getAppId());
        identityCustomerRelationDto.setRelContent(openId);
        identityCustomerRelationDto.setRelType(IdentityRelTypeEnum.BETAID.getCode());
        logger.info("宁波银行兑换波豆,param:{}",JSONObject.toJSONString(identityCustomerRelationDto));
        IdentityCustomerRelationDto relationDto = identityCustomerRelationSerivce.queryByRelContentAndPid(identityCustomerRelationDto);
        logger.info("宁波银行兑换波豆,result:{}",JSONObject.toJSONString(relationDto));
        return Optional.ofNullable(relationDto).map(IdentityCustomerRelationDto::getExtra).orElse(openId);
    }

    @PostMapping("/exchangeConfigQuery")
    @ResponseBody
    public OpenVo exchangeConfigQuery() {
        OpenVo vo = ningBoService.exchangeConfigQuery();
        return vo;
    }

    /**
     * 查询金币接口
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/custGoleBalQuery")
    @NoLoginCanAccess
    public String custGoleBalQuery(HttpServletRequest request) throws SDKException {
        try {
            BufferedReader br = request.getReader();
            String str = "";
            String listString = "";
            while ((str = br.readLine()) != null) {
                listString += str;
            }
            logger.info("宁波虚拟金币查询入参, 加密报文:{}", listString);
            String requestStr = OpenSDK.decryptMessage(listString);
            logger.info("宁波虚拟金币查询入参, 解密报文:{}", requestStr);
            QueryCreditsParams queryCreditsParams = JSONObject.parseObject(requestStr, QueryCreditsParams.class);
            //新速台查金币
            String credits = ningBoService.queryGole(queryCreditsParams);
            if(StringUtils.isBlank(credits)){
                return OpenSDK.encryptMessage(getResult2("500002", "未能查询到金币，可能用户不存在或服务异常", credits));
            }
            return OpenSDK.encryptMessage(getResult2("000000", "", credits));
        } catch (Exception e) {
            logger.error("宁波虚拟金币查询异常", e);
            return OpenSDK.encryptMessage(getResult2("500001", "金币查询服务异常", ""));
        }

    }

    /**
     * 虚拟金币加减接口
     * @param request
     * @return
     */
    @RequestMapping(value = "/deductCredits")
    @NoLoginCanAccess
    public String deductCredits(HttpServletRequest request) throws SDKException {
        try {
            BufferedReader br = request.getReader();
            String str = "";
            String listString = "";
            while ((str = br.readLine()) != null) {
                listString += str;
            }
            logger.info("宁波虚拟金币加减入参, 加密报文:{}", listString);
            String requestStr = OpenSDK.decryptMessage(listString);
            logger.info("宁波虚拟金币加减入参, 解密报文:{}", requestStr);
            DeductCreditsParams deductCreditsParams = JSONObject.parseObject(requestStr,DeductCreditsParams.class);
            String appKey = JSON.parseObject(requestStr).getString("appkey");
            String sign = JSON.parseObject(requestStr).getString("sign");
            //参数检查
            paramCheck(appKey, sign, deductCreditsParams);

            //加减金币
            ConfirmResult confirmResult = ningBoService.deductCredits(appKey, sign, deductCreditsParams);

            return OpenSDK.encryptMessage(getResult(OK, "true","加减金币成功",confirmResult.isLogonFlag()));

        } catch (BusinessException biz){
            logger.error("宁波虚拟金币加减异常 msg:{}", biz.getMsg());
            return OpenSDK.encryptMessage(getResult(FAIL, "false","金币操作失败"));
        } catch (Exception e){
            logger.error("宁波虚拟金币加减异常", e);
            return OpenSDK.encryptMessage(getResult(FAIL, "false","金币操作失败"));
        }
    }

    private String getResult(String status, String success, String message) {
        JSONObject data = new JSONObject();
        data.put("errorMessage", "");
        data.put("status", status);

        JSONObject result = new JSONObject();
        result.put("code", "");
        result.put("message", message);
        result.put("success", success);
        result.put("data", data);
        return JSON.toJSONString(result);
    }

    private String getResult(String status, String success, String message, boolean logonFlg) {
        JSONObject data = new JSONObject();
        data.put("errorMessage", "");
        data.put("status", status);
        data.put("logonFlg", logonFlg ? "1" : "0");

        JSONObject result = new JSONObject();
        result.put("code", "");
        result.put("message", message);
        result.put("success", success);
        result.put("data", data);
        return JSON.toJSONString(result);
    }

    private String getResult2(String errorCode, String errorMsg, String data) {
        JSONObject result = new JSONObject();
        result.put("returnCode", errorCode);
        result.put("returnMsg", errorMsg);
        result.put("data", data);
        return JSON.toJSONString(result);
    }

    @PostMapping("/taskListQuery")
    @ResponseBody
    public OpenVo taskListQuery(@RequestParam String openId) {
        return ningBoService.getTaskListQry(openId,0);
    }

    @PostMapping("/taskListQueryNew")
    @ResponseBody
    public OpenVo taskListQueryNew(@RequestParam String openId,@RequestParam String activityType) {
        return ningBoService.getTaskListQry(openId,Integer.valueOf(activityType));
    }

    @PostMapping("/appNotice")
    @ResponseBody
    public Result<Boolean> appNotice(@RequestBody AppNoticeRequestVo requestVo) {
        boolean result;
        try {
            if (requestVo == null) {
                return ResultBuilder.fail(ErrorCode.********.getErrorCode(), ErrorCode.********.getDesc());
            }
            if (StringUtils.isEmpty(requestVo.getTempleateType())) {
                return ResultBuilder.fail(ErrorCode.********.getErrorCode(), "templeateType不能为空");
            }
            if (!("1".equals(requestVo.getTempleateType()) || "2".equals(requestVo.getTempleateType()))) {
                return ResultBuilder.fail(ErrorCode.********.getErrorCode(), "templeateType参数无效");
            }
            if (CollectionUtils.isEmpty(requestVo.getUidList())) {
                return ResultBuilder.fail(ErrorCode.********.getErrorCode(), "uidList不能为空");
            }
            if (requestVo.getUidList().size() > 100) {
                return ResultBuilder.fail(ErrorCode.********.getErrorCode(), "uidList不能超过100");
            }
            result = ningBoService.appNotice(requestVo.getTempleateType(), requestVo.getUidList());
        } catch (Exception e) {
            logger.warn("宁波银行-虚拟社区-appNotice异常", e);
            return ResultBuilder.fail("宁波银行-虚拟社区-appNotice异常");
        }

        return ResultBuilder.success(result);
    }


    /**
     * 虚拟金币加减查证接口
     * @param request
     * @return
     */
    @RequestMapping(value = "/operationCheck")
    @NoLoginCanAccess
    public String operationCheck(HttpServletRequest request) throws SDKException {
        try {
            BufferedReader br = request.getReader();
            String str = "";
            String listString = "";
            while ((str = br.readLine()) != null) {
                listString += str;
            }
            logger.info("宁波虚拟金币加减查证接口入参, 加密报文:{}", listString);
            String requestStr = OpenSDK.decryptMessage(listString);
            logger.info("宁波虚拟金币加减查证接口入参, 解密报文:{}", requestStr);
            String appKey = JSON.parseObject(requestStr).getString("appkey");
            String sign = JSON.parseObject(requestStr).getString("sign");
            String orderNum = JSON.parseObject(requestStr).getString("orderNum");
            //参数检查
            if (StringUtils.isBlank(appKey) || StringUtils.isBlank(sign) || StringUtils.isBlank(orderNum)) {
                throw new BusinessException(ErrorCode.********);
            }

            String result = ningBoService.operationCheck(appKey, sign, orderNum);

            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject == null || !StringUtils.equals("true", jsonObject.getString("success"))) {
                return OpenSDK.encryptMessage(getResult(FAIL,"false","网络异常,稍候再试"));
            }

            return OpenSDK.encryptMessage(getResult(jsonObject.getString("data"), "true", jsonObject.getString("message")));

        } catch (BusinessException biz){
            logger.error("宁波虚拟金币加减查证异常 msg:{}", biz.getMsg());
            return OpenSDK.encryptMessage(getResult(FAIL, "false",biz.getMsg()));
        } catch (Exception e){
            logger.error("宁波虚拟金币加减查证异常", e);
            return OpenSDK.encryptMessage(getResult(FAIL, "false","系统异常,稍候再试"));
        }
    }


    @ResponseBody
    @NoLoginCanAccess
    @PostMapping("addressBookUpload")
    public String addressBookUpload(HttpServletRequest request) throws Exception {
        BufferedReader br = request.getReader();
        String str = "";
        String listString = "";
        while ((str = br.readLine()) != null) {
            listString += str;
        }
        logger.info("上传通讯录, 加密报文:{}", listString);
        String requestStr = OpenSDK.decryptMessage(listString);
        logger.info("上传通讯录, 解密报文:{}", requestStr);
        AddressBook addressBook = JSONObject.parseObject(requestStr, AddressBook.class);
        executorService.submit(() -> {
            logger.info("{}-addressBook:{}", PROJECT_NAME, JSONObject.toJSONString(addressBook));
            Map<String, String> map = new HashMap<>();
            map.put("param", JSONObject.toJSONString(addressBook));
            String resp = HttpUtil.sendPost(constants.getUploadUrl(), map);
            logger.info("{}-resp:{}", PROJECT_NAME, resp);
        });
        return OpenSDK.encryptMessage(getReturn("0", "提交成功"));
    }

    @GetMapping("/viewPage")
    @ResponseBody
    public Boolean viewPage(@RequestParam Integer taskId,@RequestParam String uid) {
        return ningBoService.viewPageTransmit(taskId, uid);
    }


    private void paramCheck(String appKey, String sign, DeductCreditsParams deductCreditsParams) throws BusinessException {
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(sign) || StringUtils.isBlank(deductCreditsParams.getTimestamp()) || StringUtils.isBlank(deductCreditsParams.getOrdernum()) ||
                StringUtils.isBlank(deductCreditsParams.getUid()) || StringUtils.isBlank(deductCreditsParams.getCredits()) || StringUtils.isBlank(deductCreditsParams.getChannel()) || StringUtils.isBlank(deductCreditsParams.getType())
        ) {
            throw new BusinessException(ErrorCode.********);
        }
    }

    private static String getReturn(String code, String message) {
        JSONObject result = new JSONObject();
        result.put("returnCode", code);
        result.put("returnMessage", message);
        return JSON.toJSONString(result);
    }


    /******************************** 水果兑换相关*******************************************/

    /**
     *
     * 订单创建
     * @param orderCreateMCParams
     * @return tradeNo
     */
    @PostMapping("/orderCreateMC")
    @ResponseBody
    public OpenVo orderCreateMC(@RequestBody OrderCreateMCParams orderCreateMCParams) {
        return ningBoService.orderCreateMC(orderCreateMCParams);
    }

    /**
     * 省市区查询
     * @return
     */
    @GetMapping("/areaMC")
    @NoLoginCanAccess
    public OpenVo areaMC() {
        return ningBoService.areaMC();
    }

    /**
     * 订单确认
     * @param uid
     * @param tradeNo 预订单号
     * @return
     */
    @GetMapping("/orderConfirmMC")
    @ResponseBody
    public OpenVo orderConfirmMC(@RequestParam String uid, @RequestParam String tradeNo) {
        return ningBoService.orderConfirmMC(uid,tradeNo);
    }

    /**
     * 订单查询
     * @param uid
     * @param outTradeNo 商户订单号
     * @return
     */
    @GetMapping("/orderDetailMC")
    @ResponseBody
    public OpenVo orderDetailMC(@RequestParam String uid,
                                @RequestParam String outTradeNo) {
        return ningBoService.orderDetailMC(uid, outTradeNo);
    }

    /**
     * 物流查询
     * @param uid
     * @param outTradeNo 商户订单号
     * @return
     */
    @GetMapping("/logisticsMC")
    @ResponseBody
    public OpenVo logisticsMC(@RequestParam String uid,
                              @RequestParam String outTradeNo) {
        return ningBoService.logisticsMC(uid, outTradeNo);
    }

    /**
     * 确认收货
     * @param uid
     * @param outTradeNo 商户订单号
     * @return
     */
    @GetMapping("/receiptConfirmMC")
    @ResponseBody
    public OpenVo receiptConfirmMC(@RequestParam String uid,
                                   @RequestParam String outTradeNo) {
        return ningBoService.receiptConfirmMC(uid, outTradeNo);
    }

    /**
     * 取消订单
     * @param uid  用户标识
     * @param outTradeNo  商户订单号
     * @return
     */
    @ResponseBody
    @GetMapping("/orderCancelMC")
    public OpenVo orderCancelMC(@RequestParam String uid, @RequestParam String outTradeNo){
        return ningBoService.orderCancelMC(uid, outTradeNo);
    }

    /**
     * 查询商品详情
     * @param productType
     * @return
     */
    @ResponseBody
    @GetMapping("/productDetailMC")
    public OpenVo productDetailMC(@RequestParam String productType){
        return ningBoService.productDetailMC(productType);
    }
}