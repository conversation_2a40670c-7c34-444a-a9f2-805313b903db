package com.duiba.activity.accessweb.controller;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerDto;
import cn.com.duiba.activity.center.api.dto.game.DuibaQuestionAnswerOrdersDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.remoteservice.game.RemoteDuibaQuestionAnswerServiceNew;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.enums.saas.SaasFuncTypeEnum;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.utils.SaasGrantUtil;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.domain.DomainService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.HtdoolConstants;
import com.duiba.activity.accessweb.service.common.ActivityCommonService;
import com.duiba.activity.accessweb.service.common.ActivitySelfOrderService;
import com.duiba.activity.accessweb.service.questionanswer.QuestionAnswerFlowService;
import com.duiba.activity.accessweb.service.questionanswer.QuestionAnswerService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.token.FormTokenService;
import com.duiba.activity.accessweb.tool.AccessLogExUtil;
import com.duiba.activity.accessweb.tool.ActivityBusinessTool;
import com.duiba.activity.accessweb.tool.CatLogTool;
import com.duiba.activity.accessweb.tool.CreditsCalculateService;
import com.duiba.activity.accessweb.tool.InnerLogUtil;
import com.duiba.activity.accessweb.tool.RequestParams;
import com.duiba.activity.accessweb.vo.DuibaActivityVo;
import com.duiba.activity.accessweb.vo.OrdersVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.javatuples.Pair;
import org.javatuples.Triplet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("/question")
public class QuestionCtrl extends BaseNewCtrl{
	private static final Logger logger = LoggerFactory.getLogger(QuestionCtrl.class);
	@Autowired
	private ActivityCommonService activityCommonService;
	@Autowired
	private QuestionAnswerService questionAnswerService;
	@Autowired
	private CreditsCalculateService creditsCalculateService;
	@Autowired
	private FormTokenService formTokenService;
	@Autowired
	private QuestionAnswerFlowService questionAnswerFlowService;
	@Autowired
	private RemoteDuibaQuestionAnswerServiceNew remoteDuibaQuestionAnswerServiceNew;
	@Autowired
	private ActivitySelfOrderService activitySelfOrderService;

	@Autowired
	private RemoteAppService remoteAppServiceForDevelop;

	@Autowired
	private DomainService domainService;
	@Autowired
	private RiskService riskService;


	@RequestMapping("/duiba")
	public ModelAndView duiba(@RequestParam(name = "id") Long duibaId, HttpServletRequest request){
		//判断活动id 是否为null
		if(duibaId == null){
			logger.warn("QuestionCtrl duiba duibaId is null");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		Pair<OperatingActivityDto, Boolean> pairOp = findByAppIdAndDuiba(consumerDto.getAppId(), duibaId, "question");
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}

		OperatingActivityDto opt = pairOp.getValue0();
		if(opt.getActivityId() != null && !SaasGrantUtil.checkGrant(appSimpleDto.getDeveloperId(), appSimpleDto.getId(), SaasFuncTypeEnum.ACTIVITY, opt.getType(), opt.getActivityId(), true)){
			return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
		}

		try {
			AccessLogExUtil.putAccessLogExPair(true, 500, 1, PageBizTypeEnum.ACT_INDEX, opt.getActivityId(),
					opt.getId(), ActivityUniformityTypeEnum.QuestionAnswer, consumerDto.isNotLoginUser(),
					consumerDto.getCredits(), null, null, null, null);

			return questionAnswerService.tupleModelAndView(pairOp.getValue0(),consumerDto,appSimpleDto,false,request);
		} catch (Exception e) {
			logger.error("QuestionCtrl duiba error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}
	
	/**
	 * 
	 * @param id 开发者运营活动表主键
	 * @param request
	 * @param id
	 * @return
	 */
	@RequestMapping("/index")
	public ModelAndView index(@RequestParam(name = "id") Long id, HttpServletRequest request){
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		// cat监控大盘
		CatLogTool.logMetric(CatLogTool.METRIC_QUESTIONINDEX);
		//查询活动信息
		Pair<OperatingActivityDto, Boolean> pairOp = findOperatingActivityById(id,consumerDto.getAppId());
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}

		OperatingActivityDto opt = pairOp.getValue0();
		if(opt.getActivityId() != null && !SaasGrantUtil.checkGrant(appSimpleDto.getDeveloperId(), appSimpleDto.getId(), SaasFuncTypeEnum.ACTIVITY, opt.getType(), opt.getActivityId(), true)){
			return new ModelAndView("redirect:https:" + domainService.getSystemDomain(RequestLocal.getAppId()).getActivityDomain() + "/customShare/share?id=1503");
		}

		try {
			AccessLogExUtil.putAccessLogExPair(true, 500, 1, PageBizTypeEnum.ACT_INDEX, opt.getActivityId(),
					opt.getId(), ActivityUniformityTypeEnum.QuestionAnswer, consumerDto.isNotLoginUser(),
					consumerDto.getCredits(), null, null, null, null);
			return questionAnswerService.tupleModelAndView(pairOp.getValue0(),consumerDto,appSimpleDto,false,request);
		} catch (Exception e) {
			logger.error("QuestionCtrl index error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}
	
	/**
	 *
	 * @param id 开发者运营活动表主键
	 * @return
	 */
	@RequestMapping("/share")
	public ModelAndView mater(@RequestParam(name = "id") Long id,@RequestParam(name = "appId") Long appId, HttpServletRequest request){
		//查询app信息
		AppSimpleDto app = remoteAppServiceForDevelop.getSimpleApp(appId).getResult();
		if (app == null) {
			logger.warn("appId is empty.");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//查询活动信息
		Pair<OperatingActivityDto, Boolean> pairOp = super.findOperatingActivityById(id,appId);
		if(pairOp.getValue1()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		try {
			return questionAnswerService.materTupleModelAndView(pairOp.getValue0(),app,request);
		} catch (Exception e) {
			logger.error("QuizzCtrl index error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}

	@ResponseBody
	@RequestMapping(value = "/shareElement", produces = "application/json")
	public String ajaxElement(@RequestParam(name = "actId") Long actId, @RequestParam(name = "hdType") String type, HttpServletRequest request){
		JSONObject json = new JSONObject();
		try {
			Pair<OperatingActivityDto, DuibaQuestionAnswerDto>  pair = getActivityInfo(type,actId,null,false);
			DuibaQuestionAnswerDto duibaQuizzDO = pair.getValue1();
			OperatingActivityDto operatingActivityDto = pair.getValue0();
			AppSimpleDto appSimpleDto = remoteAppServiceForDevelop.getSimpleApp(operatingActivityDto.getAppId()).getResult();
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDto, duibaQuizzDO==null?null:duibaQuizzDO.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			//组装element信息
			Map<String,Object> elementMap = Maps.newHashMap();
			elementMap.put(CommonConstants.STATUS, HtdoolConstants.STATUS_FOREVER);
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			json.put(CommonConstants.ELEMENT, elementMap);
			json.put("rule", duibaQuizzDO ==null ? "":duibaQuizzDO.getRule());
			// 获取活动奖项
			json.put("options", questionAnswerService.findDisplayOptions(operatingActivityDto));
			json.put(CommonConstants.SUCCESS_KEY, true);
			return json.toJSONString();
		} catch (Exception e) {
			logger.error("QuestionCtrl ajaxElement error", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toString();
		}
	}

	@ResponseBody
	@RequestMapping(value = "/ajaxElement", produces = "application/json")
	public String ajaxElement(@RequestParam(name = "preview") String preview, @RequestParam(name = "actId") Long actId, @RequestParam(name = "hdType") String type, HttpServletRequest request){
		JSONObject json = new JSONObject();
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return json.toString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		try {
			Pair<OperatingActivityDto, DuibaQuestionAnswerDto>  pair = getActivityInfo(type,actId,Long.parseLong(request.getParameter("hdToolId")),Boolean.valueOf(preview));
			DuibaQuestionAnswerDto duibaQuestionAnswerDto = pair.getValue1();
			OperatingActivityDto operatingActivityDto = pair.getValue0();
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDto, duibaQuestionAnswerDto==null?null:duibaQuestionAnswerDto.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			//组装element信息
			DuibaActivityVo activity = new DuibaActivityVo();
			if(duibaQuestionAnswerDto != null){
				activity.setFreeLimit(duibaQuestionAnswerDto.getFreeLimit());
				activity.setFreeScope(duibaQuestionAnswerDto.getFreeScope());
				activity.setLimitCount(duibaQuestionAnswerDto.getLimitCount());
				activity.setLimitScope(duibaQuestionAnswerDto.getLimitScope());
			}
			activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
			activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDto.getCreditsType()));
			Map<String,Object> elementMap = activityCommonService.getElementForHdtoolAndQu(Boolean.valueOf(preview), operatingActivityDto, consumerDto.getId(), activity,appSimpleDto);
			elementMap.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(), appSimpleDto));
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			json.put("element", elementMap);
			// 获取活动奖项
			json.put("options", questionAnswerService.findDisplayOptions(operatingActivityDto));
			json.put(CommonConstants.SUCCESS_KEY, true);
			return json.toJSONString();
		} catch (Exception e) {
			logger.warn("QuestionCtrl ajaxElement error", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toString();
		}
	}
	
	@ResponseBody
	@RequestMapping(value = "/doJoin", produces = "application/json")
	public String doJoin(@RequestParam(name = "activityId") Long operationAcitvityId, HttpServletRequest request,HttpServletResponse response){
		JSONObject json = new JSONObject();
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return json.toString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		//记录当前用户参加活动的id
		ActivityBusinessTool.setConsumerJoinActivityIdCookie(operationAcitvityId, request, response);
		Pair<OperatingActivityDto, DuibaQuestionAnswerDto>  pair = getActivityInfo("",operationAcitvityId,null,false);
		OperatingActivityDto operatingActivityDto = pair.getValue0();
		DuibaQuestionAnswerDto questionAnswer = pair.getValue1();
		//活动判断
		if(operatingActivityDto == null){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_EXTEXT_HDBCZ);
			return json.toString();
		}
		json.put(CommonConstants.SUBMITTOKEN, formTokenService.getConsumerToken(RequestLocal.getCid()));
		DuibaActivityVo activity = new DuibaActivityVo();
		activity.setBlackCheck(false);
		activity.setSpecify(false);
		if(questionAnswer != null){
			//解决 下单验证时  参数不统一 问题
			activity.setBlackCheck(questionAnswer.isOpenSwitch(DuibaQuestionAnswerDto.SWITCHES_DEV_BLACKLIST));
			activity.setSpecify(questionAnswer.isOpenSwitch(DuibaQuestionAnswerDto.SWITCHES_DIRECT));
			activity.setFreeLimit(questionAnswer.getFreeLimit());
			activity.setFreeScope(questionAnswer.getFreeScope());
			activity.setLimitCount(questionAnswer.getLimitCount());
			activity.setLimitScope(questionAnswer.getLimitScope());
			activity.setAutoOffDate(questionAnswer.getAutoOffDate());
			activity.setStatus(questionAnswer.getStatus());
			activity.setCreditsPrice(questionAnswer.getCreditsPrice());
		}
		activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
		activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDto.getCreditsType()));
		//用户及 活动校验
		if(super.checkActivityAndUserInfo(activity,operatingActivityDto,appSimpleDto,consumerDto,json,true,null)){
			json.put(CommonConstants.SUCCESS_KEY, false);
			return json.toString();
		}

		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(operatingActivityDto.getId(),operatingActivityDto.getActivityId(), ActivityUniformityTypeEnum.QuestionAnswer.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, riskResult.getCopy());
			return json.toJSONString();
		}

		try {
			RequestParams parms = RequestParams.parse(RequestLocal.getRequest());
			Long needCredits = json.get(CommonConstants.NEEDCREDITS)== null ? 0L:json.getLong(CommonConstants.NEEDCREDITS);
			DuibaQuestionAnswerOrdersDto qOrder = questionAnswerFlowService.createOrder(consumerDto, appSimpleDto, operatingActivityDto, parms,questionAnswer,needCredits);
			json.put(CommonConstants.SUCCESS_KEY, true);
			json.put("orderId", qOrder.getId());
			//计算题目并保存题目及正确答案到订单扩展表
			json.put("questions", questionAnswerService.findQuestionRecordCacheByBankIds(operatingActivityDto.getActivityId(),qOrder.getId()));

			AccessLogExUtil.putAccessLogExPair(true, 500, 2, PageBizTypeEnum.ACT_JOIN, operatingActivityDto.getActivityId(),
					operatingActivityDto.getId(), ActivityUniformityTypeEnum.QuestionAnswer, consumerDto.isNotLoginUser(),
					consumerDto.getCredits(), null, null, null, null);

			return json.toString();
		} catch (Exception e) {
			logger.warn("答题活动订单创建异常：appId=" + consumerDto.getAppId() + ",consumerId=" + consumerDto.getId() + ",ip=" + RequestTool.getIpAddr(request) + "," + e.getMessage(), e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, e.getMessage());
			return json.toString();
		}
	}
	
	@ResponseBody
	@RequestMapping(value = "/submit", produces = "application/json")
	public String submit(@RequestParam(name = "orderId") Long orderId, @RequestParam(name = "answerData") String answerData, @RequestParam(name = "token") String token, HttpServletRequest request,HttpServletResponse response) {
		JSONObject json = new JSONObject();
		json.put(CommonConstants.SUCCESS_KEY, true);
		//入参校验
		if (StringUtils.isBlank(answerData) || StringUtils.isBlank(token)) {
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "参数错误");
			return json.toJSONString();
		}
		if (!formTokenService.checkAndInvalidConsumerToken(RequestLocal.getCid(), token)) {
			logger.info("submit token check fail ,cid=" + RequestLocal.getCid() + ",ip=" + RequestTool.getIpAddr(request));
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "请勿重复提交");
			return json.toJSONString();
		}
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return json.toJSONString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		DuibaQuestionAnswerOrdersDto questionAnswerOrders = activitySelfOrderService.findQuesitonOrder(consumerDto.getId(),orderId,json);
		if(!json.getBooleanValue(CommonConstants.SUCCESS_KEY)){
			return json.toJSONString();
		}
		//答案信息校验
		Map<Long, Long> answerParams = Maps.newHashMap();
		try {
			String awardsStr = StringEscapeUtils.unescapeHtml4(answerData);
			JSONArray answers = JSONArray.parseArray(awardsStr);
			if(CollectionUtils.isNotEmpty(answers)){
				for(int i = 0;i < answers.size();i++){
					JSONObject answer = answers.getJSONObject(i);
					answerParams.put(answer.getLong("id"), answer.getLong("option"));
				}
			}
			if(answerParams.size() <= 0){
				json.put(CommonConstants.SUCCESS_KEY, false);
				json.put(CommonConstants.MESSAGE_KEY, "参数错误");
				return json.toJSONString();
			}
		} catch (Exception e) {
			logger.warn("活动参数解析错误", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "活动参数解析错误");
			return json.toJSONString();
		}

		//风控
		StormEngineResultDto riskResult = riskService.joinRisk(questionAnswerOrders.getOperatingActivityId(),null, ActivityUniformityTypeEnum.QuestionAnswer.getCode());
		if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, riskResult.getCopy());
			return json.toJSONString();
		}

		try {
			Integer rightCount = questionAnswerFlowService.submit(consumerDto,appSimpleDto,questionAnswerOrders, answerParams,RequestParams.parse(RequestLocal.getRequest()));
			json.put("rightCount", rightCount);
			json.put(CommonConstants.MESSAGE_KEY, "成功");
			return json.toJSONString();
		} catch (Exception e) {
			logger.error("QuestionCtrl submit error", e);
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toJSONString();
		}
	}
	
	@ResponseBody
	@RequestMapping(value = "/getOrderStatus", produces = "application/json")
	public String getOrderStatus(@RequestParam(name = "orderId") Long orderId, HttpServletRequest request,HttpServletResponse response) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(CommonConstants.SUCCESS_KEY, true);
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			jsonObject.put(CommonConstants.SUCCESS_KEY, false);
			jsonObject.put(CommonConstants.MESSAGE_KEY, CommonConstants.MSG_ERROR_USERINFO);
			return jsonObject.toJSONString();
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		DuibaQuestionAnswerOrdersDto orderDO = activitySelfOrderService.findQuesitonOrder(consumerDto.getId(),orderId,jsonObject);
		if(!jsonObject.getBooleanValue(CommonConstants.SUCCESS_KEY)){
			return jsonObject.toJSONString();
		}
		try {
			String slotId = RequestLocal.getSlotId();
			//查询活动信息
			OperatingActivityDto operatingActivityDO = super.findOperatingActivityDto(orderDO.getOperatingActivityId());
			DuibaQuestionAnswerDto duibaQuestionAnswerDto = remoteDuibaQuestionAnswerServiceNew.find(operatingActivityDO.getActivityId());
			
			//返回code
			Integer lotteryCode = HtdoolConstants.LOTTERY_CODE_0;
			//谢谢参与或者扣积分失败
			if (!HdtoolOrdersDto.PrizeTypeThanks.equals(orderDO.getPrizeType())) {
				lotteryCode = super.getOrderForOther(operatingActivityDO, BeanUtils.copy(orderDO,OrdersVO.class), jsonObject, consumerDto, slotId, ConsumerExchangeRecordDto.TypeQuestion,questionAnswerService.findDisplayOption(operatingActivityDO,orderDO.getPrizeId()));
			}
			//组装element信息
			DuibaActivityVo activity = new DuibaActivityVo();
			if(duibaQuestionAnswerDto != null){
				activity.setFreeLimit(duibaQuestionAnswerDto.getFreeLimit());
				activity.setFreeScope(duibaQuestionAnswerDto.getFreeScope());
				activity.setLimitCount(duibaQuestionAnswerDto.getLimitCount());
				activity.setLimitScope(duibaQuestionAnswerDto.getLimitScope());
			}
			activity.setLoginOpen(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),consumerDto.getPartnerUserId());
			activity.setCredtisNoFlag(activityCommonService.checkAppCreditsType(appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType),operatingActivityDO.getCreditsType()));
			Map<String,Object> elementMap = activityCommonService.getElementForHdtoolAndQu(false, operatingActivityDO, consumerDto.getId(), activity,appSimpleDto);
			
			elementMap.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(), appSimpleDto));
			//获取需要的积分
			Long needCredits = activityCommonService.getCredits(appSimpleDto.getCreditsRate(),operatingActivityDO, duibaQuestionAnswerDto==null?null:duibaQuestionAnswerDto.getCreditsPrice(),appSimpleDto.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
			elementMap.put(CommonConstants.NEEDCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(needCredits, appSimpleDto));
			jsonObject.put("result", lotteryCode);
			jsonObject.put("element", JSONArray.toJSON(elementMap));
			jsonObject.put(CommonConstants.MYCREDITS, creditsCalculateService.convertCreditsUnit4Consumer(consumerDto.getCredits(),appSimpleDto));
			return jsonObject.toJSONString();
		} catch (Exception e) {
			logger.error("QuestionCtrl getOrderStatus error", e);
			JSONObject json = new JSONObject();
			json.put(CommonConstants.SUCCESS_KEY, false);
			json.put(CommonConstants.MESSAGE_KEY, "系统异常");
			return json.toJSONString();
		}
	}

	@RequestMapping("/preview")
	public ModelAndView preview(@RequestParam(name = "id") Long id, @RequestParam(name = "type") String type, HttpServletRequest request, HttpServletResponse response){
		if(id == null || StringUtils.isBlank(type)){
			logger.error("QuestionCtrl preview id is null or type is null");
			return new ModelAndView(CommonConstants.ERROR);
		}
		//获取 用户信息及app信息
		Triplet<AppSimpleDto, ConsumerDto,Boolean> triplet = findAppAndConsumerInfo();
		if(triplet.getValue2()){
			return new ModelAndView(CommonConstants.ERROR);
		}
		ConsumerDto consumerDto = triplet.getValue1();
		AppSimpleDto appSimpleDto = triplet.getValue0();
		
		try {
			Pair<OperatingActivityDto, DuibaQuestionAnswerDto>  pair = getActivityInfo(type,id,id,true);
			return questionAnswerService.tupleModelAndView(pair.getValue0(),consumerDto,appSimpleDto,true,request);
		} catch (Exception e) {
			logger.warn("QuestionCtrl preview error ", e);
			return new ModelAndView(CommonConstants.ERROR);
		}
	}

	private Pair<OperatingActivityDto, DuibaQuestionAnswerDto> getActivityInfo(String type, Long opId, Long duibaId,boolean prew) {
		OperatingActivityDto operatingActivityDO;
		DuibaQuestionAnswerDto duibaQuestionAnswerDO = null;
		if (prew && CommonConstants.DUIBA.equals(type)) {
			duibaQuestionAnswerDO = remoteDuibaQuestionAnswerServiceNew.find(duibaId);
			operatingActivityDO = new OperatingActivityDto();
			operatingActivityDO.setId(1L);
			operatingActivityDO.setType(OperatingActivityDto.TypeActivityAccessQuestionAnswer);
			operatingActivityDO.setActivityId(duibaQuestionAnswerDO.getId());
			operatingActivityDO.setStatus(OperatingActivityDto.StatusIntOpen);
			operatingActivityDO.setTitle(duibaQuestionAnswerDO.getTitle());
		} else {
			operatingActivityDO = super.findOperatingActivityDto(opId);
			if(activityCommonService.isDuibaActivity(operatingActivityDO)){
				duibaQuestionAnswerDO = remoteDuibaQuestionAnswerServiceNew.find(operatingActivityDO.getActivityId());
			}
		}
		return Pair.with(operatingActivityDO, duibaQuestionAnswerDO);
	}
}
