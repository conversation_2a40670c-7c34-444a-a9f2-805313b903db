package com.duiba.activity.accessweb.controller.activity.sharecode;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.controller.BaseNewCtrl;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.activity.sharecode.masterdisciple.MasterDiscipleSystemService;
import com.duiba.activity.accessweb.service.token.FormTokenService;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMainInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMasterInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.BXWMyRedPacketsVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.DiscipleInfoVO;
import com.duiba.activity.accessweb.vo.activity.sharecode.DrawsResultVO;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Pair;
import org.javatuples.Quartet;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 18/10/30
 * @description 百姓网师徒体系
 */
@RestController
@RequestMapping("/aaw/masterSystem")
public class MasterDiscipleSystemCtrl extends BaseNewCtrl {

	@Resource
	private FormTokenService formTokenService;
	@Resource
	private MasterDiscipleSystemService masterDiscipleSystemService;


	/**
	 * 活动主页面数据
	 */
	@GetMapping(value = "/mainPageInfo")
	public Result<BXWMainInfoVO> mainPageInfo(@RequestParam Long activityId, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		accessLog(1, activityId, pair.getValue0(), consumerDO.getPartnerUserId());
		try{
			return ResultBuilder.success(masterDiscipleSystemService.getMainPageInfo(request, appSimpleDto.getId(), consumerDO, pair.getValue0()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 获取分享者信息
	 */
	@GetMapping(value = "/getInviterInfo")
	public Result<BXWMasterInfoVO> getInviterInfo(@RequestParam Long activityId,
			@RequestParam String shareCode, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		accessLog(2, activityId, pair.getValue0(), consumerDO.getPartnerUserId());
		try{
			return ResultBuilder.success(masterDiscipleSystemService.getMasterInfo(request, shareCode, appSimpleDto.getId(), consumerDO, activityId, pair.getValue0()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	private void accessLog(int status, Long operatingActivityId, Long duibaActivityId, String partnerUserId){
		AccessLogFilter.putExPair("type", "masterDisciple");
		AccessLogFilter.putExPair("sub_type", "masterDisciple");
		AccessLogFilter.putExPair("suc", 1);
		AccessLogFilter.putExPair("id", duibaActivityId);
		AccessLogFilter.putExPair("oaid", operatingActivityId);
		AccessLogFilter.putExPair("loginStatus", ConsumerDto.isNotLoginUser(partnerUserId) ? 2 : 1);
		AccessLogFilter.putExPair("pageBizId", 206);
		AccessLogFilter.putExPair("status", status);
		AccessLogFilter.putExPair("from", 2);
	}

	/**
	 * 我的红包
	 */
	@GetMapping(value = "/myRedPacket")
	public Result<BXWMyRedPacketsVO> myRedPacket(@RequestParam Long activityId, @RequestParam(defaultValue = "1") Integer pageNo,
			@RequestParam(defaultValue = "10") Integer pageSize, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		try{
			return ResultBuilder.success(masterDiscipleSystemService.getMyRedPacket(activityId, pageNo, pageSize, appSimpleDto.getId(), consumerDO));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 提现金额校验
	 */
	@GetMapping(value = "/checkDrawAmount")
	public Result<String> checkDrawAmount(@RequestParam String drawAmount, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		try{
			masterDiscipleSystemService.checkDrawAmount(drawAmount, appSimpleDto.getId(), consumerDO);
			return ResultBuilder.success();
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 提现
	 */
	@GetMapping(value = "/withdraw")
	public Result<Long> withdraw(@RequestParam Long activityId,
			@RequestParam String userName,
			@RequestParam String alipay,
			@RequestParam String drawAmount, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		// 防重复校验
		String redisKey = RedisKeyFactory.K263.toString() + appSimpleDto.getId() + "_" + consumerDO.getId();
		if (!formTokenService.lock(redisKey, 2)) {
			return ResultBuilder.fail("操作过于频繁，请稍后尝试");
		}
		try{
			return ResultBuilder.success(
					masterDiscipleSystemService.withDraw(request, appSimpleDto, consumerDO, pair.getValue0(), userName, alipay, drawAmount));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getCode(), e.getMessage());
		}
	}

	/**
	 * 轮询提现结果
	 */
	@GetMapping(value = "/withdrawStatus")
	public Result<DrawsResultVO> withdrawStatus(@RequestParam Long drawRecordId, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		try{
			return ResultBuilder.success(masterDiscipleSystemService.withdrawStatus(drawRecordId, appSimpleDto.getId(), consumerDO.getId()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 提现记录
	 */
	@GetMapping(value = "/drawRecords")
	public Result<List<DrawsResultVO>> drawRecords(@RequestParam(defaultValue = "1") Integer pageNo,
			@RequestParam(defaultValue = "10") Integer pageSize, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		try{
			return ResultBuilder.success(masterDiscipleSystemService.drawRecords(pageNo, pageSize, consumerDO.getId()));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 我的徒弟
	 */
	@GetMapping(value = "/myDisciples")
	public Result<List<DiscipleInfoVO>> myDisciples(@RequestParam Long activityId,
			@RequestParam(defaultValue = "1") Integer pageNo,
			@RequestParam(defaultValue = "10") Integer pageSize, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		try{
			return ResultBuilder.success(masterDiscipleSystemService.getMyDisciples(consumerDO.getId(), activityId, pageNo, pageSize));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}

	/**
	 * 我的徒孙
	 */
	@GetMapping(value = "/disciplesOfMyDisciple")
	public Result<List<DiscipleInfoVO>> disciplesOfMyDisciple(@RequestParam Long activityId,
			@RequestParam(defaultValue = "1") Integer pageNo,
			@RequestParam(defaultValue = "10") Integer pageSize, HttpServletRequest request, HttpServletResponse response) {
		//获取 用户信息及app信息
		Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
		if (null != quartet.getValue3()) {
			return ResultBuilder.fail(quartet.getValue3());
		}
		if (null != quartet.getValue2()) {
			return ResultBuilder.fail(quartet.getValue2());
		}
		ConsumerDto consumerDO = quartet.getValue0();
		AppSimpleDto appSimpleDto = quartet.getValue1();
		// 校验入库活动id
		Pair<Long, String> pair = findDuiBaActivityId(activityId, appSimpleDto.getId());
		if(StringUtils.isNotBlank(pair.getValue1())){
			return ResultBuilder.fail(pair.getValue1());
		}
		try{
			return ResultBuilder.success(
					masterDiscipleSystemService.getDisciplesOfMyDisciple(consumerDO.getId(), activityId, pageNo, pageSize));
		} catch (BizException e) {
			return ResultBuilder.fail(e.getMessage());
		}
	}
}
