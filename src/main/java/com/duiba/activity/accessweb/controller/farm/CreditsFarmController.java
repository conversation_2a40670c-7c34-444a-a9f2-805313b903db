package com.duiba.activity.accessweb.controller.farm;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmActDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmCropDetailDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmNutritionRecordDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmOptionDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmTaskDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmUserRecordDto;
import cn.com.duiba.activity.center.api.dto.creditsfarm.CreditsFarmUserSeedRecordDto;
import cn.com.duiba.activity.center.api.enums.creditsfarm.CropStatusEnum;
import cn.com.duiba.activity.center.api.enums.creditsfarm.TaskTypeEnum;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmActService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmCropDetailService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmNutritionRecordService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmOptionService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmTaskService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmUserRecordService;
import cn.com.duiba.activity.center.api.remoteservice.creditsfarm.RemoteCreditsFarmUserSeedRecordService;
//import cn.com.duiba.anticheat.center.api.dto.RiskRuleEngineResultDto;
import cn.com.duiba.duiba.stormrage.center.open.api.dto.StormEngineResultDto;
//import cn.com.duiba.anticheat.center.api.enums.RiskDecisionEnum;
import cn.com.duiba.duiba.stormrage.center.open.api.enums.RiskDecisionEnum;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.enums.AccountActionTypeEnum;
import cn.com.duiba.api.enums.ActivityUniformityTypeEnum;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.dto.RequestParams;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.app.AppExtraLargeFieldDto;
import cn.com.duiba.developer.center.api.domain.dto.appextra.DuibaShareDto;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.duiba.activity.accessweb.cache.ActivityCacheService;
import com.duiba.activity.accessweb.cache.DeveloperCacheService;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.constant.RedisKeyFactory;
import com.duiba.activity.accessweb.controller.BaseNewCtrl;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.pjv.ResultCode;
import com.duiba.activity.accessweb.service.WeixinShareContentService;
import com.duiba.activity.accessweb.service.activity.farm.CreditsFarmService;
import com.duiba.activity.accessweb.service.risk.RiskService;
import com.duiba.activity.accessweb.service.share.ShareConfigService;
import com.duiba.activity.accessweb.tool.ConsumerCheckUtil;
import com.duiba.activity.accessweb.vo.creditsfarm.AcquisitionDetailVo;
import com.duiba.activity.accessweb.vo.creditsfarm.CreditsFarmIndexVo;
import com.duiba.activity.accessweb.vo.creditsfarm.CreditsTakeBackResultVo;
import com.duiba.activity.accessweb.vo.creditsfarm.GainResultVo;
import com.duiba.activity.accessweb.vo.creditsfarm.HelpResultVo;
import com.duiba.activity.accessweb.vo.creditsfarm.NutritionDetailVo;
import com.duiba.activity.accessweb.vo.creditsfarm.OptionDetailVo;
import com.duiba.activity.accessweb.vo.creditsfarm.PlantResultVo;
import com.duiba.activity.accessweb.vo.creditsfarm.TaskDetailVo;
import com.duiba.activity.accessweb.vo.creditsfarm.TaskInfoVo;
import com.duiba.activity.accessweb.vo.creditsfarm.UseNutritionResultVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.javatuples.Pair;
import org.javatuples.Quartet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by liugq on 2019/08/07
 * 积分农场活动
 */
@RestController
@RequestMapping("/aaw/creditsFarm")
public class CreditsFarmController extends BaseNewCtrl {

    private static final Logger logger = LoggerFactory.getLogger(CreditsFarmController.class);

    @Autowired
    private ActivityCacheService activityCacheService;
    @Autowired
    private RemoteCreditsFarmTaskService remoteCreditsFarmTaskService;
    @Autowired
    private RemoteCreditsFarmUserRecordService remoteCreditsFarmUserRecordService;
    @Autowired
    private RemoteCreditsFarmCropDetailService remoteCreditsFarmCropDetailService;
    @Autowired
    private RemoteCreditsFarmNutritionRecordService remoteCreditsFarmNutritionRecordService;
    @Autowired
    private RemoteCreditsFarmActService remoteCreditsFarmActService;
    @Autowired
    private RemoteCreditsFarmUserSeedRecordService remoteCreditsFarmUserSeedRecordService;
    @Autowired
    private RemoteCreditsFarmOptionService remoteCreditsFarmOptionService;
    @Autowired
    private WeixinShareContentService weixinShareContentService;
    @Autowired
    private RiskService riskService;
    @Autowired
    private CreditsFarmService creditsFarmService;
    @Autowired
    private ShareConfigService shareConfigService;
    @Autowired
    private DeveloperCacheService developerCacheService;
    @Resource(name = "stringRedisTemplate")
    private RedisAtomicClient redisAtomicClient;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;


    //每次点击加速时间30s
    private static final int CLICK_SPEED_SECOND = 30;
    //单次加速次数上限 100次
    private static final int CLICK_LIMIT = 100;

    /**
     * 积分农场-任务页面
     */
    @GetMapping("/taskPage")
    public ModelAndView taskPage(@RequestParam Long id, HttpServletRequest request) {
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/creditsfarm/taskPage");

        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto) {
            return new ModelAndView(CommonConstants.COMMON_ACTIVITY_CLOSE);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null != actDto){
            modelAndView.addObject("appType", actDto.getAppType());
            modelAndView.addObject("saleSwitch", actDto.getSaleSwitch());
            modelAndView.addObject("saleTimes", actDto.getSaleTimes());
        }
        String shareCodeField = shareConfigService.getAppShareConfCode(RequestLocal.getConsumerAppDO(), request);
        //设置分享信息
        modelAndView.addObject("shareCodeField", shareCodeField);
        modelAndView.addObject("appId", operatingActivityDto.getAppId());
        modelAndView.addObject("id", id);
        modelAndView.addObject("unitName", RequestLocal.getConsumerAppDO().getUnitName());
        modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));

        return modelAndView;
    }

    /**
     * 积分农场-端外分享页面
     */
    @GetMapping("/sharePage")
    public ModelAndView sharePage(Long id, HttpServletRequest request) {
        //组装 页面展示信息
        ModelAndView modelAndView = new ModelAndView("/creditsfarm/sharePage");
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto || OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
            return new ModelAndView(CommonConstants.COMMON_ACTIVITY_CLOSE);
        }
        CreditsFarmActDto actDto = remoteCreditsFarmActService.findGoingAct(operatingActivityDto.getAppId());
        if (null == actDto){
            return new ModelAndView(CommonConstants.COMMON_ACTIVITY_CLOSE);
        }
        Date now = new Date();
        if (now.before(actDto.getStartTime()) || now.after(actDto.getEndTime())){
            return new ModelAndView(CommonConstants.COMMON_ACTIVITY_CLOSE);
        }
        modelAndView.addObject("appId", operatingActivityDto.getAppId());
        modelAndView.addObject("wechatQrCode", actDto.getWechatQrCode());
        modelAndView.addObject("nonWechatShareSwitch", actDto.getNonWechatShareSwitch());
        modelAndView.addObject("nonRegistrationUrl", actDto.getNonRegistrationUrl());
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        modelAndView.addObject("notLogin", null == consumer || consumer.isNotLoginUser() || ! consumer.getAppId().equals(operatingActivityDto.getAppId()));
        AppExtraLargeFieldDto appExtraLargeFieldDto = developerCacheService.findAppShareConfCode(operatingActivityDto.getAppId());
        if (null != appExtraLargeFieldDto && appExtraLargeFieldDto.isShareSwitch(AppExtraLargeFieldDto.SwitchDevCallUpRegistH5)){
           modelAndView.addObject("callUpHfiveCode", appExtraLargeFieldDto.getCallUpRegistH5Code());
        }
        DuibaShareDto duibaShareDto = developerCacheService.findDuibaShareByAppId(operatingActivityDto.getAppId());
        modelAndView.addObject("androidDownloadUrl", duibaShareDto.getShareAndroidLink());
        modelAndView.addObject("iosDownloadUrl", duibaShareDto.getShareIosLink());


        //设置分享信息
        modelAndView.addAllObjects(weixinShareContentService.buildShareContent(request));
        return modelAndView;
    }

    /**
     * 获取活动基本信息
     *
     * @return
     */
    @GetMapping("/homeInfo")
    public Result<CreditsFarmIndexVo> index(String ids) {
        CreditsFarmIndexVo indexVo = new CreditsFarmIndexVo();
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer){
            indexVo.setShowAct(false);
            return ResultBuilder.success(indexVo);
        }

        CreditsFarmActDto actDto;
        OperatingActivityDto operatingActivityDto;

        if (StringUtils.isNotBlank(ids)) {
            Pair<CreditsFarmActDto, OperatingActivityDto> pair = getValidActivity(ids, consumer.getAppId());
            if (pair == null) {
                indexVo.setShowAct(false);
                return ResultBuilder.success(indexVo);
            }
            actDto = pair.getValue0();
            operatingActivityDto = pair.getValue1();
        } else {
            actDto = remoteCreditsFarmActService.findGoingAct(consumer.getAppId());
            if (null == actDto) {
                indexVo.setShowAct(false);
                return ResultBuilder.success(indexVo);
            }
            if (!consumer.getAppId().equals(actDto.getAppId())) {
                indexVo.setShowAct(false);
                return ResultBuilder.success(indexVo);
            }
            operatingActivityDto = activityCacheService.findOPByAppIdAndActivityIdAndType(actDto.getAppId(), actDto.getId(), ActivityUniformityTypeEnum.CREDITS_FARM.getCode());
            //返回种植记录的主键id
            if (null == operatingActivityDto || OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
                indexVo.setShowAct(false);
                return ResultBuilder.success(indexVo);
            }
            Date now = new Date();
            if (now.before(actDto.getStartTime()) || now.after(actDto.getEndTime())) {
                indexVo.setShowAct(false);
                return ResultBuilder.success(indexVo);
            }
        }
        indexVo = creditsFarmService.getHomeInfo(consumer, actDto);
        indexVo.setOptId(operatingActivityDto.getId());
        indexVo.setUnitName(RequestLocal.getConsumerAppDO().getUnitName());
        //
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        indexVo.setEarnCreditsUrl(app.getEarnCreditsUrl());
        AppExtraLargeFieldDto appExtraLargeFieldDto = developerCacheService.findAppShareConfCode(app.getId());
        if (null != appExtraLargeFieldDto){
            if (appExtraLargeFieldDto.isShareSwitch(AppExtraLargeFieldDto.SwitchDevCallUpRegistH5)){
                indexVo.setCallUpHfiveCode(appExtraLargeFieldDto.getCallUpRegistH5Code());
            }
            if (appExtraLargeFieldDto.isShareSwitch(AppExtraLargeFieldDto.SwitchDevCallUp)){
                indexVo.setCallUpCode(appExtraLargeFieldDto.getCallUpCode());
            }
        }
        //访问日志打印
        printJoinLog(operatingActivityDto.getId(), actDto.getId(), PageBizTypeEnum.CREDITS_FARM_INDEX.getBizPageId(), consumer, true);
        riskService.visitRiskLog(operatingActivityDto);
        return ResultBuilder.success(indexVo);
    }

    private Pair<CreditsFarmActDto, OperatingActivityDto> getValidActivity(String ids, Long appId) {
        List<Long> opIds = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<OperatingActivityDto> opList = remoteOperatingActivityServiceNew.findAllByIdsAndStatus(opIds, OperatingActivityDto.StatusIntOpen);
        if (CollectionUtils.isEmpty(opList)) {
            return null;
        }
        List<Long> actIds = opList.stream().filter(o -> o.getAppId().equals(appId)).map(OperatingActivityDto::getActivityId).collect(Collectors.toList());
        if (actIds.isEmpty()) {
            return null;
        }
        List<CreditsFarmActDto> actList = remoteCreditsFarmActService.findByIds(actIds);
        if (CollectionUtils.isEmpty(actList)) {
            return null;
        }
        Date now = new Date();
        for (CreditsFarmActDto dto : actList) {
            if (now.after(dto.getStartTime()) && now.before(dto.getEndTime())) {
                for (OperatingActivityDto op : opList) {
                    if (dto.getId().equals(op.getActivityId())) {
                        return Pair.with(dto, op);
                    }
                }
            }
        }
        return null;
    }


    /**
     *  任务列表
     * @param id
     * @return
     */
    @GetMapping("/task")
    public Result<TaskInfoVo> task(@RequestParam Long id){
        TaskInfoVo vo = new TaskInfoVo();
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto){
            return ResultBuilder.success(vo);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null == actDto){
            return ResultBuilder.success(vo);
        }
        Long consumerId = RequestLocal.getCid();
        if (null == consumerId){
            return ResultBuilder.success(vo);
        }
        List<TaskDetailVo> taskDetailVos = Lists.newArrayList();
        List<CreditsFarmTaskDto> taskDtos = remoteCreditsFarmTaskService.listByActId(actDto.getId());
        CreditsFarmUserRecordDto userRecordDto = remoteCreditsFarmUserRecordService.findByUnikey(consumerId, actDto.getId());
        //根据任务类型排序
        List<CreditsFarmTaskDto> orderTaskDtos = taskDtos.stream().sorted(Comparator.comparing(CreditsFarmTaskDto::getTaskType)).collect(Collectors.toList());
        for (CreditsFarmTaskDto taskDto : orderTaskDtos){
            TaskDetailVo taskDetailVo = BeanUtils.copy(taskDto, TaskDetailVo.class);
            dealTaskStatus(taskDetailVo, userRecordDto);
            taskDetailVos.add(taskDetailVo);
        }
        vo.setTask(taskDetailVos);
        if (null != userRecordDto){
            vo.setNutritionCount(userRecordDto.getNutritionCount());
        }
        return ResultBuilder.success(vo);
    }

    /**
     * 任务完成状态
     * @param vo
     * @param userRecordDto
     */
    private void dealTaskStatus(TaskDetailVo vo, CreditsFarmUserRecordDto userRecordDto){
        if (null == userRecordDto){
            vo.setDoneCount(0);
            return;
        }
        if (TaskTypeEnum.TASK_TYPE_SIGN.getCode().equals(vo.getTaskType())){
            if (isToday(userRecordDto.getLastSignTime())){
                vo.setDoneCount(1);
            } else {
                vo.setDoneCount(0);
            }
            return;
        }
        if (TaskTypeEnum.TASK_TYPE_CROP.getCode().equals(vo.getTaskType())){
            int todayCropCount = 0;
            if (isToday(userRecordDto.getLastCropTime())){
                todayCropCount = userRecordDto.getCropCountToday();
            }
            vo.setDoneCount(todayCropCount >= vo.getSubTaskCount() ? 1 : 0);
        }
        //分享任务，查询今天分享成功次数
        if (TaskTypeEnum.TASK_TYPE_SHARE.getCode().equals(vo.getTaskType())){
            Integer todayShareReward = remoteCreditsFarmNutritionRecordService.countTodayReward(userRecordDto.getConsumerId(), userRecordDto.getActId(), TaskTypeEnum.TASK_TYPE_SHARE.getCode());
            vo.setDoneCount(Math.min(vo.getDayLimit(), todayShareReward.intValue()));
        }
    }

    /**
     * 营养液流水
     * @return
     */
    @GetMapping("/nutrition")
    public Result<Page<NutritionDetailVo>> nutrition(@RequestParam Long id, Integer pageNo, Integer pageSize){
        Page<NutritionDetailVo> page = new Page();
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto){
            return ResultBuilder.success(page);
        }
        if (null == RequestLocal.getCid()){
            return ResultBuilder.success(page);
        }
        try{
            Page<CreditsFarmNutritionRecordDto> dtoPage = remoteCreditsFarmNutritionRecordService.pageQuery(RequestLocal.getCid(), operatingActivityDto.getActivityId(), pageNo, pageSize);
            if (null != dtoPage && CollectionUtils.isNotEmpty(dtoPage.getList())){
                List<NutritionDetailVo> voList = BeanUtils.copyList(dtoPage.getList(), NutritionDetailVo.class);
                page.setTotalCount(dtoPage.getTotalCount());
                page.setTotalPages(dtoPage.getTotalPages());
                page.setList(voList);
            }
        } catch (Exception e ){
           logger.error("营养液记录查询失败，id：{}", id, e);
        }
        return ResultBuilder.success(page);
    }

    /**
     * 我的收获记录
     */
    @GetMapping("/acquisition")
    public Result<List<AcquisitionDetailVo>> acquisition(@RequestParam Long id){
        List<AcquisitionDetailVo> voList = Lists.newArrayList();
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto){
            return ResultBuilder.success(voList);
        }
        if (null == RequestLocal.getCid()){
            return ResultBuilder.success(voList);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null == actDto){
            return ResultBuilder.success(voList);
        }
        voList = creditsFarmService.acquisition(RequestLocal.getCid(), actDto);
        return ResultBuilder.success(voList);
    }

    /**
     * 待兑换奖品
     */
    @GetMapping("/options")
    public Result<List<OptionDetailVo>> options(@RequestParam Long id){
        List<OptionDetailVo> voList = Lists.newArrayList();
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto){
            return ResultBuilder.success(voList);
        }
        if (null == RequestLocal.getCid()){
            return ResultBuilder.success(voList);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        voList = creditsFarmService.queryOptions(actDto, RequestLocal.getCid());
        return ResultBuilder.success(voList);
    }

    @PostMapping("/plant")
    public Result<Long> plant(HttpServletRequest request, @RequestParam Long id, @RequestParam Long seedId, @RequestParam Integer zoneNo) {

        if (zoneNo <= 0){
            return ResultBuilder.fail(ResultCode.C101400);
        }
        //返回种植记录的主键id
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto || OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
            return ResultBuilder.fail(ResultCode.C101401);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null == actDto) {
            return ResultBuilder.fail(ResultCode.C101403);
        }
        Date now = new Date();
        if (now.before(actDto.getStartTime())) {
            return ResultBuilder.fail(ResultCode.C101402);
        }
        if (now.after(actDto.getEndTime())) {
            return ResultBuilder.fail(ResultCode.C101403);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.*********);
        }
        String redisKey = RedisKeyFactory.K1142.toString() + consumer.getId();
        try (RedisLock lock = redisAtomicClient.getLock(redisKey, 5)){
            if (lock == null) {
                return ResultBuilder.fail(ResultCode.C101424);
            }
            StormEngineResultDto riskResult = riskService.joinRisk(id, actDto.getId(), operatingActivityDto.getType());
            if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
                return ResultBuilder.fail(riskResult.getCopy());
            }
            if (!consumer.getAppId().equals(operatingActivityDto.getAppId())) {
                return ResultBuilder.fail(ResultCode.C101404);
            }
            RequestParams requestParams = RequestParams.parse(request);
            Result<Long> result = creditsFarmService.plant(id, actDto, consumer, requestParams, seedId, zoneNo);
            if(result.getSuccess()) {
                //访问日志打印
                printJoinLog(id, actDto.getId(), PageBizTypeEnum.CREDITS_FARM_JOIN.getBizPageId(), consumer, null != result.getData());
            }
            return result;
        } catch (Exception e) {
            logger.warn("种植失败，seedid：{}", seedId, e);
            return ResultBuilder.fail(ResultCode.C101409);
        }
    }

    @GetMapping("/plantResult")
    public Result<PlantResultVo> plantResult(@RequestParam Long recordId){
        return ResultBuilder.success(creditsFarmService.queryPlantResult(recordId));
    }

    @PostMapping("/speed")
    public Result<String> speed(@RequestParam Long id, int count){
        try {
            //返回种植记录的主键id
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
            if (OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
                return ResultBuilder.fail(ResultCode.C101401);
            }
            CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
            if (null == actDto) {
                return ResultBuilder.fail(ResultCode.C101401);
            }
            Date now = new Date();
            if (now.before(actDto.getStartTime())) {
                return ResultBuilder.fail(ResultCode.C101402);
            }
            if (now.after(actDto.getEndTime())) {
                return ResultBuilder.fail(ResultCode.C101403);
            }
            ConsumerDto consumer = RequestLocal.getConsumerDO();
            if (null == consumer || consumer.isNotLoginUser()) {
                return ResultBuilder.fail(ResultCode.*********);
            }
            Integer speedTime = Math.min(CLICK_LIMIT, count) * CLICK_SPEED_SECOND;
            CreditsFarmUserRecordDto userRecordDto = remoteCreditsFarmUserRecordService.findByUnikey(consumer.getId(), actDto.getId());
            if (null == userRecordDto){
                return ResultBuilder.fail(ResultCode.C101420);
            }
            if (null != userRecordDto.getSpeedFrozeTime() && now.before(userRecordDto.getSpeedFrozeTime())) {
                return ResultBuilder.fail(ResultCode.C101419);
            }
            //是否有植物
            if (remoteCreditsFarmCropDetailService.countByCondition(consumer.getId(), actDto.getId(), null) <= 0) {
                return ResultBuilder.fail(ResultCode.C101420);
            }
            if (CollectionUtils.isEmpty(remoteCreditsFarmCropDetailService.selectNotMatureId(consumer.getId(), actDto.getId()))) {
                return ResultBuilder.fail(ResultCode.C101421);

            }
            remoteCreditsFarmCropDetailService.speed(consumer.getId(), actDto.getId(), (long) speedTime);
            return ResultBuilder.success();

        } catch (Exception e) {
            logger.error("加速失败，id:{}", id, e);
        }
        return ResultBuilder.fail(ResultCode.C101400);
    }

    @PostMapping("/useNutrition")
    public Result<UseNutritionResultVo> useNutrition(@RequestParam Long id, @RequestParam Long recordId){
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto ||  OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()){
            return ResultBuilder.fail(ResultCode.C101401);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null == actDto){
            return ResultBuilder.fail(ResultCode.C101401);
        }
        Date now = new Date();
        if (now.after(actDto.getEndTime())){
            return ResultBuilder.fail(ResultCode.C101403);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()){
            return ResultBuilder.fail(ResultCode.*********);
        }
        if (! consumer.getAppId().equals(operatingActivityDto.getAppId())){
            return ResultBuilder.fail(ResultCode.C101404);
        }

        CreditsFarmNutritionRecordDto nutritionRecordDto = new CreditsFarmNutritionRecordDto();
        nutritionRecordDto.setAppId(consumer.getAppId());
        nutritionRecordDto.setConsumerId(consumer.getId());
        nutritionRecordDto.setPartnerUserId(consumer.getPartnerUserId());
        nutritionRecordDto.setActionType(AccountActionTypeEnum.ACTION_OUT.getCode());
        nutritionRecordDto.setActId(actDto.getId());
        nutritionRecordDto.setActionDesc("消耗");
        nutritionRecordDto.setChangeAmount(1);
        try {
            CreditsFarmUserRecordDto userRecordDto = remoteCreditsFarmUserRecordService.findByUnikey(consumer.getId(), actDto.getId());
            if (null == userRecordDto || userRecordDto.getNutritionCount() <= 0) {
                return ResultBuilder.fail(ResultCode.C101422);
            }
            CreditsFarmCropDetailDto cropDetailDto = remoteCreditsFarmCropDetailService.findById(recordId);
            if (! cropDetailDto.getConsumerId().equals(consumer.getId())){
                return ResultBuilder.fail(ResultCode.C101404);
            }
            if (cropDetailDto.getUseNutritionCount() > 0){
                return ResultBuilder.fail(ResultCode.C101426);
            }
            if (now.after(cropDetailDto.getMatureTime())){
                return ResultBuilder.fail(ResultCode.C101428);
            }
            Integer result = remoteCreditsFarmNutritionRecordService.useNutrition(cropDetailDto, nutritionRecordDto);
            UseNutritionResultVo vo = new UseNutritionResultVo();
            vo.setProfit(formatNumber(result));
            vo.setShowNutrition(showNutrition(userRecordDto, actDto.getNutritionCountLimit()));
            return ResultBuilder.success(vo);
        } catch (Exception e) {
            logger.error("营养液使用失败，用户ID：{}", consumer.getAppId(), e);
        }
        return ResultBuilder.fail(ResultCode.C101400);
    }

    /**
     * 是否展示营养液：不展示的情况：目前没有，且今天获得的营养液已达上限
     * @return
     */
    private boolean showNutrition(CreditsFarmUserRecordDto userRecordDto, int dayAddLimit){
        if (null == userRecordDto.getLastNutritionAddTime()){
            return true;
        }
        //如果上次获得时间早与今天
        if (DateUtils.daysBetween(userRecordDto.getLastNutritionAddTime(), new Date() ) < 0){
            return true;
        }
        if (userRecordDto.getNutritionCountToday() < dayAddLimit){
            return true;
        }

        return userRecordDto.getNutritionCount() > 0 ;
    }



    /**
     * 收获
     * @return
     */
    @PostMapping("/gain")
    public Result<GainResultVo> gain(@RequestParam Long id, @RequestParam Long recordId) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.*********);
        }
        String redisKey = RedisKeyFactory.K1140.toString() + consumer.getId() + "_" + recordId;
        try (RedisLock lock = redisAtomicClient.getLock(redisKey, 5)){
            if (lock == null) {
                return ResultBuilder.fail(ResultCode.C101424);
            }
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
            if (OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
                return ResultBuilder.fail(ResultCode.C101401);
            }
            CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
            Date now = new Date();
            if (now.after(actDto.getEndTime())) {
                return ResultBuilder.fail(ResultCode.C101403);
            }
            if (!consumer.getAppId().equals(operatingActivityDto.getAppId())) {
                return ResultBuilder.fail(ResultCode.C101404);
            }
            CreditsFarmCropDetailDto cropDetailDto = remoteCreditsFarmCropDetailService.findById(recordId);
            if (CropStatusEnum.CROP_STATUS_DONE.getCode().equals(cropDetailDto.getCropStatus())) {
                return ResultBuilder.fail(ResultCode.C101425);
            }
            if (now.before(cropDetailDto.getMatureTime())) {
                return ResultBuilder.fail(ResultCode.C101418);
            }
            Integer result = remoteCreditsFarmCropDetailService.gain(cropDetailDto);
            GainResultVo resultVo = new GainResultVo();
            resultVo.setProfit(formatNumber(result));
            CreditsFarmUserSeedRecordDto seedRecordDto = remoteCreditsFarmUserSeedRecordService.findByUnikey(cropDetailDto.getConsumerId(), cropDetailDto.getActId(), cropDetailDto.getSeedId());
            if (seedRecordDto.getTotalIncome() - seedRecordDto.getExpenses() >= actDto.getPrizeCost() * 1000) {
                resultVo.setCanExchange(true);
                List<CreditsFarmOptionDto> optionDtos = remoteCreditsFarmOptionService.selectByCondition(cropDetailDto.getActId(), cropDetailDto.getSeedId());
                if (CollectionUtils.isNotEmpty(optionDtos)) {
                    resultVo.setOptionName(optionDtos.get(0).getOptionName());
                    resultVo.setOptionPic(optionDtos.get(0).getOptionPic());
                }
            }
            return ResultBuilder.success(resultVo);

        } catch (Exception e) {
            logger.error("果实收取失败，recordId:{}", recordId, e);
        }
        return ResultBuilder.fail(ResultCode.C101400);
    }

    /**
     * 积分回退
     * @param id
     * @param seedId
     * @return
     */
    @PostMapping("/creditsTakeBack")
    public Result<Long> creditsTakeBack(HttpServletRequest request, @RequestParam Long id, @RequestParam Long seedId){
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (null == operatingActivityDto ||  OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()){
            return ResultBuilder.fail(ResultCode.C101401);
        }
        CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
        if (null == actDto){
            return ResultBuilder.fail(ResultCode.C101401);
        }
        if (!actDto.getSaleSwitch()){
            return ResultBuilder.fail(ResultCode.C101435);
        }
        Date now = new Date();
        if (now.after(actDto.getEndTime())){
            return ResultBuilder.fail(ResultCode.C101403);
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (null == consumer || consumer.isNotLoginUser()){
            return ResultBuilder.fail(ResultCode.*********);
        }
        if (! consumer.getAppId().equals(operatingActivityDto.getAppId())){
            return ResultBuilder.fail(ResultCode.C101404);
        }
        //接入风控
        StormEngineResultDto riskResult = riskService.joinRisk(id, actDto.getId(), operatingActivityDto.getType());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultBuilder.fail(riskResult.getCopy());
        }

        String redisKey = RedisKeyFactory.K1144.toString() + consumer.getId() + "_" + seedId;
        try(RedisLock lock = redisAtomicClient.getLock(redisKey, 5)){
            if (lock == null) {
                return ResultBuilder.fail(ResultCode.C101424);
            }
            RequestParams requestParams = RequestParams.parse(request);
            return creditsFarmService.creditsTakeBack(id, requestParams, consumer, actDto, seedId);
        } catch (BizException e){
            logger.warn("积分回购失败，seedid：{}, cause:{}", seedId, e.getMessage());
        } catch (Exception e){
            logger.error("积分回购失败，seedid：{}", seedId, e);
        }
        return ResultBuilder.fail(ResultCode.C101400);
    }

    /**
     * 积分回退结果轮询
     * @param recordId 积分出售记录id
     * @return
     */
    @GetMapping("creditsAddResult")
    public Result<CreditsTakeBackResultVo> creditsAddResult(@RequestParam Long recordId){
        return ResultBuilder.success(creditsFarmService.queryCreditAddResult(recordId));
    }

    /**
     * 获取用户关注公众号的状态
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/getSubscribeStatus")
    public Result<Object> getSubscribeStatus(HttpServletRequest request) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (null == app || null == consumer || consumer.isNotLoginUser()) {
            return ResultBuilder.fail(ResultCode.C100001.getCode(), ResultCode.C100001.getDescription());
        }
        return ResultBuilder.success(creditsFarmService.getSubscribeStatus(consumer.getId()));
    }

    /**
     * 助力好友
     * @param id
     * @param shareCode 好友分享码：分享码以日期字符串结尾 xxxxxxx_2019-08-08
     * @return
     */
    @PostMapping("/help")
    public Result<HelpResultVo> help(@RequestParam Long id, @RequestParam String shareCode){
        try {
            //当天分享当天助力有效
            String [] shareCodeStrs = StringUtils.split(shareCode, "_");
            if (shareCodeStrs.length <= 1){
                return ResultBuilder.fail(ResultCode.C101411);
            }
            Date now = new Date();
            if (! StringUtils.equals(DateUtils.getDayStr(now), shareCodeStrs[1])){
                return ResultBuilder.fail(ResultCode.C101410);
            }
            OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
            if (OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
                return ResultBuilder.fail(ResultCode.C101401);
            }
            ConsumerDto consumer = RequestLocal.getConsumerDO();
            if (null == consumer || consumer.isNotLoginUser()) {
                return ResultBuilder.fail(ResultCode.*********);
            }
            if (! consumer.getAppId().equals(operatingActivityDto.getAppId())) {
                return ResultBuilder.fail(ResultCode.C101404);
            }
            CreditsFarmActDto actDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());
            if (null == actDto) {
                return ResultBuilder.fail(ResultCode.C101401);
            }
            if (now.after(actDto.getEndTime())) {
                return ResultBuilder.fail(ResultCode.C101403);
            }

            return creditsFarmService.help(id, actDto, consumer, shareCode, shareCodeStrs[0]);

        } catch (Exception e) {
            logger.error("助力失败，sharecode：{}", shareCode, e);
        }
        return ResultBuilder.fail(ResultCode.C101400);
    }

    /**
     * 领奖
     */
    @PostMapping("/takePrize")
    public Result<String> openPrize(@RequestParam Long optionId, @RequestParam Long id, HttpServletRequest request) {
        //获取 用户信息及app信息
        Quartet<ConsumerDto, AppSimpleDto, String, ResultCode> quartet = findAppAndConsumerInfoNew();
        if (null != quartet.getValue3()) {
            return ResultBuilder.fail(quartet.getValue3().getCode(), quartet.getValue3().getDescription());
        }
        if (null != quartet.getValue2()) {
            return ResultBuilder.fail(quartet.getValue2());
        }
        if (ConsumerCheckUtil.isNotLoginUser(quartet.getValue0().getPartnerUserId())) {
            return ResultBuilder.fail(ResultCode.*********);
        }
        OperatingActivityDto operatingActivityDto = activityCacheService.getOperatingDto(id);
        if (operatingActivityDto == null || !operatingActivityDto.getType().equals(ActivityUniformityTypeEnum.CREDITS_FARM.getCode()) ||  OperatingActivityDto.StatusIntOpen != operatingActivityDto.getStatus()) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        CreditsFarmActDto creditsFarmActDto = activityCacheService.findCreditsFarmActById(operatingActivityDto.getActivityId());

        if (creditsFarmActDto == null) {
            return ResultBuilder.fail(ResultCode.C100015);
        }
        Date now = new Date();
        if (!(now.after(creditsFarmActDto.getStartTime()) && now.before(creditsFarmActDto.getEndTime()))) {
            return ResultBuilder.fail(ResultCode.C101434);
        }
        //接入风控
        StormEngineResultDto riskResult = riskService.joinRisk(id, creditsFarmActDto.getId(), operatingActivityDto.getType());
        if(Objects.equals(riskResult.getDecision().getType(), RiskDecisionEnum.REJECT.getType())){
            return ResultBuilder.fail(riskResult.getCopy());
        }

        if (!quartet.getValue0().getAppId().equals(creditsFarmActDto.getAppId())) {
            return ResultBuilder.fail(ResultCode.C101404);
        }
        try (RedisLock lock = redisAtomicClient.getLock(buildOpenPrizeRedisKey(quartet.getValue0().getId()), 5)) {
            if (lock == null) {
                return ResultBuilder.fail(ResultCode.C101424);
            }
            String link = creditsFarmService.takePrize(optionId, quartet.getValue0(), quartet.getValue1(), creditsFarmActDto, operatingActivityDto, request);
            return ResultBuilder.success(link);
        } catch (BizException e) {
            logger.warn("积分农场开奖出错 optionId:{},actId:{}", optionId, id, e);
            return ResultBuilder.fail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            logger.warn("积分农场开奖出错 optionId:{},actId:{}", optionId, id, e);
            return ResultBuilder.fail(ResultCode.C999999);
        }
    }

    private String buildOpenPrizeRedisKey(Long consumerId) {
        return RedisKeyFactory.K1140.toString() + consumerId;
    }


    /**
     * mg转为g，保留一位小数且不四舍五入
     *
     * @param num
     * @return
     */
    private double formatNumber(Integer num) {
        BigDecimal bd = BigDecimal.valueOf(num / 1000.0);
        return bd.setScale(2, BigDecimal.ROUND_FLOOR).doubleValue();
    }

    /**
     * 判断日期是否是今天
     *
     * @param beforeDate
     * @return
     */
    private boolean isToday(Date beforeDate) {
        return null != beforeDate  && DateUtils.daysBetween(beforeDate, new Date()) == 0;
    }

    /**
     * 访问日志打印
     * @param opId
     * @param activityId
     * @param pageBizId
     * @param consumer
     */
    private void printJoinLog(Long opId, Long activityId, String pageBizId,ConsumerDto consumer,boolean success) {
        AccessLogFilter.putExPair("suc", success ? "1" : "0");
        AccessLogFilter.putExPair("pageBizId", pageBizId);
        AccessLogFilter.putExPair("id", activityId);
        AccessLogFilter.putExPair("activityid", opId);
        AccessLogFilter.putExPair("activitytype", ActivityUniformityTypeEnum.CREDITS_FARM.getCode());
        AccessLogFilter.putExPair("loginStatus", consumer.isNotLoginUser() ? 2 : 1);
        AccessLogFilter.putExPair("userCredits", consumer.getCredits());
    }

}
