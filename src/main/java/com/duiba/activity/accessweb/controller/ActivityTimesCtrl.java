package com.duiba.activity.accessweb.controller;

import com.duiba.activity.accessweb.exception.BusinessException;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.pjv.ResultBuilder;
import com.duiba.activity.accessweb.service.ActivityTimesService;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Desc 活动次数相关接口
 * <AUTHOR>
 * @Date 2018/9/11
 */
@RestController
@RequestMapping("/aaw/activityTimes")
public class ActivityTimesCtrl {

    @Autowired
    private ActivityTimesService activityTimesService;

    /**
     * 活动次数+1
     *
     * @param activityId
     * @return
     */
    @PostMapping("/add")
    public Result<Integer> add(Long activityId,
                               @RequestParam(defaultValue = "hdtool") String activityType,
                               @RequestParam(defaultValue = "1") int times,
                               @RequestParam(defaultValue = "0") int min,
                               @RequestParam(defaultValue = "0") int max) {
        int result;
        try {
            result = activityTimesService.addTimes(activityId, activityType, times);
        } catch (BusinessException e) {
            return ResultBuilder.fail(e.getCode(), e.getMsg());
        }
        return ResultBuilder.success(result + RandomUtils.nextInt(min, max + 1));
    }

    /**
     * 活动次数+1
     *
     * @param activityId
     * @return
     */
    @PostMapping("/addDaily")
    public Result<Integer> addDaily(Long activityId,
                                    @RequestParam(defaultValue = "hdtool") String activityType,
                                    @RequestParam(defaultValue = "1") int times) {
        int result;
        try {
            result = activityTimesService.addDailyTimes(activityId, activityType, times);
        } catch (BusinessException e) {
            return ResultBuilder.fail(e.getCode(), e.getMsg());
        }
        return ResultBuilder.success(result);
    }

    /**
     * 查询活动次数
     *
     * @param activityId
     * @return
     */
    @GetMapping("/get")
    public Result<Integer> get(Long activityId,
                               @RequestParam(defaultValue = "hdtool") String activityType,
                               @RequestParam(defaultValue = "0") int min,
                               @RequestParam(defaultValue = "0") int max) {
        int times;
        try {
            times = activityTimesService.getTimes(activityId, activityType);
        } catch (BusinessException e) {
            return ResultBuilder.fail(e.getCode(), e.getMsg());
        }
        return ResultBuilder.success(times + RandomUtils.nextInt(min, max + 1));
    }

    /**
     * 查询活动次数
     *
     * @param activityId
     * @return
     */
    @GetMapping("/getDaily")
    public Result<Integer> getDaily(Long activityId, @RequestParam(defaultValue = "hdtool") String activityType) {
        int times;
        try {
            times = activityTimesService.getDailyTimes(activityId, activityType);
        } catch (BusinessException e) {
            return ResultBuilder.fail(e.getCode(), e.getMsg());
        }
        return ResultBuilder.success(times);
    }

}
