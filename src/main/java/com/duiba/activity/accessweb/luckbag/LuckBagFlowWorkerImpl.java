package com.duiba.activity.accessweb.luckbag;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.HdtoolOrdersDto;
import cn.com.duiba.activity.center.api.dto.prize.ActivityPrizeOptionDto;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.activity_order.RemoteActivityOrderService;
import cn.com.duiba.activity.center.api.remoteservice.game.RemoteDuibaQuestionAnswerOrdersService;
import cn.com.duiba.activity.center.api.remoteservice.guess.RemoteGuessOrdersConsumerService;
import cn.com.duiba.activity.center.api.remoteservice.guess.RemoteGuessOrdersExtraService;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteCreditsHdtoolOrdersService;
import cn.com.duiba.activity.center.api.remoteservice.quizz.RemoteQuizzOrdersStatusChangeService;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryOrderService;
import cn.com.duiba.boot.perftest.PerfTestContext;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.api.tools.HttpRequestBizUtil;
import cn.com.duiba.api.tools.TuiaDeviceIdGenerator;
import cn.com.duiba.order.center.api.dto.ConsumerExchangeRecordDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerExchangeRecordService;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.tuia.advert.enums.StrategyTypeEnum;
import cn.com.tuia.advert.model.ObtainAdvertReq;
import cn.com.tuia.advert.model.ObtainAdvertRsp;
import cn.com.tuia.advert.service.IEngineService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.constant.CommonConstants;
import com.duiba.activity.accessweb.enums.ActivityErrorCodeEnum;
import com.duiba.activity.accessweb.log.StatActivityThanksLog;
import com.duiba.activity.accessweb.luckbag.LuckBagCallback.LuckBagFlowWorker;
import com.duiba.activity.accessweb.service.questionanswer.QuestionAnswerFlowService;
import com.duiba.activity.accessweb.tool.CatLogTool;
import com.duiba.activity.accessweb.tool.DateUtil;
import com.duiba.activity.accessweb.tool.LuckbagMocker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ClassName:LuckBagFlowWorkerImpl.java <br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 创建时间：2016年11月18日 下午2:51:40
 * @parameter
 * @since JDK 1.6
 */
@Component("luckBagFlowWorker")
public class LuckBagFlowWorkerImpl implements LuckBagFlowWorker{
    
    private static Logger log = LoggerFactory.getLogger(LuckBagFlowWorkerImpl.class);
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private RemoteCreditsHdtoolOrdersService remoteCreditsHdtoolOrdersService;
    @Autowired
    private RemoteConsumerExchangeRecordService remoteConsumerExchangeRecordService;
    @Autowired
    private IEngineService iEngineService;
    @Autowired
    private RemoteActivityOrderService remoteActivityOrderService;
    @Autowired
    private RemoteGuessOrdersConsumerService remoteGuessOrdersConsumerService;
    @Autowired
    private RemoteDuibaQuestionAnswerOrdersService remoteDuibaQuestionAnswerOrdersService;
    @Autowired
    private RemoteQuizzOrdersStatusChangeService remoteQuizzOrdersStatusChangeService;
    @Autowired
    private RemoteSingleLotteryOrderService remoteSingleLotteryOrderService;
    @Autowired
    private QuestionAnswerFlowService questionAnswerFlowService;
    @Autowired
    private RemoteAppService remoteAppService;
    @Autowired
    private RemoteGuessOrdersExtraService remoteGuessOrdersExtraService;

    @Override
    public void asyncActivityNewHttpRequest(LuckBagRequest req,String title){
        //组装  福袋请求数据
        luckBagRequestServer(req, new ActivityLuckBagNewCallBackResponse(req),title);
    }
    
    private void luckBagRequestServer(LuckBagRequest req,ActivityLuckBagNewCallBackResponse callback,String title) {
        ObtainAdvertReq obtain = new ObtainAdvertReq();
        obtain.setConsumerId(req.getConsumerId());
        obtain.setDeviceId(TuiaDeviceIdGenerator.generateDeviceId(req.getConsumerId()));
        obtain.setUa(LuckBagRequest.getUserAgent(req.getUserAgent()));
        obtain.setOrderId(req.getType()+req.getOrderId());
        obtain.setIp(req.getIp());
        obtain.setAppId(req.getAppId());
        obtain.setActivityId(req.getOperatingActivityId());
        obtain.setTimestamp(new Date().getTime());
        obtain.setTag(req.getTag());
        obtain.setActivityUseType(req.getUserType());
        /**兑吧活动类型 ， 从兑吧传过来的活动类型，避免tuia-engine再次查询而形成相互依赖 2016-12-20 */
        obtain.setDuibaActivityType(req.getActivityType());
        /**兑吧活动id ， 从兑吧传过来的活动id，避免tuia-engine再次查询而形成相互依赖 2016-12-20 */
        obtain.setDuibaActivityId(req.getDuibaActivityId());
        obtain.setSlotId(req.getSlotId());
        //1 登录  0 未登录
        obtain.setLoginType("1");
        //按钮埋点号码 
        obtain.setButtonType(req.getButtonType());
        //细节类型
        obtain.setInfoType(req.getInfoType());
        //细节
        obtain.setInfo(req.getInfo());
        //os
        obtain.setOs(req.getOs());
        //点击：0，曝光：1，请求：2，发券：3，计费：4
        obtain.setType("2");
        //用户ua
        obtain.setUserAgent(req.getUserAgent());
        obtain.setActivityUseType(req.getUserType());
        obtain.setDuibaActivityType(req.getActivityType());
//        obtain.setDuibaActivityId(req.getDuibaActivityId());

        // 通过ua判断流量来源：1.微信;2.QQ;3.支付宝;4.其他
        Map<String, String> ext = req.getLogExtMap() == null ? new HashMap<>() : new HashMap<>(req.getLogExtMap());
        ext.put("appFlowType", String.valueOf(HttpRequestBizUtil.parseEnv4Engine(req.getUserAgent())));
        obtain.setLogExtMap(ext);

        if(req.getDuibaActivityId() != null){
            Map<String, String> extMap = obtain.getLogExtMap();
            if(extMap == null){
                obtain.setLogExtMap(new HashMap<>());
            }
            obtain.getLogExtMap().put("shortActivityId", String.valueOf(req.getDuibaActivityId()));
        }
        obtain.setProxy(req.isProxy());
        obtain.setSlotId(req.getSlotId());
        ObtainAdvertRsp advert = null;
        try {
            String mockLuckBag = System.getProperty("mockLuckBag");
            if(PerfTestContext.isCurrentInPerfTestMode() || "true".equals(mockLuckBag)){
                advert = LuckbagMocker.mockAdvert();
            }else{
                CatLogTool.logMetric(CatLogTool.METRIC_LUCKY_REQUEST);
                advert = iEngineService.obtainAdvert(obtain);
            }

            if (null == advert || !advert.isResult()) {
                CatLogTool.logMetric(CatLogTool.METRIC_LUCKY_REQUEST_FAIL);
                if(log.isDebugEnabled()){
                    log.debug("Failed to obtainAdvert, req={}, advert={}", JSON.toJSON(obtain), JSON.toJSON(advert));
                }
                callback.onException(new Exception("广告平台取券异常"));
                return;
            }
            callback.onFinish(advert,title);
        } catch (Exception e) {
            log.warn("es submit obtain advert error", e);
            callback.onException(e);
        }
    }

    private class ActivityLuckBagNewCallBackResponse implements LuckBagCallback {

        private LuckBagRequest req;

        public ActivityLuckBagNewCallBackResponse(LuckBagRequest req) {
            this.req = req;
        }

        @Override
        public void onFinish(ObtainAdvertRsp advert,String title) {
            //正常中福袋
            if(null == advert){
                onException(new Exception("获取福袋失败"));
                return;
            }
            //生成兑换记录
            ConsumerExchangeRecordDto record = new ConsumerExchangeRecordDto(true);
            record.setConsumerId(req.getConsumerId());
            record.setType(req.getRecordType());
            record.setRelationId(Long.valueOf(req.getOrderId()));
            record.enableSwitchs(ConsumerExchangeRecordDto.LUCKBAG); //设置为中福袋
            record.setOrigin(getRecordOrigin(req.getOperatingActivityId(), req.getActivityType()));
            if (advert.getEndValid() != null) {
                record.setOverDue(DateUtil.getDayDate(advert.getEndValid()));
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", advert.getTitle());
            jsonObject.put("logo", advert.getThumbnailPngUrl());
            jsonObject.put("logoNew", advert.getThumbnailPngUrl());
            jsonObject.put("smallImage", advert.getThumbnailPngUrl());
            jsonObject.put("status", OrdersDto.StatusSuccess);
            boolean isHidden=StrategyTypeEnum.STRATEGY_TYEP_FLOW.getCode().equals(advert.getStrategyType());
            jsonObject.put("isHidden", isHidden);
            //设置活动名称
            jsonObject.put("trueActivityTitle", title);
            if(isHidden){
                AppSimpleDto app=remoteAppService.getSimpleApp(req.getAppId()).getResult();
                if(app!=null&&app.isAppSwitch(AppSimpleDto.SwitchFlowRuleRecordVisible)) {
                    jsonObject.put("isAppHidden", false);
                } else {
                    jsonObject.put("isAppHidden", true);
                }
            } else {
                jsonObject.put("isAppHidden", false);
            }
            jsonObject.put("dcm","104."+advert.getAdvertId()+".0.0");
            record.setAppId(req.getAppId());
            record.setJson(jsonObject.toJSONString());
            
            remoteConsumerExchangeRecordService.insert(record);
            //订单置为领奖成功
            if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeHdtoolLottery){
                //更新订单为成功状态
                remoteCreditsHdtoolOrdersService.doTakePrize(req.getConsumerId(), Long.valueOf(req.getOrderId()));
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypePluginLottery || req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeCreditGame){
                remoteActivityOrderService.exchangeStatusToSuccess(req.getOrderId());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeGuess){
            	remoteGuessOrdersConsumerService.updateExchangeStatusToWaitOpenAndExtraIdForLuck(req.getConsumerId(), Long.valueOf(req.getOrderId()), req.getOrderExtraId());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeQuestion){
            	remoteDuibaQuestionAnswerOrdersService.updateExchangeStatusToSucess(Long.valueOf(req.getOrderId()), null, null, null);
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeQuizz){
            	remoteQuizzOrdersStatusChangeService.doTakePrize(req.getConsumerId(), Long.valueOf(req.getOrderId()));
            } else if (Objects.equals(req.getRecordType(), ConsumerExchangeRecordDto.TypeSingleLottery)) {
                remoteSingleLotteryOrderService.doTakePrize(Long.valueOf(req.getOrderId()));
            }
        }

        /**
         * 根据活动id和活动类型拼接兑换记录来源参数
         * @param activityId
         * @param activityType
         * @return
         */
        private Long getRecordOrigin(Long activityId, Integer activityType) {
            if (activityId == null || activityType == null) {
                return null;
            }
            boolean isPlugin = Objects.equals(activityType, OperatingActivityDto.TypePlugin);
            return activityId * 100 + (isPlugin ? ConsumerExchangeRecordDto.ORIGIN_PLUGIN_ACTIVITY : ConsumerExchangeRecordDto.ORIGIN_OPERATING_ACTIVITY);
        }

        @Override
        public void onException(Exception ex) {
            //更新 自订单 为 谢谢参与
            if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeHdtoolLottery){
                //更新订单为成功状态
                remoteCreditsHdtoolOrdersService.updateLotteryLuckyResult(req.getConsumerId(), Long.valueOf(req.getOrderId()), null, null, req.getOptionThanksId(), CommonConstants.SSCY, ActivityPrizeOptionDto.Prize_Type_Thanks, null, null);
                StatActivityThanksLog.log(req.getOperatingActivityId(), req.getAppId(),StatActivityThanksLog.ACT_TYPE_HDTOOL, req.getOrderId(), ActivityErrorCodeEnum.E0100022.getErrorCode());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypePluginLottery || req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeCreditGame){
                // 活动没有配置谢谢参与时，抽中的奖项达到限制条件，返回一个谢谢参与（未中奖）
                //更新订单信息为谢谢参与
                DubboResult<Boolean> result = remoteActivityOrderService.consumeCreditsSuccessDowngrade(req.getOrderId(),req.getOptionThanksId(),CommonConstants.SSCY,
                        HdtoolOrdersDto.PrizeTypeThanks,null,null, null,null,null,null);
                if(!result.isSuccess() || result.getResult() == null || !result.getResult()){
                    log.info("发福袋时降级为谢谢参与后更新抽奖订失败==================orderId============"+req.getOrderId());
                }
                StatActivityThanksLog.log(req.getOperatingActivityId(), req.getAppId(),StatActivityThanksLog.ACT_TYPE_PLUGIN, req.getOrderId(), ActivityErrorCodeEnum.E0100022.getErrorCode());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeCreditGame){

                // 活动没有配置谢谢参与时，抽中的奖项达到限制条件，返回一个谢谢参与（未中奖）
                //更新订单信息为谢谢参与
                DubboResult<Boolean> result = remoteActivityOrderService.consumeCreditsSuccessDowngrade(req.getOrderId(),req.getOptionThanksId(),CommonConstants.SSCY,
                        HdtoolOrdersDto.PrizeTypeThanks,null,null, null,null,null,null);
                if(!result.isSuccess() || result.getResult() == null || !result.getResult()){
                    log.info("发福袋时降级为谢谢参与后更新抽奖订失败,orderId={}",req.getOrderId());
                }
                StatActivityThanksLog.log(req.getOperatingActivityId(), req.getAppId(),StatActivityThanksLog.ACT_TYPE_CREDITSGAME, req.getOrderId(), ActivityErrorCodeEnum.E0100022.getErrorCode());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeGuess){
                //未中福袋不做暂不做降级处理（20170904-hww）
                remoteGuessOrdersConsumerService.updateExchangeStatusToWaitOpenAndExtraId(req.getConsumerId(), Long.valueOf(req.getOrderId()), -101L);
                remoteGuessOrdersExtraService.updateNotPrize(req.getOrderExtraId());
            }else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeQuestion){
				//答题中福袋，降级处理
            	OperatingActivityDto activity = remoteOperatingActivityServiceNew.find(req.getOperatingActivityId());
				questionAnswerFlowService.luckBagLowDownProcess(Long.valueOf(req.getOrderId()), activity);
			}else if(req.getRecordType().intValue() == ConsumerExchangeRecordDto.TypeQuizz){
				//更新订单信息为谢谢参与
				remoteQuizzOrdersStatusChangeService.updateLotteryLuckResult(req.getConsumerId(),Long.valueOf(req.getOrderId()), null,null, null,CommonConstants.SSCY, HdtoolOrdersDto.PrizeTypeThanks,null,null);
			} else if (Objects.equals(req.getRecordType(), ConsumerExchangeRecordDto.TypeSingleLottery)) {
                remoteSingleLotteryOrderService.updatePrizeTypeToThanks(Long.valueOf(req.getOrderId()));
            }
        }
    }
}