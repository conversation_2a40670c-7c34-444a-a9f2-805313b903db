package com.duiba.activity.accessweb.thirdapi.xiaomi;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 小米api对接工具类
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 2018/7/5.
 */
public class XiaoMiUtils {

    private XiaoMiUtils() {
        // x y
    }

    //appId是​由⼩米管理理后台分配
    public static final String XIAOMI_APPID = "e6b07ddac7ede4ff";
    //appSecret是​由⼩米管理理后台分配
    public static final String XIAOMI_APPSECRET = "579e2d06657904d94eb3984d495e45a2";

    /**
     * 小米签名方法
     * 1.该签名的⽣生成请放在服务端​进⾏
     * 2.将​appId,token,timestamp,appSecret这​四个参数的值按照字典排序，
     * 然后拼接成字符串串，再进⾏行行md5⼀一个32位⼩小写的字符串串
     *
     * @param token     ​由⻚页⾯面url参数带给合作⽅方，是64位的字符串
     * @param timestamp 时间戳字符串
     * @return 签名后字符串
     */
    public static String sign(String token, String timestamp) {
        String[] parameters = {XIAOMI_APPID, token, String.valueOf(timestamp), XIAOMI_APPSECRET};
        Arrays.sort(parameters);
        //将数组连接成字符串
        String s = StringUtils.join(parameters);
        return DigestUtils.md5Hex(s);
    }

}
