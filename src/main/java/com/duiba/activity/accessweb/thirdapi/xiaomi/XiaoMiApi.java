package com.duiba.activity.accessweb.thirdapi.xiaomi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duiba.activity.accessweb.pjv.Result;
import com.duiba.activity.accessweb.thirdapi.xiaomi.domain.*;
import com.duiba.activity.accessweb.tool.HttpUtil;
import com.duiba.activity.accessweb.tool.result.ResultUtil;
import com.duiba.activity.accessweb.vo.xiaomi.XiaoMiSaveUserPrizeVO;
import com.duiba.activity.accessweb.vo.xiaomi.XiaoMiUserChanceVO;
import com.duiba.activity.accessweb.vo.xiaomi.XiaoMiUserLuckyVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.*;

/**
 * Created by sun<PERSON><PERSON> on 2018/7/5.
 */
@Component
public class XiaoMiApi {
    private static final Logger LOGGER = LoggerFactory.getLogger(XiaoMiApi.class);

    private static HttpClient xiaomiHttpClient = HttpUtil.createHttpClient(10, 5, 3000, 3000, 3000);
    /**
     * 编码格式
     */
    private static Charset encoding = Charset.forName("utf8");

    /**
     * get请求模板url
     */
    private static final String GET_URL_TPL = "https://lucky.e.mi.com/cooperate/{0}/e6b07ddac7ede4ff/{1}?timestamp={2}&sign={3}";

    /**
     * post请求模板url
     */
    private static final String POST_URL_TPL = "https://lucky.e.mi.com/cooperate/{0}/e6b07ddac7ede4ff/{1}";

    /**
     * 查询用户当天剩余的抽奖机会
     * /cooperate/chance/{appId}/{token}
     *
     * @param token
     * @return null标识请求失败或出异常
     */
    public XiaoMiUserChanceVO getConsumerChance(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(token, timestamp);
        String url = MessageFormat.format(GET_URL_TPL, "chance", token, timestamp, sign);

        String resp = HttpUtil.sendGet(xiaomiHttpClient, url, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi getConsumerChance failed,url={},token={}",url, token);
            return null;
        }
        XiaoMiUserChanceResp response = null;
        try {
            response = JSON.parseObject(resp, XiaoMiUserChanceResp.class);
        } catch (Exception e) {
            LOGGER.warn("XiaoMiUserChanceResponse parseJson error,resp={}", resp);
            return null;
        }

        if (response != null && response.getStatus() == 0) {
            return new XiaoMiUserChanceVO(response.getChance());
        }
        LOGGER.info("XiaoMiApi getConsumerChance status is not 0,token={},resp={}", token, resp);
        return null;
    }

    /**
     * 查询活动的奖品列列表
     * /cooperate/prize/{appId}/{token}
     *
     * @param token
     * @return
     */
    public List<XiaoMiPrizeResp> getPrizeList(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(token, timestamp);
        String url = MessageFormat.format(GET_URL_TPL, "prize", token, timestamp, sign);
        String resp = HttpUtil.sendGet(xiaomiHttpClient, url, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi getPrizeList failed,url={},token={}", url,token);
            return new ArrayList<>();
        }
        XiaoMiPrizeListResp response = null;
        try {
            response = JSON.parseObject(resp, XiaoMiPrizeListResp.class);
        } catch (Exception e) {
            LOGGER.warn("XiaoMiPrizeListResp parseJson error,resp={}", resp);
            return new ArrayList<>();
        }
        if (response != null && response.getStatus() == 0) {
            return response.getPrizeList();

        }
        LOGGER.info("XiaoMiApi getPrizeList status is not 0,token={},resp={}", token, resp);
        return new ArrayList<>();
    }

    /**
     * 实时抽奖接⼝
     * /cooperate/lucky/{appId}/{token}
     *
     * @param token
     * @return XiaoMiUserLuckyVO中lucky是false表示出错了, 表示未中奖
     */
    public XiaoMiUserLuckyVO getLuckyOperate(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(token, timestamp);
        String url = MessageFormat.format(GET_URL_TPL, "lucky", token, timestamp, sign);
        String resp = HttpUtil.sendGet(xiaomiHttpClient, url, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi getLuckyOperate failed,url={},token={}",url, token);
            return new XiaoMiUserLuckyVO(false);
        }
        XiaoMiUserLuckyResp response = null;
        try {
            response = JSON.parseObject(resp, XiaoMiUserLuckyResp.class);
        } catch (Exception e) {
            LOGGER.warn("XiaoMiUserLuckyResp parseJson error,resp={}", resp);
            return new XiaoMiUserLuckyVO(false);
        }
        if (response == null) {
            LOGGER.info("XiaoMiApi XiaoMiUserLuckyResp is null,token={},resp={}", token, resp);
            return new XiaoMiUserLuckyVO(false);
        }
        Long prizeId = response.getPrizeId();
        return new XiaoMiUserLuckyVO()
                .setPrizeId(prizeId)
                .setLucky(prizeId > 0)
                .setCheckCode(response.getCheckCode())
                .setPrizeName(response.getPrizeName())
                .setCoupon(response.getCoupon())
                .setRid(response.getRid())
                .setPicUrl(response.getPicUrl())
                .setAd(response.getAd());
    }


    /**
     * 查询活动的获奖⽤用户
     * /cooperate/winner/{appId}/{token}
     *
     * @param token
     * @return
     */
    public List<WinnerPrizeResp> getWinnerList(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(token, timestamp);
        String url = MessageFormat.format(GET_URL_TPL, "winner", token, timestamp, sign);
        String resp = HttpUtil.sendGet(xiaomiHttpClient, url, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi getWinnerList failed,token={}", token);
            return Collections.emptyList();
        }
        WinnerPrizeListResp response = null;
        try {
            response = JSON.parseObject(resp, WinnerPrizeListResp.class);
        } catch (Exception e) {
            LOGGER.warn("getWinnerList parseJson error,resp={}", resp);
            return Collections.emptyList();
        }
        if (response == null) {
            LOGGER.info("XiaoMiApi getWinnerList is null,token={},resp={}", token, resp);
            return Collections.emptyList();
        }

        return response.getWinnerList();
    }


    /**
     * ⽤户获奖列列表接⼝
     * /cooperate/mine/{appId}/{token}
     *
     * @param token
     * @return
     */
    public List<XiaoMiMineResp> getMineList(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(token, timestamp);
        String url = MessageFormat.format(GET_URL_TPL, "mine", token, timestamp, sign);
        String resp = HttpUtil.sendGet(xiaomiHttpClient, url, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi getMineList failed,url={},token={}",url, token);
            return Collections.emptyList();
        }
        XiaoMiMineListResp response = null;
        try {
            response = JSON.parseObject(resp, XiaoMiMineListResp.class);
        } catch (Exception e) {
            LOGGER.warn("getMineList parseJson error,resp={}", resp);
            return Collections.emptyList();
        }
        if (response == null) {
            LOGGER.info("XiaoMiApi getMineList is null,token={},resp={}", token, resp);
            return Collections.emptyList();
        }
        return response.getData();
    }

    /**
     * 保存中奖信息
     *
     * @param userPrizeVO
     * @return
     */
    public Result<String> userPrizeSave(XiaoMiSaveUserPrizeVO userPrizeVO) {
        String token = userPrizeVO.getToken();
        String url = MessageFormat.format(POST_URL_TPL, "save", token);
        Map<String, String> paraMap = getSaveUserPrizePostParam(userPrizeVO);
        String resp = HttpUtil.sendPost(xiaomiHttpClient, url, paraMap, encoding);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.info("XiaoMiApi userPrizeSave failed,token={}", token);
            return ResultUtil.fail("领奖失败");
        }
        XiaoMiBaseResp response = null;
        try {
            response = JSON.parseObject(resp, XiaoMiBaseResp.class);
        } catch (Exception e) {
            LOGGER.warn("XiaoMiBaseResp parseJson error,resp={}", resp);
            return ResultUtil.fail("领奖失败");
        }
        if (response != null && response.getStatus() == 0) {
            return ResultUtil.successWithData("成功");

        }
        LOGGER.info("XiaoMiApi getConsumerChance status is not 0,token={},resp={}", token, resp);
        if (response == null) {
            return ResultUtil.fail("领奖失败");
        }

        return ResultUtil.fail(response.getMessage());
    }


    private Map<String, String> getSaveUserPrizePostParam(XiaoMiSaveUserPrizeVO userPrizeVO) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = XiaoMiUtils.sign(userPrizeVO.getToken(), timestamp);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("timestamp", timestamp);
        paramMap.put("sign", sign);
        paramMap.put("rid", String.valueOf(userPrizeVO.getRid()));
        paramMap.put("checkCode", userPrizeVO.getCheckCode());
        String name = userPrizeVO.getName();
        String phone = userPrizeVO.getPhone();
        String address = userPrizeVO.getAddress();
        String idCard = userPrizeVO.getIdCard();
        String memo = userPrizeVO.getMemo();
        if (StringUtils.isNotEmpty(name)) {
            paramMap.put("name", name);
        }
        if (StringUtils.isNotEmpty(phone)) {
            paramMap.put("phone", phone);
        }
        if (StringUtils.isNotEmpty(address)) {
            paramMap.put("address", address);
        }
        if (StringUtils.isNotEmpty(idCard)) {
            paramMap.put("idCard", idCard);
        }
        if (StringUtils.isNotEmpty(memo)) {
            paramMap.put("memo", memo);
        }

        return paramMap;
    }

}
